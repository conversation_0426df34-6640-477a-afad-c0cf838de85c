<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.ReportMapper">
    <resultMap id="ResultMap" type="java.util.TreeMap">
        <result property="brand_no" column="BRAND_NO" jdbcType="VARCHAR"/>
        <result property="last_nos" column="LAST_NOS" jdbcType="VARCHAR"/>
        <result property="constr" column="CONSTR" jdbcType="VARCHAR"/>
        <result property="model_desc" column="MODEL_DESC" jdbcType="VARCHAR"/>
        <result property="model_no" column="MODEL_NO" jdbcType="VARCHAR"/>
        <result property="procs_pic" column="PROCS_PIC" jdbcType="BLOB"
                typeHandler="org.apache.ibatis.type.BlobTypeHandler"/>
        <result property="procs_type" column="PROCS_TYPE" jdbcType="INTEGER"/>
        <result property="part_seq" column="PART_SEQ" jdbcType="VARCHAR"/>
        <result property="part_name" column="PART_NAME" jdbcType="VARCHAR"/>
        <result property="part_spec" column="PART_SPEC" jdbcType="INTEGER"/>
        <result property="add_per" column="ADD_PER" jdbcType="DOUBLE"/>
        <result property="siz_type" column="SIZ_TYPE" jdbcType="VARCHAR"/>
        <result property="bas_size" column="BAS_SIZE" jdbcType="VARCHAR"/>
        <result property="rd" column="RD" jdbcType="INTEGER"/>
        <result property="procs_count" column="PROCS_COUNT" jdbcType="INTEGER"/>
        <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
        <result property="size01" column="SIZE01" jdbcType="DOUBLE"/>
        <result property="size02" column="SIZE02" jdbcType="DOUBLE"/>
        <result property="size03" column="SIZE03" jdbcType="DOUBLE"/>
        <result property="size04" column="SIZE04" jdbcType="DOUBLE"/>
        <result property="size05" column="SIZE05" jdbcType="DOUBLE"/>
        <result property="size06" column="SIZE06" jdbcType="DOUBLE"/>
        <result property="size07" column="SIZE07" jdbcType="DOUBLE"/>
        <result property="size08" column="SIZE08" jdbcType="DOUBLE"/>
        <result property="size09" column="SIZE09" jdbcType="DOUBLE"/>
        <result property="size10" column="SIZE10" jdbcType="DOUBLE"/>
        <result property="size11" column="SIZE11" jdbcType="DOUBLE"/>
        <result property="size12" column="SIZE12" jdbcType="DOUBLE"/>
        <result property="size13" column="SIZE13" jdbcType="DOUBLE"/>
        <result property="size14" column="SIZE14" jdbcType="DOUBLE"/>
        <result property="size15" column="SIZE15" jdbcType="DOUBLE"/>
        <result property="size16" column="SIZE16" jdbcType="DOUBLE"/>
        <result property="size17" column="SIZE17" jdbcType="DOUBLE"/>
        <result property="size18" column="SIZE18" jdbcType="DOUBLE"/>
        <result property="size19" column="SIZE19" jdbcType="DOUBLE"/>
        <result property="size20" column="SIZE20" jdbcType="DOUBLE"/>
        <result property="size21" column="SIZE21" jdbcType="DOUBLE"/>
        <result property="size22" column="SIZE22" jdbcType="DOUBLE"/>
        <result property="size23" column="SIZE23" jdbcType="DOUBLE"/>
        <result property="size24" column="SIZE24" jdbcType="DOUBLE"/>
        <result property="size25" column="SIZE25" jdbcType="DOUBLE"/>
        <result property="size26" column="SIZE26" jdbcType="DOUBLE"/>
        <result property="size27" column="SIZE27" jdbcType="DOUBLE"/>
        <result property="size28" column="SIZE28" jdbcType="DOUBLE"/>
        <result property="size29" column="SIZE29" jdbcType="DOUBLE"/>
        <result property="size30" column="SIZE30" jdbcType="DOUBLE"/>
    </resultMap>

    <select id="getPicture" resultMap="ResultMap">
        select a.model_no, nvl(b.procs_pic, a.model_pic) procs_pic
        from ck_smodel a,
             (select model_no, procs_pic
              from ck_smodelp
              where procs_type = 2) b
        where a.model_no = b.model_no(+)
          and a.model_no = #{model_no}
    </select>

    <select id="getInfo" resultMap="ResultMap">
        select a.brand_no,   -- 品牌
               a.model_no,   -- 型体
               a.model_desc, -- 型体描述
               a.siz_type,   -- 码别
               a.bas_size,   -- 基本码
               a.series,     -- 系列
               c.last_nos,   -- 楦头
               b.constr      -- 结构
        from ck_smodel a,
             be_moudel b,
             bf_last c,
             cb_model d
        where a.module_no = b.module_no(+)
          and b.last_seq = c.last_seq(+)
          and a.model_no = d.model_no(+)
          and a.model_no = #{model_no}
    </select>

    <select id="getTitle" resultMap="ResultMap">
        with vck_smodelp as
                 (select b.model_no,
                         b.procs_type,
                         a.siz_no,
                         a.bas_size,
                         fn_sizecapion(#{siz_type}, b.s_size) s_size,
                         fn_sizecapion(#{siz_type}, b.e_size) e_size
                  from vba_sizetype a,
                       ck_smodelp b
                  where a.siz_type = #{siz_type}
                    and b.model_no = #{model_no}
                    and a.siz_no &gt;= fn_sizecapion(#{siz_type}, b.s_size)
                    and a.siz_no &lt;= fn_sizecapion(#{siz_type}, b.e_size))
        select a.*,
               #{siz_type}                             siz_type,
               #{bas_size}                             bas_size,
               (select count(1)
                from ck_smodelpa
                where model_no = a.model_no
                  and procs_type = a.procs_type)       rd,
               (select count(1)
                from vck_smodelp x
                where procs_type = (select min(procs_type)
                                    from vck_smodelp)) procs_count,
               b.remark
        from (select model_no,
                     procs_type,
                     bas_size,
                     substr(siz_no, 1, 4) || lpad((substr(siz_no, 5, 2) - (select substr(min(s_size), 5, 2) - 1
                                                                           from vck_smodelp)), 2, '0') siz_no
              from vck_smodelp) pivot (max(bas_size) for siz_no in ('SIZE01' SIZE01, 'SIZE02' SIZE02, 'SIZE03' SIZE03, 'SIZE04' SIZE04, 'SIZE05' SIZE05, 'SIZE06' SIZE06,
            'SIZE07' SIZE07, 'SIZE08' SIZE08, 'SIZE09' SIZE09, 'SIZE10' SIZE10, 'SIZE11' SIZE11, 'SIZE12' SIZE12, 'SIZE13' SIZE13, 'SIZE14' SIZE14,
            'SIZE15' SIZE15, 'SIZE16' SIZE16, 'SIZE17' SIZE17, 'SIZE18' SIZE18, 'SIZE19' SIZE19, 'SIZE20' SIZE20, 'SIZE21' SIZE21, 'SIZE22' SIZE22,
            'SIZE23' SIZE23, 'SIZE24' SIZE24, 'SIZE25' SIZE25, 'SIZE26' SIZE26, 'SIZE27' SIZE27, 'SIZE28' SIZE28, 'SIZE29' SIZE29, 'SIZE30' SIZE30)) a,
             ck_smodelp b
        where a.model_no = b.model_no
          and a.procs_type = b.procs_type
        order by a.model_no, a.procs_type
    </select>

    <select id="getFullSize" resultType="integer">
        select full_size
        from (select rownum rn, t.*
              from (select full_size
                    from ck_smodelp
                    where model_no = #{model_no}
                    order by full_size) t)
        where rn = 1
    </select>

    <update id="updateFullSize">
        update ck_smodelp
        set full_size = #{full_size}
        where model_no = #{model_no}
    </update>

    <select id="getCount1" resultMap="ResultMap">
        with vck_smodelp as
                 (select b.model_no,
                         b.procs_type,
                         a.siz_no,
                         a.bas_size,
                         fn_sizecapion(#{siz_type}, b.s_size) s_size,
                         fn_sizecapion(#{siz_type}, b.e_size) e_size
                  from vba_sizetype a,
                       ck_smodelp b
                  where a.siz_type = #{siz_type}
                    and b.model_no = #{model_no}
                    and b.procs_type = #{procs_type}
                    and a.siz_no &gt;= fn_sizecapion(#{siz_type}, b.s_size)
                    and a.siz_no &lt;= fn_sizecapion(#{siz_type}, b.e_size))
        select *
        from (select a.model_no,
                     a.procs_type,
                     b.part_seq,
                     b.part_name, /* a.bas_size, bas_seq,*/
                     siz_no, /*siz_seq, */b.part_spec,
                     b.add_per,
                     case
                         when b.add_per = 0 and a.bas_seq &lt;&gt; a.siz_seq then null
                         else b.part_spec + b.add_per * (siz_seq - bas_seq) / 2 end part_specs
              from (select model_no,
                           procs_type,
                           bas_size,
                           substr(fn_sizecapion(#{siz_type}, #{bas_size}), 5, 2)                             bas_seq,
                           substr(siz_no, 5, 2)                                                              siz_seq,
                           s_size,
                           e_size,
                           substr(siz_no, 1, 4) || lpad((substr(siz_no, 5, 2) - (select substr(min(s_size), 5, 2) - 1
                                                                                 from vck_smodelp)), 2, '0') siz_no
                    from vck_smodelp) a,
                   ck_smodelpa b
              where a.model_no = b.model_no
                and a.procs_type = b.procs_type) pivot (max(part_specs) for siz_no in ('SIZE01' SIZE01, 'SIZE02' SIZE02, 'SIZE03' SIZE03, 'SIZE04' SIZE04, 'SIZE05' SIZE05, 'SIZE06' SIZE06,
            'SIZE07' SIZE07, 'SIZE08' SIZE08, 'SIZE09' SIZE09, 'SIZE10' SIZE10, 'SIZE11' SIZE11, 'SIZE12' SIZE12, 'SIZE13' SIZE13, 'SIZE14' SIZE14,
            'SIZE15' SIZE15, 'SIZE16' SIZE16, 'SIZE17' SIZE17, 'SIZE18' SIZE18, 'SIZE19' SIZE19, 'SIZE20' SIZE20, 'SIZE21' SIZE21, 'SIZE22' SIZE22,
            'SIZE23' SIZE23, 'SIZE24' SIZE24, 'SIZE25' SIZE25, 'SIZE26' SIZE26, 'SIZE27' SIZE27, 'SIZE28' SIZE28, 'SIZE29' SIZE29, 'SIZE30' SIZE30))
        order by 1, 2
    </select>

    <select id="getCount2" resultMap="ResultMap">
        with vck_smodelp as
                 (select b.model_no,
                         b.procs_type,
                         a.siz_no,
                         a.bas_size,
                         fn_sizecapion(#{siz_type}, b.s_size) s_size,
                         fn_sizecapion(#{siz_type}, b.e_size) e_size
                  from vba_sizetype a,
                       ck_smodelp b
                  where a.siz_type = #{siz_type}
                    and b.model_no = #{model_no}
                    and b.procs_type = #{procs_type}
                    and a.siz_no &gt;= fn_sizecapion(#{siz_type}, b.s_size)
                    and a.siz_no &lt;= fn_sizecapion(#{siz_type}, b.e_size))
        select *
        from (select a.model_no,
                     a.procs_type,
                     b.part_seq,
                     b.part_name, /* a.bas_size, bas_seq,*/
                     siz_no, /*siz_seq, */b.part_spec,
                     b.add_per,
                     case
                         when b.add_per = 0 and a.bas_seq &lt;&gt; a.siz_seq then null
                         else b.part_spec + b.add_per * (siz_seq - bas_seq) end part_specs
              from (select model_no,
                           procs_type,
                           bas_size,
                           substr(fn_sizecapion(#{siz_type}, #{bas_size}), 5, 2)                             bas_seq,
                           substr(siz_no, 5, 2)                                                              siz_seq,
                           s_size,
                           e_size,
                           substr(siz_no, 1, 4) || lpad((substr(siz_no, 5, 2) - (select substr(min(s_size), 5, 2) - 1
                                                                                 from vck_smodelp)), 2, '0') siz_no
                    from vck_smodelp) a,
                   ck_smodelpa b
              where a.model_no = b.model_no
                and a.procs_type = b.procs_type) pivot (max(part_specs) for siz_no in ('SIZE01' SIZE01, 'SIZE02' SIZE02, 'SIZE03' SIZE03, 'SIZE04' SIZE04, 'SIZE05' SIZE05, 'SIZE06' SIZE06,
            'SIZE07' SIZE07, 'SIZE08' SIZE08, 'SIZE09' SIZE09, 'SIZE10' SIZE10, 'SIZE11' SIZE11, 'SIZE12' SIZE12, 'SIZE13' SIZE13, 'SIZE14' SIZE14,
            'SIZE15' SIZE15, 'SIZE16' SIZE16, 'SIZE17' SIZE17, 'SIZE18' SIZE18, 'SIZE19' SIZE19, 'SIZE20' SIZE20, 'SIZE21' SIZE21, 'SIZE22' SIZE22,
            'SIZE23' SIZE23, 'SIZE24' SIZE24, 'SIZE25' SIZE25, 'SIZE26' SIZE26, 'SIZE27' SIZE27, 'SIZE28' SIZE28, 'SIZE29' SIZE29, 'SIZE30' SIZE30))
        order by 1, 2
    </select>

    <select id="getInsertInfo" resultType="com.zqn.modeldata2.entity.CkSmodelp">
        select *
        from (select rownum rn, t.*
              from (select fn_sy_userdesc(ins_user) ins_user, -- 添加者
                           ins_date,                          -- 添加时间
                           upd_user,                          -- 更新者
                           upd_date                           -- 更新时间
                    from ck_smodelp
                    where model_no = #{model_no}
                    order by ins_date) t)
        where rn = 1
    </select>

    <select id="getSignatureById" resultType="com.zqn.modeldata2.entity.Sign">
        select user_no, user_id, user_desc, signature
        from sy_user
        where user_id = #{sign.user_id}
    </select>

    <select id="getSignatureFile" resultType="com.zqn.modeldata2.entity.SignFile">
        select model_no,
               chk_user,
               chk_date,
               upp_user,
               upp_date,
               sol_user,
               sol_date,
               ins_user,
               ins_date,
               upd_user,
               upd_date
        from ck_smodelpb
        where model_no = #{signFile.model_no}
    </select>

    <select id="getChkSignature" resultType="com.zqn.modeldata2.entity.Sign">
        select a.signature
        from sy_user a,
             ck_smodelpb b
        where a.user_no = b.chk_user
          and b.model_no = #{sign.model_no}
    </select>

    <select id="getUppSignature" resultType="com.zqn.modeldata2.entity.Sign">
        select a.signature
        from sy_user a,
             ck_smodelpb b
        where a.user_no = b.upp_user
          and b.model_no = #{sign.model_no}
    </select>

    <select id="getSolSignature" resultType="com.zqn.modeldata2.entity.Sign">
        select a.signature
        from sy_user a,
             ck_smodelpb b
        where a.user_no = b.sol_user
          and b.model_no = #{sign.model_no}
    </select>

    <insert id="addChkSignature">
        insert into ck_smodelpb(model_no, chk_user, chk_date, ins_user, ins_date, upd_user, upd_date)
        values (#{signFile.model_no}, #{signFile.chk_user}, sysdate, #{signFile.ins_user}, #{signFile.ins_date},
                #{signFile.upd_user}, #{signFile.upd_date})
    </insert>

    <insert id="addUppSignature">
        insert into ck_smodelpb(model_no, upp_user, upp_date, ins_user, ins_date, upd_user, upd_date)
        values (#{signFile.model_no}, #{signFile.upp_user}, sysdate, #{signFile.ins_user}, #{signFile.ins_date},
                #{signFile.upd_user}, #{signFile.upd_date})
    </insert>

    <insert id="addSolSignature">
        insert into ck_smodelpb(model_no, sol_user, sol_date, ins_user, ins_date, upd_user, upd_date)
        values (#{signFile.model_no}, #{signFile.sol_user}, sysdate, #{signFile.ins_user}, #{signFile.ins_date},
                #{signFile.upd_user}, #{signFile.upd_date})
    </insert>

    <update id="updateChkSignature">
        update ck_smodelpb
        set chk_user = #{signFile.chk_user},
            chk_date = sysdate,
            ins_user = #{signFile.ins_user},
            ins_date = #{signFile.ins_date},
            upd_user = #{signFile.upd_user},
            upd_date = #{signFile.upd_date}
        where model_no = #{signFile.model_no}
    </update>

    <update id="updateUppSignature">
        update ck_smodelpb
        set upp_user = #{signFile.upp_user},
            upp_date = sysdate,
            ins_user = #{signFile.ins_user},
            ins_date = #{signFile.ins_date},
            upd_user = #{signFile.upd_user},
            upd_date = #{signFile.upd_date}
        where model_no = #{signFile.model_no}
    </update>

    <update id="updateSolSignature">
        update ck_smodelpb
        set sol_user = #{signFile.sol_user},
            sol_date = sysdate,
            ins_user = #{signFile.ins_user},
            ins_date = #{signFile.ins_date},
            upd_user = #{signFile.upd_user},
            upd_date = #{signFile.upd_date}
        where model_no = #{signFile.model_no}
    </update>

    <update id="resetChkSignature">
        update ck_smodelpb
        set chk_user = null,
            chk_date = null
        where model_no = #{signFile.model_no}
    </update>

    <update id="resetUppSignature">
        update ck_smodelpb
        set upp_user = null,
            upp_date = null
        where model_no = #{signFile.model_no}
    </update>

    <update id="resetSolSignature">
        update ck_smodelpb
        set sol_user = null,
            sol_date = null
        where model_no = #{signFile.model_no}
    </update>
</mapper>