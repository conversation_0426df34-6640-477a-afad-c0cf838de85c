<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import urlPrefix from '@/pages/common/urlPrefix.js'

// 生产环境
// const urlPrefix = "http://***********:8200"
const user = uni.getStorageSync("loUserNo")

// 开发环境
// const urlPrefix = "http://**********:8080"
// const user = "zqn"

const step = ref()

const operationMap = new Map([
  ["加工", "1"],
  ["裁断", "2"],
  ["准备", "3"],
  ["针车", "4"],
  ["半成品", "5"],
  ["成型", "6"]
])

const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')

// 返回上一页
function back() {
  step.value = ''
  uni.navigateBack({
    delta: 1,
    animationType: 'pop-out',
    animationDuration: 200
  })
}

// 获取工序编号
function getNo() {
  uni.request({
    url: urlPrefix + "/process/getNo",
    method: "POST",
    data: {
      ...step.value,
      operation: operationMap.get(step.value.operation)
    }
  }).then(res => {
    step.value.seq_no = res.data.data ? res.data.data : ''
    step.value.wk_group = step.value.seq_no
  }).catch(err => {
    showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 获取排序号
function getKey() {
  uni.request({
    url: urlPrefix + "/process/getKey",
    method: "POST",
    data: {
      ...step.value,
      operation: operationMap.get(step.value.operation)
    }
  }).then(res => {
    step.value.skey = res.data.data ? res.data.data : ''
  }).catch(err => {
    showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 添加工序步骤
function addProcessStep() {
  if (!step.value.skey || step.value.skey.length === 0) {
    showTip('warn', '请输入排序号！')
    return
  }
  
  if (!step.value.wk_group || step.value.wk_group.length === 0) {
    showTip('warn', '请输入加工段！')
    return
  }
  
  if (!step.value.seq_name || step.value.seq_name.length === 0) {
    showTip('warn', '请输入工序名称！')
    return
  }
  
  uni.request({
    url: urlPrefix + "/process/addProcessStep",
    method: "POST",
    data: {
      ...step.value,
      operation: operationMap.get(step.value.operation)
    }
  }).then(res => {
    if (res.data.code) {
      showTip('success', '工序步骤添加成功！')
      setTimeout(() => {
        back()
      }, 1000)
    } else {
      showTip('error', '工序步骤添加失败！')
    }
  }).catch(err => {
    showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 提示信息
function showTip(type, message) {
  tipType.value = type
  tipMessage.value = message
  tipPopup.value.open()
}

onLoad((props) => {
  step.value = JSON.parse(props.step)
  step.value.remark = ''
  step.value.ins_user = user
  step.value.upd_user = user
  getNo()
  getKey()
})
</script>

<template>
  <view class="add-step">
    <view class="back">
      <uni-icons @click="back()" type="back" size="36" color="#6fa2ce"></uni-icons>
    </view>
    
    <view class="submit">
      <uni-icons @click="addProcessStep()" type="checkmarkempty" size="36" color="#45b08c"></uni-icons>
    </view>
    
    <view class="title">
      添加工序步骤
    </view>
    
    <view class="data">
      <uni-section title="型体编号" titleFontSize="20px" type="circle" class="attribute">
        <uni-easyinput :value="step.model_no" disabled></uni-easyinput>
      </uni-section>
      
      <uni-section title="制程" titleFontSize="20px" type="circle" class="attribute" style="margin-left: 0;">
        <uni-easyinput :value="step.operation" disabled></uni-easyinput>
      </uni-section>
      
      <uni-section title="主要代码" titleFontSize="20px" type="circle" class="attribute">
        <uni-easyinput :value="step.rtg_code" disabled></uni-easyinput>
      </uni-section>
      
      <uni-section title="工序编号" titleFontSize="20px" type="circle" class="attribute" style="margin-left: 0;">
        <uni-easyinput :value="step.seq_no" disabled></uni-easyinput>
      </uni-section>
      
      <uni-section title="排序号" titleFontSize="20px" type="line" class="attribute">
        <uni-easyinput v-model="step.skey" class="variable"></uni-easyinput>
      </uni-section>
      
      <uni-section title="加工段" titleFontSize="20px" type="line" class="attribute" style="margin-left: 0;">
        <uni-easyinput v-model="step.wk_group" class="variable"></uni-easyinput>
      </uni-section>
      
      <uni-section title="工序名称" titleFontSize="20px" type="line" class="attribute">
        <textarea
          v-model="step.seq_name"
          placeholder="请输入工序名称"
          class="textarea"
        ></textarea>
      </uni-section>
      
      <uni-section title="备注" titleFontSize="20px" type="line" class="attribute" style="margin-left: 0;">
        <textarea
          v-model="step.remark"
          placeholder="请输入备注内容"
          class="textarea"
        ></textarea>
      </uni-section>
    </view>
  </view>
  
  <view class="tip-popup">
    <uni-popup
      ref="tipPopup"
      type="message"
    >
      <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
    </uni-popup>
  </view>
</template>

<style lang="scss">
page {
  width: 100%;
  height: 100%;
  padding: 2.5% 2% 1.5% 2%;
  box-sizing: border-box;
  background-color: #fdf6e3;
}

.add-step {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0 0 1px 5px #ddd;
  position: relative;
  overflow: auto;
  
  .back {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 2.5%;
    top: 2.5%;
    border-radius: 50%;
    box-shadow: 0 0 5px gray;
    transition: all 0.05s ease-in-out;
    cursor: pointer;
    z-index: 1;
    
    &:active {
      transform: scale(0.97);
      box-shadow: 0 0 1px gray;
    }
  }
      
  .submit {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 2.5%;
    top: 2.5%;
    border-radius: 50%;
    box-shadow: 0 0 5px gray;
    transition: all 0.05s ease-in-out;
    cursor: pointer;
    z-index: 1;
    
    &:active {
      transform: scale(0.97);
      box-shadow: 0 0 1px gray;
    }
  }
      
  .title {
    margin-bottom: 1%;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    padding: 16px;
  }
      
  .data {
    width: 100%;
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    
    .attribute {
      width: 35%;
      margin-left: 15%;
      margin-bottom: 3%;
      background-color: #fdf6e3;
      
      .uni-easyinput {
        width: 80%;
        margin-left: 16px;
        
        &:deep(.uni-easyinput__content-input) {
          height: 40px;
          color: gray;
          font-size: 20px;
          font-weight: bold;
        }
      }
      
      .variable {
        &:deep(.uni-easyinput__content-input) {
          color: black;
        }
      }
      
      .textarea {
        width: 80%;
        height: 80px;
        padding: 10px;
        margin-left: 16px;
        border-radius: 10px;
        border: 2px solid gray;
        background-color: white;
        text-align: left;
        font-size: 20px;
        
        &:hover {
          border: 2px solid black;
        }
      }
    }
  }
}

.tip-popup {
  &:deep(.fixforpc-width) {
    min-width: 0;
    margin-top: 40px;
    padding: 10px 20px;
  }
  
  &:deep(.uni-popup-message-text) {
    font-size: 18px;
    font-weight: bold;
  }
}
</style>