<!-- 选项弹框 - 加工 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { deptMap1, optionTypeMap, optionListMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 选项弹框
const overallOptionPopup = ref()

// 选项下标
const overallOptionIndex = ref(0)

// 选项类型
const overallOptionType = ref('')

// 详细制程
const proSeq = ref('')

// 工序流程详情
const flowDetail = ref({})

// 选项列表
const overallOptionList = ref([])

// 选项输入框
const overallOptionInput = ref('')
// 聚焦选项输入框
const focusOverallOptionInput = ref(false)
// 选项搜索框
const overallOptionSearch = ref('')
// 聚焦选项搜索框
const focusOverallOptionSearch = ref(false)

// 工序信息
const processInfo = inject('processInfo')
// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getOverallFlow = inject('getOverallFlow')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')

// 获取选项列表
async function getOverallOptionList(param1, param2) {
  await uni.request({
    url: urlPrefix + '/sop/getFlowOptionList',
    method: 'POST',
    data: {
      type: optionTypeMap.get(param1),
      dept: deptMap1.get(param2)
    }
  }).then(res => {
    if (res.data.code) {
      overallOptionList.value = res.data.data ? res.data.data : []
    } else {
      overallOptionList.value = []
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示选项弹框
async function showOverallOptionPopup(param1, param2, param3) {
  flowDetail.value = {
    ...param3,
    imgList: []
  }
  overallOptionInput.value = param3['processOption' + (param1 + 1)]
  
  proSeq.value = param2
  overallOptionIndex.value = param1
  overallOptionType.value = param2 + '-' + (param1 + 1)
  
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await getOverallOptionList(overallOptionType.value, proSeq.value)
  
  uni.hideLoading()
  
  overallOptionPopup.value.open()
}

// 选择工序流程选项
function selectOverallOption(param) {
  if (overallOptionInput.value) {
    if (overallOptionInput.value.includes('\n' + param.toUpperCase() + '\n')) {
      overallOptionInput.value = overallOptionInput.value.replace(new RegExp(`\n${param}\n`, 'g'), '\n')
    } else if (overallOptionInput.value.startsWith(param + '\n')) {
      overallOptionInput.value = overallOptionInput.value.replace(param + '\n', '')
    } else {
      overallOptionInput.value += (param + '\n')
    }
  } else {
    overallOptionInput.value = param + '\n'
  }
}

// 修改整体流程
async function updateOverallFlow() {
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  if (proSeq.value === '1F' && (overallOptionIndex.value + 1) === 2) {
    flowDetail.value.processOption3 = overallOptionInput.value?.split('\n')?.filter(item => item.length > 0 && !isNaN(item))?.map(item => item * 2)?.join('\n')
  }
  
  await uni.request({
    url: urlPrefix + '/sop/updateOverallFlow',
    method: 'POST',
    data: {
      ...flowDetail.value,
      ['processOption' + (overallOptionIndex.value + 1)]: overallOptionInput.value,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getOverallFlow()
      tipPopup.value.showTipPopup('success', '修改成功！')
      overallOptionPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  showOverallOptionPopup
})
</script>

<template>
  <uni-popup
    ref="overallOptionPopup"
    type="center"
    :is-mask-click="false"
    class="overall-option-popup"
  >
    <view class="container">
      <view class="top-bar">
        <view
          @click="overallOptionPopup.close()"
          class="cancel button"
        >
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <view class="title">
          请选择{{ optionListMap.get(proSeq)[overallOptionIndex] }} - {{ flowDetail.skey }}
        </view>
        
        <view
          @click="updateOverallFlow()"
          class="submit button"
        >
          <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
        </view>
      </view>
      
      <view v-show="flowDetail.actions" class="flow-action flex-row-center">
        <view class="flow-action-description">
          {{ flowDetail.actions }}
        </view>
      </view>
      
      <view class="flow-option-input flex-row-center">
        <textarea
          v-model="overallOptionInput"
          @focus="focusOverallOptionInput = true"
          @blur="focusOverallOptionInput = false"
          :placeholder="'请输入' + optionListMap.get(proSeq)[overallOptionIndex]"
          class="textarea"
          :style="{
            boxShadow: focusOverallOptionInput ? '0 0 5px blue' : '0 0 5px gray'
          }"
        ></textarea>
        
        <view
          v-show="overallOptionInput"
          @click="overallOptionInput = ''"
          class="clear-textarea button"
        >
          <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
        </view>
      </view>
      
      <view class="flow-option-search flex-row-center">
        <input
          v-model="overallOptionSearch"
          @focus="focusOverallOptionSearch = true"
          @blur="focusOverallOptionSearch = false"          
          placeholder="请输入搜索内容"
          type="text"
          class="input"
          :style="{
            boxShadow: focusOverallOptionSearch ? '0 0 5px blue' : '0 0 5px gray'
          }"
        />
        
        <view
          v-show="overallOptionSearch"
          @click="overallOptionSearch = ''"
          class="clear-input button"
        >
          <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
        </view>
      </view>
      
      <view class="flow-option-list">
        <view
          v-for="(item, index) in overallOptionList"
          v-show="item.includes(overallOptionSearch.toUpperCase())"
          class="flow-option-item flex-row-center"
        >
          <view
            @click="selectOverallOption(item)"
            class="button"
            :style="{
              color: overallOptionInput && (overallOptionInput.includes('\n' + item.toUpperCase() + '\n') || overallOptionInput.startsWith(item.toUpperCase() + '\n')) ? 'darkorchid' : 'darkmagenta'
            }"
          >
            {{ item }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.overall-option-popup {
  .container {
    width: 500px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .top-bar {
      width: 100%;
      height: 70px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title {
        font-size: 22px;
        font-weight: bold;
      }
      
      .cancel, .submit {
        width: 75px;
        height: 50px;
      }
    }
    
    .flow-action {
      width: 100%;
      padding: 10px;
      
      .flow-action-description {
        width: 400px;
        font-size: 20px;
        font-weight: bold;
        text-align: center;
      }
    }
    
    .flow-option-input {
      width: 100%;
      height: 160px;
      position: relative;
      
      .textarea {
        width: 350px;
        height: 140px;
        padding: 5px;
      }
      
      .clear-textarea {
        width: 70px;
        height: 40px;
        position: absolute;
        right: 80px;
        bottom: 15px;
      }
    }
    
    .flow-option-search {
      width: 100%;
      height: 70px;
      position: relative;
      
      .input {
        width: 350px;
        height: 50px;
        padding: 5px;
      }
      
      .clear-input {
        width: 70px;
        height: 40px;
        position: absolute;
        right: 80px;
        bottom: 15px;
      }
    }
    
    .flow-option-list {
      width: 100%;
      height: 280px;
      overflow: auto;
      
      .flow-option-item {
        width: 100%;
        padding: 10px;
        
        .button {
          width: 350px;
          min-height: 50px;
          padding: 5px;
          color: darkmagenta;
          font-size: 20px;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .textarea {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-textarea-placeholder), &:deep(.uni-textarea-textarea) {
      width: 100%;
      height: 100%;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>