<!-- 工序流程加工段弹框 -->
<script setup>
import { ref } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 传递消息
const emit = defineEmits(['change-flow-index'])

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 工序流程加工段弹框
const flowSectionPopup = ref()

// 工序流程
const processFlow = ref()

// 工序流程数
const flowNumber = ref(0)

// 滚动 id
const scrollId = ref('')

// 显示工序流程加工段弹框
async function showFlowSectionPopup(param1, param2) {
  processFlow.value = param1
  flowNumber.value = param2
  scrollId.value = ''
  flowSectionPopup.value.open()
  setTimeout(() => {
    scrollId.value = 'item' + param1.wkGroup
  }, 1000)
}

// 选择加工段
async function selectFlowSection(param1, param2, param3, param4, param5, param6) {
  if (param5 === param6) {
    flowSectionPopup.value.close()
    return
  }
  
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateFlowSection',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3,
      skey: param4,
      wkGroup: param5,
      targetWkGroup: param6,
      updUser: user
    }
  }).then(res => {
    if (res.data.code) {
      processFlow.value.wkGroup = param6
      emit('change-flow-index')
      tipPopup.value.showTipPopup('success', '修改成功！')
      flowSectionPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  showFlowSectionPopup
})
</script>

<template>
  <uni-popup
    ref="flowSectionPopup"
    type="center"
    class="flow-section-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择加工段
      </view>
      
      <view class="description flex-row-center">
        {{ operationMap.get(processFlow.operation) ? operationMap.get(processFlow.operation) : '未知' }} - {{ processFlow.model }} - {{ processFlow.rtgCode }} - {{ processFlow.skey }}
      </view>
      
      <scroll-view
        :scroll-y="true"
        :scroll-into-view="scrollId"
        :scroll-with-animation="true"
        class="flow-section-list"
      >
        <view
          v-for="(item, index) in flowNumber"
          :key="index"
          :id="'item' + item.toString().padStart(3, '0')"
          class="flow-section flex-row-center"
        >
          <view
            @click="selectFlowSection(processFlow.model, processFlow.operation, processFlow.rtgCode, processFlow.skey, processFlow.wkGroup, item.toString().padStart(3, '0'))"
            class="button"
            :style="{
              color: item.toString().padStart(3, '0') === processFlow.wkGroup ? 'mediumseagreen' : 'darkgreen'
            }"
          >
            {{ item.toString().padStart(3, '0') }}
          </view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.flow-section-popup {
  .container {
    width: 430px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .description {
      width: 100%;
      height: 40px;
      font-size: 20px;
      font-weight: bold;
    }
    
    .flow-section-list {
      min-height: 70px;
      max-height: 350px;
      overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .flow-section {
        width: 100%;
        padding: 10px;
        
        .button {
          width: 270px;
          height: 50px;
          color: darkgreen;
          font-size: 20px;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>