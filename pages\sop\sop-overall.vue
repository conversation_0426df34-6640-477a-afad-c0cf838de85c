<!-- 工序流程 -->
<script setup>
import { ref, reactive, onMounted, watch, provide } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap, proSeqMap, deptMap, optionListMap } from '@/pages/sop/common/commonMap.js'
import OverallPartPopup from '@/pages/sop/overall/overall-part-popup.vue'
import OverallActionPopup from '@/pages/sop/overall/overall-action-popup.vue'
import OverallOptionPopup from '@/pages/sop/overall/overall-option-popup.vue'
import OverallOptionPopup1 from '@/pages/sop/overall/overall-option-popup1.vue'
import OverallTextareaPopup from '@/pages/sop/overall/overall-textarea-popup.vue'
import OverallPicturePopup from '@/pages/sop/overall/overall-picture-popup.vue'
import DeleteOverallPopup from '@/pages/sop/overall/delete-overall-popup.vue'
import SortSectionPopup from '@/pages/sop/overall/sort-section-popup.vue'
import OverallSequencePopup from '@/pages/sop/overall/overall-sequence-popup.vue'
import OverallSectionPopup from '@/pages/sop/overall/overall-section-popup.vue'
import AddOverallPopup from '@/pages/sop/overall/add-overall-popup.vue'
import PicturePopup from '@/pages/sop/common/picture-popup.vue'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()
// 图片弹框
const picturePopup = ref()
// 部位弹框
const overallPartPopup = ref()
// 动作弹框
const overallActionPopup = ref()
// 选项弹框
const overallOptionPopup = ref()
// 加工选项弹框
const overallOptionPopup1 = ref()
// 文本弹框
const overallTextareaPopup = ref()
// 图片弹框
const overallPicturePopup = ref()
// 删除弹框
const deleteOverallPopup = ref()
// 排序加工段弹框
const sortSectionPopup = ref()
// 序号弹框
const overallSequencePopup = ref()
// 加工段弹框
const overallSectionPopup = ref()
// 添加工序流程弹框
const addOverallPopup = ref()

// 接收参数
const props = defineProps({
  info: {
    type: String,
    default: '{}'
  }
})

// 工序信息
const processInfo = ref(JSON.parse(props.info))

// 型体图片
const modelPicture = ref('')

// 工序流程列表
const processFlowList = ref([])
// 是否选择所有工序流程
const isSelectedAllProcessFlow = ref(false)

// 返回
function back() {
  uni.navigateBack()
}

// 获取鞋图
async function getModelPicture() {
  modelPicture.value = ''
  await uni.request({
    url: urlPrefix + '/sop/getModelPicture',
    method: 'POST',
    data: {
      model: processInfo.value.model
    }
  }).then(res => {
    if (res.data.code) {
      modelPicture.value = res.data.data ? ('data:image/jpg;base64,' + res.data.data.modelPicture) : ''
    } else {
      tipPopup.value.showTipPopup('warn', '暂无鞋图数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 获取整体流程
async function getOverallFlow() {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  isSelectedAllProcessFlow.value = false
  await uni.request({
    url: urlPrefix + '/sop/getOverallFlow',
    method: 'POST',
    data: {
      model: processInfo.value.model,
      operation: processInfo.value.operation,
      rtgCode: processInfo.value.rtgCode
    }
  }).then(res => {
    if (res.data.code) {
      processFlowList.value = res.data.data ? res.data.data : []
    } else {
      processFlowList.value = []
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  uni.hideLoading()
}

// 选择所有工序流程
function selectAllProcessFlow() {
  if (processFlowList.value.length === 0 || user !== processInfo.value.insUser) {
    return
  }
  isSelectedAllProcessFlow.value = !isSelectedAllProcessFlow.value
  for (let item of processFlowList.value) {
    item.isSelected = isSelectedAllProcessFlow.value
  }
}

// 选择工序流程
function selectItemProcessFlow(param) {
  if (user !== processInfo.value.insUser) {
    return
  }
  param.isSelected = !param.isSelected
  let selectedCount = 0
  for (let item of processFlowList.value) {
    if (item.isSelected) {
      selectedCount++
    }
  }
  if (selectedCount === processFlowList.value.length) {
    isSelectedAllProcessFlow.value = true
  } else {
    isSelectedAllProcessFlow.value = false
  }
}

// 删除工序流程
function deleteProcessFlow() {
  let selectedProcessFlowList = []
  for (let item of processFlowList.value) {
    if (item.isSelected) {
      selectedProcessFlowList.push(item)
    }
  }
  if (selectedProcessFlowList.length === 0) {
    tipPopup.value.showTipPopup('warn', '请勾选要删除的选项！')
    return
  }
  deleteOverallPopup.value.showDeleteOverallPopup(selectedProcessFlowList)
}

// 插入工序流程
async function insertProcessFlow(param) {
  uni.showLoading({
    title: '添加中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/insertProcessFlow',
    method: 'POST',
    data: {
      model: param.model,
      operation: param.operation,
      rtgCode: param.rtgCode,
      skey: param.skey,
      insUser: user,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getOverallFlow()
      tipPopup.value.showTipPopup('success', '添加成功！')
    } else {
      tipPopup.value.showTipPopup('error', '添加失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

onMounted(() => {
  getModelPicture()
  getOverallFlow()
  overallPartPopup.value.getPartList()
})

provide('processFlowList', processFlowList)
provide('processInfo', processInfo)
provide('getOverallFlow', getOverallFlow)
provide('isSelectedAllProcessFlow', isSelectedAllProcessFlow)
</script>

<template>
  <view class="sop-flow-overall">
    <view class="top-bar flex-row-start-center">
      <view @click="back()" class="back button">
        <uni-icons type="back" size="30" color="steelblue"></uni-icons>
      </view>
      
      <view class="model-picture" :style="{ visibility: modelPicture.replace(/ /g, '+').length > 0 ? 'visible' : 'hidden' }">
        <!-- #ifdef APP -->
        <uni-transition
          :show="modelPicture.replace(/ /g, '+').length > 0"
          mode-class="fade"
          class="watermark"
        >
        <!-- #endif -->
        <!-- #ifdef WEB -->
        <transition name="fade">
        <view
          v-show="modelPicture.replace(/ /g, '+').length > 0"
          class="watermark"
        >
        <!-- #endif -->
          <img
            @click="picturePopup.showPicturePopup(modelPicture.replace(/ /g, '+'))"
            :src="modelPicture.replace(/ /g, '+')"
            alt=""
            class="button"
          />
        <!-- #ifdef WEB -->
        </view>
        </transition>
        <!-- #endif -->
        <!-- #ifdef APP -->
        </uni-transition>
        <!-- #endif -->
      </view>
      
      <view class="title flex-row-center">
        工序流程
      </view>
      
      <view
        v-show="user === processInfo.insUser"
        @click="addOverallPopup.showAddOverallPopup()"
        class="add button"
      >
        <uni-icons type="plusempty" size="30" color="green"></uni-icons>
      </view>
      
      <view
        v-show="user === processInfo.insUser"
        @click="deleteProcessFlow()"
        class="delete button">
        <uni-icons type="trash" size="30" color="red"></uni-icons>
      </view>
    </view>
    
    <view class="process-info flex-row-evenly-center">
      <view class="operation process-info-item flex-row-start-center">
        制程：{{ operationMap.get(processInfo.operation) ? operationMap.get(processInfo.operation) : '未知' }} - {{ proSeqMap.get(processInfo.proSeq) ? proSeqMap.get(processInfo.proSeq) : '未知' }}
      </view>
      
      <view class="brand process-info-item flex-row-start-center">
        品牌：{{ processInfo.brand }}
      </view>
      
      <view class="model process-info-item flex-row-start-center">
        型体：{{ processInfo.model }}
      </view>
      
      <view class="code process-info-item flex-row-start-center">
        主要代码：{{ processInfo.rtgCode }}
      </view>
      
      <view class="type process-info-item flex-row-start-center">
        生产类型：{{ processInfo.rtgType }}
      </view>
      
      <view class="material process-info-item flex-row-start-center">
        材质：{{ processInfo.material ? processInfo.material : '/' }}
      </view>
      
      <view class="model-desc process-info-item flex-row-start-center">
        型体描述：{{ processInfo.modelDesc ? processInfo.modelDesc : '/' }}
      </view>
      
      <view class="last-nos process-info-item flex-row-start-center">
        楦头编号：{{ processInfo.lastNos ? processInfo.lastNos : '/' }}
      </view>
      
      <view class="outsole process-info-item flex-row-start-center">
        Outsole：{{ processInfo.osNo ? processInfo.osNo : '/' }}
      </view>
    </view>
    
    <scroll-view
      :scroll-x="true"
      :scroll-y="true"
      class="process-flow-table"
    >
      <table>
        <thead>
          <tr>
            <th v-show="user === processInfo.insUser" style="width: 60px;">
              <checkbox
                @click="selectAllProcessFlow()"
                :checked="isSelectedAllProcessFlow"
                :disabled="processFlowList.length === 0"
                color="white"
                activeBackgroundColor="violet"
                borderColor="gray"
                activeBorderColor="black"
              ></checkbox>
            </th>
            <th v-show="user === processInfo.insUser" style="width: 80px;">新增</th>
            <th style="width: 90px; left: 0px; z-index: 3;">序号</th>
            <th style="width: 100px;">
              <view
                v-show="processFlowList.length > 0 && user === processInfo.insUser"
                @click="sortSectionPopup.showSortSectionPopup()"
                class="sort-section button"
              >
                加工段
              </view>
              <text v-show="processFlowList.length === 0 || user !== processInfo.insUser">
                加工段
              </text>
            </th>
            <th style="width: 100px;">版本</th>
            <th v-show="processInfo.operation === '1'" style="width: 200px;">部位</th>
            <th style="width: 300px; left: 90px; z-index: 3;">动作</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 1">化学品</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 1">防护用品</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2">机器</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 1">温度</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 1">时间</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 1">压力</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2">工具</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 2">胶水</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 2">车线</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 2">边距</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 2">针距</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 2">间距</th>
            <th style="width: 160px;" v-show="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2">车针</th>
            <th
              v-for="(item, index) in optionListMap.get(processInfo.proSeq)"
              :key="index"
              v-show="deptMap.get(processInfo.operation) === 3"
              style="width: 160px;"
            >
              {{ item }}
            </th>
            <th style="width: 300px;">图片备注</th>
            <th style="width: 302px;">图片</th>
            <th style="width: 310px;">操作标准</th>
            <th style="width: 310px;">自检点</th>
          </tr>
        </thead>
        
        <tbody v-show="processFlowList.length > 0">
          <tr
            v-for="(item, index) in processFlowList"
            :key="index"
            :style="{
              backgroundColor: index % 2 === 0 ? '#edfbe4' : '#fdf6e3'
            }"
          >
            <td v-show="user === processInfo.insUser">
              <checkbox
                @click="selectItemProcessFlow(item)"
                :checked="item.isSelected"
                color="white"
                activeBackgroundColor="violet"
                borderColor="gray"
                activeBorderColor="black"
              ></checkbox>
            </td>
            <td v-show="user === processInfo.insUser">
              <uni-icons
                @click="insertProcessFlow(item)"
                type="plusempty"
                size="30"
                color="seagreen"
                class="insert button"
              ></uni-icons>
            </td>
            <td
              style="position: sticky; left: 0px; z-index: 2;"
              :style="{
                backgroundColor: index % 2 === 0 ? '#edfbe4' : '#fdf6e3'
              }"
            >
              <view
                v-show="user === processInfo.insUser"
                @click="overallSequencePopup.showOverallSequencePopup(item)"
                class="sequence button"
              >
                {{ item.skey }}
              </view>
              
              <view
                v-show="user !== processInfo.insUser"
                class="sequence"
                style="color: black;"
              >
                {{ item.skey }}
              </view>
            </td>
            <td>
              <view
                v-show="user === processInfo.insUser"
                @click="overallSectionPopup.showOverallSectionPopup(item)"
                class="section button"
              >
                {{ item.wkGroup ? item.wkGroup : '/' }}
              </view>
              
              <view
                v-show="user !== processInfo.insUser"
                class="section"
                style="color: black;"
              >
                {{ item.wkGroup ? item.wkGroup : '/' }}
              </view>
            </td>
            <td>
              v{{ item.version }}
            </td>
            <td v-show="processInfo.operation === '1'">
              <view
                v-show="user === processInfo.insUser"
                @click="overallPartPopup.showOverallPartPopup(item)"
                class="part button"
              >
                {{ item.partName ? item.partName : '/' }}
              </view>
              
              <view v-show="user !== processInfo.insUser" class="part">
                {{ item.partName ? item.partName : '/' }}
              </view>
            </td>
            <td
              style="position: sticky; left: 90px; z-index: 2;"
              :style="{
                backgroundColor: index % 2 === 0 ? '#edfbe4' : '#fdf6e3'
              }"
            >
              <view
                v-show="user === processInfo.insUser"
                @click="overallActionPopup.showOverallActionPopup(item, processInfo.proSeq)"
                class="actions button"
              >
                {{ item.actions ? item.actions : '/' }}
              </view>
              
              <view v-show="user !== processInfo.insUser" class="actions">
                {{ item.actions ? item.actions : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 1">
              <view @click="overallOptionPopup.showOverallOptionPopup('chemical', item)" class="chemical button">
                {{ item.chemical ? item.chemical : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 1">
              <view @click="overallOptionPopup.showOverallOptionPopup('defence', item)" class="defence button">
                {{ item.defence ? item.defence : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2">
              <view @click="overallOptionPopup.showOverallOptionPopup('machine', item)" class="machine button">
                {{ item.machine ? item.machine : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 1">
              <view @click="overallOptionPopup.showOverallOptionPopup('temp', item)" class="temp button">
                {{ item.temp ? item.temp : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 1">
              <view @click="overallOptionPopup.showOverallOptionPopup('time', item)" class="time button">
                {{ item.time ? item.time : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 1">
              <view @click="overallOptionPopup.showOverallOptionPopup('pressure', item)" class="pressure button">
                {{ item.pressure ? item.pressure : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2">
              <view @click="overallOptionPopup.showOverallOptionPopup('tools', item)" class="tools button">
                {{ item.tools ? item.tools : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 2">
              <view @click="overallOptionPopup.showOverallOptionPopup('glue', item)" class="glue button">
                {{ item.glue ? item.glue : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 2">
              <view @click="overallOptionPopup.showOverallOptionPopup('carLine', item)" class="carLine button">
                {{ item.carLine ? item.carLine : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 2">
              <view @click="overallOptionPopup.showOverallOptionPopup('margin', item)" class="margin button">
                {{ item.margin ? item.margin : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 2">
              <view @click="overallOptionPopup.showOverallOptionPopup('needleSpacing', item)" class="needleSpacing button">
                {{ item.needleSpacing ? item.needleSpacing : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 2">
              <view @click="overallOptionPopup.showOverallOptionPopup('spacing', item)" class="spacing button">
                {{ item.spacing ? item.spacing : '/' }}
              </view>
            </td>
            <td v-show="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2">
              <view @click="overallOptionPopup.showOverallOptionPopup('needle', item)" class="needle button">
                {{ item.needle ? item.needle : '/' }}
              </view>
            </td>
            <td
              v-for="(item1, index1) in optionListMap.get(processInfo.proSeq)"
              :key="index1"
              v-show="deptMap.get(processInfo.operation) === 3"
            >
              <view @click="overallOptionPopup1.showOverallOptionPopup(index1, processInfo.proSeq, item)" class="button">
                {{ item['processOption' + (index1 + 1)] ? item['processOption' + (index1 + 1)] : '/' }}
              </view>
            </td>
            <td>
              <view @click="overallTextareaPopup.showOverallTextareaPopup('imgTit1', item)" class="picture-remark button">
                {{ item.imgTit1 ? item.imgTit1 : '/' }}
              </view>
            </td>
            <td>
              <view class="picture-list">
                <view v-for="i in item.imgList" class="preview-picture watermark">
                  <img
                    @click="picturePopup.showPicturePopup(urlPrefix + i.imgUrl)"
                    :src="urlPrefix + i.imgUrl"
                    alt=""
                    class="button"
                  />
                </view>
                
                <view class="upload-picture">
                  <uni-icons
                    @click="overallPicturePopup.showOverallPicturePopup(item)"
                    type="upload"
                    size="40"
                    color="tomato"
                    class="button"
                  ></uni-icons>
                </view>
              </view>
            </td>
            <td>
              <view
                @click="overallTextareaPopup.showOverallTextareaPopup('standard', item)"
                class="standard button"
                :style="{
                  justifyContent: item.standard ? 'flex-start' : 'center'
                }"
              >
                {{ item.standard ? item.standard : '/' }}
              </view>
            </td>
            <td>
              <view
                @click="overallTextareaPopup.showOverallTextareaPopup('checkPoint', item)"
                class="check-point button"
                :style="{
                  justifyContent: item.checkPoint ? 'flex-start' : 'center'
                }"
              >
                {{ item.checkPoint ? item.checkPoint : '/' }}
              </view>
            </td>
          </tr>
        </tbody>
        
        <tbody v-show="processFlowList.length === 0">
          <tr>
            <td v-show="user !== processInfo.insUser" colspan="16" style="position: sticky; left: 0px; z-index: 2;">
              暂无流程数据
            </td>
            
            <td v-show="user === processInfo.insUser" colspan="18" style="position: sticky; left: 0px; z-index: 2;">
              暂无流程数据
            </td>
          </tr>
        </tbody>
      </table>
    </scroll-view>
  </view>
  
  <overall-part-popup ref="overallPartPopup" />
  <overall-action-popup ref="overallActionPopup" />
  <overall-option-popup ref="overallOptionPopup" />
  <overall-option-popup1 ref="overallOptionPopup1" />
  <overall-textarea-popup ref="overallTextareaPopup" />
  <overall-picture-popup ref="overallPicturePopup" />
  <delete-overall-popup ref="deleteOverallPopup" />
  <sort-section-popup ref="sortSectionPopup" />
  <overall-sequence-popup ref="overallSequencePopup" />
  <overall-section-popup ref="overallSectionPopup" />
  <add-overall-popup ref="addOverallPopup" />
  <picture-popup ref="picturePopup" />
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss">
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

page {
  width: 100%;
  height: 100%;
  padding: 2.5% 2% 1.5% 2%;
  background-color: #fdf6e3;
}

.sop-flow-overall {
  width: 100%;
  height: 100%;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 0 1px 5px #ddd;
  
  .top-bar {
    width: 100%;
    height: 50px;
    margin-bottom: 20px;
    
    .back, .model-picture {
      height: 50px;
      margin-right: 12.5%;
    }
    
    .back {
      width: 6%;
    }
    
    .model-picture {
      width: 9%;
    }
    
    .model-picture img {
      min-width: 50px;
      max-width: 100%;
      height: 50px;
    }
    
    .title {
      width: 20%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .add, .delete {
      width: 6%;
      height: 50px;
    }
    
    .add {
      margin-left: 26%;
      margin-right: 2%;
    }
  }
  
  .process-info {
    width: 100%;
    height: 120px;
    padding: 0px 10px;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    overflow: auto;
    
    .process-info-item {
      width: calc(100% / 3);
      height: 40px;
    }
  }
  
  .process-flow-table {
    width: 100%;
    height: calc(100% - 210px);
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    overflow: hidden;
    
    &:deep(.uni-scroll-view) {
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
    }
    
    table {
      width: fit-content;
      min-width: 100%;
      max-height: 100%;
      border-radius: 10px;
      border-spacing: 0;
      table-layout: fixed;
      
      &:deep(.uni-checkbox-input) {
        width: 30px;
        height: 30px;
        margin: 0;
        border-width: 2px;
      }
      
      tr {
        height: 70px;
        
        th, td {
          text-align: center;
          border-right: 2px solid #ccc;
          border-bottom: 2px solid #ccc;
          
          &:last-child {
            border-right: none;
          }
        }
        
        th {
          position: sticky;
          top: 0;
          z-index: 2;
          padding: 10px;
          background-color: #fdf6e3;
          
          .button {
            padding: 5px;
            word-break: break-all;
            white-space: pre-wrap;
          }
          
          .sort-section {
            height: 40px;
            color: seagreen;
          }
        }
        
        td {
          padding: 10px;
          
          .button {
            min-height: 40px;
            padding: 5px;
            word-break: break-all;
            white-space: pre-wrap;
          }
          
          .insert, .sequence, .section {
            color: seagreen;
          }
          
          .standard, .check-point {
            padding: 5px 10px;
            text-align: left;
          }
          
          .picture-list {
            display: flex;
            flex-wrap: wrap;
            
            .preview-picture {
              width: 60px;
              height: 60px;
              margin: 5px;
              
              img {
                width: 60px;
                height: 60px;
                overflow: hidden;
              }
              
              .button {
                padding: 0;
              }
            }
            
            .upload-picture {
              width: 60px;
              height: 60px;
              margin: 5px;
              
              .button {
                width: 60px;
                height: 60px;
                padding: 0;
              }
            }
          }
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .watermark {
    position: relative;
    word-break: break-all;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    
    &:before {
      content: 'STELLA';
      position: absolute;
      top: 2px;
      left: 2px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      text-shadow: 0 0 2px black;
      z-index: 1;
      pointer-events: none;
    }
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
    }
  }
  
  .textarea {
    width: 100%;
    padding: 5px;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-textarea-placeholder), &:deep(.uni-textarea-textarea) {
      font-size: 18px;
      font-weight: bold;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
    }
  }
  
  .flex-row-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
  
  .flex-row-start {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
  }
  
  .flex-row-start-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
  }
  
  .flex-row-evenly-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    align-items: center;
  }
  
  .flex-row-between-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }
  
  .flex-row-between-start {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
  }
  
  .flex-column-start {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
  }
  
  /* #ifdef WEB */
  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.3s ease;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  
  .fade-enter-to,
  .fade-leave-from {
    opacity: 1;
  }
  /* #endif */
}
</style>