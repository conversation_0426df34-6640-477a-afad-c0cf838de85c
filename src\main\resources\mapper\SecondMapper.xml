<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.SecondMapper">
    <resultMap id="ResultMap" type="java.util.HashMap">
        <result property="model_no" column="MODEL_NO" jdbcType="VARCHAR"/>
        <result property="procs_type" column="PROCS_TYPE" jdbcType="INTEGER"/>
        <result property="procs_pic" column="PROCS_PIC" jdbcType="BLOB"
                typeHandler="org.apache.ibatis.type.BlobTypeHandler"/>
        <result property="bas_size" column="BAS_SIZE" jdbcType="VARCHAR"/>
        <result property="s_size" column="S_SIZE" jdbcType="VARCHAR"/>
        <result property="e_size" column="E_SIZE" jdbcType="VARCHAR"/>
        <result property="ins_user" column="INS_USER" jdbcType="VARCHAR"/>
        <result property="ins_date" column="INS_DATE" jdbcType="DATE"/>
        <result property="upd_user" column="UPD_USER" jdbcType="VARCHAR"/>
        <result property="upd_date" column="UPD_DATE" jdbcType="DATE"/>
        <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getSpecification" resultType="com.zqn.modeldata2.entity.CkSmodelp">
        select model_no,   -- 型体
               procs_type, -- 规格类型
               procs_pic,  -- 图片
               s_size,     -- 起始号码
               e_size,     -- 终止号码
               ins_user,   -- 添加者
               ins_date,   -- 添加时间
               upd_user,   -- 修改者
               upd_date,   -- 修改时间
               remark      -- 备注
        from ck_smodelp
        where model_no = #{ckSmodelp.model_no}
        order by model_no, procs_type
    </select>

    <insert id="addSpecification">
        insert into ck_smodelp (model_no, -- 型体
                                procs_type, -- 规格类型
                                procs_pic, -- 图片
                                s_size, -- 起始号码
                                e_size, -- 终止号码
                                remark, -- 备注
                                ins_user, -- 添加者
                                ins_date, -- 添加时间
                                upd_user, -- 修改者
                                upd_date -- 修改时间
        )
        values (#{ckSmodelp.model_no},
                #{ckSmodelp.procs_type},
                #{ckSmodelp.procs_pic},
                #{ckSmodelp.s_size},
                #{ckSmodelp.e_size},
                #{ckSmodelp.remark},
                #{ckSmodelp.ins_user},
                sysdate,
                #{ckSmodelp.upd_user},
                sysdate)
    </insert>

    <update id="updateSpecification">
        update ck_smodelp
        set procs_pic = #{ckSmodelp.procs_pic}, -- 图片
            s_size    = #{ckSmodelp.s_size},    -- 起始号码
            e_size    = #{ckSmodelp.e_size},    -- 终止号码
            remark    = #{ckSmodelp.remark},    -- 备注
            upd_user  = #{ckSmodelp.upd_user},  -- 修改者
            upd_date  = sysdate                 -- 修改时间
        where model_no = #{ckSmodelp.model_no} -- 型体
          and procs_type = #{ckSmodelp.procs_type} -- 规格类型
    </update>

    <delete id="deleteSpecification">
        delete
        from ck_smodelp
        where model_no = #{ckSmodelp.model_no} -- 型体
          and procs_type = #{ckSmodelp.procs_type} -- 规格类型
    </delete>

    <delete id="batchDeleteSpecification">
        delete from ck_smodelp
        where
        <foreach item="item" collection="ckSmodelpList" separator="or">
            (model_no = #{item.model_no}
            and procs_type = #{item.procs_type})
        </foreach>
    </delete>

    <update id="updateStartSize">
        update ck_smodelp
        set s_size   = #{ckSmodelp.s_size},
            upd_user = #{ckSmodelp.upd_user},
            upd_date = sysdate
        where model_no = #{ckSmodelp.model_no}
    </update>

    <update id="updateEndSize">
        update ck_smodelp
        set e_size   = #{ckSmodelp.e_size},
            upd_user = #{ckSmodelp.upd_user},
            upd_date = sysdate
        where model_no = #{ckSmodelp.model_no}
    </update>
</mapper>