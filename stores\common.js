/**
 * 公共 store
 */
import { ref } from 'vue'
import { defineStore } from 'pinia'
import { useI18n } from 'vue-i18n'

// 语言 store
export const useLanguageStore = defineStore('language', () => {
  // 语言
  const language = ref(uni.getStorageSync('language') ? uni.getStorageSync('language') : '简体中文')
  // 地区，对应 i18n 实例中的 locale 属性
  const { locale } = useI18n()
  // 初始化 locale
  locale.value = uni.getStorageSync('locale') ? uni.getStorageSync('locale') : 'zh_cn'
  // 语言弹框
  // const languagePopup = ref()
  
  /**
   * 切换语言
   * param1：语言
   * param2：地区
   */
  function changeLanguage(param1, param2) {
    uni.setStorageSync('language', param1)
    uni.setStorageSync('locale', param2)
    language.value = param1
    locale.value = param2
    // languagePopup.value.close()
  }
  
  // return { language, locale, languagePopup, changeLanguage }
  return { language, locale, changeLanguage }
})

// 工厂 store
export const useFactoryStore = defineStore('factory', () => {
  // 工厂
  const factory = ref(uni.getStorageSync('factory') ? uni.getStorageSync('factory') : 'TP')
  // 工厂弹框
  const factoryPopup = ref()
  
  /**
   * 切换工厂
   * @param param 工厂
   */
  function changeFactory(param) {
    uni.setStorageSync('factory', param)
    factory.value = param
    factoryPopup.value.close()
  }
  
  return { factory, factoryPopup, changeFactory }
})

// 提示 store
export const useTipStore = defineStore('tip', () => {
  // 提示类型
  const tipType = ref('')
  // 提示信息
  const tipMessage = ref('')
  // 提示弹框
  const tipPopup = ref()
  
  /**
   * 显示提示
   * @param type 提示类型
   * @param message 提示信息
   */
  function showTip(type, message) {
    tipType.value = type
    tipMessage.value = message
    tipPopup.value.open()
  }
  
  return { tipType, tipMessage, tipPopup, showTip }
})

// 图片 store
export const usePictureStore = defineStore('picture', () => {
  // 图片
  const picture = ref('')
  // 图片弹框
  const picturePopup = ref()
  
  /**
   * 显示图片
   * @param param 图片
   */
  function showPicture(param) {
    picture.value = param
    picturePopup.value.open()
  }
  
  return { picture, picturePopup, showPicture }
})