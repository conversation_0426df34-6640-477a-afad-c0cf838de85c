<script setup>
import {ref, watch, reactive, defineProps, onMounted, getCurrentInstance} from 'vue'
import {pathToBase64,base64ToPath} from '@/pages/common/image-tools.js'
import {onLoad} from '@dcloudio/uni-app'
import urlPrefix from '@/pages/common/urlPrefix.js'


const currentInstance = getCurrentInstance()
const data = ref('') 
const url = ref(null);
const path = ref(null);

onLoad((option) => {
	path.value = option.data
    data.value = option.data
	// console.log("onLoad:" + option.data)
   	url.value = `/hybrid/html/index.html?data=` + encodeURIComponent(option.data)
})


const handleMessage = e => {
	    const data = e.detail.data;
	    if (data.length > 0) {
	        const firstElement = data[0];
	        if (firstElement && firstElement.action) {
	            const action = firstElement.action;
	            if (action === 'cancel') {
					
					uni.navigateBack({
					  delta: 1
					});
					
	    //             console.log('------------Cancel-----------------');
					// console.log('path:' +  path.value)  
					// closeCurrentPageWithResult(path.value);
					// uni.uploadFile({
					//     url: urlPrefix + "/api/files/upload", 
					//     filePath: path.value,
					//     name: 'file',
					//     success: (uploadFileRes) => {//回调方法
					//         var img = JSON.parse(uploadFileRes.data).data.url;
					// 		closeCurrentPageWithResult(path.value, img)
					//     }
					// });
					
	            }else if (action === 'save') {
	                console.log('--------------save---------------------');
					const base64Data = firstElement.data;
						base64ToPath(base64Data)  
						  .then(path => {  
							console.log('path:' + path)  
							closeCurrentPageWithResult(path);
							// console.log('url:' + urlPrefix + "/api/files/upload")  
						// 	uni.uploadFile({
						// 		url: urlPrefix + "/api/files/upload",
						// 		filePath: path,
						// 		name: 'file',
						// 		success: (uploadFileRes) => {
						// 			var img = JSON.parse(uploadFileRes.data).data.url;
						// 			console.log('img:' + img)
						// 			closeCurrentPageWithResult(path, img)
						
						// 		}
						// 	});
						  })  
						  .catch(error => {  
							console.error(error)  
						  })
	            } else {
	                console.log('Unknown action:', action);
	            }
	        } else {
	            console.log('No action found in the message data');
	        }
	    } else {
	        console.log('Message data is empty');
	    }
}
		

function closeCurrentPageWithResult(path) {
	  const data = {
	    path: path
	  };
    uni.setStorage({
      key: 'customData',
      data: data,
      success: () => {
        uni.navigateBack({
          delta: 1
        });
      }
    });
}

</script>

<template>
 <view class="container">
    <web-view :src="url" @message="handleMessage"></web-view>
  </view>
</template>

<style lang="scss">
	
</style>