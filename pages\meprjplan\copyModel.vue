<template>
	<view class="container">
		<view class="back">
			<uni-icons @click="back" type="back" size="36"></uni-icons>
		</view>

        <view class="targetTitle">
            <text>选择你的目标型体</text>
        </view>

		<view class="search">
			<view style="width: 10%;margin-left: 5%;">
				<!-- <uni-data-select class="searchSelect" placeholder="部门" :clear="false" :localdata="depts"
                                 :value="selectWt"></uni-data-select> -->
			</view>

			<button class="search-brand" @click="brandPopup.open()">
				{{ brand }}
			</button>

			<!-- <button class="search-type" @click="searchType = !searchType"> -->
			<button class="search-type" >
				{{ searchType ? "型体" : "楦头" }}
			</button>

			<input ref="searchInput" v-show="searchType" v-model="inputModel" class="search-input" type="text"
				placeholder="请输入型体编号" @focus="modelListShow = true" @blur="hideModelList">

			<input v-show="!searchType" v-model="shoe_last" type="text" placeholder="请输入楦头编号"
				class="search-input-shoe-last" />

			<view v-show="inputModel.length > 0 && searchType" @click="inputModel = '',search('')" class="search-clear">
				<uni-icons type="clear" size="34"></uni-icons>
			</view>

			<view v-show="shoe_last.length > 0 && !searchType" @click="shoe_last = ''" class="search-clear-shoe-last">
				<uni-icons type="clear" size="34"></uni-icons>
			</view>

			<view v-show="!searchType" @click="getShoeLastModelList" class="search-icon-shoe-last"
				:style="{ marginLeft: shoe_last.length > 0 ? '11px' : '0' }">
				<uni-icons type="search" size="48"></uni-icons>
			</view>

			<view class="inpBr"
				:style="{ marginLeft: (inputModel.length > 0) ? '15px' : '4px', width: '10%', marginTop: '-10rpx' }">
				<button type="success" @click="copy()"
					style="background-color: #18bc37; color: white; font-weight: bold;">确认复制
				</button>
			</view>

			<transition name="list">
				<view v-show="modelListShow" class="search-list">
					<view class="search-box">
						<view v-for="(item, index) in searchModel" 
						:key="index" 
						@click="search(item.model_no)"
							class="search-item" :style="{ border: searchModel.length === 1 ? 'none' : '' }">
							{{ item.model_no }}
						</view>
						<view v-show="searchModel.length === 0" class="search-item" style="border: none;">
							暂无该型体数据
						</view>
					</view>
				</view>
			</transition>

			<transition name="list">
				<view v-show="shoeLastModelListShow" class="search-list-shoe-last">
					<view class="search-box-shoe-last">
						<view v-for="(item, index) in searchShoeLastModel" :key="index" @click="search(item.model_no)"
							class="search-item-shoe-last"
							:style="{ border: shoeLastModelList.length === 1 ? 'none' : '' }">
							{{ item.model_no }}
						</view>
						<view v-show="shoeLastModelList.length === 0" class="search-item-shoe-last"
							style="border: none;">
							暂无型体数据
						</view>
					</view>
				</view>
			</transition>
		</view>

        <view class="sourceTitle">
            <text>来源信息</text>
        </view>

        <view class="sourceData">
            <text>客户：{{sourceBrand}}</text>
            <text>部门：{{sourceDept}}</text>
            <text>来源型体编号：{{sourceModel}}</text>

        </view>

		<view class="brand-popup">
			<uni-popup ref="brandPopup" type="center">
				<view class="brand-box">
					<view v-for="(item, index) in brandList" :key="index" v-show="item.data.length > 0"
						class="brand-part">
						<view class="brand-initial">{{ item.letter }}</view>
						<view v-for="(brand, index) in item.data" :key="index" @click="select(brand)" class="brand">
							{{ brand }}
						</view>
					</view>
				</view>
			</uni-popup>
		</view>
	</view>

    <view class="tip-popup">
        <uni-popup ref="tipPopup" type="message">
            <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
        </uni-popup>
    </view>

</template>

<script setup>
	import {
		onMounted,
		ref,
		reactive,
		watch
	} from 'vue'
	import {
		onPullDownRefresh
	} from '@dcloudio/uni-app'
	import {
		onShow
	} from '@dcloudio/uni-app';
    import {onLoad} from '@dcloudio/uni-app'
	import urlPrefix from '@/pages/common/urlPrefix.js'

	//表单数据
	const searchModel = ref([])
	const shoeLastModelList = ref([])
	const dataList = ref([])
	const searchShoeLastModel = ref([])
	const shoeLastModelListShow = ref(false)
	const searchInput = ref()
	const modelList = ref([])

    const info = ref();
    const sourceBrand = ref();
    const sourceDept = ref();
    const sourceModel = ref();
    const factory = ref();

	//消息提示
	const tipPopup = ref()
	const tipType = ref('')
	const tipMessage = ref('')

	//扫描状态

	//详情弹窗
	const modelData = ref()
	const modelPopup = ref()
	const selectWt = ref()

	//参数
	const model_no = ref('')
	const inputModel = ref('')
	const itemNo = ref('')
	const id = ref('')
	const shoe_last = ref('')
	const brand = ref('AB')
	const brandList = ref([])

	//下拉选择显示
	const modelListShow = ref(false)
	const searchType = ref(true)
	const brandPopup = ref()

	const insUs = uni.getStorageSync("loUserNo")
	const insName = uni.getStorageSync("loginUserName")
	const iuser = ref("")
    const isSubmitting = ref(false);

	const depts = ref([{
			value: 1,
			text: "成型"
		},
		{
			value: 2,
			text: "半成品"
		},
		{
			value: 3,
			text: "鞋面"
		}
	])
	
	function hideModelList(){
		setTimeout(() => {
		    modelListShow.value = false;
		}, 200); // 延
	}

	// 提示信息
	function showTip(type, message) {
		tipType.value = type
		tipMessage.value = message
		tipPopup.value.open()
	}

    function copy(){
        if (isSubmitting.value) {
            showTip('warn', '正在复制中，请稍候...');
        } else {
            isSubmitting.value = true;
            let data = {
                "sourceBrand": sourceBrand.value,
                "sourceDept": sourceDept.value,
                "sourceModel": sourceModel.value,
                "model": model_no.value,
                "brand": brand.value,
				"factory":factory.value,
				"userFactory":uni.getStorageSync("currentFactory"),
                "createBy":uni.getStorageSync("loginUserName")
            }

            uni.request({
                url: urlPrefix + "/pccmeprjplanhd/copy",
                data: data,
                method: "POST"
            }).then(res => {
                if (res.statusCode != 200) {
                    showTip('error', res.data.message);
                } else {
                    back();
                }
                isSubmitting.value = false;
            }).catch(err => {
                console.log(err)
                uni.showToast({
                    title: '保存數據失敗..',
                    icon: "error"
                });
            })
        }
    }

	function search(model) {
		model_no.value = model;
		inputModel.value = model;
	}

	// 监视输入框中的 model_no，更新搜索提示列表
	watch(inputModel, () => {
		searchModel.value = modelList.value.filter(item => item.model_no.includes(inputModel.value.toUpperCase())).slice(0, 50)
	})

	// 获取品牌列表
	function getBrands() {
		uni.request({
			url: urlPrefix + "/first/getBrandsPlus",
			method: "POST"
		}).then(res => {
			brandList.value = res.data.data ? res.data.data : []
		}).catch(err => {
			console.log(err)
		})
	}

	function getShoeLastModelList() {
		uni.request({
			url: urlPrefix + "/first/getListByShoeLast",
			method: "POST",
			data: {
				brand_no: brand.value,
				shoe_last: shoe_last.value
			}
		}).then(res => {
			shoeLastModelList.value = res.data.data ? res.data.data : []
			searchShoeLastModel.value = shoeLastModelList.value.slice(0, 50)
			shoeLastModelListShow.value = true
		}).catch(err => {
			console.log(err)
		})
	}

	// 选择品牌
	function select(param) {
		model_no.value = ""
		shoe_last.value = ""
		brand.value = param
		brandPopup.value.close()

		uni.request({
			url: urlPrefix + "/first/getList",
			method: "POST",
			data: {
				brand_no: brand.value
			}
		}).then(res => {
			modelList.value = res.data.data ? res.data.data : []
			searchModel.value = modelList.value.filter(item => item.model_no.includes(model_no.value
					.toUpperCase()))
				.slice(0, 50)
		}).catch(err => {
			console.log(err)
		})

		modelList.value = []
	}

	//返回首页
	function back() {
		let back = getCurrentPages();
		if(back && back.length > 1) {
			uni.navigateBack({  
			  delta: 1  
			});  
		} else {  
			history.back();  
		}
	}

	async function firstChange(e) {
		firstPageNo.value = e.current;
		await uni.request({
			url: urlPrefix + "/pccmeprjplanhd/query",
			data: {
				"pageNo": firstPageNo.value,
				"pageSize": firstPageSize.value
			},
			method: "GET"
		}).then(res => {
			//console.log(res.data);
			dataList.value = res.data.data.list
		}).catch(err => {
			console.log(err)
		})
	}
	
	//登录校验
	function loginCheck(){
		if(uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == ''){
			uni.navigateTo({
				url: `/pages/login/login`,
				animationType: 'pop-in',
				animationDuration: 200
			})
		}
	}
	
	onShow(async (props) => {
		loginCheck();
	})

    onLoad(async (props) => {
        await getBrands();
        select(brand.value);
        info.value = JSON.parse(props.info);
        sourceBrand.value = info.value.brand;
        sourceDept.value = info.value.dept;
        sourceModel.value = info.value.model;
        factory.value = info.value.factory
        console.log(info.value)
    })
</script>

<style lang="scss">
	page {
		width: 100%;
		height: 100%;
		padding: 2.5% 2% 1.5% 2%;
		box-sizing: border-box;
		background-color: #fdf6e3;
	}

	.back {
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		position: absolute;
		left: 1.5%;
		top: 3%;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		transition: all 0.05s ease-in-out;
		cursor: pointer;
		z-index: 1;
	}

	.container {
		width: 100%;
		height: 100%;
		padding: 1%;
		box-sizing: border-box;
		border-radius: 10px;
		box-shadow: 0 0 1px 5px #dddddd;
		position: relative;
	}

	.right-top-top {
		display: flex;
	}

	.inpBr {
		width: 15%;
		margin-left: 10rpx;
	}

	.left-bottom {
		width: 100%;
		height: 10%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.uni-pagination__total {
		margin-right: 0.4375rem !important;
	}

	.uni-pagination__num-tag {
		min-width: 1.875rem !important;
		background-color: #F0F0F0 !important;
	}

	.page--active {
		color: white !important;
		background-color: deeppink !important;
	}


	.search {
		width: 100%;
		min-height: 10%;
		margin-bottom: 1%;
		box-sizing: border-box;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		position: relative;

		.searchSelect {
			width: 100%;
			margin-left: 10%;
			margin-right: 0.5%;
			font-size: 24px;
			font-weight: bold;
			padding: 10px;
			border-radius: 10px;
		}

		.search-brand {
			width: 10%;
			margin-left: 0.5%;
			margin-right: 0.5%;
			background: linear-gradient(to right bottom, orangered, pink);
			color: white;
			font-size: 24px;
			font-weight: bold;
			border-radius: 10px;
		}

		.search-type {
			width: 8%;
			margin-left: 0.5%;
			margin-right: 1%;
			background-color: #333;
			color: white;
			font-size: 24px;
			font-weight: bold;
			border-radius: 10px;
		}

		.search-input {
			width: 40%;
			padding: 10px;
			border: 2px solid gray;
			font-size: 24px;
			color: black;
			background-color: white;
			border-radius: 5px;

			&:hover {
				border: 2px solid black;
			}
		}

		.search-input-shoe-last {
			width: 35%;
			padding: 10px;
			border: 2px solid gray;
			font-size: 24px;
			color: black;
			background-color: white;
			border-radius: 5px;

			&:hover {
				border: 2px solid black;
			}
		}

		.search-clear {
			margin-left: -45px;
			margin-top: 2px;
			cursor: pointer;
			z-index: 1;
		}

		.search-clear-shoe-last {
			margin-left: -45px;
			margin-top: 2px;
			cursor: pointer;
			z-index: 1;
		}

		.search-icon-shoe-last {
			width: 5%;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			z-index: 1;
		}

		.search-list {
			width: calc(40% + 20px + 4px);
			height: 270px;
			position: absolute;
			left: 30%;
			top: 120%;
			z-index: 1;

			.search-box {
				max-height: 270px;
				text-align: center;
				border-radius: 5px;
				color: white;
				font-size: 18px;
				font-weight: bold;
				background-color: gray;
				overflow: auto;

				.search-item {
					box-sizing: border-box;
					padding: 15px 0;
					border-bottom: 1px solid white;
					cursor: pointer;

					&:hover {
						background-color: #aaaaaa;
					}
				}
			}

			&::before {
				content: "";
				position: absolute;
				left: calc(50% - 10px);
				top: -10px;
				border-top: 0px solid transparent;
				border-left: 10px solid transparent;
				border-bottom: 10px solid gray;
				border-right: 10px solid transparent;
			}
		}

		.search-list-shoe-last {
			width: calc(35% + 20px + 4px);
			height: 270px;
			position: absolute;
			left: 30%;
			top: 120%;
			z-index: 1;

			.search-box-shoe-last {
				max-height: 270px;
				text-align: center;
				border-radius: 5px;
				color: white;
				font-size: 18px;
				font-weight: bold;
				background-color: gray;
				overflow: auto;

				.search-item-shoe-last {
					box-sizing: border-box;
					padding: 15px 0;
					border-bottom: 1px solid white;
					cursor: pointer;

					&:hover {
						background-color: #aaaaaa;
					}
				}
			}

			&::before {
				content: "";
				position: absolute;
				left: calc(50% - 10px);
				top: -10px;
				border-top: 0px solid transparent;
				border-left: 10px solid transparent;
				border-bottom: 10px solid gray;
				border-right: 10px solid transparent;
			}
		}
	}


	.brand-popup {
		.brand-box {
			width: 80vw;
			height: 80vh;
			border-radius: 1vw;
			background-color: #fdf6e3;
			overflow: auto;

			.brand-part {
				width: 100%;
				margin-bottom: 1%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.brand-initial {
					width: 100%;
					margin-left: 1.5%;
					margin-top: 1%;
					font-size: 32px;
					font-weight: bold;
				}

				.brand {
					width: 18%;
					height: 8vh;
					margin: 1%;
					display: flex;
					justify-content: center;
					align-items: center;
					box-sizing: border-box;
					border-radius: 1vw;
					cursor: pointer;
					font-size: 24px;
					font-weight: bold;
					color: white;
					background: linear-gradient(to right bottom, pink, blue);

					&:hover {
						color: black;
						background: linear-gradient(to right bottom, lightpink, lightblue);
					}
				}
			}
		}
	}


	.add-part-popup {
		.add-part {
			width: 100vw;
			height: 100vh;
			overflow: auto;
			padding: 2.5% 2% 1.5% 2%;
			box-sizing: border-box;
			background-color: #fdf6e3;

			.add-part-box {
				width: 100%;
				min-height: 100%;
				border-radius: 10px;
				box-shadow: 0 0 1px 5px #dddddd;
				box-sizing: border-box;
				position: relative;

				.back {
					width: 50px;
					height: 50px;
					display: flex;
					justify-content: space-evenly;
					align-items: center;
					position: absolute;
					left: 2.5%;
					top: 4.5%;
					cursor: pointer;
					z-index: 1;
				}

				.submit {
					display: flex;
					justify-content: space-evenly;
					align-items: center;
					position: absolute;
					right: 2.5%;
					top: 4.5%;
					cursor: pointer;
					z-index: 1;
				}

				.title {
					margin-bottom: 1%;
					font-size: 24px;
					font-weight: bold;
					text-align: center;
					padding: 16px;
				}

				.add-part-data {
					width: 100%;
					position: relative;
					display: flex;
					justify-content: flex-start;
					align-items: flex-start;
					flex-wrap: wrap;

					.add-part-attribute {
						width: 28%;
						margin-left: 8%;
						margin-bottom: 4%;
						background-color: #fdf6e3;

						.uni-easyinput {
							width: 80%;
							margin-left: 16px;

							&:deep(.uni-easyinput__content-input) {
								height: 40px;
								font-size: 20px;
								font-weight: bold;
							}
						}

						.uni-stat__select {
							width: 80%;
							margin-left: 16px;

							&:deep(.uni-select) {
								height: 40px;
								font-size: 20px;
								font-weight: bold;
							}

							&:deep(.uni-select__input-placeholder) {
								font-size: 20px;
							}


						}

						.uni-data-checklist {
							margin-left: 16px;

							&:deep(.checklist-box) {
								margin-top: 0px;
							}

							&:deep(.checklist-text) {
								padding: 8px;
								font-size: 20px;
								font-weight: bold;
							}
						}

						.uni-numbox {
							margin-left: 16px;
							height: 40px;

							&:deep(.uni-numbox-btns) {
								width: 40px;
								box-sizing: border-box;

								.uni-numbox--text {
									font-weight: bold;
								}
							}

							&:deep(.uni-numbox__value) {
								width: 60px;
								height: 40px;
								font-size: 20px;
								font-weight: bold;
							}
						}
					}
				}
			}
		}
	}

	.addInput {
		font-size: 20px;
		width: 80%;
		height: 250px;
		margin-left: 8%;
		margin-bottom: 20px;
		background-color: white;
		border: 2px solid #000;
		/* 设置边框颜色和宽度 */
		border-radius: 10px;
		/* 设置边框圆角 */

		&:deep(.uni-easyinput__content-textarea) {
			height: 235px;
			font-size: 20px;
			font-weight: bold;
		}

		&:deep(.uni-easyinput__placeholder-class) {
			font-size: 20px;
		}
	}

    .sourceData{
        height: 20%;
        font-size: 26px;
        padding-left: 15%;
        padding-top: 2%;
        display: flex;
        flex-wrap: wrap;

        text{
            width: 100%;
            margin-bottom: 20px;
        }
    }

    .targetTitle{
        font-size: 26px;
        padding-left: 15%;
        display: flex;
        flex-wrap: wrap;

        text{
            width: 100%;
        }
    }

    .sourceTitle{
        font-size: 26px;
        padding-left: 15%;
        margin-top: 2%;
        display: flex;
        flex-wrap: wrap;

        text{
            width: 100%;
        }
    }
</style>