<!-- 图片弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import PicturePopup from '@/pages/sop/common/picture-popup.vue'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()
// 预览图片弹框
const picturePopup = ref()
// 图片弹框
const overallPicturePopup = ref()

// 图片列表
const imgList = ref([])

// 工序流程详情
const flowDetail = ref({})

// 工序信息
const processInfo = inject('processInfo')
// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getOverallFlow = inject('getOverallFlow')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')

// 显示图片弹框
async function showOverallPicturePopup(param) {
  flowDetail.value = param
  imgList.value = [...param.imgList]
  overallPicturePopup.value.open()
}

// 修改整体流程图片
async function updateOverallPicture() {
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateOverallPicture',
    method: 'POST',
    data: {
      ...flowDetail.value,
      imgList: imgList.value,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getOverallFlow()
      tipPopup.value.showTipPopup('success', '修改成功！')
      overallPicturePopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 选择图片
function selectPicture() {
  uni.chooseImage({
    count: 9,
    sizeType: ['original', 'compressed'],
    extension: ['jpg', 'png'],
    sourceType: ['camera ', 'album'],
    success: async (res) => {
      for (let i = 0; i < res.tempFiles.length; i++) {
        // #ifdef WEB
        let file = res.tempFiles[i]
        let imgUrl = await webReadFileAsDataURL(file)
        // #endif
        
        // #ifdef APP
        let path = res.tempFiles[i].path
        let imgUrl = await appReadFileAsDataURL(path)
        // #endif
        
        imgList.value.push({
          id: -1,
          imgUrl: imgUrl
        })
      }
    }
  })
}

// web 以 url 方式读取文件
function webReadFileAsDataURL(file) {
  return new Promise((resolve, reject) => {
    let reader = new FileReader()
    reader.onload = () => {
      resolve(reader.result)
    }
    reader.onerror = (err) => {
      reject(err)
    }
    reader.readAsDataURL(file)
  })
}

// app 以 url 方式读取文件
function appReadFileAsDataURL(path) {
  return new Promise((resolve, reject) => {
    plus.io.resolveLocalFileSystemURL(path, (entry) => {
      entry.file((file) => {
        let reader = new plus.io.FileReader()
        reader.onload = () => {
          resolve(reader.result)
        }
        reader.onerror = (err) => {
          reject(err)
        }
        reader.readAsDataURL(file)
      })
    })
  })
}

// 清除图片
function clearPicture(param) {
  imgList.value.splice(param, 1)
}

defineExpose({
  showOverallPicturePopup
})
</script>

<template>
  <uni-popup
    ref="overallPicturePopup"
    type="center"
    :is-mask-click="false"
    class="overall-picture-popup"
  >
    <view class="container">
      <view class="top-bar">
        <view
          @click="overallPicturePopup.close()"
          class="cancel button"
        >
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <view class="title">
          请选择图片 - {{ flowDetail.skey }}
        </view>
        
        <view
          @click="updateOverallPicture()"
          class="submit button"
        >
          <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
        </view>
      </view>
      
      <view v-show="flowDetail.actions" class="flow-action flex-row-center">
        <view class="flow-action-description">
          {{ flowDetail.actions }}
        </view>
      </view>
      
      <view class="picture-list">
        <view v-for="(item, index) in imgList" class="preview-picture watermark">
          <img
            @click="picturePopup.showPicturePopup(item.id > 0 ? (urlPrefix + item.imgUrl) : item.imgUrl)"
            :src="item.id > 0 ? (urlPrefix + item.imgUrl) : item.imgUrl"
            alt=""
            class="button"
          />
          
          <view @click="clearPicture(index)" class="clear-picture">
            <uni-icons type="closeempty" size="20" color="darkred"></uni-icons>
          </view>
        </view>
        
        <view @click="selectPicture()" class="add-picture button">
          <uni-icons type="plusempty" size="70" color="green"></uni-icons>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <picture-popup ref="picturePopup" />
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.overall-picture-popup {
  .container {
    width: 900px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .top-bar {
      width: 100%;
      height: 70px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title {
        font-size: 22px;
        font-weight: bold;
      }
      
      .cancel, .submit {
        width: 75px;
        height: 50px;
      }
    }
    
    .flow-action {
      width: 100%;
      padding: 10px;
      
      .flow-action-description {
        width: 700px;
        font-size: 20px;
        font-weight: bold;
        text-align: center;
      }
    }
    
    .picture-list {
      width: 100%;
      max-height: 500px;
      padding: 10px;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      align-content: flex-start;
      flex-wrap: wrap;
      overflow: auto;
      
      .preview-picture {
        width: fit-content;
        margin-top: 25px;
        margin-right: 25px;
        position: relative;
        
        img {
          min-width: 60px;
          height: 140px;
          overflow: hidden;
        }
        
        .clear-picture {
          width: 30px;
          height: 30px;
          position: absolute;
          right: -15px;
          top: -15px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: bold;
          text-align: center;
          background-color: #fdf6e3;
          border-radius: 50%;
          box-shadow: 0 0 5px gray;
          transition: all 0.1s linear;
          z-index: 1;
          /* #ifdef WEB */
          cursor: pointer;
          /* #endif */
          
          &:active {
            transform: scale(0.98);
            box-shadow: 0 0 1px gray;
            /* #ifdef APP */
            background-color: #ccc;
            /* #endif */
          }
          
          /* #ifdef WEB */
          &:hover {
            background-color: #ccc;
          }
          /* #endif */
        }
      }
      
      .add-picture {
        width: 140px;
        height: 140px;
        margin-top: 25px;
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .watermark {
    position: relative;
    word-break: break-all;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    
    &:before {
      content: 'STELLA';
      position: absolute;
      top: 2px;
      left: 2px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      text-shadow: 0 0 2px black;
      z-index: 1;
      pointer-events: none;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>