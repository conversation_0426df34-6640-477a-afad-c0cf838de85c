package com.zqn.email.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送权限配置实体类
 * @date 2025/01/22 10:00
 */
@Data
public class EmailPushPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 工号（关联sy_user.user_no）
     */
    private String userNo;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 推送報價1版權限 (Y-有权限, N-无权限, P-待定)
     */
    private String pushQuoteV1;

    /**
     * 推送報價2版權限 (Y-有权限, N-无权限, P-待定)
     */
    private String pushQuoteV2;

    /**
     * 推送報價3版權限 (Y-有权限, N-无权限, P-待定)
     */
    private String pushQuoteV3;

    /**
     * 推送報價4版權限 (Y-有权限, N-无权限, P-待定)
     */
    private String pushQuoteV4;

    /**
     * 推送報價5版權限 (Y-有权限, N-无权限, P-待定)
     */
    private String pushQuoteV5;

    /**
     * 推送預估Z版權限 (Y-有权限, N-无权限, P-待定)
     */
    private String pushEstimateZ;

    /**
     * 推送預估ZZ版權限 (Y-有权限, N-无权限, P-待定)
     */
    private String pushEstimateZz;

    /**
     * 推送P版權限 (Y-有权限, N-无权限, P-待定)
     */
    private String pushPVersion;

    /**
     * 更新版本邮件提醒 (Y-启用, N-禁用)
     */
    private String updateEmailNotify;

    /**
     * 状态 (Y-启用, N-禁用)
     */
    private String status;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}
