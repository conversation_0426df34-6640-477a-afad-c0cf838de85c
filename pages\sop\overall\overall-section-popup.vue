<!-- 加工段弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 加工段弹框
const overallSectionPopup = ref()

// 工序流程详情
const flowDetail = ref({})

// 工序信息
const processInfo = inject('processInfo')
// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getOverallFlow = inject('getOverallFlow')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')

// 滚动 id
const scrollId = ref('')

// 显示加工段弹框
async function showOverallSectionPopup(param) {
  flowDetail.value = param
  scrollId.value = ''
  overallSectionPopup.value.open()
  setTimeout(() => {
    scrollId.value = 'item' + param.wkGroup
  }, 1000)
}

// 选择加工段
async function selectOverallSection(param) {
  if (param === flowDetail.value.wkGroup) {
    overallSectionPopup.value.close()
    return
  }
  
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateFlowSection',
    method: 'POST',
    data: {
      model: flowDetail.value.model,
      operation: flowDetail.value.operation,
      rtgCode: flowDetail.value.rtgCode,
      skey: flowDetail.value.skey,
      wkGroup: flowDetail.value.wkGroup,
      targetWkGroup: param,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getOverallFlow()
      tipPopup.value.showTipPopup('success', '修改成功！')
      overallSectionPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  showOverallSectionPopup
})
</script>

<template>
  <uni-popup
    ref="overallSectionPopup"
    type="center"
    class="overall-section-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择加工段 - {{ flowDetail.skey }}
      </view>
      
      <view v-show="flowDetail.actions" class="flow-action flex-row-center">
        <view class="flow-action-description">
          {{ flowDetail.actions }}
        </view>
      </view>
      
      <scroll-view
        :scroll-y="true"
        :scroll-into-view="scrollId"
        :scroll-with-animation="true"
        class="flow-section-list"
      >
        <view
          v-for="(item, index) in processFlowList.length"
          :key="index"
          :id="'item' + item.toString().padStart(3, '0')"
          class="flow-section flex-row-center"
        >
          <view
            @click="selectOverallSection(item.toString().padStart(3, '0'))"
            class="button"
            :style="{
              filter: item.toString().padStart(3, '0') === flowDetail.wkGroup ? 'hue-rotate(90deg)' : 'none',
              backdropFilter: item.toString().padStart(3, '0') === flowDetail.wkGroup ? 'hue-rotate(90deg)' : 'none'
            }"
          >
            {{ item.toString().padStart(3, '0') }}
          </view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.overall-section-popup {
  .container {
    width: 430px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .flow-action {
      width: 100%;
      padding: 10px;
      
      .flow-action-description {
        width: 400px;
        font-size: 20px;
        font-weight: bold;
        text-align: center;
      }
    }
    
    .flow-section-list {
      min-height: 70px;
      max-height: 350px;
      overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .flow-section {
        width: 100%;
        padding: 10px;
        
        .button {
          width: 270px;
          height: 50px;
          color: darkgreen;
          font-size: 20px;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>