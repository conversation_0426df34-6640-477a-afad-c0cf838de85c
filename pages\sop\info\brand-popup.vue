<!-- 品牌弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 提示弹框
const tipPopup = ref()

// 品牌
const brand = inject('brand')
// 品牌列表
const brandList = ref([])
// 品牌弹框
const brandPopup = ref()

// 滚动 id
const scrollId = ref('A')

// 获取品牌列表
async function getBrandList() {
  await uni.request({
    url: urlPrefix + '/sop/getBrandList',
    method: 'GET'
  }).then(res => {
    if (res.data.code) {
      brandList.value = res.data.data ? res.data.data : []
    } else {
      brandList.value = []
      tipPopup.value.showTipPopup('warn', '暂无品牌列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示品牌弹框
async function showBrandPopup() {
  if (brandList.value.length === 0) {
    uni.showLoading({
      title: '加载中...',
      mask: true
    })
    
    await getBrandList()
    
    uni.hideLoading()
    
    if (brandList.value.length === 0) {
      return
    }
  }
  scrollId.value = 'A'
  brandPopup.value.open()
}

// 选择品牌
function selectBrand(param) {
  if (brand.value !== param) {
    brand.value = param
    uni.setStorageSync('sop-brand', brand.value)
  }
  brandPopup.value.close()
}

defineExpose({
  getBrandList,
  showBrandPopup
})
</script>

<template>
  <uni-popup
    ref="brandPopup"
    type="center"
    class="brand-popup"
  >
    <view class="navbar-list flex-row-evenly-center">
      <view
        v-for="(item, index) in brandList"
        :key="index"
        v-show="item.brandList.length > 0"
        @click="scrollId = item.initial"
        class="navbar flex-row-center"
        :style="{
          color: item.initial === scrollId ? 'white' : 'black',
          backgroundColor: item.initial === scrollId ? '#b6b7a4' : '#e6e6fa'
        }"
      >
        {{ item.initial }}
      </view>
    </view>
    
    <scroll-view
      :scroll-y="true"
      :scroll-into-view="scrollId"
      :scroll-with-animation="true"
      :show-scrollbar="false"
      class="option-list"
    >
      <view
        v-for="(item, index) in brandList"
        :key="index"
        v-show="item.brandList.length > 0"
        class="option"
      >
        <view class="initial" :id="item.initial">
          {{ item.initial }}
        </view>
        
        <view class="brand-list flex-row-start-center">
          <view
            v-for="(i, j) in item.brandList"
            :key="j"
            @click="selectBrand(i)"
            class="brand button flex-row-center"
            :style="{
              color: i === brand ? 'deeppink' : 'purple'
            }"
          >
            {{ i }}
          </view>
        </view>
      </view>
    </scroll-view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.brand-popup {
  .navbar-list {
    width: 80vw;
    height: 50px;
    margin-bottom: 10px;
    background-color: #e6e6fa;
    border-radius: 25px;
    box-shadow: 0 0 5px white;
    flex-shrink: 0;
    overflow-x: auto;
    overflow-y: hidden;
    
    /* #ifdef WEB */
    &::-webkit-scrollbar {
      display: none;
    }
    /* #endif */
    
    .navbar {
      width: 40px;
      height: 40px;
      font-size: 24px;
      font-weight: bold;
      border-radius: 50%;
      flex-shrink: 0;
      transition: all 0.25s ease;
      /* #ifdef WEB */
      cursor: pointer;
      /* #endif */
      
      /* #ifdef APP */
      &:active {
        background-color: #ccc !important;
      }
      /* #endif */
      
      /* #ifdef WEB */
      &:hover {
        background-color: #ccc !important;
      }
      /* #endif */
    }
  }
  
  .option-list {
    width: 80vw;
    height: 80vh;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    
    .option {
      width: 100%;
      
      .initial {
        padding: 10px;
        font-size: 32px;
        font-weight: bold;
      }
      
      .brand-list {
        flex-wrap: wrap;
        
        .brand {
          width: calc(20% - 20px);
          margin: 10px;
          height: 60px;
          color: purple;
          font-size: 24px;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .flex-row-start-center {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  
  .flex-row-evenly-center {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }
}
</style>