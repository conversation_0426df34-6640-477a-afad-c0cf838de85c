<template>
	<view class="container">
		<view class="topcontainer">
			<view class="back">
				<uni-icons @click="back" type="back" size="36" color="#FFFFFF"></uni-icons>
			</view>

			<view class="title">
				<text>面部技術分析</text>
			</view>
		</view>

		<view class="menucontainer">

			<view class="menu-item" v-for="(item, index) in menuItems" :key="index" @click="handleMenuClick(item)">
				<view class="image-container">
					<image class="menu-icon" :src="item.icon"></image>
					<span class="badge" v-show="item.badge !=0 ">{{item.badge}}</span>
				</view>
				<view class="menu-item-text">
					<text class="menu-text">{{ item.text }}</text>
					<text class="menu-text-red">{{ item.tip }}</text>
				</view>
			</view>
		</view>

		<view class="contentcontainer">
			<view class="leftcontainer">
				<view class="title">版師開版異常(當月)</view>
				<uni-collapse>

					<uni-collapse-item :open="true" v-for="(item, index) in items" :key="index">
						<template v-slot:title>
							<view class="custom-title">
								<image class="custom-thumb" src="../../static/icon_houserent.png"></image>
								<text class="title-text">{{ item.title }}</text>
								<view class="dot-badge">
									<text class="dot-badge-text">{{ item.list.length }}</text>
								</view>
							</view>
						</template>
						<view class="scrollable-list">
							<!-- <uni-list>
								<uni-list-item clickable @click="handleListItemClick(listItem)"
									v-for="listItem in item.list" :key="listItem" :title="listItem.model_no" :rightText="listItem.brand_no"></uni-list-item>
							</uni-list> -->
							<view v-for="listItem in item.list" class="modelList">
								<text>{{listItem.model_no}}</text>
								<text>{{listItem.brand_no}}</text>
							</view>
						</view>
					</uni-collapse-item>

				</uni-collapse>
			</view>

			<view class="rightcontainer">
				<view class="datacontainer">

					<view class="item">
						<!-- <image class="image" src="../../static/lk.png"></image> -->
						<view class="data">
							<text class="item-title">當日開版型體</text>
							<view class="valuecontainer">
								<text class="item-value">{{todayModelNum}}</text>
								<text class="item-value-unit"></text>
							</view>
						</view>
					</view>

					<view class="item">
						<!-- <image class="image" src="../../static/lk.png"></image> -->
						<view class="data">
							<text class="item-title">當日開版客戶</text>
							<view class="valuecontainer">
								<text class="item-value">{{todayCustomerNum}}</text>
								<text class="item-value-unit"></text>
							</view>
						</view>
					</view>

					<view class="item">
						<!-- <image class="image" src="../../static/lk.png"></image> -->
						<view class="data">
							<text class="item-title">技術部總人數</text>
							<view class="valuecontainer">
								<text class="item-value">{{technicalTotalNum}}</text>
								<text class="item-value-unit"></text>
							</view>
						</view>
					</view>
					<view class="item">
						<!-- <image class="image" src="../../static/lk.png"></image> -->
						<view class="data">
							<text class="item-title">SOP統計</text>
							<view class="valuecontainer">
								<text class="item-value">0</text>
								<text class="item-value-unit"></text>
							</view>
						</view>
					</view>

				</view>
				<view class="echartcontainer">
					<view style="display: flex;align-items: center;">
						<image class="image" style=" margin-left: 10px; width: 35px; height: 35px;" src="../../static/jl.png"></image>
						<view class="chart-title">各廠面部技師人員分析</view>
					</view>

					<!-- <qiun-title-bar title="基本柱状图" /> -->
					<view class="charts-box" v-if="items[0].list.length > 0">
						<qiun-data-charts type="column" :chartData="chartsDataColumn1" @getIndex="viewPeopleDetail"/>
					</view>

				</view>
			</view>

		</view>

		<!-- 添加弹窗组件 -->
		<uni-popup ref="popup" type="center">
			<view class="popup-content">
				<view class="popup-title">人員詳情</view>
				<view class="popup-list">
					<view v-for="(item, index) in peopleDetailList" :key="index" class="popup-item">
						<view class="popup-row">
							<view class="popup-col">
								<text class="item-label">中文姓名：</text>
								<text class="item-value">{{item.LEGALNAME}}</text>
							</view>
							<view class="popup-col">
								<text class="item-label">英文姓名：</text>
								<text class="item-value">{{item.ENGLISHNAME}}</text>
							</view>
						</view>
						<view class="popup-row">
							<view class="popup-col">
								<text class="item-label">職位：</text>
								<text class="item-value">{{item.JOBTITLE}}</text>
							</view>
							<view class="popup-col">
								<text class="item-label">職級：</text>
								<text class="item-value">{{item.JOBLEVEL}}</text>
							</view>
						</view>
						<view class="popup-row">
							<view class="popup-col">
								<text class="item-label">部門：</text>
								<text class="item-value">{{item.DEPARTMENT}}</text>
							</view>
							<view class="popup-col">
								<text class="item-label">工廠：</text>
								<text class="item-value">{{item.FACTORY}}</text>
							</view>
						</view>
						<view class="popup-row">
							<view class="popup-col">
								<text class="item-label">組織：</text>
								<text class="item-value">{{item.ORGANIZATIONNAME}}</text>
							</view>
							<view class="popup-col">
								<text class="item-label">狀態：</text>
								<text class="item-value">{{item.EMPLOYMENTSTATUS}}</text>
							</view>
						</view>
					</view>
				</view>
				<button class="close-btn" @click="closePopup">關閉</button>
			</view>
		</uni-popup>
	</view>
</template>


<script setup>
	import {
		ref,
		onMounted,
		onUnmounted,
		onBeforeUnmount
	} from 'vue';
	import {onLoad,onShow} from '@dcloudio/uni-app'
	import urlPrefix from '@/pages/common/urlPrefix.js'

	const isWeb = typeof window !== 'undefined';
	
	//返回首页
	function back() {
	    let back = getCurrentPages();
	    if(back && back.length > 1) {
	    	uni.navigateBack({  
	    	  delta: 1  
	    	});  
	    } else {  
	    	history.back();  
	    }
	}


	const menuItems = ref([
		{
			icon: isWeb ? "/pcc/static/pz.png" : "/static/pz.png",
			text: "開版追蹤",
			tip: "",
			badge: 0
		},
		{
			icon: isWeb ? "/pcc/static/pz.png" : "/static/pz.png",
			text: "開版異常",
			tip: "",
			badge: 0
		},
		{
			icon: isWeb ? "/pcc/static/pz.png" : "/static/pz.png",
			text: "重做预警",
			tip: "",
			badge: 0
		},
		{
			icon: isWeb ? "/pcc/static/gy.png" : "/static/gy.png",
			text: "開版明細",
			tip: "",
			badge: 0
		},
		{
			icon: isWeb ? "/pcc/static/tb.png" : "/static/tb.png",
			text: "面部A",
			tip: "",
			badge: 0
		},
		{
			icon: isWeb ? "/pcc/static/ck.png" : "/static/ck.png",
			text: "面部B",
			tip: "",
			badge: 0
		},
		{
			icon: isWeb ? "/pcc/static/ck.png" : "/static/ck.png",
			text: "面部C",
			tip: "",
			badge: 0
		}

	]);
	
	
	
	//明细数据查看
	function viewDetail(type) {
		var viewUrl = ""
		if(type === '開版追蹤'){
			viewUrl = "/pages/factorymanager/devTrackingTop"
		}else if(type === '開版異常'){
			viewUrl = "/pages/factorymanager/devTrackingWarnTop"
		}else if(type === '重做预警'){
			viewUrl = "/pages/factorymanager/retryAlertTop"
		}
		
		if(viewUrl !== ''){
			uni.navigateTo({
				url: viewUrl,
				animationType: 'pop-in',
				animationDuration: 200
			})
			return;
		}
		
		
		if(type === '面部A'){
			viewUrl = "http://***************/eip/System/Kaifa/CxPanel/PanelViewMB-A-Test.html"
		}else if(type === '面部B'){
			viewUrl = "http://***************/eip/System/Kaifa/CxPanel/PanelViewMB-B-Test.html"
		}else if(type === '面部C'){
			viewUrl = "http://***************/eip/System/Kaifa/CxPanel/PanelViewMB-C-Test.html"
		}else if(type === '開版明細'){
			viewUrl = "http://***************/eip/System/Kaifa/Cxfiles/kbBs2-2023.html"
		}
		
		if(viewUrl !== ''){
			uni.navigateTo({
				url: '/pages/webview/webview?url=' + encodeURIComponent(viewUrl),
				animationType: 'pop-in',
				animationDuration: 200
			});
		}
	}
	
	const popup = ref(null);
	const peopleDetailList = ref([]);

	function viewPeopleDetail(e){
		var index = e.currentIndex.index;
		const factory = chartsDataColumn1.value.categories[index];
		console.log(factory);
		
		// 调用接口获取人员详情
		uni.request({
			url: urlPrefix + "/technicaldept/queryPeopleDetail",
			data: {
				factory: factory
			},
			method: "GET"
		}).then(res => {
			peopleDetailList.value = res.data.data || [];
			popup.value.open();
		}).catch(err => {
			console.log(err)
			uni.showToast({
				title: '获取数据失败',
				icon: "error"
			});
		})
	}

	function closePopup() {
		popup.value.close();
	}

	const items = ref([{
			title: '开版预警',
			badge: 3,
			list: [
			],
			brandList: []
		}
	]);



	const handleMenuClick = (item) => {
		console.log('Menu item clicked:', item.text);
		viewDetail(item.text);
	};


	const handleListItemClick = (item) => {
		console.log('handleListItemClick:' + item);
	};

	const chartsDataColumn1 = ref({
		categories: ["2016", "2017", "2018", "2019", "2020", "2021", "2022", "2023"],
		series: [{
				name: "技師",
				data: [0, 0, 0, 0, 0, 0, 0, 0]
			}
		]
	});
	
	// 当日开版型体数量
	const todayModelNum = ref(0)
	//当日开版客户数量
	const todayCustomerNum = ref(0)
	//技术部总人数
	const technicalTotalNum = ref(0)
	//SOP统计
	const sopNum = ref(0)
	
	function getData(){
		uni.request({
			url: urlPrefix + "/technicaldept/queryModelList",
			data: {
			},
			method: "GET"
		}).then(res => {
			items.value[0].list = res.data.data.modelList;
		}).catch(err => {
			console.log(err)
			uni.showToast({
				title: '获取数据失败..',
				icon: "error"
			});
		})
		
		uni.request({
		    url: urlPrefix + "/technicaldept/queryByDEUSER",
		    data: {
		    },
		    method: "GET"
		}).then(res => {
			todayModelNum.value = res.data.data.todayModelNum;
			todayCustomerNum.value = res.data.data.todayCustomerNum;
			sopNum.value = res.data.data.sopNum;
		}).catch(err => {
		    console.log(err)
		    uni.showToast({
		        title: '获取数据失败..',
		        icon: "error"
		    });
		})
		
		uni.request({
		    url: urlPrefix + "/technicaldept/queryByWEB",
		    data: {
		    },
		    method: "GET"
		}).then(res => {
			chartsDataColumn1.value.categories = res.data.data.categories;
			chartsDataColumn1.value.series[0].data = res.data.data.faceData;
			technicalTotalNum.value = res.data.data.technicalTotalNum;
		}).catch(err => {
		    console.log(err)
		    uni.showToast({
		        title: '获取数据失败..',
		        icon: "error"
		    });
		})
		
		// 获取当前日期
		var currentDate = new Date();
		
		// 设置startTime为当天的00:00:00
		var startTime = new Date(currentDate.setHours(0, 0, 0, 0)).getTime();
		
		// 设置endTime为当天的23:59:59
		var endTime = new Date(currentDate.setHours(23, 59, 59, 999)).getTime();
		// 当日开版追踪数量
		uni.request({
		    url: urlPrefix + "/devTracking/query",
		    data: {
		        "pageNo": 1,
		        "pageSize": 999,
				"startTime": startTime,
				"endTime": endTime,
				"type": 1
		    },
		    method: "GET"
		}).then(res => {
			menuItems.value[0].badge = res.data.data.endRow;
			uni.hideLoading();
		}).catch(err => {
			uni.hideLoading();
		    console.log(err)
		})
		
		// 当日开版预警追踪数量
		uni.request({
		    url: urlPrefix + "/devTracking/query",
		    data: {
		        "pageNo": 1,
		        "pageSize": 999,
				"startTime": startTime,
				"endTime": endTime,
				"type": 3
		    },
		    method: "GET"
		}).then(res => {
			menuItems.value[1].badge = res.data.data.endRow;
			uni.hideLoading();
		}).catch(err => {
			uni.hideLoading();
		    console.log(err)
		})
		
		// 当日开版预警追踪数量
		uni.request({
		    url: urlPrefix + "/retryAlert/query",
		    data: {
		        "pageNo": 1,
		        "pageSize": 999,
				"startTime": startTime,
				"endTime": endTime,
				"type": 1
		    },
		    method: "GET"
		}).then(res => {
			menuItems.value[2].badge = res.data.data.endRow;
			uni.hideLoading();
		}).catch(err => {
			uni.hideLoading();
		    console.log(err)
		})
	}
	
	onShow(()=>{
		getData();
	})
	
</script>



<style lang="scss">
	page {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		background-color: #E3E0EC;
	}
	
	.container {
		height:100%;
	}

	.topcontainer {
		background-image: url('../../static/bg.jfif');
		background-repeat: no-repeat;
		background-size: cover;
		background-position: center -2px;
		width: 100%;
		height: 12vh;
		position: relative;
		display: flex;
		align-items: center;

		.back {
			width: 40px;
			height: 40px;
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			left: 1.5%;
			border-radius: 50%;
			box-shadow: 0 0 5px #fff;
			cursor: pointer;
			z-index: 1;
		}

		.title {
			line-height: 6vh;
			height: 6vh;
			margin: 0 auto;
			text-align: center;
			font-size: 28px;
			color: #fff;
			flex: 1;
		}

	}

	.menucontainer {
		margin-top: 10px;
		background-color: #fff;
		width: calc(100% - 20px);
		height: 16vh;
		margin-left: 10px;
		margin-right: 10px;
		text-align: center;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-sizing: border-box;
		// border: 2px solid #fff;
		border-radius: 10px;

		.menu-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 100%;
		}

		.menu-icon {
			width: 50px;
			height: 50px;
		}
		
		.image-container {
		    position: relative;
		    width: 50px;
		    height: 50px;
		}
		
		
		.menu-item-text{
			display: flex;
			align-items: center;
		}
		
		.badge {
		    position: absolute;
		    top: 0;
		    right: 3;
		    background-color: red; /* 角标背景颜色 */
		    color: white; /* 角标文字颜色 */
		    border-radius: 50%; /* 圆形外观 */
		    padding: 2px 5px; /* 角标大小 */
		    font-size: 12px; /* 角标文字大小 */
		}

		.menu-text {
			font-size: 16px;
			color: #000;
		}
		
		
		.menu-text-red{
			color: red;
		}

	}

	.contentcontainer {
		margin-top: 10px;
		margin-left: 10px;
		margin-right: 10px;
		display: flex;
		width: calc(100% - 10px);
		height: calc(72vh - 30px);

		.leftcontainer {
			width: 20vw;
			height: 100%;
			background-color: white;
			margin: 0 auto;
			font-size: 14px;
			overflow: hidden;
			position: relative;
			padding-bottom: 10px;
			border-radius: 10px;
			box-sizing: border-box;
			// border: 2px solid #fff;

			.title {
				margin: 10px;
				font-size: 18px;
			}

			.scrollable-list {
				height: 60vh;
				overflow-y: auto;
			}


			.scrollable-list::-webkit-scrollbar {
				display: none;
			}
			
			.scrollable-list .modelList{
				font-size: 16px;
				padding: 5px 15px;
				margin-bottom: 5px;
				border-bottom: 1px solid #E3E0EC;
				display: flex;
				justify-content: space-between;
			}

		}

		.rightcontainer {
			width: 80vw;
			height: 100%;
			display: flex;
			flex-direction: column;
		}

		.datacontainer {
			height: 20vh;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-left: 10px;
			margin-right: 10px;

			.item {
				background-color: #fff;
				margin-right: 20px;
				flex: 1;
				height: 100%;
				// border: 2px solid #fff;
				border-radius: 10px;
				justify-items: center;
				align-items: center;
				display: flex;
				// flex-direction: column;
				box-sizing: border-box;

				.image {
					margin-left: 20px;
					width: 140rpx;
					height: 100rpx;
					object-fit: contain;
				}

				.data {
					// margin-left: -30px;
					width: 100%;
					height: 100%;
					justify-items: center;
					align-items: center;
					display: flex;
					flex-direction: column;
				}
			}

			.item-title {
				margin-top: 15px;
				color: #333;
				font-size: 20px;
			}

			.valuecontainer {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: flex-end;

				.item-value {
					color: #6052E9;
					font-size: 30px;
				}

				.item-value-unit {
					margin-left: 5px;
					margin-top: 8px;
					color: #6052E9;
					font-size: 18px;

				}
			}




		}

		.datacontainer .item:last-child {
			margin-right: 0;
		}

		.echartcontainer {
			margin-top: 10px;
			padding-top: 10px;
			box-sizing: border-box;
			margin-left: 10px;
			margin-right: 10px;
			background-color: #fff;
			box-sizing: border-box;
			// border: 2px solid #fff;
			border-radius: 10px;
			// display: flex;

			.chart-title {
				margin-left: 10px;
				font-size: 20px;
				color: #000;
			}

			.charts-box {
				width: 100%;
				height: 46vh;
			}

		}




		.custom-title {
			height: 40px;
			position: relative;
			display: flex;
			align-items: center;
		}

		.custom-thumb {
			margin-left: 10px;
			width: 25px;
			height: 25px;
			margin-right: 10px;
		}

		.title-text {
			flex-grow: 1;
		}

		.dot-badge {
			position: absolute;
			right: 10px;
			top: 50%;
			transform: translateY(-50%);
			background-color: red;
			color: white;
			border-radius: 50%;
			width: 20px;
			height: 20px;
			line-height: 20px;
			text-align: center;
			font-size: 12px;
		}

		.dot-badge-text {}

	}

	.popup-content {
		background-color: #fff;
		border-radius: 10px;
		padding: 20px;
		width: 80vw;
		max-height: 80vh;
		
		.popup-title {
			font-size: 20px;
			font-weight: bold;
			text-align: center;
			margin-bottom: 20px;
			color: #333;
			border-bottom: 2px solid #6052E9;
			padding-bottom: 10px;
		}
		
		.popup-list {
			max-height: 60vh;
			overflow-y: auto;
		}
		
		.popup-item {
			padding: 15px;
			border-bottom: 1px solid #eee;
			margin-bottom: 10px;
			background-color: #f8f9fa;
			border-radius: 5px;
			
			&:last-child {
				margin-bottom: 0;
			}
		}
		
		.popup-row {
			display: flex;
			margin-bottom: 10px;
			
			&:last-child {
				margin-bottom: 0;
			}
		}
		
		.popup-col {
			flex: 1;
			display: flex;
			align-items: center;
		}
		
		.item-label {
			color: #666;
			width: 80px;
			font-size: 14px;
		}
		
		.item-value {
			color: #333;
			flex: 1;
			font-size: 14px;
		}
		
		.close-btn {
			margin-top: 20px;
			background-color: #6052E9;
			color: #fff;
			border: none;
			border-radius: 5px;
			padding: 8px 20px;
			width: 120px;
			font-size: 16px;
			
			&:active {
				background-color: darken(#6052E9, 10%);
			}
		}
	}
</style>