<template>
  <view class="container">
    <view class="header">
      <view class="header-left">
        <button class="back-btn" @click="goBack">
          <text class="back-icon">←</text>
          <text class="back-text">返回</text>
        </button>
      </view>
      <view class="header-center">
        <text class="title">{{ isEdit ? '编辑学员记录' : '新增学员记录' }}</text>
      </view>
      <view class="header-right">
        <button class="save-btn" @click="saveRecord">
          <text class="save-icon">💾</text>
          <text class="save-text">保存</text>
        </button>
      </view>
    </view>
    
    <view class="content">
      <scroll-view scroll-y class="form-container">
        <view class="form-section">
          <!-- 基本信息 -->
          <view class="section-title">基本信息</view>
          
          <!-- 基本信息布局：左侧表单，右侧鞋图 -->
          <view class="form-layout">
            <!-- 左侧表单区域 -->
            <view class="form-left">
              <!-- 第一行：日期和单号 -->
              <view class="form-row">
                <view class="form-item">
                  <text class="form-label required">日期</text>
                  <picker mode="date" :value="formData.recordDate" @change="onDateChange">
                    <view class="picker-input">{{ formData.recordDate || '请选择日期' }}</view>
                  </picker>
                </view>
                
                <view class="form-item">
                  <text class="form-label required">单号/数据</text>
                  <view class="input-with-scan">
                    <input 
                      type="text" 
                      v-model="formData.orderNo" 
                      placeholder="请输入单号或扫码获取"
                      class="form-input"
                    />
                    <button class="scan-btn" @click="scanOrderNo">
                      <text class="scan-icon">📷</text>
                      <text>扫码</text>
                    </button>
                  </view>
                </view>
              </view>
              
              <!-- 第二行：学员选择 -->
              <view class="form-row">
                <view class="form-item">
                  <text class="form-label required">学员</text>
                  <picker 
                    mode="selector" 
                    :range="studentOptions" 
                    :value="selectedStudentIndex"
                    @change="onStudentChange">
                    <view class="picker-input">{{ selectedStudentIndex >= 0 ? studentOptions[selectedStudentIndex] : '请选择学员' }}</view>
                  </picker>
                </view>
              </view>
              
              <!-- 第二行：订单数量和型体 -->
              <view class="form-row" v-if="(formData.ordQty !== null && formData.ordQty !== undefined && formData.ordQty !== '') || (formData.modelNo && formData.modelNo.trim())">
                <!-- 新增订单数量字段 -->
                <view class="form-item" v-if="formData.ordQty !== null && formData.ordQty !== undefined && formData.ordQty !== ''">
                  <text class="form-label">订单数量</text>
                  <view class="readonly-field">{{ formData.ordQty }}</view>
                </view>
                
                <!-- 新增型体字段 -->
                <view class="form-item" v-if="formData.modelNo && formData.modelNo.trim()">
                  <text class="form-label">型体</text>
                  <view class="readonly-field">{{ formData.modelNo }}</view>
                </view>
              </view>
            </view>
            
            <!-- 右侧鞋图区域 -->
            <view class="form-right">
              <view class="form-item shoe-pic-item">
                <text class="form-label">鞋图预览</text>
                <view class="image-display-area-compact">
                  <view v-if="formData.shoePic" class="image-preview-compact">
                    <image 
                      :src="getImageUrl(formData.shoePic)"
                      mode="aspectFit" 
                      class="preview-image-compact"
                      @click="previewImage(formData.shoePic)"
                    />
                  </view>
                  <view v-else class="no-image-placeholder-compact">
                    <text class="placeholder-icon-compact">📷</text>
                    <text class="placeholder-text-compact">请先扫码获取单号</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 动作图片 -->
        <view class="form-section">
          <view class="section-title">动作图片</view>
          
          <view class="form-item full-width">
            <text class="form-label">实物动作（可上传多张图片）</text>
            <view class="multi-image-upload">
              <view class="image-list">
                <view 
                  v-for="(pic, index) in formData.actionPics" 
                  :key="index" 
                  class="image-item">
                  <image 
                    :src="getImageUrl(pic)"
                    mode="aspectFill" 
                    class="multi-preview-image"
@click="previewImage(pic)"
                  />
                  <button class="remove-multi-image-btn" @click="removeMultiImage('actionPics', index)">×</button>
                </view>
                <button class="add-image-btn" @click="uploadMultiImage('actionPics')" v-if="formData.actionPics.length < 9">
                  <text class="upload-icon">+</text>
                  <text class="upload-text">添加图片</text>
                </button>
              </view>
            </view>
          </view>
          
          <view class="form-item full-width">
            <text class="form-label">改善动作（可上传多张图片）</text>
            <view class="multi-image-upload">
              <view class="image-list">
                <view 
                  v-for="(pic, index) in formData.improvePics" 
                  :key="index" 
                  class="image-item">
                  <image 
                    :src="getImageUrl(pic)"
                    mode="aspectFill" 
                    class="multi-preview-image"
@click="previewImage(pic)"
                  />
                  <button class="remove-multi-image-btn" @click="removeMultiImage('improvePics', index)">×</button>
                </view>
                <button class="add-image-btn" @click="uploadMultiImage('improvePics')" v-if="formData.improvePics.length < 9">
                  <text class="upload-icon">+</text>
                  <text class="upload-text">添加图片</text>
                </button>
              </view>
            </view>
          </view>
          
          <view class="form-item full-width">
            <text class="form-label">成品（可上传多张图片）</text>
            <view class="multi-image-upload">
              <view class="image-list">
                <view 
                  v-for="(pic, index) in formData.productPics" 
                  :key="index" 
                  class="image-item">
                  <image 
                    :src="getImageUrl(pic)"
                    mode="aspectFill" 
                    class="multi-preview-image"
@click="previewImage(pic)"
                  />
                  <button class="remove-multi-image-btn" @click="removeMultiImage('productPics', index)">×</button>
                </view>
                <button class="add-image-btn" @click="uploadMultiImage('productPics')" v-if="formData.productPics.length < 9">
                  <text class="upload-icon">+</text>
                  <text class="upload-text">添加图片</text>
                </button>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 文字信息 -->
        <view class="form-section">
          <view class="section-title">文字信息</view>
          
          <view class="form-item full-width">
            <text class="form-label">学员感想</text>
            <textarea 
              v-model="formData.studentThoughts" 
              placeholder="请输入学员感想"
              class="form-textarea"
              :maxlength="500"
            ></textarea>
            <view class="char-count">{{ formData.studentThoughts.length }}/500</view>
          </view>
          
          <view class="form-item full-width">
            <text class="form-label">教官评语</text>
            <textarea 
              v-model="formData.communicationIssues" 
              placeholder="请描述教官评语"
              class="form-textarea"
              :maxlength="500"
            ></textarea>
            <view class="char-count">{{ formData.communicationIssues.length }}/500</view>
          </view>
          
          <view class="form-item">
            <text class="form-label">动作学会</text>
            <view class="learned-options">
              <view 
                class="learned-option" 
                :class="{ 'learned-option-active': formData.learned === true }"
                @click="setLearned(true)"
              >
                <view class="learned-icon">✓</view>
                <text class="learned-text">是</text>
              </view>
              <view 
                class="learned-option" 
                :class="{ 'learned-option-active': formData.learned === false }"
                @click="setLearned(false)"
              >
                <view class="learned-icon">✗</view>
                <text class="learned-text">否</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 图片预览组件 -->
    <image-popup
      v-model:show="popupVisible"
      :imageUrl="previewImageUrl"
    />
  </view>
</template>

<script>
import ImagePopup from '@/components/ImagePopup.vue';
import urlPrefix from '@/pages/common/urlPrefix.js';

export default {
  components: {
    ImagePopup
  },
  data() {
    return {
      isEdit: false,
      recordId: null,
      formData: {
        recordDate: '',
        orderNo: '',
        student: '',
        ordQty: null,
        modelNo: '',
        shoePic: '',
        actionPics: [],
        improvePics: [],
        productPics: [],
        studentThoughts: '',
        communicationIssues: '',
        learned: false
      },
      previewImageUrl: '',
      popupVisible: false,
      orderNoChangeTimer: null,
      studentOptions: ['阮氏惠', '阮氏香', '阮秋容', '范氏李'],
      selectedStudentIndex: -1
    }
  },
  onLoad(options) {
    if (options.id) {
      this.isEdit = true;
      this.recordId = options.id;
      this.loadRecord();
    } else {
      // 新增时设置默认日期为今天
      const today = new Date();
      const year = today.getFullYear();
      const month = (today.getMonth() + 1).toString().padStart(2, '0');
      const day = today.getDate().toString().padStart(2, '0');
      this.formData.recordDate = `${year}-${month}-${day}`;
      
      // 如果从列表页面传递了学员参数，设置默认学员
      if (options.student) {
        const studentName = decodeURIComponent(options.student);
        this.formData.student = studentName;
        const studentIndex = this.studentOptions.findIndex(name => name === studentName);
        if (studentIndex >= 0) {
          this.selectedStudentIndex = studentIndex;
        }
      }
    }
  },
  watch: {
    // 监听工单号变化，自动获取型体图片
    'formData.orderNo'(newVal, oldVal) {
      // 防止初次加载时重复调用
      if (newVal && newVal.trim() && newVal !== oldVal) {
        // 防抖处理，避免用户快速输入时频繁调用
        clearTimeout(this.orderNoChangeTimer);
        this.orderNoChangeTimer = setTimeout(() => {
          this.getShoeImageByOrderNo(newVal);
        }, 1000); // 1秒后执行
      }
    }
  },
  methods: {
    async loadRecord() {
      try {
        uni.showLoading({
          title: '加载记录中',
          mask: true
        });
        
        const res = await uni.request({
          url: urlPrefix + `/student-record/${this.recordId}`,
          method: 'GET'
        });
        
        uni.hideLoading();
        
        if (res.data && res.data.code === 1 && res.data.data) {
          const record = res.data.data;
          this.formData = {
            recordDate: record.recordDate || '',
            orderNo: record.orderNo || '',
            student: record.student || '',
            ordQty: record.ordQty || null,
            modelNo: record.modelNo || '',
            shoePic: record.shoePic || '',
            actionPics: record.actionPics || [],
            improvePics: record.improvePics || [],
            productPics: record.productPics || [],
            studentThoughts: record.studentThoughts || '',
            communicationIssues: record.communicationIssues || '',
            learned: record.learned || false
          };
          
          // 设置学员选择器的索引
          if (this.formData.student) {
            const studentIndex = this.studentOptions.findIndex(name => name === this.formData.student);
            if (studentIndex >= 0) {
              this.selectedStudentIndex = studentIndex;
            }
          }
          
          // 如果有工单号，自动获取型体图片
          if (this.formData.orderNo && this.formData.orderNo.trim()) {
            // 延迟一下，确保界面已经渲染完毕
            setTimeout(() => {
              this.getShoeImageByOrderNo(this.formData.orderNo);
            }, 500);
          }
        } else {
          throw new Error(res.data?.msg || res.data?.message || '获取记录失败');
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '获取记录失败',
          icon: 'none'
        });
        console.error('获取记录失败:', error);
      }
    },
    
    onDateChange(e) {
      this.formData.recordDate = e.detail.value;
    },
    
    onLearnedChange(e) {
      this.formData.learned = e.detail.value === 'true';
    },
    
    scanOrderNo() {
      // TODO: 调用扫码功能
      uni.scanCode({
        success: (res) => {
          console.log('扫码结果:', res.result);
          this.formData.orderNo = res.result;
          // 根据单号获取鞋图
          this.getShoeImageByOrderNo(res.result);
        },
        fail: (error) => {
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          });
        }
      });
    },
    
    async getShoeImageByOrderNo(orderNo) {
      if (!orderNo || !orderNo.trim()) {
        uni.showToast({
          title: '请先输入单号',
          icon: 'none'
        });
        return;
      }
      
      try {
        
        const res = await uni.request({
          url: urlPrefix + '/student-record/shoe-image?orderNo=' + encodeURIComponent(orderNo),
          method: 'GET'
        });
        console.log('获取鞋图响应:', res);
        
        // 处理后端响应格式 - 现在返回的是StudentRecord对象
        if (res.data && res.data.code === 1 && res.data.data) {
          const studentRecord = res.data.data;
          
          // 更新订单数量和型体信息（只有在后端返回了数据时才更新）
          if (studentRecord.ordQty !== null && studentRecord.ordQty !== undefined) {
            this.formData.ordQty = studentRecord.ordQty;
          }
          if (studentRecord.modelNo && studentRecord.modelNo.trim()) {
            this.formData.modelNo = studentRecord.modelNo;
          }
          
          // 更新鞋图信息
          if (studentRecord.shoePic && studentRecord.shoePic.trim()) {
            // 后端返回的shoePic可能是base64数据或文件路径
            const shoePicData = studentRecord.shoePic;
            
            // 如果是base64数据但没有前缀，添加前缀
            if (!shoePicData.startsWith('data:') && !shoePicData.startsWith('http://') && !shoePicData.startsWith('https://')) {
              // 假设是base64数据，添加前缀
              this.formData.shoePic = 'data:image/jpeg;base64,' + shoePicData;
            } else {
              this.formData.shoePic = shoePicData;
            }
          }
          
          // 如果没有获取到任何有用信息，显示提示
          if (!studentRecord.shoePic && !studentRecord.ordQty && !studentRecord.modelNo) {
            uni.showToast({
              title: '未找到该单号的相关信息',
              icon: 'none'
            });
          }
        } else {
          // 显示后端返回的具体错误信息
          const errorMsg = res.data?.msg || res.data?.message || '未找到对应信息';
          console.log('获取订单信息失败:', errorMsg);
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('获取鞋图异常:', error);
        uni.showToast({
          title: '获取鞋图失败',
          icon: 'none'
        });
      }
    },
    
    uploadMultiImage(field) {
      uni.chooseImage({
        count: 9 - this.formData[field].length, // 最多9张
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          uni.showLoading({
            title: '图片上传中',
            mask: true
          });
          
          const uploadPromises = [];
          
          res.tempFilePaths.forEach((tempFilePath) => {
            const uploadPromise = new Promise((resolve, reject) => {
              uni.uploadFile({
                url: urlPrefix + '/api/files/upload',
                filePath: tempFilePath,
                name: 'file',
                success: (uploadFileRes) => {
                  try {
                    console.log('上传响应状态码:', uploadFileRes.statusCode);
                    console.log('上传响应原始数据:', uploadFileRes.data);
                    
                    if (uploadFileRes.statusCode !== 200) {
                      reject(new Error(`上传失败，状态码：${uploadFileRes.statusCode}`));
                      return;
                    }
                    
                    const responseData = JSON.parse(uploadFileRes.data);
                    console.log('解析后的响应数据:', responseData);
                    
                    // 根据后端 FileUploadController.java 的返回结构处理
                    if (responseData.code === 1 && responseData.data && responseData.data.url) {
                      const relativePath = responseData.data.url;
                      console.log('获取到的图片路径:', relativePath);
                      // 存储相对路径，显示时生成完整URL
                      this.formData[field].push(relativePath);
                      resolve();
                    } else {
                      const errorMsg = responseData.msg || responseData.message || '上传失败';
                      console.error('上传失败:', errorMsg, '完整响应:', responseData);
                      reject(new Error(errorMsg));
                    }
                  } catch (error) {
                    console.error('解析上传响应失败:', error);
                    console.error('原始响应数据:', uploadFileRes.data);
                    console.error('响应状态码:', uploadFileRes.statusCode);
                    reject(new Error('解析响应失败'));
                  }
                },
                fail: (error) => {
                  console.error('上传失败:', error);
                  reject(new Error(error.errMsg || '上传失败'));
                }
              });
            });
            uploadPromises.push(uploadPromise);
          });
          
          Promise.all(uploadPromises)
            .then(() => {
              uni.hideLoading();
              uni.showToast({
                title: '上传成功',
                icon: 'success'
              });
            })
            .catch((error) => {
              uni.hideLoading();
              console.error('批量上传失败:', error);
              uni.showToast({
                title: error.message || '上传失败',
                icon: 'none'
              });
            });
        },
        fail: (error) => {
          console.error('选择图片失败:', error);
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    },
    
    removeMultiImage(field, index) {
      this.formData[field].splice(index, 1);
    },
    
    getImageUrl(imagePath) {
      if (!imagePath) return '';
      // 如果已经是完整URL，直接返回
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        return imagePath;
      }
      // 如果是base64格式，直接返回
      if (imagePath.startsWith('data:')) {
        return imagePath;
      }
      // 否则生成完整的文件访问URL
      return `${urlPrefix}/api/files/get?url=${imagePath}`;
    },
    
    previewImage(imagePath) {
      if (!imagePath) return;
      this.previewImageUrl = this.getImageUrl(imagePath);
      this.popupVisible = true;
    },
    
    validateForm() {
      if (!this.formData.recordDate) {
        uni.showToast({
          title: '请选择记录日期',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.orderNo || !this.formData.orderNo.trim()) {
        uni.showToast({
          title: '请输入单号',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.student || !this.formData.student.trim()) {
        uni.showToast({
          title: '请选择学员',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    async saveRecord() {
      if (!this.validateForm()) {
        return;
      }
      
      try {
        uni.showLoading({
          title: this.isEdit ? '更新中' : '保存中',
          mask: true
        });
        
        const data = {
          recordDate: this.formData.recordDate, // 使用后端期望的字段名
          orderNo: this.formData.orderNo,
          student: this.formData.student,
          actionPics: this.formData.actionPics,
          improvePics: this.formData.improvePics,
          productPics: this.formData.productPics,
          studentThoughts: this.formData.studentThoughts,
          communicationIssues: this.formData.communicationIssues,
          learned: this.formData.learned
        };
        
        console.log('发送数据:', data);
        
        let res;
        if (this.isEdit) {
          res = await uni.request({
            url: urlPrefix + `/student-record/${this.recordId}`,
            method: 'PUT',
            data: data,
            header: {
              'Content-Type': 'application/json'
            }
          });
        } else {
          res = await uni.request({
            url: urlPrefix + '/student-record',
            method: 'POST',
            data: data,
            header: {
              'Content-Type': 'application/json'
            }
          });
        }
        
        uni.hideLoading();
        
        console.log('后端响应:', res.data);
        
        // 处理后端响应格式
        if (res.data && res.data.code === 1) {
          uni.showToast({
            title: this.isEdit ? '更新成功' : '保存成功',
            icon: 'success'
          });
          
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          // 显示后端返回的具体错误信息
          const errorMsg = res.data?.msg || res.data?.message || '保存失败';
          console.error('保存失败:', errorMsg, '完整响应:', res.data);
          throw new Error(errorMsg);
        }
      } catch (error) {
        uni.hideLoading();
        console.error('保存异常:', error);
        const errorMessage = error.message || (this.isEdit ? '更新失败' : '保存失败');
        uni.showToast({
          title: errorMessage,
          icon: 'none'
        });
      }
    },
    
    goBack() {
      uni.showModal({
        title: '确认返回',
        content: '返回将丢失未保存的数据，确定要返回吗？',
        success: (res) => {
          if (res.confirm) {
            uni.navigateBack();
          }
        }
      });
    },
    
    setLearned(value) {
      this.formData.learned = value;
    },
    
    onStudentChange(e) {
      this.selectedStudentIndex = e.detail.value;
      if (this.selectedStudentIndex >= 0) {
        this.formData.student = this.studentOptions[this.selectedStudentIndex];
      } else {
        this.formData.student = '';
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20px;
  height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f9fafb;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
  padding: 10px 5px;
  background: linear-gradient(135deg, #fdf6e3, #eee8d5);
  color: #657b83;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-left {
  width: 10%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 2%;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-right {
  width: 10%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: 2%;
}

.back-btn, .save-btn {
  display: flex;
  align-items: center;
  border: none;
  color: #657b83;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.back-btn {
  background-color: rgba(101, 123, 131, 0.2);
}

.back-btn:hover {
  background-color: rgba(101, 123, 131, 0.3);
}

.save-btn {
  background-color: rgba(147, 161, 161, 0.3);
}

.save-btn:hover {
  background-color: rgba(147, 161, 161, 0.4);
}

.back-icon, .save-icon {
  font-size: 18px;
  margin-right: 5px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 1px;
  text-align: center;
}

.content {
  flex: 1;
  overflow: hidden;
}

.form-container {
  height: 100%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  padding-bottom: 60px;
}

.form-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #657b83;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #eee8d5;
}

.form-layout {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.form-left {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-right {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  min-width: 180px;
  max-width: 200px;
}

.form-right .form-item {
  margin-bottom: 0;
}

.form-row {
  display: flex;
  gap: 20px;
  align-items: flex-end;
}

.form-item {
  flex: 1;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.form-item > .form-label {
  margin-bottom: 8px;
  height: 20px;
  display: flex;
  align-items: center;
}

.form-item > .picker-input,
.form-item > .input-with-scan {
  height: 40px;
}

.form-item.full-width {
  width: 100%;
}

.form-item.shoe-pic-item {
  flex: 0 0 auto;
  width: auto;
  min-width: 160px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #657b83;
  margin-bottom: 8px;
}

.form-label.required::after {
  content: "*";
  color: #dc3545;
  margin-left: 4px;
}

.form-input {
  width: 100%;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0 12px;
  font-size: 14px;
  background-color: #f5f7fa;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #93a1a1;
  box-shadow: 0 0 0 3px rgba(147, 161, 161, 0.1);
  outline: none;
}

.form-textarea {
  width: 100%;
  min-height: 80px;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 12px;
  font-size: 14px;
  background-color: #f5f7fa;
  box-sizing: border-box;
  resize: vertical;
}

.form-textarea:focus {
  border-color: #93a1a1;
  box-shadow: 0 0 0 3px rgba(147, 161, 161, 0.1);
  outline: none;
}

.picker-input {
  width: 100%;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0 12px;
  font-size: 14px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  cursor: pointer;
  box-sizing: border-box;
}

.input-with-scan {
  display: flex;
  gap: 10px;
  align-items: stretch;
  width: 100%;
  height: 40px;
}

.input-with-scan .form-input {
  flex: 1;
  min-width: 0;
}

.scan-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  background-color: #93a1a1;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s;
  flex-shrink: 0;
  min-width: 80px;
  height: 40px;
}

.scan-btn:hover {
  background-color: #7a8a8a;
}

.scan-icon {
  font-size: 16px;
}

.image-upload-area {
  width: 100%;
  min-height: 120px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: #fafafa;
}

.image-display-area {
  width: 100%;
  min-height: 120px;
  border: 1px solid #e0e7ff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: #f8f9fa;
}

.image-display-area-compact {
  width: 120px;
  height: 120px;
  border: 1px solid #e0e7ff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: #f8f9fa;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview-compact {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: pointer;
  border-radius: 4px;
}

.preview-image-compact {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: pointer;
  border-radius: 4px;
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #8c9db5;
  cursor: pointer;
  padding: 20px;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 10px;
  text-align: center;
}

.no-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #8c9db5;
  text-align: center;
  padding: 20px;
}

.no-image-placeholder-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  color: #8c9db5;
  text-align: center;
  padding: 4px;
}

.placeholder-icon {
  font-size: 32px;
  opacity: 0.5;
}

.placeholder-icon-compact {
  font-size: 16px;
  opacity: 0.5;
}

.placeholder-text {
  font-size: 14px;
  font-weight: 500;
}

.placeholder-text-compact {
  font-size: 12px;
  font-weight: 500;
}

.placeholder-sub {
  font-size: 12px;
  opacity: 0.7;
}

.remove-image-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background-color: rgba(220, 53, 69, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.multi-image-upload {
  width: 100%;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
}

.image-item {
  position: relative;
  width: 80px;
  height: 80px;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
  margin: 0;
}

.multi-preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  cursor: pointer;
}

.remove-multi-image-btn {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 18px;
  height: 18px;
  background-color: rgba(220, 53, 69, 0.9);
  color: white;
  border: none;
  border-radius: 9px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.add-image-btn {
  width: 80px;
  height: 80px;
  border: 2px dashed #ddd;
  border-radius: 6px;
  background: none;
  color: #8c9db5;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  flex-shrink: 0;
  margin: 0;
  padding: 0;
}

.add-image-btn:hover {
  border-color: #93a1a1;
  color: #93a1a1;
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #8c9db5;
  margin-top: 4px;
}

.learned-options {
  display: flex;
  gap: 15px;
  margin-top: 5px;
}

.learned-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  padding: 15px 20px;
  border: 2px solid #e0e7ff;
  border-radius: 12px;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  min-width: 80px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.learned-option:hover {
  border-color: #93a1a1;
  background-color: #f0f3f7;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.learned-option:focus {
  outline: none;
}

.learned-option:active {
  transform: translateY(0);
}

.learned-option-active {
  background-color: #93a1a1;
  border-color: #93a1a1;
  color: white;
  box-shadow: 0 4px 12px rgba(147, 161, 161, 0.3);
}

.learned-option-active .learned-icon,
.learned-option-active .learned-text {
  color: white;
}

.learned-option-active:hover {
  background-color: #93a1a1;
  border-color: #93a1a1;
  color: white;
}

.learned-option-active:focus {
  outline: none;
  background-color: #93a1a1;
  border-color: #93a1a1;
  color: white;
}

.learned-option-active:active {
  background-color: #93a1a1;
  border-color: #93a1a1;
  color: white;
  transform: translateY(0);
}

.learned-icon {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.learned-text {
  font-size: 14px;
  font-weight: 600;
  color: #657b83;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .form-layout {
    flex-direction: column;
    gap: 15px;
  }
  
  .form-right {
    max-width: none;
    justify-content: flex-start;
  }
  
  .form-right .form-item {
    width: 100%;
  }
  
  .input-with-scan {
    flex-direction: row;
    align-items: stretch;
    gap: 8px;
  }
  
  .scan-btn {
    min-width: 70px;
    padding: 8px 12px;
  }
  
  .image-list {
    justify-content: center;
  }
  
  .form-container {
    padding: 15px;
    padding-bottom: 80px;
  }
}

@media screen and (orientation: landscape) {
  .container {
    padding: 15px;
    box-sizing: border-box;
    width: 100%;
  }
  
  .header {
    margin-bottom: 15px;
  }
  
  .form-container {
    padding: 15px;
    box-sizing: border-box;
    width: 100%;
  }
  
  .form-section {
    margin-bottom: 25px;
  }
  
  .form-layout {
    align-items: flex-end;
  }
  
  .input-with-scan {
    align-items: stretch;
    height: 40px;
  }
}

.readonly-field {
  width: 100%;
  height: 40px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 0 12px;
  font-size: 14px;
  background-color: #f9f9f9;
  color: #666;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  cursor: default;
}
</style>