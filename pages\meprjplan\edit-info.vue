<script setup>
import {ref, watch, reactive, defineProps, computed, watchEffect} from 'vue'
import {onLoad, onShow} from '@dcloudio/uni-app'
import urlPrefix from '@/pages/common/urlPrefix.js'
import {pathToBase64,base64ToPath} from '@/pages/common/image-tools.js'
import UniEasyinput from "../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue";
import UvImage from "../../uni_modules/uv-image/components/uv-image/uv-image.vue";
import {useI18n} from 'vue-i18n'
import {useTipStore, useFactoryStore} from '@/stores/common'

const {t} = useI18n()

// 同步获取系统信息
const systemInfo = uni.getSystemInfoSync().platform;

const user = uni.getStorageSync("loUserNo")
const factory = uni.getStorageSync('currentFactory');
const info = ref()
const ToolPopup = ref()
const ActionsPopup = ref()
const preViewPicture = ref();
const picturePopup = ref();

const inputCountNum = ref(1)
const inputCount = ref([{id: uniqueId(), text: ''}])
const items = ref([])

const deptPart = ref([[t('底部SOP'), t('鞋面SOP')], [t('成型'), t('中底'),t('中底皮'),t('大底'),t('单底'),t('包粘')]])

const depts = ref([
    [t('成型'), t('中底'),t('中底皮'),t('大底'),t('单底'),t('包粘')],
    [t('鞋面')]
])

//型体图片
const modelPicture = ref('')

//所有上传的图片临时存放路径
const uploadImgs = ref([])
//图片分类
const imageListMain1 = ref([])
const imageListMain2 = ref([])
const videoListMain = ref([])

//消息提示
const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')

const pccSopConts = ref([])
const addPartDetail = reactive({
    id:null,
	new_item: "",
    brand: "",
    model_no: "",
    shoe_last: "",
    factory:"",
    dept: "",
    deptType: 1,
	audit_flag: 0,
	shoe_make_head: '', //制鞋排头
	printmaker: '', //版师
	senior_technician: '', //高级技师
    pccMeProjectPlanDts: {
        item_no: 1,
        op_std: "", //操作标准
        self_check_points: "",//自检点
        op_std_en: "", //操作标准
        self_check_points_en: "",//自检点
        op_std_vn: "", //操作标准
        self_check_points_vn: "",//自检点
        op_std_id: "", //操作标准
        self_check_points_id: "",//自检点
        op_std_bd: "", //操作标准
        self_check_points_bd: "",//自检点
        op_std_ph: "", //操作标准
        self_check_points_ph: "",//自检点
        tools: "",//工具
        imgUrls: [],
        videoUrls: [],
        actions: "",//动作
        machine: "",//机器
        chemical_substance: "",//化学品
        temp: "",//温度
        speed: "",//电车转速
        time: "",//时间
        pressure: "",//压力
        glue: "",//胶水
        car_line: "",//车线
        margin: "",//边距
        needle_spacing: "",//针距
        spacing: "",//间距
        needle: "",//车针
		protective_gear: "",//防护用品
        img_tit1: "",//图片备注
        img_tit2: "",//图片备注
        create_by: user,
        update_by: user
    },
    pccSopConts:[]
})

// 返回上一页
function back() {
    info.value = ''
    let back = getCurrentPages();
    if(back && back.length > 1) {
    	uni.navigateBack({  
    	  delta: 1  
    	});  
    } else {  
    	history.back();  
    }
}

//选中部门
function changeDept(e) {
    const {columnIndex, index} = e
    if (columnIndex === 0) {
        picker.value.setColumnValues(1, depts.value[index])
    }
}

//选中部门
function changeDeptType() {
    if (typeof addPartDetail.dept === 'undefined') {

    } else {
        if (addPartDetail.dept.indexOf(t('底部SOP')) > -1) {
            addPartDetail.deptType = 1;
        } else if (addPartDetail.dept.indexOf(t('鞋面SOP')) > -1) {
            addPartDetail.deptType = 2;
        }
    }
    getActionsData('');
}

// 获取鞋图
function getPicture() {
    uni.request({
        url: urlPrefix + "/process/getPicture",
        method: "POST",
        data: {
            model_no: addPartDetail.model_no
        }
    }).then(res => {
        modelPicture.value = res.data.data.model_pic ? res.data.data.model_pic : ""
    }).catch(err => {
        uni.showToast({
            icon: "error",
            title: "请检查网络！"
        })
    })
}

const isSubmitting = ref(false)
const lang = ref([]);


//改用微软实现
function translation(type, data) {
    if (isSubmitting.value) {
        showTip('warn', '翻译正在进行，请稍候...');
    } else {
        if (data.trim() === '' || lang.value.length == 0) {
            showTip('warn', '请输入你需要翻译的内容并选择目标语言！');
        } else {
            isSubmitting.value = true;
            var text = {'sourceLang':'zh-Hans','textToTranslate': data, "lang": lang.value};
            uni.request({
                url: urlPrefix + "/translate",
                data: text,
                method: "POST"
            }).then(res => {
                for (var i = 0; i < res.data.data[0].translations.length; i++) {
                    var tempLang = res.data.data[0].translations[i].to;
                    var tempData = res.data.data[0].translations[i].text;
                    if (type == 1) {
                        addPartDetail.pccMeProjectPlanDts.actions = addPartDetail.pccMeProjectPlanDts.actions + "\n" + tempData;
                    }
                    if (type == 2) {
                        if ('en' == tempLang) {
                            addPartDetail.pccMeProjectPlanDts.op_std_en = tempData;
                        }
                        if ('vi' == tempLang) {
                            addPartDetail.pccMeProjectPlanDts.op_std_vn = tempData;
                        }
                        if ('id' == tempLang) {
                            addPartDetail.pccMeProjectPlanDts.op_std_id = tempData;
                        }
                        if ('bn' == tempLang) {
                            addPartDetail.pccMeProjectPlanDts.op_std_bd = tempData;
                        }
                        if ('fil' == tempLang) {
                            addPartDetail.pccMeProjectPlanDts.op_std_ph = tempData;
                        }
                    }
                    if (type == 3) {
                        if ('en' == tempLang) {
                            addPartDetail.pccMeProjectPlanDts.self_check_points_en = tempData;
                        }
                        if ('vi' == tempLang) {
                            addPartDetail.pccMeProjectPlanDts.self_check_points_vn = tempData;
                        }
                        if ('id' == tempLang) {
                            addPartDetail.pccMeProjectPlanDts.self_check_points_id = tempData;
                        }
                        if ('bn' == tempLang) {
                            addPartDetail.pccMeProjectPlanDts.self_check_points_bd = tempData;
                        }
                        if ('fil' == tempLang) {
                            addPartDetail.pccMeProjectPlanDts.self_check_points_ph = tempData;
                        }
                    }
                }
                isSubmitting.value = false;
            }).catch(err => {
                console.log(err)
                uni.showToast({
                    title: '保存数据失败..',
                    icon: "error"
                });
            })
        }
    }
}

const shouldShowEnInput = ref(false);
const shouldShowVnInput = ref(false);
const shouldShowIdInput = ref(false);
const shouldShowBdInput = ref(false);
const shouldShowPhInput = ref(false);

function shouldShowInput() {
    // 确保 this.lang 是一个数组，并且检查是否包含 'en'
    if (Array.isArray(lang.value) && lang.value.includes('en')) {
        shouldShowEnInput.value = true;
    } else {
        shouldShowEnInput.value = false;
    }
    if (Array.isArray(lang.value) && lang.value.includes('vi')) {
        shouldShowVnInput.value = true;
    } else {
        shouldShowVnInput.value = false;
    }
    if (Array.isArray(lang.value) && lang.value.includes('id')) {
        shouldShowIdInput.value = true;
    } else {
        shouldShowIdInput.value = false;
    }
    if (Array.isArray(lang.value) && lang.value.includes('bn')) {
        shouldShowBdInput.value = true;
    } else {
        shouldShowBdInput.value = false;
    }
    if (Array.isArray(lang.value) && lang.value.includes('fil')) {
        shouldShowPhInput.value = true;
    } else {
        shouldShowPhInput.value = false;
    }
}

// 提示信息
function showTip(type, message) {
    tipType.value = type
    tipMessage.value = message
    tipPopup.value.open()
}

// 上传图片
function addSpecificationUpload1(e) {
	uni.showLoading({
	    title: '图片上传中',
	    mask: true // 设置遮罩层
	});
	
	console.log(e);
	const tempFilePaths = e.tempFilePaths;
	const uploadPromises = []; // 用于存储所有上传任务的 Promise
	
	// 处理每张选中的图片
	tempFilePaths.forEach(tempFilePath => {
	    const uploadPromise = new Promise((resolve, reject) => {
	        uni.uploadFile({
	            url: urlPrefix + "/api/files/upload", //附件上传的服务器接口地址，非真实的接口地址
	            filePath: tempFilePath, // 要上传文件资源的路径,不同的组件
	            name: 'file', // 文件对应的 key , 开发者在服务器端通过这个 key 可以获取到文件二进制内容,即附件信息的接收字段名称
	            success: (uploadFileRes) => { // 回调方法
	                var img = JSON.parse(uploadFileRes.data).data.url;
	                uploadImgs.value.push({id: tempFilePath, value: img, type: 1});
	                resolve(); // 上传成功，解析 Promise
	            },
	            fail: (error) => {
	                reject(error); // 上传失败，拒绝 Promise
	            }
	        });
	    });
	    uploadPromises.push(uploadPromise); // 将每个上传任务的 Promise 添加到数组中
	});
	
	// 使用 Promise.all 等待所有上传任务完成
	Promise.all(uploadPromises)
	    .then(() => {
	        console.log(uploadImgs.value);
	        uni.hideLoading(); // 所有上传任务完成后隐藏遮罩层
	    })
	    .catch((error) => {
			showTip("error",error);
	        console.error('上传失败', error);
	        uni.hideLoading(); // 即使有任务失败，也要隐藏遮罩层
	    });
	
}



// 上传图片
function uploadVideo(e) {
	uni.showLoading({
	    title: '视频上传中',
	    mask: true // 设置遮罩层
	});
	
	console.log(e);
	const tempFilePaths = e.tempFilePaths;
	const tempFiles = e.tempFiles;
	const uploadPromises = []; // 用于存储所有上传任务的 Promise
	
	// 处理每张选中的图片
	tempFiles.forEach(tempFile => {
	    const uploadPromise = new Promise((resolve, reject) => {
	        uni.uploadFile({
	            url: urlPrefix + "/api/files/uploadVideo", //附件上传的服务器接口地址，非真实的接口地址
	            filePath: tempFile.path, // 要上传文件资源的路径,不同的组件
	            name: 'file', // 文件对应的 key , 开发者在服务器端通过这个 key 可以获取到文件二进制内容,即附件信息的接收字段名称
	            success: (uploadFileRes) => { // 回调方法
					console.log(uploadFileRes)
					var link = JSON.parse(uploadFileRes.data).link;
					videoListMain.value.push({"url": link, "name": tempFile.name});
					console.log(videoListMain.value)
	                resolve(); // 上传成功，解析 Promise
	            },
	            fail: (error) => {
	                reject(error); // 上传失败，拒绝 Promise
	            }
	        });
	    });
		uploadPromises.push(uploadPromise); // 将 Promise 添加到数组中
	});
	
	// 使用 Promise.all 等待所有上传任务完成
	Promise.all(uploadPromises)
	    .then(() => {
	        uni.hideLoading(); // 所有上传任务完成后隐藏遮罩层
	    })
	    .catch((error) => {
			showTip("error",error);
	        console.error('上传失败', error);
	        uni.hideLoading(); // 即使有任务失败，也要隐藏遮罩层
	});
	
}

//删除图片
function deleteImg1(e) {
    var temp = [];
    for (let i = 0; i < uploadImgs.value.length; i++) {
        var tempFilePath = e.tempFilePath;
        if (tempFilePath !== uploadImgs.value[i].id) {
            temp.push(uploadImgs.value[i])
        }
    }
    uploadImgs.value = temp;
}

//删除图片
function deleteImg2(e) {
    var temp = [];
    for (let i = 0; i < uploadImgs.value.length; i++) {
        var tempFilePath = e.tempFilePath;
        if (tempFilePath !== uploadImgs.value[i].id) {
            temp.push(uploadImgs.value[i])
        }
    }
    uploadImgs.value = temp;
}

//删除视频
function deleteVideo(e) {
    var temp = [];
    for (let i = 0; i < videoListMain.value.length; i++) {
        var tempFilePath = e.tempFilePath;
        if (tempFilePath !== videoListMain.value[i].url) {
            temp.push(videoListMain.value[i])
        }
    }
    uploadImgs.value = temp;
}

//增加输入框
function addInput() {
    if (inputCount.value.length > 9) {
        showTip('error', '最多创建10条！')
    } else {
        inputCount.value.push({id: uniqueId(), text: ''});
    }
}

//移除输入框
function deleteInput(id) {
    if (inputCount.value.length > 1) {
        uni.showModal({
            title: '提示',
            content: '确定要删除这条记录吗？',
            success: function (res) {
                if (res.confirm) {
                    console.log('用户点击确定');
                    inputCount.value = inputCount.value.filter(input => input.id !== id);
                    // 这里可以添加其他逻辑，比如实际删除数据等
                } else if (res.cancel) {
                    console.log('用户点击取消');
                }
            }
        });
    } else {
        inputCount.value = [{id: uniqueId(), text: ''}]
    }
}

async function changeItem(e) {
  await getData(addPartDetail.model_no, e);
}

function uniqueId() {
    // 简单的 ID 生成器，每次调用递增
    return inputCountNum.value++;
}

async function getData(model_no, itemNo) {
	uploadImgs.value = [];
	imageListMain1.value = [];
	imageListMain2.value = [];
	selectActions.value = '';
	selectTool.value = '';
    await uni.request({
        url: urlPrefix + "/pccmeprjplandt/queryByItem",
        data: {
            "modelNo": model_no,
            "item": addPartDetail.pccMeProjectPlanDts.item_no,
            "dept": addPartDetail.dept,
             parentId:addPartDetail.id
        },
        method: "GET"
    }).then(res => {
		addPartDetail.new_item = "";
		addPartDetail.shoe_make_head = res.data.data.shoe_make_head;
		addPartDetail.printmaker = res.data.data.printmaker;
		addPartDetail.senior_technician = res.data.data.senior_technician;
        addPartDetail.pccMeProjectPlanDts = res.data.data;

        let a = new Array;
        let b = new Array;
        uploadImgs.value = [];
        for (let i = 0; i < res.data.data.imgUrls.length; i++) {
            if (res.data.data.imgUrls[i].type == 1) {
                a.push({"name": 'test.png', "extname": 'picture', "url": res.data.data.imgUrls[i].img_url})
            } else {
                b.push({"name": 'test.png', "extname": 'picture', "url": res.data.data.imgUrls[i].img_url})
            }
            uploadImgs.value.push({
                "id": res.data.data.imgUrls[i].img_url,
                "value": res.data.data.imgUrls[i].remark,
                "type": res.data.data.imgUrls[i].type
            });
        }
        imageListMain1.value = a;
        imageListMain2.value = b;
		videoListMain.value = res.data.data.videoUrls;
		
		uni.request({
		    url: urlPrefix + "/pccmeprjplandt/queryAllItem",
		    data: {
		        "modelNo": model_no,
		        "dept": addPartDetail.dept,
                parentId:addPartDetail.id
		    },
		    method: "GET"
		}).then(res => {
		    items.value = res.data.data;
		}).catch(err => {
		    console.log(err)
		    uni.showToast({
		        title: '获取数据失败..',
		        icon: "error"
		    });
		})
		changeDeptType();
		getActionsData('');
		getPicture();
    }).catch(err => {
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

const toolDataList = ref([])
const searToolInput = ref()
const selectTool = ref()
//搜索类型
const searchType = ref();
async function getToolData(content) {
	var dept = 0;
	if (typeof addPartDetail.dept === 'undefined') {
		dept = 0;
	} else {
	    if (addPartDetail.dept.indexOf('底部SOP') > -1) {
	        addPartDetail.deptType = 1;
	        dept = 1;
	    } else if (addPartDetail.dept.indexOf('鞋面SOP') > -1) {
	        addPartDetail.deptType = 2;
	        dept = 2;
	    }
	}
    await uni.request({
        url: urlPrefix + "/pccmeprjplandt/queryAllTools",
        data: {
            "content": content,
            "searchType": searchType.value,
			"dept": dept
        },
        method: "GET"
    }).then(res => {
        toolDataList.value = res.data.data;
    }).catch(err => {
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

const actionsDataList = ref([])
const searActionsInput = ref()
const selectActions = ref()
const tagList = ref([])
const selectActionTag = ref()

async function getActionsData(index) {
	var type = 0;
	if (typeof addPartDetail.dept === 'undefined') {
		type = 0;
	} else {
	    if (addPartDetail.dept.indexOf('底部SOP-成型') > -1) {
	        addPartDetail.deptType = 1;
	        type = 1;
	    } if (addPartDetail.dept.indexOf('底部SOP-中底') > -1) {
	        addPartDetail.deptType = 1;
	        type = 2;
	    } if (addPartDetail.dept.indexOf('底部SOP-中底皮') > -1) {
	        addPartDetail.deptType = 1;
	        type = 3;
	    } if (addPartDetail.dept.indexOf('底部SOP-大底') > -1) {
	        addPartDetail.deptType = 1;
	        type = 4;
	    } if (addPartDetail.dept.indexOf('底部SOP-单底') > -1) {
	        addPartDetail.deptType = 1;
	        type = 5;
	    } if (addPartDetail.dept.indexOf('底部SOP-包粘') > -1) {
	        addPartDetail.deptType = 1;
	        type = 6;
	    } else if (addPartDetail.dept.indexOf('鞋面SOP-鞋面') > -1) {
	        addPartDetail.deptType = 2;
	        type = 21;
	    }
	}
    await uni.request({
        url: urlPrefix + "/pccmeprjplandt/queryAllActions",
        data: {
            "index": index,
			"type": type,
			"tag": selectActionTag.value
        },
        method: "GET"
    }).then(res => {
        actionsDataList.value = res.data.data;
    }).catch(err => {
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

function searchInput(type) {
    searchType.value = type;
    searToolInput.value = '';
    selectTool.value = [];
    getToolData('');
    ToolPopup.value.open();
}

function searchActions(type) {
	queryTag();
    ActionsPopup.value.open();
}

//查询所有可用于过滤的标签
function queryTag(){
	var type = 0;
	if (addPartDetail.dept.indexOf('底部SOP-成型') > -1) {
	    type = 1;
	} if (addPartDetail.dept.indexOf('底部SOP-中底') > -1) {
	    type = 2;
	} if (addPartDetail.dept.indexOf('底部SOP-中底皮') > -1) {
	    type = 3;
	} if (addPartDetail.dept.indexOf('底部SOP-大底') > -1) {
	    type = 4;
	} if (addPartDetail.dept.indexOf('底部SOP-单底') > -1) {
	    type = 5;
	} if (addPartDetail.dept.indexOf('底部SOP-包粘') > -1) {
	    type = 6;
	} else if (addPartDetail.dept.indexOf('鞋面SOP-鞋面') > -1) {
	    type = 21;
	}
	uni.request({
	    url: urlPrefix + "/pccmeprjplanaction/queryTag",
		data: {
		    "type": type
		},
	    method: "GET"
	}).then(res => {
	    tagList.value = res.data.data;
	}).catch(err => {
	    console.log(err)
		isSubmitting.value = false;
	    uni.showToast({
	        title: '查詢數據失敗..',
	        icon: "error"
	    });
	})
}

function selectToolItem(e) {
    var toolName = "";
    for (var i = 0; i < e.detail.data.length; i++) {
        toolName = toolName + e.detail.data[i].text;
    }
    if(searchType.value == 3){
        addPartDetail.pccMeProjectPlanDts.protective_gear = toolName;
    }
    if(searchType.value == 4){
        addPartDetail.pccMeProjectPlanDts.tools = toolName;
    }
    if(searchType.value == 5){
        addPartDetail.pccMeProjectPlanDts.chemical_substance = toolName;
    }
    if(searchType.value == 6){
        addPartDetail.pccMeProjectPlanDts.machine = toolName;
    }
    if(searchType.value == 7){
        addPartDetail.pccMeProjectPlanDts.temp = toolName;
    }
    if(searchType.value == 8){
        addPartDetail.pccMeProjectPlanDts.pressure = toolName;
    }
    if(searchType.value == 9){
        addPartDetail.pccMeProjectPlanDts.time = toolName;
    }
}

function selectActionsItem(e) {
    var action = "";
    var ids = "";
    for (var i = 0; i < e.detail.data.length; i++) {
        let text = e.detail.data[i].text;
        let id = e.detail.data[i].value;
        action = action + text;
        ids = ids + "-" + id;
    }
    queryContByAction(ids);
    addPartDetail.pccMeProjectPlanDts.actions = action;
}

function selectActionTagItem(e) {
	getActionsData('');
}

function queryContByAction(ids){
    uni.request({
        url: urlPrefix + "/pccmeprjplanaction/queryContByAction",
        data: {
            "ids": ids
        },
        method: "GET"
    }).then(res => {
        addPartDetail.pccMeProjectPlanDts.op_std = res.data.data.op_std;
        addPartDetail.pccMeProjectPlanDts.op_std_en = res.data.data.op_std_en;
        addPartDetail.pccMeProjectPlanDts.op_std_vi = res.data.data.op_std_vi;
        addPartDetail.pccMeProjectPlanDts.op_std_id = res.data.data.op_std_id;
        addPartDetail.pccMeProjectPlanDts.op_std_bd = res.data.data.op_std_bd;
        addPartDetail.pccMeProjectPlanDts.op_std_ph = res.data.data.op_std_ph;

        addPartDetail.pccMeProjectPlanDts.self_check_points = res.data.data.self_check_points;
        addPartDetail.pccMeProjectPlanDts.self_check_points_en = res.data.data.self_check_points_en;
        addPartDetail.pccMeProjectPlanDts.self_check_points_vi = res.data.data.self_check_points_vi;
        addPartDetail.pccMeProjectPlanDts.self_check_points_id = res.data.data.self_check_points_id;
        addPartDetail.pccMeProjectPlanDts.self_check_points_bd = res.data.data.self_check_points_bd;
        addPartDetail.pccMeProjectPlanDts.self_check_points_ph = res.data.data.self_check_points_ph;
		
		//底部字段
		addPartDetail.pccMeProjectPlanDts.protective_gear = res.data.data.protective_gear;
		addPartDetail.pccMeProjectPlanDts.tools = res.data.data.tools;
		addPartDetail.pccMeProjectPlanDts.chemical_substance = res.data.data.chemical_substance;
		addPartDetail.pccMeProjectPlanDts.machine = res.data.data.machine;
		addPartDetail.pccMeProjectPlanDts.pressure = res.data.data.pressure;
		addPartDetail.pccMeProjectPlanDts.temp = res.data.data.temp;
		addPartDetail.pccMeProjectPlanDts.speed = res.data.data.speed;
		addPartDetail.pccMeProjectPlanDts.time = res.data.data.time;
		addPartDetail.pccMeProjectPlanDts.needle = res.data.data.needle;
    }).catch(err => {
        console.log(err)
        uni.showToast({
            title: '獲取數據失敗..',
            icon: "error"
        });
    })
}


async function addProcessInfo() {
    if (addPartDetail.deptType == 0) {
        showTip('error', '未選擇部門！')
    } else {
		uni.showLoading({
		    title: '保存中',
		    mask: true // 设置遮罩层
		});
        //处理图片
        addPartDetail.pccMeProjectPlanDts.imgUrls = [];
        for (let i = 0; i < uploadImgs.value.length; i++) {
            addPartDetail.pccMeProjectPlanDts.imgUrls.push({
                "img_url": uploadImgs.value[i].value,
                "type": uploadImgs.value[i].type
            });
        }
		//处理视频
		addPartDetail.pccMeProjectPlanDts.videoUrls = [];
		for (let i = 0; i < videoListMain.value.length; i++) {
		   addPartDetail.pccMeProjectPlanDts.videoUrls.push({
			   "url": videoListMain.value[i].url,
			   "name": videoListMain.value[i].name
		   });
		}
        //操作流程与自检点
        pccSopConts.value = [];
        pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.op_std,'lang':'zh-Hans','type':'1'});
        pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.op_std_en,'lang':'en','type':'1'});
        pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.op_std_vn,'lang':'vi','type':'1'});
        pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.op_std_id,'lang':'id','type':'1'});
        pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.op_std_bd,'lang':'bn','type':'1'});
        pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.op_std_ph,'lang':'fil','type':'1'});

        pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.self_check_points,'lang':'zh-Hans','type':'2'});
        pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.self_check_points_en,'lang':'en','type':'2'});
        pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.self_check_points_vn,'lang':'vi','type':'2'});
        pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.self_check_points_id,'lang':'id','type':'2'});
        pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.self_check_points_bd,'lang':'bn','type':'2'});
        pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.self_check_points_ph,'lang':'fil','type':'2'});
        addPartDetail.pccSopConts = pccSopConts.value;
        uni.request({
            url: urlPrefix + "/pccmeprjplanhd/create",
            data: addPartDetail,
            method: "POST"
        }).then(res => {
            toolDataList.value = res.data.data;
            if (res.statusCode != 200) {
                showTip('error', res.data.message);
				uni.hideLoading();
				return;
            } else {
                // back();
                //继续进行添加动作
                addInfo();
            }
			uni.hideLoading();
			showTip('success', '保存成功！');
        }).catch(err => {
			uni.hideLoading();
            console.log(err)
            uni.showToast({
                title: '保存數據失敗..',
                icon: "error"
            });
        })
    }
}


// 跳转至添加界面
function addInfo() {
	if(addPartDetail.dept == '选择'){
		showTip("warn","请先选择部门");
		return;
	}
    if (addPartDetail.model_no.length === 0) {
        showTip('warn', '请选择型体！')
        return
    }
    const temp_model = addPartDetail.model_no;
    const temp_item = addPartDetail.pccMeProjectPlanDts.item_no;

    getData(temp_model, 'add', temp_item);
    showTip('success', '保存成功！')
    clearData();
}

async function editProcessInfo() {
	uni.showLoading({
		title: '保存中',
		mask: true // 设置遮罩层
	});
    //处理图片
    addPartDetail.pccMeProjectPlanDts.imgUrls = [];
    for (let i = 0; i < uploadImgs.value.length; i++) {
        addPartDetail.pccMeProjectPlanDts.imgUrls.push({
            "img_url": uploadImgs.value[i].value,
            "type": uploadImgs.value[i].type
        });
    }
	
	//处理视频
	addPartDetail.pccMeProjectPlanDts.videoUrls = [];
	for (let i = 0; i < videoListMain.value.length; i++) {
	    addPartDetail.pccMeProjectPlanDts.videoUrls.push({
	        "url": videoListMain.value[i].url,
	        "name": videoListMain.value[i].name
	    });
	}
    //操作流程与自检点
    pccSopConts.value = [];
    pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.op_std,'lang':'zh-Hans','type':'1'});
    pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.op_std_en,'lang':'en','type':'1'});
    pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.op_std_vn,'lang':'vi','type':'1'});
    pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.op_std_id,'lang':'id','type':'1'});
    pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.op_std_bd,'lang':'bn','type':'1'});
    pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.op_std_ph,'lang':'fil','type':'1'});

    pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.self_check_points,'lang':'zh-Hans','type':'2'});
    pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.self_check_points_en,'lang':'en','type':'2'});
    pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.self_check_points_vn,'lang':'vi','type':'2'});
    pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.self_check_points_id,'lang':'id','type':'2'});
    pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.self_check_points_bd,'lang':'bn','type':'2'});
    pccSopConts.value.push({'content':addPartDetail.pccMeProjectPlanDts.self_check_points_ph,'lang':'fil','type':'2'});
    addPartDetail.pccSopConts = pccSopConts.value;
    await uni.request({
        url: urlPrefix + "/pccmeprjplanhd/update",
        data: addPartDetail,
        method: "POST"
    }).then(res => {
        if (res.statusCode != 200) {
			uni.hideLoading();
			showTip('error', res.data.message);
			return;
        } else {
            edit();
        }
		uni.hideLoading();
		showTip('success', '保存成功！');
    }).catch(err => {
		uni.hideLoading();
        console.log(err)
        uni.showToast({
            title: '保存数据失败..',
            icon: "error"
        });
    })
}


function deleteProcessInfo() {
	uni.showLoading({
		title: '删除中',
		mask: true // 设置遮罩层
	});
    uni.request({
        url: urlPrefix + "/pccmeprjplandt/deleteItem",
        data: addPartDetail,
        method: "POST"
    }).then(res => {
        if (res.statusCode != 200) {
            showTip('error', res.data.message);
        } else {
			if(addPartDetail.pccMeProjectPlanDts.item_no == items.value[0].value){
				if(addPartDetail.pccMeProjectPlanDts.item_no == items.value[items.value.length-1].value){
					back();
				}else{
					next();
				}
			}else{
				last();
			}
			showTip('success', '删除成功！');
        }
		uni.hideLoading();
    }).catch(err => {
		uni.hideLoading();
        console.log(err)
        uni.showToast({
            title: '保存数据失败..',
            icon: "error"
        });
    })
}


// 上一项次
function last() {
    // 找到当前元素的索引
    const currentIndex = items.value.findIndex(item => item.value === addPartDetail.pccMeProjectPlanDts.item_no);
    // 如果当前索引大于0，则递减索引并更新当前元素的值
    if (currentIndex > 0) {
        addPartDetail.pccMeProjectPlanDts.item_no = items.value[currentIndex - 1].value;
        changeItem();
    } else if (currentIndex == 0) {
        addPartDetail.pccMeProjectPlanDts.item_no = items.value[currentIndex].value;
        changeItem();
    }
}

// 下一项次
function next() {
    // 找到当前元素的索引
    const currentIndex = items.value.findIndex(item => item.value === addPartDetail.pccMeProjectPlanDts.item_no);
    // 如果当前索引大于0，则递减索引并更新当前元素的值
    if (currentIndex > -1 && addPartDetail.pccMeProjectPlanDts.item_no < items.value[items.value.length - 1].value) {
        addPartDetail.pccMeProjectPlanDts.item_no = items.value[currentIndex + 1].value;
        changeItem();
    }
}


// 跳转至添加界面
function edit() {
    //判断当前项次是否已经是最后一个，是就直接返回列表，不是就修改下一个
    if (addPartDetail.pccMeProjectPlanDts.item_no == items.value[items.value.length - 1].text) {
		uni.request({
		    url: urlPrefix + "/pccmeprjplandt/queryAllItem",
		    data: {
		        "modelNo": addPartDetail.model_no,
		        "dept": addPartDetail.dept,
                "parentId":addPartDetail.id
		    },
		    method: "GET"
		}).then(res => {
		    items.value = res.data.data;
		}).catch(err => {
		    console.log(err)
		    uni.showToast({
		        title: '获取数据失败..',
		        icon: "error"
		    });
		})
        let info = {
            model_no: addPartDetail.model_no,
            brand: addPartDetail.brand,
            shoeLast: addPartDetail.shoe_last,
            dept: addPartDetail.dept,
            item_no: Number(addPartDetail.pccMeProjectPlanDts.item_no),
            type: 'add'
        }

        console.log(info);
        uni.navigateTo({
            url: `/pages/meprjplan/add-info?info=${JSON.stringify(info)}`,
            animationType: 'pop-in',
            animationDuration: 200
        })
    } else {
		const index = items.value.findIndex(item => item.text === addPartDetail.pccMeProjectPlanDts.item_no);
        addPartDetail.pccMeProjectPlanDts.item_no = items.value[Number(index) + 1].text;
        getData(addPartDetail.model_no, addPartDetail.pccMeProjectPlanDts.item_no, addPartDetail.dept);
    }
}

function clearData() {
    addPartDetail.pccMeProjectPlanDts = {
        item_no: Number(addPartDetail.pccMeProjectPlanDts.item_no) + 1,
        op_std: "", //操作标准
        self_check_points: "",//自检点
        op_std_en: "", //操作标准
        self_check_points_en: "",//自检点
        op_std_vn: "", //操作标准
        self_check_points_vn: "",//自检点
        op_std_id: "", //操作标准
        self_check_points_id: "",//自检点
        op_std_bd: "", //操作标准
        self_check_points_bd: "",//自检点
        op_std_ph: "", //操作标准
        self_check_points_ph: "",//自检点
        tools: "", //工具
        imgUrls: [],
        actions: "", //动作
        machine: "", //机器
        chemical_substance: "", //化学品
        temp: "", //温度
        time: "", //时间
        speed: "", //电车转速
        pressure: "", //压力
        glue: "", //胶水
        car_line: "", //车线
        margin: "", //边距
        needle_spacing: "", //针距
        spacing: "", //间距
        needle: "", //车针
		protective_gear: "",//防护用品
        img_tit1: "", //图片备注
        img_tit2: "", //图片备注
        create_by: user,
        update_by: user,
    };
	addPartDetail.new_item = "";

    uploadImgs.value = [];
    imageListMain1.value = [];
    imageListMain2.value = [];
	videoListMain.value = [];
    selectActions.value = '';
    selectTool.value = '';

}

// 跳转至动作界面
function actionsInfo() {
	uni.navigateTo({
		url: `/pages/meprjplan/actions`,
		animationType: 'pop-in',
		animationDuration: 200
	})
}

//登录校验
function loginCheck(){
	if(uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == ''){
		uni.navigateTo({
			url: `/pages/login/login`,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}
}

onLoad(async (props) => {
    info.value = JSON.parse(props.info);
    addPartDetail.id =  info.value.id
    addPartDetail.factory = info.value.factory
    addPartDetail.model_no = info.value.model_no;
    addPartDetail.shoe_last = info.value.shoeLast;
    addPartDetail.brand = info.value.brand;
    addPartDetail.pccMeProjectPlanDts.item_no = info.value.item_no;
    addPartDetail.dept = info.value.dept;
    info.value.ins_user = user;
    info.value.upd_user = user;
    await getData(info.value.model_no, info.value.item_no, info.value.dept);
})


onShow(() => {
	loginCheck();
	console.log('------------onShow---------------------')	
	uni.getStorage({
		key: 'customData',
		success: (res) => {
			let path = res.data.path;
			console.log(res.data.path)
			uni.removeStorage({
			    key: 'customData',
			    success: function (res) {
			      console.log('数据清除成功');
			    }
			  });
			  if(editImage1){
				 // editImage1.value = ''
				  uni.uploadFile({
				      url: urlPrefix + "/api/files/upload", 
				      filePath: path,
				      name: 'file',
				      success: (uploadFileRes) => {
						  var currentIndex = editImage1.value.index;
				          var img = JSON.parse(uploadFileRes.data).data.url;
						  console.log(img)
						  uploadImgs.value[currentIndex] = {id: path, value: img, type: 1};
				          // uploadImgs.value.push({id: path, value: img, type: 1});
				      }
				  });

			  }else{
				   editImage2.value = ''
				  uni.uploadFile({
				      url: urlPrefix + "/api/files/upload", 
				      filePath: path,
				      name: 'file',
				      success: (uploadFileRes) => {
						  var currentIndex = editImage1.value.index;
				          var img = JSON.parse(uploadFileRes.data).data.url;
				          // uploadImgs.value.push({id: path, value: img, type: 2});
						  uploadImgs.value[currentIndex] = {id: path, value: img, type: 2};
				      }
				  });
			
			  }
			  
			  
		}
  });
});
//图片预览
function picturePreView(img_url) {
    preViewPicture.value = img_url;
    picturePopup.value.open();
}

// 打开图片编辑器
const openEditor = () => {
  if (!selectedImage.value) {
    console.log('请先选择图片')
    return
  }
	uni.navigateTo({
	  url: '/pages/meprjplan/webview?data=' + selectedImage.value
	});
}

const editImage1 = ref(null) 
const editImage2 = ref(null) 
const prviewImage1 = (data) => {
	console.log(data)
	if(data.img.url.indexOf("http") == -1){
		data.img.url =  data.img.image.location;
	}
	//PC端直接预览，不进行编辑
	if(systemInfo == 'windows'){
		picturePreView(data.img.url);
		return;
	}
	editImage1.value = data
	// console.log(data)
	// console.log(data.img.url)
	// let url = 'http://10.1.11.119:8691/pcc/api/files/get/?url=/img/2024-07-02-16/4b2ed2e3-e953-4170-b243-a28e7526d289.png';
	uni.navigateTo({
	  url: '/pages/meprjplan/webview?data=' + data.img.url
	});
}
const prviewImage2 = (data) => {
	//PC端直接预览，不进行编辑
	if(data.img.url.indexOf("http") == -1){
		data.img.url =  data.img.image.location;
	}
	if(systemInfo == 'windows'){
		picturePreView(data.img.url);
		return;
	}
	editImage2.value = data
	// let url = 'http://10.1.11.119:8691/pcc/api/files/get/?url=/img/2024-07-02-16/4b2ed2e3-e953-4170-b243-a28e7526d289.png';
	uni.navigateTo({
	  url: '/pages/meprjplan/webview?data=' + data.img.url
	});
}

// 添加图片重新排序相关的 ref 变量
const imageOrderPopup = ref(null); // To control the uni-popup component
const imagesToReorderInPopup = ref([]); // Holds a deep copy of images for reordering in the popup

// Computed property to filter images of type 1 for reordering
const imagesOfType1 = computed(() => {
    return uploadImgs.value.filter(img => img.type === 1);
});

// Functions to move images up/down in the main uploadImgs array (NO LONGER USED directly by UI, but logic is useful)
function moveImageUp(imageObjectId) {
    const images = uploadImgs.value.filter(img => img.type === 1);
    const visualIndex = images.findIndex(img => img.id === imageObjectId);
    if (visualIndex <= 0) return;

    const itemToMove = images[visualIndex];
    const itemToSwapWith = images[visualIndex - 1];

    const actualIndexOfItemToMove = uploadImgs.value.findIndex(img => img.id === itemToMove.id);
    const actualIndexOfItemToSwapWith = uploadImgs.value.findIndex(img => img.id === itemToSwapWith.id);

    if (actualIndexOfItemToMove === -1 || actualIndexOfItemToSwapWith === -1) return;

    const temp = uploadImgs.value[actualIndexOfItemToMove];
    uploadImgs.value[actualIndexOfItemToMove] = uploadImgs.value[actualIndexOfItemToSwapWith];
    uploadImgs.value[actualIndexOfItemToSwapWith] = temp;
}

function moveImageDown(imageObjectId) {
    const images = uploadImgs.value.filter(img => img.type === 1);
    const visualIndex = images.findIndex(img => img.id === imageObjectId);
    if (visualIndex < 0 || visualIndex >= images.length - 1) return;

    const itemToMove = images[visualIndex];
    const itemToSwapWith = images[visualIndex + 1];

    const actualIndexOfItemToMove = uploadImgs.value.findIndex(img => img.id === itemToMove.id);
    const actualIndexOfItemToSwapWith = uploadImgs.value.findIndex(img => img.id === itemToSwapWith.id);

    if (actualIndexOfItemToMove === -1 || actualIndexOfItemToSwapWith === -1) return;

    const temp = uploadImgs.value[actualIndexOfItemToMove];
    uploadImgs.value[actualIndexOfItemToMove] = uploadImgs.value[actualIndexOfItemToSwapWith];
    uploadImgs.value[actualIndexOfItemToSwapWith] = temp;
}

// --- Popup Reordering Logic ---
function openImageReorderDialog() {
    // Deep copy only type 1 images to the popup's list
    // We need to ensure the objects are cloned properly to avoid direct mutation
    const type1Images = uploadImgs.value.filter(img => img.type === 1);
    imagesToReorderInPopup.value = JSON.parse(JSON.stringify(type1Images));
    if (imageOrderPopup.value) {
        imageOrderPopup.value.open();
    }
}

function moveImageInPopupUp(index) {
    if (index <= 0) return;
    const list = imagesToReorderInPopup.value;
    const itemToMove = list[index];
    list.splice(index, 1);
    list.splice(index - 1, 0, itemToMove);
}

function moveImageInPopupDown(index) {
    const list = imagesToReorderInPopup.value;
    if (index < 0 || index >= list.length - 1) return;
    const itemToMove = list[index];
    list.splice(index, 1);
    list.splice(index + 1, 0, itemToMove);
}

function confirmImageOrder() {
    // Create a map of the new order based on id
    const newOrderMap = new Map();
    imagesToReorderInPopup.value.forEach((img, index) => {
        newOrderMap.set(img.id, index);
    });

    // Get all type 1 images from the original uploadImgs
    const originalType1Images = uploadImgs.value.filter(img => img.type === 1);
    // Get all other images
    const otherImages = uploadImgs.value.filter(img => img.type !== 1);

    // Sort the originalType1Images based on the newOrderMap
    originalType1Images.sort((a, b) => {
        const orderA = newOrderMap.get(a.id) ?? Infinity;
        const orderB = newOrderMap.get(b.id) ?? Infinity;
        return orderA - orderB;
    });

    // Combine the newly ordered type 1 images with other images
    uploadImgs.value = [...originalType1Images, ...otherImages];
    
    if (imageOrderPopup.value) {
        imageOrderPopup.value.close();
    }
}

function cancelImageOrder() {
    if (imageOrderPopup.value) {
        imageOrderPopup.value.close();
    }
    // No need to do anything else, as changes were on a copy
}

// Function to generate the full URL for an image
function getFullImageUrl(imageValue) {
  // For debugging:
  console.log('[getFullImageUrl] urlPrefix:', urlPrefix);
  console.log('[getFullImageUrl] imageValue:', imageValue);
  if (!imageValue) {
    console.warn('[getFullImageUrl] imageValue is empty');
    return ''; // Return empty string or a placeholder for empty imageValue
  }
  // Ensure imageValue doesn't already contain a full http/https prefix from somewhere else
  if (imageValue.startsWith('http://') || imageValue.startsWith('https://')) {
    return imageValue; // It's already a full URL
  }
  const fullUrl = `${urlPrefix}/api/files/get/?url=${imageValue}`;
  console.log('[getFullImageUrl] fullUrl:', fullUrl);
  return fullUrl;
}

</script>

<template>
    <view class="add-info">
        <view class="back">
            <uni-icons @click="back()" type="back" size="36" color="#6fa2ce"></uni-icons>
        </view>

        <view class="submit">
			<uv-button @click="addProcessInfo()"  type="success" text="插入新項次"></uv-button>
        </view>
		<view class="submit2">
			<uv-button style="margin-left: 25px;" @click="editProcessInfo()"  type="primary" text="保存當前項次"></uv-button>
		</view>
		<view class="deleteBtn">
			<uv-button style="margin-left: 25px;" @click="deleteProcessInfo()"  type="error" text="删除當前項次"></uv-button>
		</view>
        <view class="title">
			<uni-icons @click="last()" type="arrow-left" style="margin-right: 35px;" size="36" color="#6fa2ce"></uni-icons>
            编辑 SOP
			<uni-icons @click="next()" type="arrow-right" size="36" style="margin-left: 35px;" color="#6fa2ce"></uni-icons>
        </view>

        <view class="picture">
            <view class="watermark">
                <img
                    :src="'data:image/jpg;base64,' + modelPicture"
                    alt=""
                    class="img"
                />
            </view>
        </view>

        <view class="data">
            <view class="top">

                <uni-section title="厂别" titleFontSize="20px" type="line" class="title-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.factory" :value="addPartDetail.factory"></uni-easyinput>
                </uni-section>

                <uni-section title="客戶" titleFontSize="20px" type="line" class="title-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.brand" :value="addPartDetail.brand"></uni-easyinput>
                </uni-section>

                <uni-section title="部門" titleFontSize="20px" type="line" class="title-part-attribute"
                             style="margin-left: 2%;">
                    <uni-easyinput disabled v-model="addPartDetail.dept" :value="addPartDetail.dept"></uni-easyinput>
                </uni-section>

                <uni-section title="型體編號" titleFontSize="20px" type="line" class="title-part-attribute"
                             style="margin-left: 2%;">
                    <uni-easyinput disabled :value="addPartDetail.model_no"></uni-easyinput>
                </uni-section>

                <uni-section title="楦頭編號" titleFontSize="20px" type="line" class="title-part-attribute">
                    <uni-easyinput disabled :value="addPartDetail.shoe_last"></uni-easyinput>
                </uni-section>

                <uni-section title="項次" titleFontSize="20px" type="line" class="title-part-attribute">
                    <uni-data-select v-model="addPartDetail.pccMeProjectPlanDts.item_no" :localdata="items"
                                     :clear="false"
                                     style="width: 80%;margin-left: 16px;background-color: white;"
                                     @change="changeItem"></uni-data-select>
                </uni-section>
				
				<!-- <uni-section title="修改項次" titleFontSize="20px" type="line" class="title-part-attribute">
				    <uni-easyinput type="number" v-model="addPartDetail.new_item"></uni-easyinput>
				</uni-section> -->
            </view>
            <view id="leftImgView">
                <uni-section title="動作" titleFontSize="20px" type="line" class="action"
                             style="flex: 1; position: relative;">
                    <view style="text-align: left;align-items: center;font-size: 18px; margin-bottom: 4px;">
                        <text>選擇動作</text>
                        <uni-icons type="search" size="30" @click="searchActions" color="#007aff"></uni-icons>
                    </view>
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.actions" type="textarea" maxlength="-1"
                                   :placeholder="`請輸入`" style="width: 100%;margin-left: 0"/>
                    <uni-icons custom-prefix="iconfont" type="icon-tubiao-fanyi" size="35" color="blue"
                               @click="translation(1,addPartDetail.pccMeProjectPlanDts.actions)"
                               style="position: absolute; right: 5px; bottom: 5px;"></uni-icons>

                </uni-section>
				
                <view class="imageview">
                    <uni-section title="圖片與視頻" titleFontSize="20px" type="line" class="add-specification-attribute">
                        <uni-easyinput style="margin-top: 0;margin-bottom: 15px" placeholder="圖片"
                                       v-model="addPartDetail.pccMeProjectPlanDts.img_tit1"></uni-easyinput>
                        <uni-file-picker v-model="imageListMain1" limit="9" @select="addSpecificationUpload1"
                                         @delete="deleteImg1"  @prviewImage="prviewImage1"></uni-file-picker>
										 
						<!-- Button to open the reorder popup -->
						<view style="margin-top: 10px;">
						    <button @click="openImageReorderDialog" v-if="imagesOfType1.length > 1" size="mini">调整图片顺序</button>
						</view>
										 
						<view style="margin-top:15px"></view>
										 
						 <uni-file-picker  file-mediatype="video" v-model="videoListMain" limit="9" @select="uploadVideo"
																  @delete="deleteVideo"></uni-file-picker>

                        <!-- <uni-easyinput style="margin-top: 0;margin-bottom: 15px" placeholder="成品照"
                                       v-model="addPartDetail.pccMeProjectPlanDts.img_tit2"></uni-easyinput>
                        <uni-file-picker v-model="imageListMain2" limit="9" @select="addSpecificationUpload2"
                                         @delete="deleteImg2" @prviewImage="prviewImage2"></uni-file-picker> -->
                    </uni-section>
                </view>
            </view>

            <view id="rightContent">
				<!-- <uni-section v-show="addPartDetail.deptType== 2" title="制鞋排頭" titleFontSize="20px" type="line"
							 class="add-part-attribute">
					<uni-easyinput v-model="addPartDetail.shoe_make_head"></uni-easyinput>
				</uni-section>
				<uni-section v-show="addPartDetail.deptType== 2" title="版師" titleFontSize="20px" type="line"
							 class="add-part-attribute">
					<uni-easyinput v-model="addPartDetail.printmaker"></uni-easyinput>
				</uni-section>
				<uni-section v-show="addPartDetail.deptType== 2" title="高級技師" titleFontSize="20px" type="line"
							 class="add-part-attribute">
					<uni-easyinput v-model="addPartDetail.senior_technician"></uni-easyinput>
				</uni-section> -->
                <uni-section title="工具" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.tools"></uni-easyinput>

                    <uni-icons type="search" size="30" @click="searchInput(4)"></uni-icons>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType == 1" title="化學品" titleFontSize="20px" type="line"
                             class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.chemical_substance"></uni-easyinput>
                    <uni-icons type="search" size="30" @click="searchInput(5)"></uni-icons>
                </uni-section>
                <uni-section title="機器" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.machine"></uni-easyinput>
                    <uni-icons type="search" size="30" @click="searchInput(6)"></uni-icons>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType == 1 || factory === 'FS'" title="溫度" titleFontSize="20px" type="line"
                             class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.temp"></uni-easyinput>
                    <uni-icons type="search" size="30" @click="searchInput(7)"></uni-icons>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType == 1 || factory === 'FS'" title="壓力" titleFontSize="20px" type="line"
                             class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.pressure"></uni-easyinput>
                    <uni-icons type="search" size="30" @click="searchInput(8)"></uni-icons>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType == 1 || factory === 'FS'" title="時間" titleFontSize="20px" type="line"
                             class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.time"></uni-easyinput>
                    <uni-icons type="search" size="30" @click="searchInput(9)"></uni-icons>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType == 1 || factory === 'FS'" title="防護用品" titleFontSize="20px" type="line"
                             class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.protective_gear"></uni-easyinput>
                    <uni-icons type="search" size="30" @click="searchInput(3)"></uni-icons>
                </uni-section>


				<uni-section  v-show="addPartDetail.deptType == 2 && factory === 'FS'" title="电车转速" titleFontSize="20px" type="line"
				             class="add-part-attribute">
				    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.speed"></uni-easyinput>
				</uni-section>
                <uni-section v-show="addPartDetail.deptType == 2" title="膠水" titleFontSize="20px" type="line"
                             class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.glue"></uni-easyinput>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType == 2" title="車綫" titleFontSize="20px" type="line"
                             class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.car_line"></uni-easyinput>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType == 2" title="邊距" titleFontSize="20px" type="line"
                             class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.margin"></uni-easyinput>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType == 2" title="針距" titleFontSize="20px" type="line"
                             class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.needle_spacing"></uni-easyinput>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType == 2" title="間距" titleFontSize="20px" type="line"
                             class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.spacing"></uni-easyinput>
                </uni-section>
                <uni-section title="車針" titleFontSize="20px" type="line"
                             class="add-part-attribute">
                    <uni-easyinput v-model="addPartDetail.pccMeProjectPlanDts.needle"></uni-easyinput>
                </uni-section>

                <view style="width: 100%;">
                    <text style="padding-left: 50px; height: 40px;">選擇你的目標翻譯語言：</text>
                    <view style="margin: 8px 0; padding-left: 50px; height: 40px;">
                        <uv-checkbox-group @change="shouldShowInput" v-model="lang" placement="row" shape="circle"
                                           labelSize="26" szie="40" iconSize="32">
                            <uv-checkbox disabled="true" checked name="zh-Hans" label="Chinese"></uv-checkbox>
                            <uv-checkbox name="en" label="English"></uv-checkbox>
                            <uv-checkbox name="vi" label="Vietnamese"></uv-checkbox>
                            <uv-checkbox name="id" label="Indonesian"></uv-checkbox>
                            <uv-checkbox name="bn" label="Bengali"></uv-checkbox>
                            <uv-checkbox name="fil" label="Filipino"></uv-checkbox>
                        </uv-checkbox-group>
                    </view>
                    <view style="display: flex; align-items: center; width: 100%; margin: 8px 0;">
                        <text style="width: 94%;margin-left: 4%;font-size: 24px;">操作標準</text>
                    </view>
                    <view style="display: flex; align-items: center; width: 100%; margin: 8px 0;">
                        <view style="flex: 1; position: relative;">
                            <uni-easyinput autoHeight type="textarea" v-model="addPartDetail.pccMeProjectPlanDts.op_std"
                                           maxlength="-1" :placeholder="`請輸入操作標準`"
                                           class="addInput"/>
                            <uni-icons custom-prefix="iconfont" type="icon-tubiao-fanyi" size="40" color="blue"
                                       @click="translation(2,addPartDetail.pccMeProjectPlanDts.op_std)"
                                       style="position: absolute; right: 20px; bottom: 25px;"></uni-icons>
                        </view>
                    </view>
                    <!-- 英文 -->
                    <view v-if="shouldShowEnInput" style="align-items: center; width: 100%; margin: 8px 0;">
                        <view style="flex: 1; position: relative;">
                            <uni-easyinput autoHeight type="textarea"
                                           v-model="addPartDetail.pccMeProjectPlanDts.op_std_en" maxlength="-1"
                                           :placeholder="`Please enter the operational standards`"
                                           class="addInput"/>
                        </view>
                    </view>
                    <!-- 越南语 -->
                    <view v-if="shouldShowVnInput" style="align-items: center; width: 100%; margin: 8px 0;">
                        <view style="flex: 1; position: relative;">
                            <uni-easyinput autoHeight type="textarea"
                                           v-model="addPartDetail.pccMeProjectPlanDts.op_std_vn" maxlength="-1"
                                           :placeholder="`Vietnamese`"
                                           class="addInput"/>
                        </view>
                    </view>
                    <!-- 印尼语 -->
                    <view v-if="shouldShowIdInput" style="align-items: center; width: 100%; margin: 8px 0;">
                        <view style="flex: 1; position: relative;">
                            <uni-easyinput autoHeight type="textarea"
                                           v-model="addPartDetail.pccMeProjectPlanDts.op_std_id" maxlength="-1"
                                           :placeholder="`Indonesian`"
                                           class="addInput"/>
                        </view>
                    </view>
                    <!-- 孟加拉语 -->
                    <view v-if="shouldShowBdInput" style="align-items: center; width: 100%; margin: 8px 0;">
                        <view style="flex: 1; position: relative;">
                            <uni-easyinput autoHeight type="textarea"
                                           v-model="addPartDetail.pccMeProjectPlanDts.op_std_bd" maxlength="-1"
                                           :placeholder="`Bengali`"
                                           class="addInput"/>
                        </view>
                    </view>
                    <!-- 菲律宾语 -->
                    <view v-if="shouldShowPhInput" style="align-items: center; width: 100%; margin: 8px 0;">
                        <view style="flex: 1; position: relative;">
                            <uni-easyinput autoHeight type="textarea"
                                           v-model="addPartDetail.pccMeProjectPlanDts.op_std_ph" maxlength="-1"
                                           :placeholder="`Filipino`"
                                           class="addInput"/>
                        </view>
                    </view>
                    <view style="display: flex; align-items: center; width: 100%; margin: 8px 0;">
                        <text style="width: 94%;margin-left: 4%;font-size: 24px;">自檢點</text>
                    </view>
                    <view style="display: flex; align-items: center; width: 100%; margin: 8px 0;">
                        <view style="flex: 1; position: relative;">
                            <uni-easyinput autoHeight type="textarea"
                                           v-model="addPartDetail.pccMeProjectPlanDts.self_check_points"
                                           maxlength="-1" :placeholder="`請輸入自檢點`" class="addInput"/>
                            <uni-icons custom-prefix="iconfont" type="icon-tubiao-fanyi" size="40" color="blue"
                                       @click="translation(3,addPartDetail.pccMeProjectPlanDts.self_check_points)"
                                       style="position: absolute; right: 20px; bottom: 25px;"></uni-icons>
                        </view>
                    </view>
                    <!-- 英文 -->
                    <view v-if="shouldShowEnInput" style="align-items: center; width: 100%; margin: 8px 0;">
                        <view style="flex: 1; position: relative;">
                            <uni-easyinput autoHeight type="textarea"
                                           v-model="addPartDetail.pccMeProjectPlanDts.self_check_points_en"
                                           maxlength="-1" :placeholder="`Please enter the self check points`"
                                           class="addInput"/>
                        </view>
                    </view>
                    <!-- 越南语 -->
                    <view v-if="shouldShowVnInput" style="align-items: center; width: 100%; margin: 8px 0;">
                        <view style="flex: 1; position: relative;">
                            <uni-easyinput autoHeight type="textarea"
                                           v-model="addPartDetail.pccMeProjectPlanDts.self_check_points_vn"
                                           maxlength="-1" :placeholder="`Vietnamese`"
                                           class="addInput"/>
                        </view>
                    </view>
                    <!-- 印尼语 -->
                    <view v-if="shouldShowIdInput" style="align-items: center; width: 100%; margin: 8px 0;">
                        <view style="flex: 1; position: relative;">
                            <uni-easyinput autoHeight type="textarea"
                                           v-model="addPartDetail.pccMeProjectPlanDts.self_check_points_id"
                                           maxlength="-1" :placeholder="`Indonesian`"
                                           class="addInput"/>
                        </view>
                    </view>
                    <!-- 孟加拉语 -->
                    <view v-if="shouldShowBdInput" style="align-items: center; width: 100%; margin: 8px 0;">
                        <view style="flex: 1; position: relative;">
                            <uni-easyinput autoHeight type="textarea"
                                           v-model="addPartDetail.pccMeProjectPlanDts.self_check_points_bd"
                                           maxlength="-1" :placeholder="`Bengali`"
                                           class="addInput"/>
                        </view>
                    </view>
                    <!-- 菲律宾语 -->
                    <view v-if="shouldShowPhInput" style="align-items: center; width: 100%; margin: 8px 0;">
                        <view style="flex: 1; position: relative;">
                            <uni-easyinput autoHeight type="textarea"
                                           v-model="addPartDetail.pccMeProjectPlanDts.self_check_points_ph"
                                           maxlength="-1" :placeholder="`Filipino`"
                                           class="addInput"/>
                        </view>
                    </view>
                </view>
            </view>

        </view>
    </view>

    <view class="toolPopupClass">
        <!-- 工具选择 -->
        <uni-popup ref="ToolPopup" type="center">
            <view id="userTable">
                <uni-easyinput :styles="{ color: '#2979FF', borderColor: '#2979FF' }" type="text" class="uni-input"
                               @input="getToolData" v-model="searToolInput" placeholder="輸入内容"/>
                <uni-section title="選中" subTitle="下滑瀏覽並選中" type="line">
                    <scroll-view scroll-y="true" class="scroll-Y selectUser">
                        <view class="uni-px-5 uni-pb-5">
                            <uni-data-checkbox multiple v-model="selectTool" @change="selectToolItem"
                                               :localdata="toolDataList"></uni-data-checkbox>
                        </view>
                    </scroll-view>
                </uni-section>
            </view>
        </uni-popup>
    </view>
    <view class="toolPopupClass">
        <!-- 动作选择 -->
        <uni-popup ref="ActionsPopup" type="center">
            <view id="userTable">
				<uv-button style="margin-bottom: 20px;width: 20%;" type="primary" text="新增动作" @click="actionsInfo()"></uv-button>
				<view>
					<uni-data-checkbox class="tags" multiple max="1" v-model="selectActionTag" @change="selectActionTagItem"
					                   :localdata="tagList"></uni-data-checkbox>
				</view>
                <uni-easyinput :styles="{ color: '#2979FF', borderColor: '#2979FF' }" type="text" class="uni-input"
                               @input="getActionsData" v-model="searActionsInput" placeholder="輸入内容"/>
                <uni-section title="選中" subTitle="下滑瀏覽並選中" type="line">
                    <scroll-view scroll-y="true" class="scroll-Y selectUser">
                        <view class="uni-px-5 uni-pb-5">
                            <uni-data-checkbox multiple max="1" v-model="selectActions" @change="selectActionsItem"
                                               :localdata="actionsDataList"></uni-data-checkbox>
                        </view>
                    </scroll-view>
                </uni-section>
            </view>
        </uni-popup>
    </view>

    <view class="tip-popup">
        <uni-popup ref="tipPopup" type="message">
            <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
        </uni-popup>
    </view>
	
	<view class="picture-popup">
	    <uni-popup
	        ref="picturePopup"
	        type="center">
	        <view class="watermark">
	            <img
	                :src="preViewPicture"
	                alt=""
	                class="img"
	            >
	        </view>
	    </uni-popup>
	</view>

    <!-- Reordering Popup -->
    <uni-popup ref="imageOrderPopup" type="dialog" background-color="#fff">
        <view class="reorder-popup-container">
            <view class="reorder-popup-header">
                <text class="reorder-popup-title">图片顺序调整</text>
            </view>
            <scroll-view scroll-y="true" class="reorder-popup-scroll-view">
                <view v-if="imagesToReorderInPopup.length > 0" class="reorder-popup-content">
                    <view v-for="(image, index) in imagesToReorderInPopup" :key="image.id" class="reorder-popup-item">
                        <img :src="getFullImageUrl(image.value)" class="reorder-popup-item-img" />
                        <view class="reorder-popup-item-controls">
                            <button @click="moveImageInPopupUp(index)" :disabled="index === 0" size="mini" class="reorder-popup-button">上移</button>
                            <button @click="moveImageInPopupDown(index)" :disabled="index === imagesToReorderInPopup.length - 1" size="mini" class="reorder-popup-button">下移</button>
                        </view>
                    </view>
                </view>
                <view v-else class="reorder-popup-empty-text">
                    <text>没有可排序的图片。</text>
                </view>
            </scroll-view>
            <view class="reorder-popup-footer">
                 <button @click="cancelImageOrder" class="reorder-popup-button cancel">取消</button>
                <button @click="confirmImageOrder" class="reorder-popup-button confirm">确定</button>
            </view>
        </view>
    </uni-popup>
</template>

<style lang="scss">
page {
    width: 100%;
    height: 100%;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;
}

.add-info {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0 0 1px 5px #ddd;
    position: relative;
    overflow: auto;

    .back {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 2.5%;
        top: 2.5%;
        border-radius: 50%;
        box-shadow: 0 0 5px gray;
        transition: all 0.05s ease-in-out;
        cursor: pointer;
        z-index: 1;

        &:active {
            transform: scale(0.97);
            box-shadow: 0 0 1px gray;
        }
    }

    .submit {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: 24.5%;
        top: 2.5%;
        transition: all 0.05s ease-in-out;
        cursor: pointer;
        z-index: 1;

        &:active {
            transform: scale(0.97);
            box-shadow: 0 0 1px gray;
        }
    }
	
	
	.submit2 {
	    width: 50px;
	    height: 50px;
	    display: flex;
	    justify-content: center;
	    align-items: center;
	    position: absolute;
	    right: 15.5%;
	    top: 2.5%;
	    transition: all 0.05s ease-in-out;
	    cursor: pointer;
	    z-index: 1;
	
	    &:active {
	        transform: scale(0.97);
	        box-shadow: 0 0 1px gray;
	    }
	}
	
	.deleteBtn {
	    width: 50px;
	    height: 50px;
	    display: flex;
	    justify-content: center;
	    align-items: center;
	    position: absolute;
	    right: 5.5%;
	    top: 2.5%;
	    transition: all 0.05s ease-in-out;
	    cursor: pointer;
	    z-index: 1;
	
	    &:active {
	        transform: scale(0.97);
	        box-shadow: 0 0 1px gray;
	    }
	}

    .title {
        margin-bottom: 1%;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        padding: 16px;
    }

    .data {
        width: 100%;
        position: relative;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;

        .title-part-attribute {
            width: 28%;
            margin-left: 2%;
            background-color: #fdf6e3;

            .uni-easyinput {
                width: 90%;
                margin-bottom: 5px;

                &:deep(.uni-easyinput__content-input) {
                    height: 40px;
                    font-size: 20px;
                    font-weight: bold;
                }
            }
        }

        .add-part-attribute {
            width: 28%;
            margin-left: 4%;
            margin-bottom: 1%;
            background-color: #fdf6e3;

            &:deep(.uni-section-content) {
                display: flex;
                align-items: center;
            }

            .uni-easyinput {
                width: 100%;
                margin-bottom: 5px;

                &:deep(.uni-easyinput__content-input) {
                    height: 40px;
                    font-size: 20px;
                    font-weight: bold;
                }
            }
        }

        .uni-stat__select {
            width: 80%;
            margin-left: 16px;

            &:deep(.uni-select) {
                height: 40px;
                font-size: 20px;
                font-weight: bold;
            }

            &:deep(.uni-select__input-placeholder) {
                font-size: 20px;
            }


        }

        .uni-data-checklist {

            &:deep(.checklist-box) {
                margin-top: 0px;
            }

            &:deep(.checklist-text) {
                padding: 8px;
                font-size: 20px;
                font-weight: bold;
            }
        }

        .uni-numbox {
            margin-left: 16px;
            height: 40px;

            &:deep(.uni-numbox-btns) {
                width: 40px;
                box-sizing: border-box;

                .uni-numbox--text {
                    font-weight: bold;
                }
            }

            &:deep(.uni-numbox__value) {
                width: 60px;
                height: 40px;
                font-size: 20px;
                font-weight: bold;
            }
        }

        .top {
            display: flex;
            width: 100%;
        }

        #leftImgView {
            width: 23%;
            height: 600px;
            margin-left: 2%;
            margin-top: 1%;
            background-color: #fdf6e3;

            .action {
                margin-bottom: 2%;
                width: 100%;
                background-color: #fdf6e3;

                &:deep(.uni-easyinput__content-textarea) {
                    height: 15vh;
                    font-size: 18px;
                    font-weight: bold;
                }
            }

            .imageview {
                &:deep(.uni-section-content) {
                    padding-left: 10px;
                    padding-bottom: 10px;
                    padding-right: 10px;
                }
            }
        }

        :deep(.uv-checkbox-label--left) {
            padding-right: 10px;
        }

        #rightContent {
            margin-top: 1%;
            width: 75%;
            display: flex;
            flex-wrap: wrap;
        }

        .addInput {
            font-size: 20px;
            width: 94%;
            margin-left: 4%;
            margin-bottom: 20px;
            background-color: white;
            border: 2px solid #000; /* 设置边框颜色和宽度 */
            border-radius: 10px; /* 设置边框圆角 */

            &:deep(.uni-easyinput__content) {
                border-radius: 10px; /* 设置边框圆角 */
            }

            &:deep(.uni-easyinput__content-textarea) {
                font-size: 20px;
                font-weight: bold;
            }

            &:deep(.uni-easyinput__placeholder-class) {
                font-size: 20px;
            }
        }

        .addInputParent {
            display: flex;
            align-items: center;

            .uni-icons {
                margin-left: 2%;
                margin-right: 2%;
            }
        }
    }
}

.tip-popup {
    &:deep(.fixforpc-width) {
        min-width: 0;
        margin-top: 40px;
        padding: 10px 20px;
    }

    &:deep(.uni-popup-message-text) {
        font-size: 18px;
        font-weight: bold;
    }
}

.list-enter-active, .list-leave-active {
    transition: all 0.25s ease-in-out;
}

.list-enter-from, .list-leave-to {
    opacity: 0;
}

.list-enter-to, .list-leave-from {
    opacity: 1;
}

.toolPopupClass {
    #userTable {
        width: 50vw;
        height: 60vh;
        overflow: auto;
        padding: 2.5% 2% 1.5% 2%;
        box-sizing: border-box;
        background-color: #fdf6e3;
    }

    &:deep(.selectUser .uni-data-checklist .checklist-group) {
        flex-direction: column;
    }
    
    .tags{
    	flex-direction: row;
    }
}

#userTable :deep(.uni-section) {
    background-color: #fdf6e3;
}

.picture {
    height: 9%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0px;
    left: calc(21% + 20px + 4px);
    cursor: pointer;

    .watermark {
        position: relative;

        &::before {
            content: "STELLA";
            position: absolute;
            top: 2px;
            left: 2px;
            color: white;
            font-size: 12px;
            font-weight: bold;
            text-shadow: 0 0 5px black;
            z-index: 1;
            pointer-events: none;
        }

        .img {
            max-width: 140px;
            max-height: 50px;
            border-radius: 10px;
            box-shadow: 0 0 5px gray;
            transition: all 0.05s ease-in-out;
            cursor: pointer;

            &:active {
                box-shadow: 0 0 1px gray;
                transform: scale(0.97);
            }
        }
    }
}

:deep(.uni-data-checklist .checklist-group .checklist-box .checkbox__inner) {
    width: 26px !important;
    height: 26px !important;
}

:deep(.uni-data-checklist .checklist-group .checklist-box .checklist-content .checklist-text) {
    font-size: 22px !important;
}

:deep(.uni-data-checklist .checkbox__inner-icon){
    height: 15px !important;
    width: 10px !important;
}

.picture-popup {
    .watermark {
        position: relative;
        transition: all 0.15s ease-in-out;

        &::before {
            content: "STELLA";
            position: absolute;
            top: 2px;
            left: 2px;
            color: black;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 0 0 5px white;
            z-index: 1;
            pointer-events: none;
        }

        &:active {
            transform: scale(1.5);
        }

        .img {
            min-width: 100px;
            min-height: 100px;
            max-width: 100vw;
            max-height: 50vw;
            border-radius: 10px;
            box-shadow: 0 0 10px white;

            &:active {
                box-shadow: 0 0 1px white;
            }
        }
    }
}

.picture {
    height: 9%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0px;
    left: calc(15% + 24px);
    cursor: pointer;

    .watermark {
        position: relative;

        &::before {
            content: "STELLA";
            position: absolute;
            top: 2px;
            left: 2px;
            color: white;
            font-size: 12px;
            font-weight: bold;
            text-shadow: 0 0 5px black;
            z-index: 1;
            pointer-events: none;
        }

        .img {
            max-width: 140px;
            max-height: 50px;
            border-radius: 10px;
            box-shadow: 0 0 5px gray;
            transition: all 0.05s ease-in-out;
            cursor: pointer;

            &:active {
                box-shadow: 0 0 1px gray;
                transform: scale(0.97);
            }
        }
    }
}

.reorder-popup-container {
    width: 80vw; /* Adjust width as needed */
    max-width: 500px;
    padding: 15px;
    background-color: #fff; /* Ensure popup background is white */
    border-radius: 8px;
}

.reorder-popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.reorder-popup-title {
    font-size: 18px;
    font-weight: bold;
}

.reorder-popup-scroll-view {
    max-height: 60vh; /* Limit height and enable scrolling */
    margin-bottom: 15px;
}

.reorder-popup-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.reorder-popup-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.reorder-popup-item-img {
    width: 80px; /* Adjust image size in popup */
    height: 80px;
    object-fit: cover;
    border-radius: 3px;
    margin-right: 10px;
}

.reorder-popup-item-controls {
    display: flex;
    flex-direction: column; /* Stack buttons vertically or use row for side-by-side */
    gap: 5px;
}

.reorder-popup-button {
    font-size: 12px; /* Ensure consistent button styling */
    padding: 5px 10px; /* Adjust padding */
    // margin: 0 2px; /* Adjust margin if needed */
}

.reorder-popup-button.confirm {
    background-color: #007aff;
    color: white;
}
.reorder-popup-button.cancel {
    background-color: #f0f0f0;
    color: #333;
}

.reorder-popup-empty-text {
    text-align: center;
    color: #888;
    padding: 20px;
}

.reorder-popup-footer {
    display: flex;
    justify-content: flex-end; /* Align buttons to the right */
    gap: 10px;
    margin-top: 10px;
}
</style>