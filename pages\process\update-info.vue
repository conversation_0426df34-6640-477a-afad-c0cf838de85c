<script setup>
import { ref, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import urlPrefix from '@/pages/common/urlPrefix.js'

// 生产环境
// const urlPrefix = "http://***********:8200"
const user = uni.getStorageSync("loUserNo")

// 开发环境
// const urlPrefix = "http://**********:8080"
// const user = "zqn"

const info = ref()

const typeList = ref([])

const materialList = ref([])
const materialOptionList = ref([])
const materialListShow = ref(false)

const operationMap = new Map([
  ["1", "加工"],
  ["2", "裁断"],
  ["3", "准备"],
  ["4", "针车"],
  ["5", "半成品"],
  ["6", "成型"]
])

const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')

// 返回上一页
function back() {
  info.value = ''
  uni.navigateBack({
    delta: 1,
    animationType: 'pop-out',
    animationDuration: 200
  })
}

// 获取生产类型
function getType() {
  uni.request({
    url: urlPrefix + "/process/getType",
    method: "POST",
    data: {
      operation: info.value.operation
    }
  }).then(res => {
    let data = res.data.data ? res.data.data : []
    for (let item of data) {
      typeList.value.push({
        text: item,
        value: item
      })
    }
  }).catch(err => {
    uni.showToast({
      icon: "error",
      title: "请检查网络！"
    })
  })
}

// 获取材质
function getMaterial() {
  uni.request({
    url: urlPrefix + "/process/getMaterial",
    method: "POST"
  }).then(res => {
    materialList.value = res.data.data ? res.data.data : [],
    materialOptionList.value = materialList.value.filter(item => item.includes(info.value.material ? info.value.material.toUpperCase() : '')).slice(0, 50)
  }).catch(err => {
    uni.showToast({
      icon: "error",
      title: "请检查网络！"
    })
  })
}

// 修改工序信息
function updateProcessInfo() {
  uni.request({
    url: urlPrefix + "/process/updateProcessInfo",
    method: "POST",
    data: info.value
  }).then(res => {
    if (res.data.code) {
      showTip('success', '工序信息修改成功！')
      setTimeout(() => {
        back()
      }, 1000)
    } else {
      showTip('error', '工序信息修改失败！')
    }
  }).catch(err => {
    showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 提示信息
function showTip(type, message) {
  tipType.value = type
  tipMessage.value = message
  tipPopup.value.open()
}

watch(info, () => {
  materialOptionList.value = materialList.value.filter(item => item.includes(info.value.material ? info.value.material.toUpperCase() : '')).slice(0, 50)
}, { deep: true })

onLoad((props) => {
  info.value = JSON.parse(props.info)
  info.value.upd_user = user
  getType()
  getMaterial()
})
</script>

<template>
  <view class="update-info">
    <view class="back">
      <uni-icons @click="back()" type="back" size="36" color="#6fa2ce"></uni-icons>
    </view>
    
    <view class="submit">
      <uni-icons @click="updateProcessInfo()" type="checkmarkempty" size="36" color="#45b08c"></uni-icons>
    </view>
    
    <view class="title">
      修改工序信息
    </view>
    
    <view class="data">
      <uni-section title="型体编号" titleFontSize="20px" type="circle" class="attribute">
        <uni-easyinput :value="info.model_no" disabled></uni-easyinput>
      </uni-section>
      
      <uni-section title="制程" titleFontSize="20px" type="circle" class="attribute" style="margin-left: 0;">
        <uni-easyinput :value="operationMap.get(info.operation)" disabled></uni-easyinput>
      </uni-section>
      
      <uni-section title="主要代码" titleFontSize="20px" type="circle" class="attribute">
        <uni-easyinput :value="info.rtg_code" disabled></uni-easyinput>
      </uni-section>
      
      <uni-section title="生产类型" titleFontSize="20px" type="line" class="attribute" style="margin-left: 0;">
        <uni-data-select
          v-model="info.rtg_type"
          :placeholder="info.rtg_type ? info.rtg_type : '请选择'"
          :localdata="typeList"
          :clear="false"
        ></uni-data-select>
      </uni-section>
      
      <uni-section title="材质" titleFontSize="20px" type="line" class="attribute">
        <view class="material">
          <input
            v-model="info.material"
            type="text"
            @focus="materialListShow = true"
            @blur="materialListShow = false"
            class="input"
          />
          
          <view
            v-show="info && info.material && info.material.length > 0"
            @click="info.material = ''"
            class="clear"
          >
            <uni-icons type="clear" size="34"></uni-icons>
          </view>
          
          <transition name="list">
            <view v-show="materialListShow" class="box">
              <view class="list">
                <view
                  v-for="item in materialOptionList"
                  :key="item"
                  @click="info.material = item"
                  class="item"
                  :style="{ border: materialOptionList.length === 1 ? 'none' : '' }"
                >
                  {{ item }}
                </view>
                
                <view
                  v-show="materialOptionList.length === 0"
                  class="item"
                  style="border: none;"
                >
                  暂无该材质数据
                </view>
              </view>
            </view>
          </transition>
        </view>
      </uni-section>
    </view>
  </view>
  
  <view class="tip-popup">
    <uni-popup
      ref="tipPopup"
      type="message"
    >
      <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
    </uni-popup>
  </view>
</template>

<style lang="scss">
page {
  width: 100%;
  height: 100%;
  padding: 2.5% 2% 1.5% 2%;
  box-sizing: border-box;
  background-color: #fdf6e3;
}

.update-info {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0 0 1px 5px #ddd;
  position: relative;
  overflow: auto;
  
  .back {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 2.5%;
    top: 2.5%;
    border-radius: 50%;
    box-shadow: 0 0 5px gray;
    transition: all 0.05s ease-in-out;
    cursor: pointer;
    z-index: 1;
    
    &:active {
      transform: scale(0.97);
      box-shadow: 0 0 1px gray;
    }
  }
      
  .submit {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 2.5%;
    top: 2.5%;
    border-radius: 50%;
    box-shadow: 0 0 5px gray;
    transition: all 0.05s ease-in-out;
    cursor: pointer;
    z-index: 1;
    
    &:active {
      transform: scale(0.97);
      box-shadow: 0 0 1px gray;
    }
  }
      
  .title {
    margin-bottom: 1%;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    padding: 16px;
  }
      
  .data {
    width: 100%;
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    
    .attribute {
      width: 35%;
      margin-left: 15%;
      margin-bottom: 5%;
      background-color: #fdf6e3;
      
      .uni-easyinput {
        width: 80%;
        margin-left: 16px;
        
        &:deep(.uni-easyinput__content-input) {
          height: 40px;
          color: gray;
          font-size: 20px;
          font-weight: bold;
        }
      }
      
      .uni-stat__select {
        width: 80%;
        margin-left: 16px;
        
        &:deep(.uni-select) {
          height: 40px;
          color: black;
          font-size: 20px;
          font-weight: bold;
          background-color: white;
        }
        
        &:deep(.uni-select__input-placeholder) {
          color: black;
          font-size: 20px;
          font-weight: bold;
        }
        
        &:deep(.uni-scroll-view-content) {
          max-height: 200px;
        }
        
        &:deep(.uni-icons) {
          margin-left: -25px;
          line-height: 26px;
        }
        
        &:deep(.uni-select__selector-item) {
          font-size: 20px;
          padding: 2px 10px;
        }
      }
      
      .material {
        width: 100%;
        height: 40px;
        margin-left: 16px;
        position: relative;
        
        .input {
          width: 100%;
          height: 100%;
          padding: 0 10px;
          font-size: 20px;
          font-weight: bold;
          background-color: white;
          border: 1px solid #f0f0f0;
          border-radius: 4px;
          box-sizing: border-box;
          
          &:hover {
            border: 1px solid black;
          }
        }
        
        .clear {
          position: absolute;
          top: 3px;
          right: 3px;
          cursor: pointer;
          z-index: 1;
        }
        
        .box {
          width: 100%;
          height: 270px;
          position: absolute;
          left: 0;
          top: -285px;
          z-index: 1;
          transform: rotate(180deg);
          
          .list {
            max-height: 270px;
            text-align: center;
            border-radius: 5px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            background-color: gray;
            overflow: auto;
            transform: rotate(180deg);
            
            .item {
              box-sizing: border-box;
              padding: 15px 0;
              border-top: 1px solid white;
              cursor: pointer;
              
              &:active {
                background-color: #aaa;
              }
            }
          }
          
          &::before {
            content: "";
            position: absolute;
            left: calc(50% - 10px);
            top: -10px;
            border-top: 0px solid transparent;
            border-left: 10px solid transparent;
            border-bottom: 10px solid gray;
            border-right: 10px solid transparent;
          }
        }
      }
    }
  }
}

.tip-popup {
  &:deep(.fixforpc-width) {
    min-width: 0;
    margin-top: 40px;
    padding: 10px 20px;
  }
  
  &:deep(.uni-popup-message-text) {
    font-size: 18px;
    font-weight: bold;
  }
}

.list-enter-active, .list-leave-active {
  transition: all 0.25s ease-in-out;
}

.list-enter-from, .list-leave-to {
  opacity: 0;
}

.list-enter-to, .list-leave-from {
  opacity: 1;
}
</style>