<!-- 预览工序 -->
<script setup>
import { ref, reactive, onMounted, watch, provide } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { deptMap, optionListMap, optionEnglishMap } from '@/pages/sop/common/commonMap.js'
import PreviewListPopup from '@/pages/sop/preview/preview-list-popup.vue'
import PicturePopup from '@/pages/sop/common/picture-popup.vue'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()
// 图片弹框
const picturePopup = ref()

// 预览列表弹框
const previewListPopup = ref()

// 接收参数
const props = defineProps({
  info: {
    type: String,
    default: '{}'
  }
})

// 工序信息
const processInfo = ref(JSON.parse(props.info))

// 型体图片
const modelPicture = ref('')

// 预览下标
const previewIndex = ref(-1)
// 预览列表
const previewList = ref([])

// 预览详情
const previewDetail = reactive({
  brand: '',  // 客户
  model: '',  // 型体
  operation: '',  // 制程
  rtgCode: '',  // 主要代码
  seqNo: '',  // 工序编号
  skey: -1,  // 序号
  wkGroup: '',  // 加工段
  actions: '',  // 动作
  imgTit1: '',  // 图片备注
  imgList: [],  // 图片列表
  standardId: -1,  // 操作标准 ID
  standard: '',  // 操作标准
  checkPointId: -1,  // 自检点 ID
  checkPoint: '',  // 自检点
  tools: '',  // 工具
  machine: '',  // 机器
  margin: '',  // 边距
  temp: '',  // 温度
  pressure: '',  // 压力
  glue: '',  // 胶水
  carLine: '',  // 车线
  chemical: '',  // 化学品
  needleSpacing: '',  // 针距
  spacing: '',  // 间距
  needle: '',  // 车针
  time: '',  // 时间
  defence: '',  // 防护用品
  last: '',  // 楦头
  osNo: '',  // Outsole
  tab: '',  // 制表人
  insUser: '',  // 添加者
  insDate: '',  // 添加时间
  updUser: '',  // 修改者
  updDate: '',  // 修改时间
  version: '',  // 版本
  processOption1: '',
  processOption2: '',
  processOption3: '',
  processOption4: '',
  processOption5: '',
  processOption6: '',
  processOption7: '',
  processOption8: '',
  processOption9: '',
  processOption10: ''
})

// 图片映射
let pictureMap = ref(new Map())

// 返回
function back() {
  uni.navigateBack()
}

// 获取鞋图
async function getModelPicture(param) {
  modelPicture.value = ''
  await uni.request({
    url: urlPrefix + '/sop/getModelPicture',
    method: 'POST',
    data: {
      model: param
    }
  }).then(res => {
    if (res.data.code) {
      modelPicture.value = res.data.data ? ('data:image/jpg;base64,' + res.data.data.modelPicture) : ''
    } else {
      tipPopup.value.showTipPopup('warn', '暂无鞋图数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 上一个
function lastPreview() {
  if (previewIndex.value === 0) {
    return
  }
  previewIndex.value--
}

// 下一个
function nextPreview() {
  if (previewIndex.value === previewList.value.length - 1) {
    return
  }
  previewIndex.value++
}

// 获取流程预览列表
async function getPreviewList(param1, param2, param3) {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/getPreviewList',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3
    }
  }).then(res => {
    if (res.data.code) {
      previewList.value = res.data.data ? res.data.data : []
      previewIndex.value = 0
    } else {
      tipPopup.value.showTipPopup('warn', '暂无流程预览列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 图片 url 转 base64 字符串
function urlToBase64(param) {
  return new Promise((resolve, reject) => {
    uni.request({
      url: param,
      responseType: 'arraybuffer'
    }).then(res => {
      let base64 = uni.arrayBufferToBase64(res.data)
      resolve('data:' + res.header['content-type'] + ';base64,' + base64)
    }).catch(err => {
      reject(err)
    })
  })
}

watch(previewIndex, () => {
  let data = previewList.value[previewIndex.value]
  previewDetail.brand = processInfo.value.brand
  previewDetail.model = data.model
  previewDetail.operation = data.operation
  previewDetail.rtgCode = data.rtgCode
  previewDetail.seqNo = data.seqNo
  previewDetail.skey = data.skey
  previewDetail.wkGroup = data.wkGroup
  previewDetail.actions = data.actions
  previewDetail.imgTit1 = data.imgTit1
  previewDetail.imgList = data.imgList
  previewDetail.standardId = data.standardId
  previewDetail.standard = data.standard
  previewDetail.checkPointId = data.checkPointId
  previewDetail.checkPoint = data.checkPoint
  previewDetail.tools = data.tools
  previewDetail.machine = data.machine
  previewDetail.margin = data.margin
  previewDetail.temp = data.temp
  previewDetail.pressure = data.pressure
  previewDetail.glue = data.glue
  previewDetail.carLine = data.carLine
  previewDetail.chemical = data.chemical
  previewDetail.needleSpacing = data.needleSpacing
  previewDetail.spacing = data.spacing
  previewDetail.needle = data.needle
  previewDetail.time = data.time
  previewDetail.defence = data.defence
  previewDetail.last = data.last
  previewDetail.osNo = data.osNo
  previewDetail.tab = data.tab
  previewDetail.version = data.version
  previewDetail.insUser = data.insUser
  previewDetail.insDate = data.insDate
  previewDetail.updUser = data.updUser
  previewDetail.updDate = data.updDate
  previewDetail.processOption1 = data.processOption1
  previewDetail.processOption2 = data.processOption2
  previewDetail.processOption3 = data.processOption3
  previewDetail.processOption4 = data.processOption4
  previewDetail.processOption5 = data.processOption5
  previewDetail.processOption6 = data.processOption6
  previewDetail.processOption7 = data.processOption7
  previewDetail.processOption8 = data.processOption8
  previewDetail.processOption9 = data.processOption9
  previewDetail.processOption10 = data.processOption10
  
  // 图片映射
  // pictureMap.value.clear()
  for (let p of previewDetail.imgList) {
    let url = urlPrefix + p.imgUrl
    urlToBase64(url).then((res) => {
      pictureMap.value.set(p.id, res)
    }).catch(err => {
      tipPopup.value.showTipPopup('warn', '图片缓存失败！')
    })
  }
})

onMounted(() => {
  getModelPicture(processInfo.value.model)
  getPreviewList(processInfo.value.model, processInfo.value.operation, processInfo.value.rtgCode)
})

provide('processInfo', processInfo)
provide('previewIndex', previewIndex)
provide('previewList', previewList)
</script>

<template>
  <view class="sop-preview">
    <view class="top-bar flex-row-start-center">
      <view @click="back()" class="back button">
        <uni-icons type="back" size="30" color="steelblue"></uni-icons>
      </view>
      
      <view class="model-picture" :style="{ visibility: modelPicture.replace(/ /g, '+').length > 0 ? 'visible' : 'hidden' }">
        <!-- #ifdef APP -->
        <uni-transition
          :show="modelPicture.replace(/ /g, '+').length > 0"
          mode-class="fade"
          class="watermark"
        >
        <!-- #endif -->
        <!-- #ifdef WEB -->
        <transition name="fade">
        <view
          v-show="modelPicture.replace(/ /g, '+').length > 0"
          class="watermark"
        >
        <!-- #endif -->
          <img
            @click="picturePopup.showPicturePopup(modelPicture.replace(/ /g, '+'))"
            :src="modelPicture.replace(/ /g, '+')"
            alt=""
            class="button"
          />
        <!-- #ifdef WEB -->
        </view>
        </transition>
        <!-- #endif -->
        <!-- #ifdef APP -->
        </uni-transition>
        <!-- #endif -->
      </view>
      
      <view
        @click="lastPreview()"
        class="last button"
        :style="{
          visibility: previewIndex > 0 ? 'visible' : 'hidden'
        }"
      >
        <uni-icons type="arrow-left" size="30" color="brown"></uni-icons>
      </view>
      
      <view class="title flex-row-center">
        流程预览
      </view>
      
      <view
        @click="nextPreview()"
        class="next button"
        :style="{
          visibility: previewIndex < previewList.length - 1 ? 'visible' : 'hidden'
        }"
      >
        <uni-icons type="arrow-right" size="30" color="brown"></uni-icons>
      </view>
      
      <view
        @click="previewListPopup.showPreviewListPopup()"
        class="list button"
      >
        <uni-icons type="list" size="30" color="chocolate"></uni-icons>
      </view>
      
      <view
        class="version button"
        :style="{
          visibility: previewDetail.version ? 'visible' : 'hidden'
        }"
      >
        {{ 'v' + previewDetail.version }}
      </view>
    </view>
    
    <view class="other flex-row-between-center">
      <view class="brand flex-row-center">
        客户 Brand: {{ previewDetail.brand }}
      </view>
      
      <view class="model flex-row-center">
        Style: {{ previewDetail.model }}{{ previewDetail.last ? ('/' + previewDetail.last) : '' }}{{ previewDetail.osNo ? ('/' + previewDetail.osNo) : '' }}
      </view>
      
      <view class="date flex-row-center">
        日期 Date: {{ previewDetail.updDate ? previewDetail.updDate.substring(0, 10) : '' }}
      </view>
    </view>
    
    <view class="main">
      <table>
        <tr>
          <th style="width: 8%; border-left: none; border-top: none;">
            项次<br>No.
          </th>
          <th style="width: 17%; border-top: none;">
            流程步骤<br>Process
          </th>
          <th style="width: 35%; border-top: none;">
            操作标准<br>Operation Standard
          </th>
          <th
            v-if="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2"
            style="width: 10%; border-top: none;"
          >
            工具<br>Tool
          </th>
          <th
            v-if="deptMap.get(processInfo.operation) === 1"
            style="width: 10%; border-top: none;"
          >
            化学品<br>Chemical
          </th>
          <th
            v-if="deptMap.get(processInfo.operation) === 2"
            style="width: 10%; border-top: none;"
          >
            胶水<br>Glue
          </th>
          <th
            v-if="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2"
            style="width: 10%; border-top: none;"
          >
            机器<br>Machine
          </th>
          <th
            v-if="deptMap.get(processInfo.operation) === 1"
            style="width: 10%; border-right: none; border-top: none;"
          >
            防护用品<br>Protective<br>Gear
          </th>
          <th
            v-if="deptMap.get(processInfo.operation) === 2"
            style="width: 10%; border-right: none; border-top: none;"
          >
            车线<br>Thread
          </th>
          
          <th
            v-for="index in 4"
            :key="index"
            v-if="deptMap.get(processInfo.operation) === 3"
            style="width: 10%; border-top: none;"
            :style="{ borderRight: index === 4 ? 'none' : '' }"
          >
            {{ index <= optionListMap.get(processInfo.proSeq).length ? optionListMap.get(processInfo.proSeq)[index - 1] : '' }}<br>{{ index <= optionListMap.get(processInfo.proSeq).length ? optionEnglishMap.get(optionListMap.get(processInfo.proSeq)[index - 1]) : '' }}
          </th>
        </tr>
        
        <tr>
          <td rowspan="3" style="border-left: none;">
            {{ previewDetail.skey > 0 ? previewDetail.skey : '' }}
          </td>
          <td rowspan="3">{{ previewDetail.actions }}</td>
          <td rowspan="3" style="vertical-align: top;">
            <view class="standard">
              {{ previewDetail.standard }}
            </view>
          </td>
          <td v-if="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2">
            {{ previewDetail.tools }}
          </td>
          <td v-if="deptMap.get(processInfo.operation) === 1">
            {{ previewDetail.chemical }}
          </td>
          <td v-if="deptMap.get(processInfo.operation) === 2">
            {{ previewDetail.glue }}
          </td>
          <td v-if="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2">
            {{ previewDetail.machine }}
          </td>
          <td
            v-if="deptMap.get(processInfo.operation) === 1"
            style="border-right: none;"
          >
            {{ previewDetail.defence }}
          </td>
          <td
            v-if="deptMap.get(processInfo.operation) === 2"
            style="border-right: none;"
          >
            {{ previewDetail.carLine }}
          </td>
          
          <td
            v-for="index in 4"
            :key="index"
            v-if="deptMap.get(processInfo.operation) === 3"
            :style="{ borderRight: index === 4 ? 'none' : '' }"
          >
            {{ index <= optionListMap.get(processInfo.proSeq).length ? previewDetail['processOption' + index] : '' }}
          </td>
        </tr>
        
        <tr v-if="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2">
          <th v-if="deptMap.get(processInfo.operation) === 1">
            温度<br>Temp
          </th>
          <th v-if="deptMap.get(processInfo.operation) === 2">
            边距<br>Margin
          </th>
          <th v-if="deptMap.get(processInfo.operation) === 1">
            压力<br>Pressure
          </th>
          <th v-if="deptMap.get(processInfo.operation) === 2">
            针距<br>Stitch
          </th>
          <th v-if="deptMap.get(processInfo.operation) === 1">
            时间<br>Time
          </th>
          <th v-if="deptMap.get(processInfo.operation) === 2">
            间距<br>Space
          </th>
          <th style="border-right: none;">
            车针<br>Needle
          </th>
        </tr>
        
        <tr v-if="deptMap.get(processInfo.operation) === 3">
          <th
            v-for="index in 4"
            :key="index"
            :style="{ borderRight: index === 4 ? 'none' : '' }"
          >
            {{ (index + 4) <= optionListMap.get(processInfo.proSeq).length ? optionListMap.get(processInfo.proSeq)[index + 3] : '' }}<br>{{ (index + 4) <= optionListMap.get(processInfo.proSeq).length ? optionEnglishMap.get(optionListMap.get(processInfo.proSeq)[index + 3]) : '' }}
          </th>
        </tr>
        
        <tr v-if="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2">
          <td v-if="deptMap.get(processInfo.operation) === 1">
            {{ previewDetail.temp }}
          </td>
          <td v-if="deptMap.get(processInfo.operation) === 2">
            {{ previewDetail.margin }}
          </td>
          <td v-if="deptMap.get(processInfo.operation) === 1">
            {{ previewDetail.pressure }}
          </td>
          <td v-if="deptMap.get(processInfo.operation) === 2">
            {{ previewDetail.needleSpacing }}
          </td>
          <td v-if="deptMap.get(processInfo.operation) === 1">
            {{ previewDetail.time }}
          </td>
          <td v-if="deptMap.get(processInfo.operation) === 2">
            {{ previewDetail.spacing }}
          </td>
          <td style="border-right: none;">
            {{ previewDetail.needle }}
          </td>
        </tr>
        
        <tr v-if="deptMap.get(processInfo.operation) === 3">
          <td
            v-for="index in 4"
            :key="index"
            :style="{ borderRight: index === 4 ? 'none' : '' }"
          >
            {{ (index + 4) <= optionListMap.get(processInfo.proSeq).length ? previewDetail['processOption' + (index + 4)] : '' }}
          </td>
        </tr>
        
        <tr>
          <td
            rowspan="2"
            colspan="3"
            style="border-left: none; border-bottom: none; vertical-align: top;"
          >
            <view class="picture-remark">
              {{ previewDetail.imgTit1 }}
            </view>
            
            <view class="picture-list flex-row-start">
              <view v-for="(item, index) in previewDetail.imgList" class="preview-picture watermark">
                <img
                  @click="picturePopup.showPicturePopup(pictureMap.get(item.id) ? pictureMap.get(item.id) : (urlPrefix + item.imgUrl))"
                  :src="pictureMap.get(item.id) ? pictureMap.get(item.id) : (urlPrefix + item.imgUrl)"
                  alt=""
                  class="button"
                />
              </view>
            </view>
          </td>
          <th colspan="4" style="border-right: none;">
            自检点<br>Self-check point
          </th>
        </tr>
        
        <tr>
          <td colspan="4" style="border-right: none; border-bottom: none; vertical-align: top;">
            <view class="check-point">
              {{ previewDetail.checkPoint }}
            </view>
          </td>
        </tr>
      </table>
    </view>
    
    <view class="person flex-row-between-center">
      <view class="approval flex-row-center">
        核准 Approval: 
      </view>
      
      <view class="check flex-row-center">
        审核 Check by: 
      </view>
      
      <view class="tab flex-row-center">
        制表 Tab: {{ previewDetail.tab }}
      </view>
    </view>
  </view>
  
  <preview-list-popup ref="previewListPopup" />
  <picture-popup ref="picturePopup" />
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss">
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

page {
  width: 100%;
  height: 100%;
  padding: 2.5% 2% 1.5% 2%;
  background-color: #fdf6e3;
}

.sop-preview {
  width: 100%;
  height: 100%;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 0 1px 5px #ddd;
  
  .top-bar {
    width: 100%;
    height: 50px;
    margin-bottom: 10px;
    
    .back, .model-picture {
      height: 50px;
      margin-right: 9.5%;
    }
    
    .back {
      width: 6%;
    }
    
    .model-picture {
      width: 9%;
    }
    
    .model-picture img {
      min-width: 50px;
      max-width: 100%;
      height: 50px;
    }
    
    .title {
      width: 20%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .last, .next, .list, .version {
      width: 6%;
      height: 50px;
    }
    
    .list {
      margin-left: 20%;
      margin-right: 2%;
    }
    
    .version {
      color: darkviolet;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .other {
    width: 100%;
    height: 40px;
    
    .brand, .model, .date {
      font-size: 20px;
      font-weight: bold;
    }
    
    .brand {
      width: 30%;
    }
    
    .model {
      width: 38%;
    }
    
    .date {
      width: 30%;
    }
  }
  
  .main {
    width: 100%;
    height: calc(100% - 150px);
    margin: 5px 0;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    overflow: auto;
    
    table {
      width: 100%;
      min-height: 100%;
      border-spacing: 0;
      
      tr {
        height: 80px;
        
        th, td {
          font-size: 20px;
          text-align: center;
          word-break: break-all;
          border: 1px solid #ccc;
          
          .standard, .check-point {
            padding: 10px;
            font-size: 20px;
            text-align: start;
          }
          
          .picture-remark {
            width: 100%;
            padding-top: 10px;
            padding-left: 10px;
            font-size: 20px;
            text-align: left;
          }
          
          .picture-list {
            .preview-picture {
              margin: 10px;
              
              img {
                height: 120px;
              }
            }
          }
        }
        
        td {
          white-space: pre-wrap;
        }
      }
    }
  }
  
  .person {
    width: 100%;
    height: 40px;
    
    .approval, .check, .tab {
      width: calc(100% / 3);
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .watermark {
    position: relative;
    word-break: break-all;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    
    &:before {
      content: 'STELLA';
      position: absolute;
      top: 2px;
      left: 2px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      text-shadow: 0 0 2px black;
      z-index: 1;
      pointer-events: none;
    }
  }
  
  .flex-row-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
  
  .flex-row-start {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
  }
  
  .flex-row-start-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
  }
  
  .flex-row-between-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }
  
  /* #ifdef WEB */
  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.3s ease;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  
  .fade-enter-to,
  .fade-leave-from {
    opacity: 1;
  }
  /* #endif */
}
</style>