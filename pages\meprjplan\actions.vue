<script setup>
import {ref, reactive} from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import {onShow} from '@dcloudio/uni-app'
import {useI18n} from 'vue-i18n'

const {t} = useI18n()

const user = uni.getStorageSync("loUserNo")
const info = ref()
const ToolPopup = ref()
//消息提示
const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')
function showTip(type, message) {
    tipType.value = type
    tipMessage.value = message
    tipPopup.value.open()
}

//语言选择
const langList = ref([
    {"text":"Chinese","value":"zh-Hans"},
    {"text":"English","value":"en"},
    {"text":"Vietnamese","value":"vi"},
    {"text":"Indonesian","value":"id"},
    {"text":"Bengali","value":"bn"},
    {"text":"Filipino","value":"fil"}
]);

//部门
const deptPart = ref([[t('底部SOP'), t('鞋面SOP')], [t('成型'), t('中底'),t('中底皮'),t('大底'),t('单底'),t('包粘')]])

const depts = ref([
    [t('成型'), t('中底'),t('中底皮'),t('大底'),t('单底'),t('包粘')],
    [t('鞋面')]
])
const deptType = ref();
const picker = ref()
function openPicker() {
    picker.value.open();
}

//选中部门
function changeDept(e) {
    const {columnIndex, index} = e
    if (columnIndex === 0) {
        picker.value.setColumnValues(1, depts.value[index])
    }
}

function confirm(e) {
    vo.dept_name = e.value[0] + "-" + e.value[1];
    changeDeptType();
}

//选中部门
function changeDeptType() {
    if (typeof vo.dept_name === 'undefined') {

    } else {
        if (vo.dept_name.indexOf('底部SOP-成型') > -1) {
            vo.dept = 1;
        } if (vo.dept_name.indexOf('底部SOP-中底') > -1) {
            vo.dept = 2;
        } if (vo.dept_name.indexOf('底部SOP-中底皮') > -1) {
            vo.dept = 3;
        } if (vo.dept_name.indexOf('底部SOP-大底') > -1) {
            vo.dept = 4;
        } if (vo.dept_name.indexOf('底部SOP-单底') > -1) {
            vo.dept = 5;
        } if (vo.dept_name.indexOf('底部SOP-包粘') > -1) {
            vo.dept = 6;
        } else if (vo.dept_name.indexOf('鞋面SOP-鞋面') > -1) {
            vo.dept = 21;
        }
    }
}

const vo = reactive({
	dept_name: "选择",
	dept: 0,
	create_by: user,
	update_by: user,
	pccMeProjectPlanDts: {
		"lang": '',
		"actions": '',
		"protective_gear": '',
		"tools": '',
		"chemical_substance": '',
		"machine": '',
		"temp": '',
		"pressure": '',
		"time": '',
		"op_std": '',
		"self_check_points": ''
	}
})

function save(){
	if(vo.pccMeProjectPlanDts.lang == null || vo.pccMeProjectPlanDts.lang == ''){
		showTip("error","请先选择语言和部门！");
		return;
	}
	if(vo.dept_name == '选择'){
		showTip("error","请先选择语言和部门！");
		return;
	}
    uni.request({
        url: urlPrefix + "/pccmeprjplanaction/save",
        method: "POST",
        data: vo
    }).then(res => {
        console.log(res.data.data);
		showTip("success","保存成功！")
		clearData();
    }).catch(err => {
        uni.showToast({
            icon: "error",
            title: t('请检查网络！')
        })
    })
}

function clearData(){
	vo.pccMeProjectPlanDts = {
		"lang": vo.pccMeProjectPlanDts.lang,
		"actions": '',
		"protective_gear": '',
		"tools": '',
		"chemical_substance": '',
		"machine": '',
		"temp": '',
		"pressure": '',
		"time": '',
		"op_std": '',
		"self_check_points": ''
	}
}

// 返回上一页
function back() {
    info.value = ''
	let back = getCurrentPages();
    if(back && back.length > 1) {
    	uni.navigateBack({  
    	  delta: 1  
    	});  
    } else {  
    	history.back();  
    }
}

//登录校验
function loginCheck(){
	if(uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == ''){
		uni.navigateTo({
			url: `/pages/login/login`,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}
}

onShow(() => {
	loginCheck();
});


</script>

<template>
    <view class="main-info">
        <view class="back">
            <uni-icons @click="back()" type="back" size="36" color="#6fa2ce"></uni-icons>
        </view>
        <view class="title">
            動作管理
        </view>
        <view class="submit">
            <uv-button @click="save()"  type="success" text="保存"></uv-button>
        </view>


        <view class="data-info">
            <view class="add-info">
                <view class="langSelect">
                    <view style="width: 50%;display: flex;align-items: center;">
                        <text class="text">選擇語言</text>
                        <uni-data-select max="1" v-model="vo.pccMeProjectPlanDts.lang" :localdata="langList"
                                         :clear="false"
                                         emptyTips="請選擇"
                                         style="width: 80%;background-color: white;"></uni-data-select>
                    </view>
                    <view style="width: 48%;padding-left: 2%;display: flex;align-items: center;">
                        <text class="text">選擇部門</text>
                        <uv-picker ref="picker" :columns="deptPart" @confirm="confirm" @change="changeDept"></uv-picker>
                        <button style="background-color: white;width: 80%;" @click="openPicker" v-text="vo.dept_name"></button>
                    </view>
                </view>

                <view class="input">
                    <text class="text">動作</text>
                    <uv-input style="background-color: white" placeholder="動作" border="surround" v-model="vo.pccMeProjectPlanDts.actions"></uv-input>
                </view>
                <view class="input">
                    <text class="text">防護用品</text>
                    <uv-input style="background-color: white" placeholder="防護用品" border="surround" v-model="vo.pccMeProjectPlanDts.protective_gear"></uv-input>
                </view>
                <view class="input">
                    <text class="text">工具</text>
                    <uv-input style="background-color: white" placeholder="工具" border="surround" v-model="vo.pccMeProjectPlanDts.tools"></uv-input>
                </view>
                <view class="input">
                    <text class="text">化學品</text>
                    <uv-input style="background-color: white" placeholder="化學品" border="surround" v-model="vo.pccMeProjectPlanDts.chemical_substance"></uv-input>
                </view>
                <view class="input">
                    <text class="text">機器</text>
                    <uv-input style="background-color: white" placeholder="機器" border="surround" v-model="vo.pccMeProjectPlanDts.machine"></uv-input>
                </view>
                <view class="input">
                    <text class="text">溫度</text>
                    <uv-input style="background-color: white" placeholder="溫度" border="surround" v-model="vo.pccMeProjectPlanDts.temp"></uv-input>
                </view>
                <view class="input">
                    <text class="text">壓力</text>
                    <uv-input style="background-color: white" placeholder="壓力" border="surround" v-model="vo.pccMeProjectPlanDts.pressure"></uv-input>
                </view>
                <view class="input">
                    <text class="text">時間</text>
                    <uv-input style="background-color: white" placeholder="時間" border="surround" v-model="vo.pccMeProjectPlanDts.time"></uv-input>
                </view>
                <view class="input">
                    <text class="text">操作標準</text>
                    <uv-textarea maxlength="-1" v-model="vo.pccMeProjectPlanDts.op_std" placeholder="操作標準"></uv-textarea>
                </view>
                <view class="input">
                    <text class="text">自檢點</text>
                    <uv-textarea maxlength="-1" v-model="vo.pccMeProjectPlanDts.self_check_points" placeholder="自檢點"></uv-textarea>
                </view>
            </view>
        </view>
    </view>

    <view class="tip-popup">
        <uni-popup ref="tipPopup" type="message">
            <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
        </uni-popup>
    </view>
</template>

<style lang="scss">
page {
    width: 100%;
    height: 100%;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;
}

.main-info {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0 0 1px 5px #ddd;
    position: relative;
    overflow: auto;

    .back {

        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 2.5%;
        top: 2.5%;
        border-radius: 50%;
        box-shadow: 0 0 5px gray;
        transition: all 0.05s ease-in-out;
        cursor: pointer;
        z-index: 1;

        &:active {
            transform: scale(0.97);
            box-shadow: 0 0 1px gray;
        }
    }

    .submit {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: 2.5%;
        top: 2.5%;
        transition: all 0.05s ease-in-out;
        cursor: pointer;
        z-index: 1;

        &:active {
            transform: scale(0.97);
            box-shadow: 0 0 1px gray;
        }
    }

}

.title {
    margin-bottom: 1%;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    padding: 16px;
}

.data-info {
    margin-top: 2%;
    padding: 0% 2.5% 2.5% 2.5%;

    .add-info {
        height: 50%;

        .langSelect{
            display: flex;
            align-items: center;
        }

        .input{
            margin-top: 20px;
            display: flex;
            align-items: center;
        }

        .text{
            margin-right: 5px;
            width: 10vh;
        }
    }

    .list-info{
        height: 50%;
    }
}
</style>