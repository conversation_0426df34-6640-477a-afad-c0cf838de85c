<template>
    <view class="container">
        <view class="back">
            <uni-icons @click="back" type="back" size="36"></uni-icons>
        </view>
		
		<view style="width: 100%; text-align: center; font-size: 24px;">
			<text>裁断生产进度表</text>
		</view>
		
		<view class="search">

			<view class="flex-box" >
				<text>投入时间：</text>
				<picker  mode="date" :value="cinStartTime" @change="bindCinStartDateChange">
					<view style="padding: 8px;background-color: white;width: 90px" v-if="cinStartTime === ''" class="uni-input">请选择</view>
					<view style="padding: 8px;background-color: white;width: 90px" v-if="cinStartTime != ''" class="uni-input">{{cinStartTimeText}}</view>
				</picker>
				<picker  mode="date" :value="cinEndTime" @change="bindCinEndDateChange" style="margin-left: 10px">
					<view style="padding: 8px;background-color: white; width: 90px" v-if="cinEndTime === ''" class="uni-input">请选择</view>
					<view style="padding: 8px;background-color: white;width: 90px" v-if="cinEndTime != ''" class="uni-input">{{cinEndTimeText}}</view>
				</picker>
			</view>

			<view class="flex-box">
				<text style="margin-left: 25px;">品牌：</text>
				<!--<view class="inputDate">
					<uv-input v-model="brand"></uv-input>
				</view>-->
				<view class="inputDate">
					<uni-combox
					        :candidates="brandList"
					        placeholder="请选择品牌"
					        v-model="brand"
					        @input="bindBrandChange"
					></uni-combox>
				</view>
			</view>

			<view class="flex-box">
				<text style="margin-left: 25px;">样品类型：</text>
				<view>
					<uni-data-select v-model="devType"
									 :localdata="devTypes"
									 :clear="false"
									 emptyTips="請選擇"
									 style="margin-left: 16px;background-color: white;width: 130px"></uni-data-select>
				</view>
			</view>

			<view style="margin-left: 25px;" class="flex-box">
				<text >发外状态：</text>
				<view>
					<uni-data-select v-model="externalStatus"
									 :localdata="externaTypes"
									 :clear="false"
									 emptyTips="請選擇"
									 style="width: 80px;margin-left: 16px;background-color: white;"></uni-data-select>
				</view>
			</view>

			<uv-button @click="query()" style="margin-left: 25px;" type="primary" text="查询"></uv-button>
		</view>

        <view class="title">
            <table>
                <thead>
                <tr class="boderBox">
                    <th class="rowClass3">样品单号\单据序号</th>
                    <th class="rowClass3">样品类型</th>
                    <th class="rowClass3">鞋图</th>
                    <th class="rowClass4">业务/版师</th>
                    <th class="rowClass3">型体</th>
                    <th class="rowClass3">楦头编号</th>
                    <th class="rowClass3">派工日</th>
                    <th class="rowClass3" @click="sortByShipDate">出货日 
                        <text v-if="sortField === 'shp_date' && sortOrder === 'asc'">↑</text>
                        <text v-if="sortField === 'shp_date' && sortOrder === 'desc'">↓</text>
                    </th>
                    <th class="rowClass3">订单量</th>
                    <th class="rowClass3" style="background-color: #ffc000;">鞋面</th>
                    <th class="rowClass3" style="background-color: #ffc000;">中底皮</th>
                    <th class="rowClass3" style="background-color: #9bbb59;">中底</th>
                    <th class="rowClass3" style="background-color: #9bbb59;">大底</th>
                    <th class="rowClass3" style="background-color: #ffc000;">滚片</th>
                    <th class="rowClass3" style="background-color: #ffc000;">跟皮</th>
                    <th class="rowClass3">裁断发外</th>
                    <th class="rowClass3">排入时间</th>
                    <th class="rowClass3">异常信息</th>
                    <th class="rowClass3">取消</th>
                </tr>
                </thead>
                <tbody>
					<template v-for="(item, index) in tableData" :key="item.id">
						<tr  :class="{ 'highlight-green': item.c7_qty > 0 }">
							<td class="rowClass" rowspan="2">
								<text style="text-decoration: underline;color: skyblue;"  @click="viewDetail(item.ord_no)">{{ item.ord_no }}</text>
								<text>{{item.ord_seq}}</text>
							</td>
							<td class="rowClass" rowspan="2">{{ item.dev_type }}</td>
							<td class="rowClass" rowspan="2"><img :src="'data:image/jpg;base64,' + item.model_pic " alt="鞋图"/></td>
							<td class="rowClass2" rowspan="2">
								{{ formattedBRLetters(item.dutyer) }}<br/>
								{{ formattedBRNumbers(item.dutyer) }}
							</td>
							<td class="rowClass" rowspan="2">{{ item.model_no }}</td>
							<td class="rowClass" rowspan="2" style="white-space: pre-wrap;">{{ item.last_no }}</td>
							<td class="rowClass" rowspan="2">{{ item.wo_date }}</td>
							<td class="rowClass" rowspan="2">{{ item.shp_date }}</td>
							<td class="rowClass" rowspan="2">{{ item.tot_qty }}</td>
							<td class="rowClass" rowspan="2">{{ item.c1_qty }}</td>
							<td class="rowClass" rowspan="2">{{ item.c2_qty }}</td>
							<td class="rowClass" rowspan="2">{{ item.c3_qty }}</td>
							<td class="rowClass" rowspan="2">{{ item.c4_qty }}</td>
							<td class="rowClass" rowspan="2">{{ item.c5_qty }}</td>
							<td class="rowClass" rowspan="2">{{ item.c6_qty }}</td>
							<td class="rowClass">{{ item.c_f_status }}</td>
							<td class="rowClass" rowspan="2">{{ item.cin_date }}</td>
							<td class="rowClass" rowspan="2">{{ item.pb_desc }}</td>
							<td class="rowClass" rowspan="2">
							    <uv-button text="取消投入" type="error" @click="cancel(item.ord_no,item.item_no)"></uv-button>
							</td>
						</tr>
						<tr  :class="{ 'highlight-green': item.c7_qty > 0 }">
							<td class="rowClass">{{ item.u_f_status }}</td>
						</tr>
					</template>
					 <tr class="flex-row" style="position: sticky;bottom: 0;z-index: 90;background: white;">
						 <td class="rowClass">合计：</td>
						 <td class="rowClass"></td>
						 <td class="rowClass"></td>
						 <td class="rowClass"></td>
						 <td class="rowClass"></td>
						 <td class="rowClass"></td>
						 <td class="rowClass"></td>
						 <td class="rowClass"></td>
						<td class="rowClass">{{ totalOrderQuantity1 }}</td>
						<td class="rowClass">{{ totalOrderQuantity2 }}</td>
						<td class="rowClass">{{ totalOrderQuantity3 }}</td>
						<td class="rowClass">{{ totalOrderQuantity4 }}</td>
						<td class="rowClass">{{ totalOrderQuantity5 }}</td>
						<td class="rowClass">{{ totalOrderQuantity6 }}</td>
						<td class="rowClass">{{ totalOrderQuantity7 }}</td>
						<td class="rowClass"></td>
						<td class="rowClass"></td>
						<td class="rowClass"></td>
					</tr>
                </tbody>
            </table>
        </view>
		
		<!--单击单行弹出界面-->
		<view class="updateSpecificationPopup">
		  <uni-popup
		    ref="updateSpecificationPopup"
		    type="center"
			style="height: 80%;">
		  
		    <view class="updateSpecificationBox">
		
		      <uni-title title="倉庫掃描明細" type="h2" align="center" ></uni-title>
			  <uni-icons type="closeempty" @click="backDrom" size="32" style="position: absolute;top: 25rpx;right: 1vw;"></uni-icons>
		      <view class="updateSpecificationData">
					<zb-table
						:show-header="true"
						:columns="column"
						:stripe="true"
						ref="zbTable"
						:border="true"
						:cell-style="cellStyle"
						:data="detailTableData"></zb-table>
		      </view>
			  <view style="display: flex; justify-content: center; align-items: center;">
			      <uv-button type="primary" text="已完成" @click="manualClose"></uv-button>
			  </view>
			</view>
			</uni-popup>
		</view>
		
		<!--<view class="left-bottom">
			<uni-pagination show-icon="true" :total="pageCount" :current="firstPageNo" :pageSize="firstPageSize"
				@change="firstChange"></uni-pagination>
		</view>-->
    </view>
	
	<view class="tip-popup">
	    <uni-popup ref="tipPopup" type="message">
	        <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
	    </uni-popup>
	</view>

</template>

<script setup>
import {
    onMounted,
    ref,
    reactive,
    watch,computed
} from 'vue'
import {
    onPullDownRefresh
} from '@dcloudio/uni-app'
import {
    onShow
} from '@dcloudio/uni-app';
import urlPrefix from '@/pages/common/urlPrefix.js'

//第几页
const firstPageNo = ref(1)
const firstPageSize = ref(999)
const pageCount = ref(0)

//表单数据
const dataList = ref([])

const insUs = uni.getStorageSync("loUserNo")
const insName = uni.getStorageSync("loginUserName")

//消息提示
const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')

//日期选择
const endTime = ref(new Date().getTime())
const date = new Date();
date.setHours(date.getHours() - 24);
const startTime = ref(date.getTime())
const datetimePickerSt = ref()
const datetimePickerEnd = ref()
const startTimeText = ref('2024/06/03')
const endTimeText = ref('2024/06/03')

// 投入时间选择
const cinEndTime = ref(new Date().getTime())
const cinStartTime = ref(new Date().getTime())
const cinStartTimeText = ref('2024/06/03')
const cinEndTimeText = ref('2024/06/03')
const cinStartTimeTamp = ref(new Date().getTime())
const cinEndTimeTamp = ref(new Date().getTime())

const brand = ref()
const brandList = ref([])
const devType = ref()
const devTypes = ref([])

const tableData = ref([]);
const updateSpecificationPopup = ref();
const currentOrdNo = ref();
const detailTableData = ref([]);
const column=ref([
          { name: 'ord_no', label: '樣品單號',emptyString:'--',width:150},
		  { name: 'bar_date', label: '條碼日期',emptyString:' ',width:150},
		  { name: 'semi_su', label: '製程部位',width:150},
		  { name: 'made_dept', label: '製作組別',emptyString:'/',width:150},
		  { name: 'emp_name', label: '製作人員',emptyString:'/'},
		  { name: 'bar_qty', label: '條碼雙數',emptyString:'0'},
          { name: 'ins_user', label: '建立人',emptyString:'/'}
        ]);

const externalStatus = ref();
const externaTypes = ref(
    [
	  {value:3,text:"ALL"},
      { value: 0, text: "未完成" },
      { value: 1, text: "完成" },
	]
)

//排序相关
const sortField = ref('');
const sortOrder = ref('');

// 获取品牌列表
function getBrands() {
    uni.request({
        url: urlPrefix + "/first/getBrandsPlus",
        method: "POST"
    }).then(res => {
        let arr = []
        /*     brandList.value = res.data.data.map(item=>item.data.map(obj=>obj)) ? res.data.data : []*/
        for (const item of  res.data.data) {
            item.data.forEach(i=>{
                arr.push({value:i,text:i})
            })
        }
       
		brandList.value = arr.map(item=>{
		   return item.value
		});

    }).catch(err => {
        console.log(err)
    })
}


function bindBrandChange(e) {
    brand.value = e
}

function bindDateChange(e){
    startTime.value = new Date(e.detail.value).getTime();
    startTimeText.value = e.detail.value;
}

function bindEndDateChange(e){
    endTime.value = new Date(e.detail.value).getTime();
    endTimeText.value = e.detail.value;
}

function bindCinStartDateChange(e){
    cinStartTime.value = e.detail.value;
    cinStartTimeText.value = e.detail.value;
    cinStartTimeTamp.value = new Date(e.detail.value).getTime();
}

function bindCinEndDateChange(e){
    cinEndTime.value = e.detail.value;
    cinEndTimeText.value = e.detail.value;
    cinEndTimeTamp.value = new Date(e.detail.value).getTime();
}

// 换行方法
function formattedBRNumbers(text) {
    const numbers = text.match(/\d+/g) || []; // 提取数字
    return  numbers[0];
};

function formattedBRLetters(text){
    const letters = text.match(/[^\d]+/g) || []; // 提取非数字字符
    return letters[0]
};



//判断颜色
function cellStyle({row, column, rowIndex, columnIndex}){
	if(row.key_flag == "Y" && row.bar_qty == '' && column.name == 'bar_qty'){
		return {
		  'color': 'red'
		};
	}
	// 如果不符合条件，返回空对象
	return {};
}

//关闭弹窗
function backDrom() {
  detailTableData.value=[];
  updateSpecificationPopup.value.close()
}	

//查看明细
function viewDetail(ord_no){
	currentOrdNo.value = ord_no;
	uni.request({
	    url: urlPrefix + "/qdpt/queryDetailTableData",
	    data: {
	        "ordNo": ord_no
	    },
	    method: "GET"
	}).then(res => {
	    detailTableData.value = res.data.data;
	}).catch(err => {
	    console.log(err)
	})
	updateSpecificationPopup.value.open()
}


function manualClose(){
	uni.showModal({
		title: '提示',
		content: '确定要更新这条记录吗？',
		confirmColor: "#ff0000",
		success: function(res) {
			if (res.confirm) {
				console.log('用户点击确定');
				uni.request({
					url: urlPrefix + "/qdpt/manualClose",
					data: {
						"ord_no": currentOrdNo.value
					},
					method: "POST"
				}).then(res => {
					if (res.statusCode != 200) {
						uni.showToast({
							title: res.data.message,
							icon: "error"
						});
					} else{
						updateSpecificationPopup.value.close();
						query();
					}
				}).catch(err => {
					console.log(err);
					uni.showToast({
						title: '操作失败..',
						icon: "error"
					});
				})
			} else if (res.cancel) {
				console.log('用户点击取消');
			}
		}
	});
	
}

function openSt() {
	datetimePickerSt.value.open();
}

function confirmSt(e) {
	console.log('confirm', e);
	// 创建一个新的日期对象e
	var date = new Date(e.value);
	
	// 获取年、月、日
	var year = date.getFullYear();
	var month = date.getMonth() + 1; // 月份从0开始，需要加1
	var day = date.getDate();
	
	// 格式化日期为yyyy/MM/DD的样式
	var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
	
	startTimeText.value = formattedDate;
	cinStartTimeText.value = formattedDate;
}


function openEnd() {
	datetimePickerEnd.value.open();
}

function confirmEnd(e) {
	// 创建一个新的日期对象e
	var date = new Date(e.value);
	
	// 获取年、月、日
	var year = date.getFullYear();
	var month = date.getMonth() + 1; // 月份从0开始，需要加1
	var day = date.getDate();
	
	// 格式化日期为yyyy/MM/DD的样式
	var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
	
	endTimeText.value = formattedDate;
	cinEndTimeText.value = formattedDate;
}

const totalOrderQuantity1 = ref()
const totalOrderQuantity2 = ref()
const totalOrderQuantity3 = ref()
const totalOrderQuantity4 = ref()
const totalOrderQuantity5 = ref()
const totalOrderQuantity6 = ref()
const totalOrderQuantity7 = ref()

// 按出货日期排序
function sortByShipDate() {
    if (sortField.value === 'shp_date') {
        // 如果已经按此字段排序，则切换排序顺序
        sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
    } else {
        // 如果是新的排序字段，默认为升序
        sortField.value = 'shp_date';
        sortOrder.value = 'asc';
    }
    
    // 执行排序
    sortTableData();
}

// 排序表格数据
function sortTableData() {
    if (!sortField.value) return;
    
    tableData.value = [...tableData.value].sort((a, b) => {
        let aValue = a[sortField.value];
        let bValue = b[sortField.value];
        
        // 处理日期格式 (假设格式为 YYYY/MM/DD)
        if (sortField.value === 'shp_date') {
            aValue = aValue ? new Date(aValue.replace(/\//g, '-')).getTime() : 0;
            bValue = bValue ? new Date(bValue.replace(/\//g, '-')).getTime() : 0;
        }
        
        if (sortOrder.value === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });
}

function query(){
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
	firstPageNo.value = 1;
	uni.request({
	    url: urlPrefix + "/cutProGress/query",
	    data: {
	        "pageNo": 1,
	        "pageSize": firstPageSize.value,
			"startTime": startTime.value,
			"endTime": endTime.value,
			"brand": brand.value,
			"devType": devType.value,
			"externalStatus":externalStatus.value,
			"cinStartTime": cinStartTimeTamp.value,
			"cinEndTime": cinEndTimeTamp.value
	    },
	    method: "GET"
	}).then(res => {
		totalOrderQuantityDeal(res.data.data.list[0]);
		firstPageNo.value = 1;
	    tableData.value = res.data.data.list;
		pageCount.value = res.data.data.total;
		
		// 如果有排序字段，应用排序
		if (sortField.value) {
		    sortTableData();
		}
		
		uni.hideLoading();
	}).catch(err => {
		uni.hideLoading();
	    console.log(err)
	})
}

function totalOrderQuantityDeal(res){
	if(res !== undefined){
		totalOrderQuantity1.value = res.total_order_quantity1;
		totalOrderQuantity2.value = res.total_order_quantity2;
		totalOrderQuantity3.value = res.total_order_quantity3;
		totalOrderQuantity4.value = res.total_order_quantity4;
		totalOrderQuantity5.value = res.total_order_quantity5;
		totalOrderQuantity6.value = res.total_order_quantity6;
		totalOrderQuantity7.value = res.total_order_quantity7;
	}else{
		totalOrderQuantity1.value = 0;
		totalOrderQuantity2.value = 0;
		totalOrderQuantity3.value = 0;
		totalOrderQuantity4.value = 0;
		totalOrderQuantity5.value = 0;
		totalOrderQuantity6.value = 0;
		totalOrderQuantity7.value = 0;
	}
}


//取消投入、产出
function cancel(ord_no,item_no){
	uni.showModal({
	    title: '提示',
	    content: ord_no +': 确定取消投入吗？',
	    success: function (res) {
	        if (res.confirm) {
				uni.request({
				   url: urlPrefix + "/cutProGress/update",
				   data: {
					   "ord_no": ord_no,
					   "item_no": item_no
				   },
				   method: "POST"
				}).then(res => {
				   uni.hideLoading();
				   showTip('success', res.data.data);
				   query();
				}).catch(err => {
				   uni.hideLoading();
				   console.log(err)
				})
	        } else if (res.cancel) {
	            console.log('用户点击取消');
	        }
	    }
	});
}

// 提示信息
function showTip(type, message) {
    tipType.value = type
    tipMessage.value = message
    tipPopup.value.open()
}

//返回首页
function back() {
    let back = getCurrentPages();
    if(back && back.length > 1) {
    	uni.navigateBack({  
    	  delta: 1  
    	});  
    } else {  
    	history.back();  
    }
}

async function firstChange(e) {
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
    firstPageNo.value = e.current;
    await uni.request({
        url: urlPrefix + "/cutProGress/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
			"startTime": startTime.value,
			"endTime": endTime.value,
			"brand": brand.value,
			"devType": devType.value,
			"externalStatus":externalStatus.value,
			"cinStartTimeTamp": cinStartTimeTamp.value,
			"cinEndTimeTamp": cinEndTimeTamp.value
        },
        method: "GET"
    }).then(res => {
        tableData.value = res.data.data.list;
		totalOrderQuantityDeal(res.data.data.list[0]);
		
		// 如果有排序字段，应用排序
		if (sortField.value) {
		    sortTableData();
		}
		
		uni.hideLoading();
    }).catch(err => {
		uni.hideLoading();
        console.log(err)
    })
}

const tableRef = ref();

//获取数据
function queryAllDevType() {
    uni.request({
        url: urlPrefix + "/cutProGress/queryAllDevType",
        method: "GET"
    }).then(res => {
        console.log(res.data.data);
		devTypes.value = res.data.data;
    }).catch(err => {
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

//获取数据
async function getData() {
	tableData.value = [];
    if (tableRef.value) {
        tableRef.value.clearSelection();
    }
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
    await uni.request({
        url: urlPrefix + "/cutProGress/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
            "startTime": startTime.value,
            "endTime": endTime.value,
            "brand": brand.value,
            "devType": devType.value,
			"externalStatus":externalStatus.value,
			"cinStartTime": cinStartTime.value,
			"cinEndTime": cinEndTime.value
        },
        method: "GET"
    }).then(res => {
		totalOrderQuantityDeal(res.data.data.list[0]);
        dataList.value = res.data.data.list;
        pageCount.value = res.data.data.total;
		tableData.value = res.data.data.list;
		
		// 如果有排序字段，应用排序
		if (sortField.value) {
		    sortTableData();
		}
		
		uni.hideLoading();
    }).catch(err => {
		uni.hideLoading();
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

//预加载
onMounted(async () => {
	if(1 == 1){
		// 创建一个新的日期对象e
		var date = new Date();
		
		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		
		// 格式化日期为yyyy/MM/DD的样式
		var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
		
		
		endTimeText.value = formattedDate;
		cinEndTimeText.value = formattedDate;
		cinStartTimeText.value = formattedDate;
	}
	if(1 == 1){
		// 创建一个新的日期对象e
		var date = new Date();
		
		// 获取当前日期的天数
		var temp = date.getDate();
		
		// 将日期减去一天
		date.setDate(temp - 1);
		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		
		var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
		startTimeText.value = formattedDate;
	}
    await getData();
	queryAllDevType();
	getBrands();
})

//登录校验
function loginCheck(){
	if(uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == ''){
		uni.navigateTo({
			url: `/pages/login/login`,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}
}

onShow(async (props) => {
	loginCheck();
})
</script>

<style lang="scss">
page {
    width: 100%;
    height: 100%;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;
}

.back {
	width: 50px;
	height: 50px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    position: absolute;
    left: 1.5%;
    top: 3%;
	border-radius: 50%;
	box-shadow: 0 0 5px gray;
    cursor: pointer;
    z-index: 1;
}


.search {

	display: flex;
	align-items: center;
	margin-left: 5%;
	margin-top: 2%;
	
	.inputDate{
		width: 110px;
		margin-left: 5px;
		z-index: 999;
	}
	
	.search button{
		width: 20%;
	}
}

.container {
    width: 100%;
    height: 100%;
    padding: 1%;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0 0 1px 5px #dddddd;
    position: relative;
}

.right-top-top {
    display: flex;
}

.inpBr {
    width: 15%;
    margin-left: 10rpx;
}

.left-bottom {
    width: 100%;
    height: 10%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.uni-pagination__total {
    margin-right: 0.4375rem !important;
}

.uni-pagination__num-tag {
    min-width: 1.875rem !important;
    background-color: #F0F0F0 !important;
}

.page--active {
    color: white !important;
    background-color: deeppink !important;
}

.title {
    width: 100%;
    overflow-x: auto; /* 添加横向滚动条 */
    height: 85%;
    margin-bottom: 1%;
    margin-top: 1%;
    display: block;
	table-layout: fixed; /* 使用固定布局 */
	border: 2px solid #ddd;

}

.table-container {
    width: 100%;
    overflow-x: auto; /* 使表格容器可横向滚动 */
}

table {
	background-color: #fff;
	table-layout: fixed; /* 使用固定布局 */
    border-collapse: collapse;
    position: relative;
    border-radius: 5px;
    box-sizing: border-box;
}

tbody {
}

thead {
	background-color: #F0F0F0;
}

.rowClass {
	width: 150px;
    height: 2.5rem;
    border-bottom: 2px solid #ddd;
	border-right: 2px solid #ddd;
    padding: 8px;
    text-align: center;
    box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
    overflow: visible; /* 允许内容溢出 */
    white-space: nowrap; /* 禁止换行 */
}

.rowClass3{
	width: 150px;
	height: 2.5rem;
	border-right: 2px solid #ddd;
	padding: 8px;
	text-align: center;
	box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
	overflow: visible; /* 允许内容溢出 */
	white-space: nowrap; /* 禁止换行 */
}
.rowClass4{
	width: 30px;
	height: 1rem;
	border-right: 2px solid #ddd;
	padding: 8px;
	text-align: center;
	box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
	// white-space: nowrap; /* 禁止换行 */
}

.rowClass2 {
	width: 30px;
    height: 1rem;
	border-bottom: 2px solid #ddd;
	border-right: 2px solid #ddd;
    padding: 8px;
    text-align: center;
    box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
    // white-space: nowrap; /* 禁止换行 */
}

tr {
    display: table-row;
}

img {
    width: 50px;
    height: auto;
}

.left-bottom {
	width: 100%;
	height: 10%;
	display: flex;
	justify-content: center;
	align-items: center;
	
	
	.uni-pagination__total {
		margin-right: 0.4375rem !important;
	}
	
	.uni-pagination__num-tag {
		min-width: 1.875rem !important;
		background-color: #F0F0F0 !important;
	}
	
	.page--active {
		color: white !important;
		background-color: deeppink !important;
	}
}


.updateSpecificationPopup {
  .updateSpecificationBox {
	width: 96vw;
	height: 90vh;
	
	border-radius: 1vw;
	background-color: white;
	
	.updateSpecificationData {
	  width: 100%;
	  position: relative;
	  display: flex;
	  justify-content: center;
	  align-items: flex-start;
	  flex-wrap: wrap;
	  height: 85%;
	  
	  .updateSpecificationAttribute {
		width: 35%;
		margin-left: 15%;
		
		.uni-easyinput {
		  width: 70%;
		  margin-left: 1rem;
		}
		
		.uni-stat__select {
		  width: 70%;
		  margin-left: 1rem;
		}
		
		.uni-numbox {
		  margin-left: 1rem;
		}
		
		.uni-file-picker {
		  margin-left: 1rem;
		}
	  }
	}
  }
}

.highlight-green {
        background-color: #c3eed3;
}

/* 固定表头 */
thead th {
	position: sticky;
	top: 0; /* 表头始终固定在顶部 */
	background-color: #f2f2f2; /* 表头背景色 */
	z-index: 2; /* 确保表头在最上层 */
}

thead th::after{
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 2px;
	background-color: #ddd;
}

thead th{
	border-bottom: none;
}


.flex-box{
	display: flex;
	align-items: center;

}

.flex-row::after{
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 2px;
	background-color: #ddd;
	}
</style>