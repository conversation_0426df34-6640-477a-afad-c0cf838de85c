<template>
	<view class="wrap">
		<image 
			:src="src"
			:mode="mode"
			:class="imageClass"
			@error="imageError">
		</image>
	</view>
</template>

<script setup>
	import { watch, onBeforeMount, ref } from 'vue'

	const props = defineProps({
		src: { // 将 url 改为 src
			type: String,
			default: ''
		},

		mode: {
			type: String,
			default: 'widthFix'
		},

		width: {
			type: String,
			default: '200rpx'
		},

		height: {
			type: String,
			default: '200rpx'
		},

		radius: {
			type: String,
			default: '0'
		},

		imageClass: { // 用于接收自定义类名
			type: String,
			default: ''
		}
	})

	const src = ref('');
	let storageKey = '';

	watch(() => props.src, () => {
		getImageCache();
	});

	onBeforeMount(() => {
		getImageCache();
	});

	function imageError(e) {
		if (storageKey) {
			uni.removeStorageSync(storageKey);
		}
		console.error('Image error', e);
	}

	async function getImageCache() {
		// #ifdef APP-PLUS
		var result = await fetchImageCache(props.src);

		if (result) {
			src.value = result.path;
			storageKey = result.storageKey;
		} else {
			src.value = props.src;
		}
		// #endif
		
		// #ifndef APP-PLUS
		src.value = props.src;
		// #endif
	}

	function fetchImageCache(filePath) {
		// #ifdef APP-PLUS
		return new Promise((resolve, reject) => {
			const isLocalPath = (path) => !/^https?:\/\//.test(path);

			if (isLocalPath(filePath)) {
				resolve({ path: filePath });
				return;
			}

			let fileMd5 = '';
			try {
				fileMd5 = stringToBase64(filePath);
			} catch (e) {
				fileMd5 = filePath;
			}

			let storageKey = 'IMAGE_CACHE_INFO_' + fileMd5;
			const cacheFileInfo = uni.getStorageSync(storageKey);

			if (cacheFileInfo) {
				resolve({ path: cacheFileInfo, storageKey });
				return;
			} else {
				uni.downloadFile({
					url: filePath,
					success: (res) => {
						if (res.statusCode === 200) {
							uni.saveFile({
								tempFilePath: res.tempFilePath,
								success: (res2) => {
									const savedFilePath = res2.savedFilePath;
									uni.setStorageSync(storageKey, savedFilePath);
									resolve({ path: savedFilePath, storageKey });
								},
								fail: () => {
									resolve(filePath);
								}
							});
						} else {
							console.log('下载临时文件失败');
							resolve(filePath);
						}
					},
					fail: () => {
						console.log('下载失败');
						resolve(filePath);
					}
				});
			}
		});
		// #endif

		// #ifndef APP-PLUS
		return new Promise((resolve) => {
			resolve(filePath);
		});
		// #endif
	}
</script>

<style>
	
	.picture-popup {
		.watermark {
			position: relative;
			transition: all 0.15s ease-in-out;
	
			&::before {
				content: "STELLA";
				position: absolute;
				top: 2px;
				left: 2px;
				color: black;
				font-size: 18px;
				font-weight: bold;
				text-shadow: 0 0 5px white;
				z-index: 1;
				pointer-events: none;
			}
	
			&:active {
				transform: scale(1.5);
			}
	
			.img {
				min-width: 100px;
				min-height: 100px;
				max-width: 90vw;
				max-height: 90vh;
				border-radius: 10px;
				box-shadow: 0 0 10px white;
	
				&:active {
					box-shadow: 0 0 1px white;
				}
			}
		}
	
		.watermark2 {
			position: relative;
			transition: all 0.15s ease-in-out;
	
			&::before {
				content: "STELLA";
				position: absolute;
				top: 2px;
				left: 2px;
				color: black;
				font-size: 18px;
				font-weight: bold;
				text-shadow: 0 0 5px white;
				z-index: 1;
				pointer-events: none;
			}
	
			&::after {
				content: "双峰兴昂";
				position: absolute;
				top: 18px;
				left: 2px;
				color: black;
				font-size: 18px;
				font-weight: bold;
				text-shadow: 0 0 5px white;
				z-index: 1;
				pointer-events: none;
			}
	
			&:active {
				transform: scale(1.5);
			}
	
			.img {
				min-width: 100px;
				min-height: 100px;
				max-width: 90vw;
				max-height: 90vh;
				border-radius: 10px;
				box-shadow: 0 0 10px white;
	
				&:active {
					box-shadow: 0 0 1px white;
				}
			}
		}
	}
</style>