<!-- 工序流程选项弹框 - 加工 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { deptMap1, optionTypeMap, optionListMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 提示弹框
const tipPopup = ref()

// 工序流程选项弹框
const flowOptionPopup = ref()

// 详细制程
const proSeq = ref('')

// 工序流程选项下标
const flowOptionIndex = ref(0)

// 工序流程选项类型
const flowOptionType = ref('')

// 工序流程详情
const flowDetail = inject('flowDetail')

// 工序流程选项列表
const flowOptionList = ref([])

// 聚焦工序流程选项输入框
const focusFlowOptionInput = ref(false)
// 工序流程选项搜索框
const flowOptionSearch = ref('')
// 聚焦工序流程选项搜索框
const focusFlowOptionSearch = ref(false)

// 获取工序流程选项列表
async function getFlowOptionList(param1, param2) {
  await uni.request({
    url: urlPrefix + '/sop/getFlowOptionList',
    method: 'POST',
    data: {
      type: optionTypeMap.get(param1),
      dept: deptMap1.get(param2)
    }
  }).then(res => {
    if (res.data.code) {
      flowOptionList.value = res.data.data ? res.data.data : []
    } else {
      flowOptionList.value = []
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示工序流程选项弹框
async function showFlowOptionPopup(param1, param2) {
  flowOptionIndex.value = param1
  proSeq.value = param2
  
  flowOptionType.value = param2 + '-' + (param1 + 1)
  
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await getFlowOptionList(flowOptionType.value, proSeq.value)
  
  uni.hideLoading()
  
  flowOptionPopup.value.open()
}

// 选择工序流程选项
function selectFlowOption(param) {
  if (flowDetail['processOption' + (flowOptionIndex.value + 1)]) {
    if (flowDetail['processOption' + (flowOptionIndex.value + 1)].includes('\n' + param.toUpperCase() + '\n')) {
      flowDetail['processOption' + (flowOptionIndex.value + 1)] = flowDetail['processOption' + (flowOptionIndex.value + 1)].replace(new RegExp(`\n${param}\n`, 'g'), '\n')
    } else if (flowDetail['processOption' + (flowOptionIndex.value + 1)].startsWith(param + '\n')) {
      flowDetail['processOption' + (flowOptionIndex.value + 1)] = flowDetail['processOption' + (flowOptionIndex.value + 1)].replace(param + '\n', '')
    } else {
      flowDetail['processOption' + (flowOptionIndex.value + 1)] += (param + '\n')
    }
  } else {
    flowDetail['processOption' + (flowOptionIndex.value + 1)] = param + '\n'
  }
  
  if (proSeq.value === '1F' && (flowOptionIndex.value + 1) === 2) {
    flowDetail.processOption3 = flowDetail.processOption2?.split('\n')?.filter(item => item.length > 0 && !isNaN(item))?.map(item => item * 2)?.join('\n')
  }
}

defineExpose({
  showFlowOptionPopup
})
</script>

<template>
  <uni-popup
    ref="flowOptionPopup"
    type="center"
    class="flow-option-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择{{ optionListMap.get(proSeq)[flowOptionIndex] }}
      </view>
      
      <view class="flow-option-input flex-row-center">
        <textarea
          v-model="flowDetail['processOption' + (flowOptionIndex + 1)]"
          @focus="focusFlowOptionInput = true"
          @blur="focusFlowOptionInput = false"
          :placeholder="'请输入' + optionListMap.get(proSeq)[flowOptionIndex]"
          class="textarea"
          :style="{
            boxShadow: focusFlowOptionInput ? '0 0 5px blue' : '0 0 5px gray'
          }"
        ></textarea>
        
        <view
          v-show="flowDetail['processOption' + (flowOptionIndex + 1)]"
          @click="flowDetail['processOption' + (flowOptionIndex + 1)] = ''"
          class="clear-textarea button"
        >
          <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
        </view>
      </view>
      
      <view class="flow-option-search flex-row-center">
        <input
          v-model="flowOptionSearch"
          @focus="focusFlowOptionSearch = true"
          @blur="focusFlowOptionSearch = false"          
          placeholder="请输入搜索内容"
          type="text"
          class="input"
          :style="{
            boxShadow: focusFlowOptionSearch ? '0 0 5px blue' : '0 0 5px gray'
          }"
        />
        
        <view
          v-show="flowOptionSearch"
          @click="flowOptionSearch = ''"
          class="clear-input button"
        >
          <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
        </view>
      </view>
      
      <view class="flow-option-list">
        <view
          v-for="(item, index) in flowOptionList"
          v-show="item.includes(flowOptionSearch.toUpperCase())"
          class="flow-option-item flex-row-center"
        >
          <view
            @click="selectFlowOption(item)"
            class="button"
            :style="{
              color: flowDetail['processOption' + (flowOptionIndex + 1)] && (flowDetail['processOption' + (flowOptionIndex + 1)].includes('\n' + item.toUpperCase() + '\n') || flowDetail['processOption' + (flowOptionIndex + 1)].startsWith(item.toUpperCase() + '\n')) ? 'darkorchid' : 'darkmagenta'
            }"
          >
            {{ item }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.flow-option-popup {
  .container {
    width: 500px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .flow-option-input {
      width: 100%;
      height: 160px;
      position: relative;
      
      .textarea {
        width: 350px;
        height: 140px;
        padding: 5px;
      }
      
      .clear-textarea {
        width: 70px;
        height: 30px;
        position: absolute;
        right: 80px;
        bottom: 15px;
      }
    }
    
    .flow-option-search {
      width: 100%;
      height: 50px;
      position: relative;
      
      .input {
        width: 350px;
        height: 40px;
        padding: 5px;
      }
      
      .clear-input {
        width: 70px;
        height: 30px;
        position: absolute;
        right: 80px;
        bottom: 10px;
      }
    }
    
    .flow-option-list {
      width: 100%;
      height: 360px;
      overflow: auto;
      
      .flow-option-item {
        width: 100%;
        margin: 15px 0;
        
        .button {
          width: 350px;
          min-height: 40px;
          padding: 5px;
          color: darkmagenta;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .textarea {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-textarea-placeholder), &:deep(.uni-textarea-textarea) {
      width: 100%;
      height: 100%;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>