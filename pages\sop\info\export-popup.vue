<!-- 导出弹框 -->
<script setup>
import { ref, inject } from 'vue'
import { operationMap, proSeqMap } from '@/pages/sop/common/commonMap.js'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 提示弹框
const tipPopup = ref()

// 导出弹框
const exportPopup = ref()

// 品牌
const brand = inject('brand')
// 型体
const model = ref('')
// 制程
const operation = ref('')
// 详细制程
const proSeq = ref('')
// 主要代码
const rtgCode = ref('')

// 客户 Excel 列表
const brandList = ref(['UA', 'SC', 'LM'])

// 显示导出弹框
function showExportPopup(param) {
  model.value = param.model
  operation.value = param.operation
  proSeq.value = param.proSeq
  rtgCode.value = param.rtgCode
  exportPopup.value.open()
}

// 通用 Excel 导出
function generalExcelExport() {
  exportPopup.value.close()
  
  let downloadUrl = urlPrefix
  if (operation.value === '1') {
    switch (proSeq.value) {
      case '1F':
        downloadUrl += `/sop/all/ysexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
        break
      case '1G':
        downloadUrl += `/sop/all/dxexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
        break
      case '1H':
        downloadUrl += `/sop/all/rqexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
        break
      case '1M':
        downloadUrl += `/sop/all/lyexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
        break
      case '1E':
        downloadUrl += `/sop/all/ymexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
        break
      case '1D':
        downloadUrl += `/sop/all/ptexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
        break
      default:
        tipPopup.value.showTipPopup('warn', '暂无此制程模板！')
        return
    }
  } else {
    downloadUrl += `/sop/export/excel?brand=${brand.value}&model=${model.value}&operation=${operation.value}&proSeq=${proSeq.value}&rtgCode=${rtgCode.value}`
  }
  
  uni.showLoading({
    title: '下载中...',
    mask: true
  })
  
  // #ifdef WEB
  uni.request({
    url: downloadUrl,
    method: 'GET',
    responseType: 'arraybuffer'
  }).then(res => {
    const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    let url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    if (operation.value === '1') {
      a.download = `${model.value}${proSeqMap.get(proSeq.value)}${rtgCode.value}.xlsx`
    } else {
      a.download = `${model.value}${operationMap.get(operation.value)}${rtgCode.value}.xlsx`
    }
    a.style.display = 'none'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    tipPopup.value.showTipPopup('success', '下载成功！')
    uni.hideLoading()
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '下载失败！')
    uni.hideLoading()
  })
  // #endif
  
  // #ifdef APP
  const downloadTask = uni.downloadFile({
    url: downloadUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        tipPopup.value.showTipPopup('success', '下载成功！')
        setTimeout(() => {
          uni.openDocument({
            filePath: res.tempFilePath
          })
        }, 1000)
      }
      uni.hideLoading()
    },
    fail: (err) => {
      tipPopup.value.showTipPopup('error', '下载失败！')
      uni.hideLoading()
    }
  })
  // #endif
}

// 客户 Excel 导出
function brandExcelExport() {
  exportPopup.value.close()
  
  let downloadUrl = urlPrefix
  if (brand.value === 'UA') {
    if (operation.value === '1') {
      switch (proSeq.value) {
        case '1F':
          downloadUrl += `/sop/ua/ysexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
          break
        case '1G':
          downloadUrl += `/sop/ua/dxexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
          break
        case '1H':
          downloadUrl += `/sop/ua/rqexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
          break
        case '1M':
          downloadUrl += `/sop/ua/lyexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
          break
        case '1E':
          downloadUrl += `/sop/ua/ymexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
          break
        case '1D':
          downloadUrl += `/sop/ua/ptexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
          break
        default:
          tipPopup.value.showTipPopup('warn', '暂无此制程模板！')
          return
      }
    } else if (operation.value === '4') {
      downloadUrl += `/sop/ua/xmexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
    } else if (operation.value === '5') {
      downloadUrl += `/sop/ua/bcpexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
    } else if (operation.value === '6') {
      downloadUrl += `/sop/ua/cxexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
    } else {
      tipPopup.value.showTipPopup('warn', '暂无此制程模板！')
      return
    }
  } else if (brand.value === 'SC') {
    if (operation.value === '4') {
      downloadUrl += `/sop/sc/xmexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
    } else if (operation.value === '5') {
      downloadUrl += `/sop/sc/bcpexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
    } else if (operation.value === '6') {
      downloadUrl += `/sop/sc/cxexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
    } else {
      tipPopup.value.showTipPopup('warn', '暂无此制程模板！')
      return
    }
  } else if (brand.value === 'LM') {
    if (operation.value === '4') {
      downloadUrl += `/sop/lm/xmexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
    } else if (operation.value === '5') {
      downloadUrl += `/sop/lm/bcpexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
    } else if (operation.value === '6') {
      downloadUrl += `/sop/lm/cxexport/excel?model=${model.value}&operation=${operation.value}&rtgCode=${rtgCode.value}`
    } else {
      tipPopup.value.showTipPopup('warn', '暂无此制程模板！')
      return
    }
  } else {
    tipPopup.value.showTipPopup('warn', '暂无此品牌模板！')
    return
  }
  
  uni.showLoading({
    title: '下载中...',
    mask: true
  })
  
  // #ifdef WEB
  uni.request({
    url: downloadUrl,
    method: 'GET',
    responseType: 'arraybuffer'
  }).then(res => {
    const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    let url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    if (operation.value === '1') {
      a.download = `${model.value}${proSeqMap.get(proSeq.value)}${rtgCode.value}.xlsx`
    } else {
      a.download = `${model.value}${operationMap.get(operation.value)}${rtgCode.value}.xlsx`
    }
    a.style.display = 'none'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    tipPopup.value.showTipPopup('success', '下载成功！')
    uni.hideLoading()
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '下载失败！')
    uni.hideLoading()
  })
  // #endif
  
  // #ifdef APP
  const downloadTask = uni.downloadFile({
    url: downloadUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        tipPopup.value.showTipPopup('success', '下载成功！')
        setTimeout(() => {
          uni.openDocument({
            filePath: res.tempFilePath
          })
        }, 1000)
      }
      uni.hideLoading()
    },
    fail: (err) => {
      tipPopup.value.showTipPopup('error', '下载失败！')
      uni.hideLoading()
    }
  })
  // #endif
}

defineExpose({
  showExportPopup
})
</script>

<template>
  <uni-popup
    ref="exportPopup"
    type="center"
    class="export-popup"
  >
    <view class="container">
      <view class="title">
        请选择导出方式
      </view>
      
      <view class="description">
        {{ operation === '1' ? proSeqMap.get(proSeq) : operationMap.get(operation) }} - {{ model }} - {{ rtgCode }}
      </view>
      
      <view class="type">
        <view @click="generalExcelExport()" class="general button">
          通用 Excel 模板
        </view>
        
        <view
          v-show="brandList.includes(brand)"
          @click="brandExcelExport()"
          class="brand button"
        >
          客户 Excel 模板
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.export-popup {
  .container {
    min-width: 300px;
    padding: 0 10px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 22px;
      font-weight: bold;
    }
    
    .description {
      width: 100%;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
    
    .type {
      display: flex;
      justify-content: center;
      align-items: center;
      
      .general, .brand {
        padding: 10px;
        margin: 10px 15px 15px 15px;
        font-size: 20px;
      }
      
      .general {
        color: darkred;
      }
      
      .brand {
        color: lightseagreen;
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
}
</style>