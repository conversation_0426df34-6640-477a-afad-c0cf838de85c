<template>
	<view class="container">
		
		<view style="display: flex; align-items: center;">
			<view class="back">
				<uni-icons @click="back" type="back" size="36"></uni-icons>
			</view>
			
			<view style="width: 100%; text-align: center; font-size: 24px; margin-left: -50px;">
				<text>鞋面小組排程表</text>
			</view>
		</view>

	

		<view class="top" style="margin-top: 1px">
			<view style="margin-left: 20px; display: flex; align-items: center;">
				<text style="margin-right: 10px;">制鞋組別</text>
			    <input type="text"   class="bordered-input" placeholder="請輸入制鞋組別"  v-model="empName" />




	<!-- 			<text style="margin-right: 10px;margin-left: 15px">出貨日期</text>

				<picker mode="date" :value="startTime" @change="bindStartDateChange">
					<view style="padding: 8px;background-color: white;width: 90px" v-if="startTime == ''"
						class="uni-input">請選擇</view>
					<view style="padding: 8px;background-color: white;width: 90px" v-if="startTime != ''"
						class="uni-input">{{startTimeText}}</view>
				</picker>




				<text style="margin-left: 5px;">~</text>

				<picker mode="date" :value="endTime" @change="bindEndDateChange" style="margin-left: 5px">
					<view style="padding: 8px;background-color: white; width: 90px;" v-if="endTime == ''"
						class="uni-input">請選擇</view>
					<view style="padding: 8px;background-color: white;width: 90px" v-if="endTime != ''"
						class="uni-input">{{endTimeText}}</view>
				</picker> -->
				
				
				<text style="margin-right: 10px;margin-left: 15px;">設定日期</text>
				
				<picker mode="date" :value="endTime2" @change="bindEndDateChange2">
					<view style="padding: 8px;background-color: white; width: 90px;" v-if="endTime2 == ''"
						class="uni-input">請選擇</view>
					<view style="padding: 8px;background-color: white;width: 90px" v-if="endTime2 != ''"
						class="uni-input">{{endTimeText2}}</view>
				</picker>
				

				<button type="success" @click="queryData()" class="addButton"
					style="background-color: #18bc37; color: white; font-weight: bold; margin-left: 15px;">
					査詢
				</button>


			</view>

		<!-- 	// #ifdef H5
			<view style="  display: flex;">

				<strong>
					<button type="success" @click="exportData()" class="addButton"
						style="background-color: #e43d33; color: white; font-weight: bold; margin-left: 10px;">
						导出
					</button>
				</strong>
			</view>

			// #endif -->

		</view>




		<view class="table-container">
			<table>
				<!-- 表头行 -->
				<tr style="background-color: #fdf6e3;z-index: 999">




					<th style="width: 5vw;" align="center">制鞋組別</th>
						<th style="width: 5vw;" align="center">當日投入數量</th>
					<th style="width: 5vw;" align="center">單號</th>
					<th style="width: 5vw;" align="center">樣品類型</th>
					<th style="width: 5vw;" align="center">鞋圖</th>
					<th style="width: 5vw;" align="center">出貨日</th>


					<th style="width: 5vw;" align="center">訂單數量</th>
					<th style="width: 5vw;" align="center">鞋面產出</th>
					<th style="width: 5vw;" align="center">設定日期</th>
					<th style="width: 5vw;" align="center">目標PPH</th>
					<th style="width: 5vw;" align="center">達成率</th>
					<th style="width: 5vw;" align="center">异常原因</th>

				</tr>
				<tr v-for="(item, index) in dataList" :key="item" class="datalist-tr">



					<td  v-if="shouldDisplayPdLine(index)" :rowspan="getRowspan(index)" align="center">{{ item.emp_name }}</td>
						<td align="center">{{ item.bar_qty2 }}</td>
					<td align="center">{{ item.ord_no }}</td>
					<td align="center">{{ item.dev_type }}</td>
					<td align="center">
						<image :src="item.pic" @click="previewImage(item.pic)" class="img"
							mode="aspectFit"></image>
					</td>
					<td align="center"> {{ item.shp_date.split(' ')[0] }}</td>
				
					
					<td align="center">{{ item.tot_qty }}</td>
					<td  v-if="shouldDisplayPdLine(index)" :rowspan="getRowspan(index)" align="center">{{ item.total }}</td>
					<td  v-if="shouldDisplayPdLine(index)" :rowspan="getRowspan(index)" align="center">  {{ item.prd_date.split(' ')[0] }}</td>
					<td  v-if="shouldDisplayPdLine(index)" :rowspan="getRowspan(index)" align="center">{{ item.run_rate }}</td>
					<td  v-if="shouldDisplayPdLine(index)" :rowspan="getRowspan(index)" align="center">{{ item.rate1 }}%</td> 
	<!-- 				<td  v-if="shouldDisplayPdLine(index)" :rowspan="getRowspan(index)" align="center">{{ calculateEfficiencyPercentage(item) }}</td> -->

					<td align="center">{{ item.pb_desc }}</td>



				</tr>
			</table>
		</view>

		<view class="picture-popup">
			<uni-popup ref="picturePopup" type="center">
				<view class="watermark">
					<img :src="preViewPicture" alt="" class="img">
				</view>
			</uni-popup>
		</view>


	</view>

</template>

<script setup>
	import {
		computed,
		onMounted,
		ref,
		reactive,
		watch
	} from 'vue'
	import {
		onPullDownRefresh
	} from '@dcloudio/uni-app'
	import {
		onShow
	} from '@dcloudio/uni-app';
	import urlPrefix from '@/pages/common/urlPrefix.js'


  // 定义一个方法来计算效率百分比
    function calculateEfficiencyPercentage(item) {
		console.log(item.run_rate)
      if (item.run_rate == 0) {
        return '0%';
      } else {
        return ((item.total / item.run_rate) * 100).toFixed(2) + '%';
      }
    }


	const dataList = ref([])

	const empName = ref('');

	const preViewPicture = ref()

	const picturePopup = ref();

	//日期选择开始
	const date = new Date();
	date.setHours(date.getHours() - 24);
	const startTime = ref(date.getTime())
	const datetimePickerSt = ref()
	const startTimeText = ref('')



 // 添加辅助函数到 script 部分
 function shouldDisplayPdLine(rowIndex) {
   const info = mergeInfo.value.find(info => info.start === rowIndex)
   return info !== undefined
 }
 
 function getRowspan(rowIndex) {
   const info = mergeInfo.value.find(info => info.start === rowIndex)
   return info ? info.count : 1
 }
 
 function calculateMergeInfo(data) {
   let mergeInfo = []
   let lastPdLine = null
   let count = 0
 
   data.forEach((item, index) => {
     if (item.emp_name !== lastPdLine) {
       if (lastPdLine !== null) {
         mergeInfo.push({ start: index - count, count: count })
       }
       lastPdLine = item.emp_name
       count = 1
     } else {
       count++
     }
   })
 
   if (lastPdLine !== null) {
     mergeInfo.push({ start: data.length - count, count: count })
   }
   return mergeInfo
 }
 
     const mergeInfo = ref([]) 


	function previewImage(url) {
		preViewPicture.value = url;
		picturePopup.value.open();
	}

	function bindStartDateChange(e) {
		startTime.value = new Date(e.detail.value).getTime();
		startTimeText.value = e.detail.value;
	}

	function bindEndDateChange(e) {
		endTime.value = new Date(e.detail.value).getTime();
		endTimeText.value = e.detail.value;
	}


	function openSt() {
		datetimePickerSt.value.open();
	}

	function confirmSt(e) {
		console.log('confirm', e);
		// 创建一个新的日期对象e
		var date = new Date(e.value);

		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
		startTimeText.value = formattedDate;
	}





	//日期选择结束
	const date2 = new Date();
	date2.setHours(date2.getHours() - 24);
	const endTime = ref(date2.getTime())
	const datetimePickerEnd = ref()
	const endTimeText = ref()


	function bindBrandChange(e) {
		console.log(e)
		brandNo.value = e
	}
	
	
	
	//日期选择结束
	const date3 = new Date();
	date3.setHours(date3.getHours() - 24);
	const endTime2 = ref(date3.getTime())
	const datetimePickerEnd2 = ref()
	const endTimeText2 = ref()
	
	function bindEndDateChange2(e) {
		endTime2.value = new Date(e.detail.value).getTime();
		endTimeText2.value = e.detail.value;
	}
	
	
	
	



	function openEnd() {
		datetimePickerEnd.value.open();
	}


	function confirmEnd(e) {
		console.log('confirm', e);
		// 创建一个新的日期对象e
		var date = new Date(e.value);

		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
		endTimeText.value = formattedDate;
	}





	// 提示信息
	function showTip(type, message) {
		tipType.value = type
		tipMessage.value = message
		tipPopup.value.open()
	}


	//返回首页
	function back() {
		let back = getCurrentPages();
		console.log('back.length：' + back.length)
		if (back && back.length > 1) {
			uni.navigateBack({
				delta: 1
			});
		} else {
			history.back();
		}
	}




	//获取数据
	async function queryData() {


		uni.showLoading({
			title: '加载中',
			mask: true // 设置遮罩层
		});

		if (tableRef.value) {
			tableRef.value.clearSelection();
		}
		await uni.request({
			url: urlPrefix + "/api/shoeUpperScheduling/list",
			data: {
				
				"prdDate":endTimeText2.value,
				"empName":empName.value,
				"beginShpDate":startTimeText.value,
				"endShpDate":endTimeText.value
			},
			method: "GET"
		}).then(res => {
			if (res.data.data && res.data.data.length === 0) {
				uni.showToast({
				    title: '没有相关数据',
				    icon: 'none'
				});
			} 
			dataList.value = res.data.data;
			mergeInfo.value = calculateMergeInfo(dataList.value)
			uni.hideLoading();
		}).catch(err => {
			console.log(err)
			uni.hideLoading();
			uni.showToast({
				title: '获取数据失败..',
				icon: "error"
			});
		})
	}



	const tableRef = ref();


	//预加载
	onMounted(async () => {

		if (1 == 1) {
			// 创建一个新的日期对象e
			var date = new Date();

			// 获取年、月、日
			var year = date.getFullYear();
			var month = date.getMonth() + 1; // 月份从0开始，需要加1
			var day = date.getDate();

			// 格式化日期为yyyy/MM/DD的样式
			var formattedDate = year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day :
				day);

			endTimeText.value = formattedDate;
						endTimeText2.value = formattedDate;
		}
		if (1 == 1) {
			// 创建一个新的日期对象e
			var date = new Date();

			// 获取当前日期的天数
			var temp = date.getDate();

			// 将日期减去一天
			date.setDate(temp - 1);
			// 获取年、月、日
			var year = date.getFullYear();
			var month = date.getMonth() + 1; // 月份从0开始，需要加1
			var day = date.getDate();

			var formattedDate = year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day :
				day);
			startTimeText.value = formattedDate;
			
		}


		await queryData('')

	})
</script>

<style lang="scss">
	.img {
		max-width: 50px;
		max-height: 50px;
		/* 	height: 50px;*/
		border-radius: 10px;
		box-shadow: 0 0 5px gray;
		transition: all 0.05s ease-in-out;
		cursor: pointer;

		&:active {
			box-shadow: 0 0 1px gray;
			transform: scale(0.97);
		}
	}

	.back {
		width: 50px;
		height: 50px;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		transition: all 0.05s ease-in-out;
	}

	page {
		width: 100%;
		height: 100%;
		padding: 2.5% 2% 1.5% 2%;
		box-sizing: border-box;
		background-color: #fdf6e3;
	}

	.back {
		width: 50px;
		height: 50px;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		// position: absolute;
		left: 1.5%;
		top: 3%;
		cursor: pointer;
		z-index: 1;
	}

	.top {
		display: flex;
		justify-content: space-between;
		/* 使得子元素分布在两端 */
		align-items: center;
		/* 使得子元素垂直居中 */
		padding: 10px;
		/* 添加一些内边距 */
	}



	.addButton {
		/* 由于已经在内联样式中设置了背景颜色和字体颜色，这里可以省略这些属性 */
		// padding: 5px 12px; /* 添加一些内边距 */
		border: none;
		/* 移除边框 */
		border-radius: 5px;
		/* 添加圆角边框 */
		cursor: pointer;
		/* 鼠标悬停时显示手形光标 */
	}

	.container {
		width: 100%;
		height: 100%;
		padding: 1%;
		box-sizing: border-box;
		border-radius: 10px;
		box-shadow: 0 0 1px 5px #dddddd;
		position: relative;
	}

	.right-top-top {
		display: flex;
	}

	.inpBr {
		width: 15%;
		margin-left: 10rpx;
	}

	.left-bottom {
		width: 100%;
		height: 10%;
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
	}

	.uni-pagination__total {
		margin-right: 0.4375rem !important;
	}

	.uni-pagination__num-tag {
		min-width: 1.875rem !important;
		background-color: #F0F0F0 !important;
	}

	.page--active {
		color: white !important;
		background-color: deeppink !important;
	}


	.search {
		width: 100%;
		min-height: 10%;
		margin-bottom: 1%;
		box-sizing: border-box;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		position: relative;

		.searchSelect {
			width: 100%;
			margin-left: 10%;
			margin-right: 0.5%;
			font-size: 24px;
			font-weight: bold;
			padding: 10px;
			border-radius: 10px;
		}

		.search-brand {
			width: 10%;
			margin-left: 0.5%;
			margin-right: 0.5%;
			background: linear-gradient(to right bottom, orangered, pink);
			color: white;
			font-size: 24px;
			font-weight: bold;
			border-radius: 10px;
		}

		.search-type {
			width: 8%;
			margin-left: 0.5%;
			margin-right: 1%;
			background-color: #333;
			color: white;
			font-size: 24px;
			font-weight: bold;
			border-radius: 10px;
		}

		.search-input {
			width: 40%;
			padding: 10px;
			border: 2px solid gray;
			font-size: 24px;
			color: black;
			background-color: white;
			border-radius: 5px;

			&:hover {
				border: 2px solid black;
			}
		}

		.search-input-shoe-last {
			width: 35%;
			padding: 10px;
			border: 2px solid gray;
			font-size: 24px;
			color: black;
			background-color: white;
			border-radius: 5px;

			&:hover {
				border: 2px solid black;
			}
		}

		.search-clear {
			margin-left: -45px;
			margin-top: 2px;
			cursor: pointer;
			z-index: 1;
		}

		.search-clear-shoe-last {
			margin-left: -45px;
			margin-top: 2px;
			cursor: pointer;
			z-index: 1;
		}

		.search-icon-shoe-last {
			width: 5%;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			z-index: 1;
		}

		.search-list {
			width: calc(40% + 20px + 4px);
			height: 270px;
			position: absolute;
			left: 30%;
			top: 120%;
			z-index: 1;

			.search-box {
				max-height: 270px;
				text-align: center;
				border-radius: 5px;
				color: white;
				font-size: 18px;
				font-weight: bold;
				background-color: gray;
				overflow: auto;

				.search-item {
					box-sizing: border-box;
					padding: 15px 0;
					border-bottom: 1px solid white;
					cursor: pointer;

					&:hover {
						background-color: #aaaaaa;
					}
				}
			}

			&::before {
				content: "";
				position: absolute;
				left: calc(50% - 10px);
				top: -10px;
				border-top: 0px solid transparent;
				border-left: 10px solid transparent;
				border-bottom: 10px solid gray;
				border-right: 10px solid transparent;
			}
		}

		.search-list-shoe-last {
			width: calc(35% + 20px + 4px);
			height: 270px;
			position: absolute;
			left: 30%;
			top: 120%;
			z-index: 1;

			.search-box-shoe-last {
				max-height: 270px;
				text-align: center;
				border-radius: 5px;
				color: white;
				font-size: 18px;
				font-weight: bold;
				background-color: gray;
				overflow: auto;

				.search-item-shoe-last {
					box-sizing: border-box;
					padding: 15px 0;
					border-bottom: 1px solid white;
					cursor: pointer;

					&:hover {
						background-color: #aaaaaa;
					}
				}
			}

			&::before {
				content: "";
				position: absolute;
				left: calc(50% - 10px);
				top: -10px;
				border-top: 0px solid transparent;
				border-left: 10px solid transparent;
				border-bottom: 10px solid gray;
				border-right: 10px solid transparent;
			}
		}
	}


	.brand-popup {
		.brand-box {
			width: 80vw;
			height: 80vh;
			border-radius: 1vw;
			background-color: #fdf6e3;
			overflow: auto;

			.brand-part {
				width: 100%;
				margin-bottom: 1%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.brand-initial {
					width: 100%;
					margin-left: 1.5%;
					margin-top: 1%;
					font-size: 32px;
					font-weight: bold;
				}

				.brand {
					width: 18%;
					height: 8vh;
					margin: 1%;
					display: flex;
					justify-content: center;
					align-items: center;
					box-sizing: border-box;
					border-radius: 1vw;
					cursor: pointer;
					font-size: 24px;
					font-weight: bold;
					color: white;
					background: linear-gradient(to right bottom, pink, blue);

					&:hover {
						color: black;
						background: linear-gradient(to right bottom, lightpink, lightblue);
					}
				}
			}
		}
	}

	.dept-popup {
		.dept-box {
			width: 80vw;
			height: 80vh;
			border-radius: 1vw;
			background-color: #fdf6e3;
			overflow: auto;

			.dept-part {
				width: 100%;
				margin-bottom: 1%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.dept-initial {
					width: 100%;
					margin-left: 1.5%;
					margin-top: 1%;
					font-size: 28px;
					font-weight: bold;
				}

				.dept {
					width: 18%;
					height: 8vh;
					margin: 1%;
					display: flex;
					justify-content: center;
					align-items: center;
					box-sizing: border-box;
					border-radius: 1vw;
					cursor: pointer;
					font-size: 16px;
					font-weight: bold;
					color: white;
					background: linear-gradient(to right bottom, pink, blue);

					&:hover {
						color: black;
						background: linear-gradient(to right bottom, lightpink, lightblue);
					}
				}
			}
		}
	}


	.add-part-popup {
		.add-part {
			width: 100vw;
			height: 100vh;
			overflow: auto;
			padding: 2.5% 2% 1.5% 2%;
			box-sizing: border-box;
			background-color: #fdf6e3;

			.add-part-box {
				width: 100%;
				min-height: 100%;
				border-radius: 10px;
				box-shadow: 0 0 1px 5px #dddddd;
				box-sizing: border-box;
				position: relative;

				.back {
					width: 50px;
					height: 50px;
					display: flex;
					justify-content: space-evenly;
					align-items: center;
					position: absolute;
					left: 2.5%;
					top: 4.5%;
					cursor: pointer;
					z-index: 1;
				}

				.submit {
					display: flex;
					justify-content: space-evenly;
					align-items: center;
					position: absolute;
					right: 2.5%;
					top: 4.5%;
					cursor: pointer;
					z-index: 1;
				}

				.title {
					margin-bottom: 1%;
					font-size: 24px;
					font-weight: bold;
					text-align: center;
					padding: 16px;
				}

				.add-part-data {
					width: 100%;
					position: relative;
					display: flex;
					justify-content: flex-start;
					align-items: flex-start;
					flex-wrap: wrap;

					.add-part-attribute {
						width: 28%;
						margin-left: 8%;
						margin-bottom: 4%;
						background-color: #fdf6e3;

						.uni-easyinput {
							width: 80%;
							margin-left: 16px;

							&:deep(.uni-easyinput__content-input) {
								height: 40px;
								font-size: 20px;
								font-weight: bold;
							}
						}

						.uni-stat__select {
							width: 80%;
							margin-left: 16px;

							&:deep(.uni-select) {
								height: 40px;
								font-size: 20px;
								font-weight: bold;
							}

							&:deep(.uni-select__input-placeholder) {
								font-size: 20px;
							}


						}

						.uni-data-checklist {
							margin-left: 16px;

							&:deep(.checklist-box) {
								margin-top: 0px;
							}

							&:deep(.checklist-text) {
								padding: 8px;
								font-size: 20px;
								font-weight: bold;
							}
						}

						.uni-numbox {
							margin-left: 16px;
							height: 40px;

							&:deep(.uni-numbox-btns) {
								width: 40px;
								box-sizing: border-box;

								.uni-numbox--text {
									font-weight: bold;
								}
							}

							&:deep(.uni-numbox__value) {
								width: 60px;
								height: 40px;
								font-size: 20px;
								font-weight: bold;
							}
						}
					}
				}
			}
		}
	}

	#leftImgView {
		width: 23%;
		height: 600px;
		margin-left: 2%;
		margin-top: 5%;
		background-color: #fdf6e3;
	}

	#rightContent {
		margin-top: 3%;
		width: 75%;
		display: flex;
		flex-wrap: wrap;
	}

	.addInput {
		font-size: 20px;
		width: 80%;
		height: 250px;
		margin-left: 8%;
		margin-bottom: 20px;
		background-color: white;
		border: 2px solid #000;
		/* 设置边框颜色和宽度 */
		border-radius: 10px;
		/* 设置边框圆角 */

		&:deep(.uni-easyinput__content-textarea) {
			height: 235px;
			font-size: 20px;
			font-weight: bold;
		}

		&:deep(.uni-easyinput__placeholder-class) {
			font-size: 20px;
		}
	}

	.addInputParent {
		display: flex;
		align-items: center;

		.uni-icons {
			margin-left: 2%;
			margin-right: 2%;
		}
	}

	.table-container {
		height: 75vh;
		width: 100%;
		overflow-x: auto;
		border: 2px solid #ddd;
		background: #fff;
	}

	table {
		font-size: 14px;
		color: #606266;
		border-collapse: collapse;
		// margin: 20px 0;
		font-size: 18px;
		text-align: left;
		width: 100%;
		/* 确保表格宽度大于容器宽度 */
		border-radius: 5px;
		box-sizing: border-box;
		text-align: center;
		white-space: nowrap;
	}

	th,
	td {
		font-size: 14px;
		border: 1px #DDDDDD solid;
		padding: 5px 0px;
		border-bottom: 1px solid #DDDDDD;
		/* 表格底部边框 */
		height: 44px;
		/* 固定行高 */
		box-sizing: border-box;
		/* 包含内边距和边框 */
	}

	th {
		color: #333;
		background-color: #F0F0F0;
		/* 表头文字颜色 */
	}

	tr {
		font-size: 14px;
		height: 44px;
		/* 固定行高 */
	}

	tr:hover {
		background-color: #f1f1f1;
		/* 行悬停效果 */
	}

	uv-button {
		width: 80px;
		/* 固定按钮宽度 */
		box-sizing: border-box;
		/* 包含内边距和边框 */
	}

	th {
		background-color: #f2f2f2;
		/* 表头背景色 */
		position: sticky;
		/* 固定表头 */
		top: 0;
		/* 固定在顶部 */
		z-index: 999;
		/* 确保表头在其他内容之上 */
	}

	.preview-image {
		width: 100%;
		height: 200px;
		object-fit: contain;
	}

	.picture-popup {
		.watermark {
			position: relative;
			transition: all 0.15s ease-in-out;

			&::before {
				content: "STELLA";
				position: absolute;
				top: 2px;
				left: 2px;
				color: black;
				font-size: 18px;
				font-weight: bold;
				text-shadow: 0 0 5px white;
				z-index: 1;
				pointer-events: none;
			}

			&:active {
				transform: scale(1.5);
			}

			.img {
				min-width: 100px;
				min-height: 100px;
				max-width: 100vw;
				max-height: 50vw;
				border-radius: 10px;
				box-shadow: 0 0 10px white;

				&:active {
					box-shadow: 0 0 1px white;
				}
			}
		}
	}
	
	.bordered-input {
	  border: 1px solid #ccc; 
	  border-radius: 4px; 
	  width: 150px;
	  height: 30px;
	}
</style>