package com.zqn.email.controller;

import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.email.entity.EmailPushConfig;
import com.zqn.email.service.EmailPushConfigService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送配置Controller
 * @date 2025/01/22 10:00
 */
@RestController
@RequestMapping("/emailPushConfig")
@Validated
public class EmailPushConfigController {

    @Resource
    private EmailPushConfigService emailPushConfigService;

    /**
     * 分页查询邮件推送配置列表
     *
     * @param pageNo     页码
     * @param pageSize   页大小
     * @param configKey  配置键
     * @param configType 配置类型
     * @param status     状态
     * @return 分页结果
     */
    @GetMapping("/query")
    public R<PageInfo<EmailPushConfig>> queryConfigList(
            @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(value = "configKey", required = false) String configKey,
            @RequestParam(value = "configType", required = false) String configType,
            @RequestParam(value = "status", required = false) String status) {
        
        return emailPushConfigService.queryConfigList(pageNo, pageSize, configKey, configType, status);
    }

    /**
     * 根据ID查询配置
     *
     * @param id 配置ID
     * @return 配置信息
     */
    @GetMapping("/getById")
    public R<EmailPushConfig> getConfigById(@RequestParam("id") @NotNull(message = "配置ID不能为空") Long id) {
        return emailPushConfigService.getConfigById(id);
    }

    /**
     * 根据配置键查询配置
     *
     * @param configKey 配置键
     * @return 配置信息
     */
    @GetMapping("/getByKey")
    public R<EmailPushConfig> getConfigByKey(@RequestParam("configKey") @NotBlank(message = "配置键不能为空") String configKey) {
        return emailPushConfigService.getConfigByKey(configKey);
    }

    /**
     * 根据配置键获取配置值
     *
     * @param configKey    配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    @GetMapping("/getValue")
    public R<String> getConfigValue(@RequestParam("configKey") @NotBlank(message = "配置键不能为空") String configKey,
                                   @RequestParam(value = "defaultValue", required = false) String defaultValue) {
        String value = emailPushConfigService.getConfigValue(configKey, defaultValue);
        return R.success(value);
    }

    /**
     * 根据配置类型查询配置列表
     *
     * @param configType 配置类型
     * @return 配置列表
     */
    @GetMapping("/getByType")
    public R<List<EmailPushConfig>> getConfigsByType(@RequestParam("configType") @NotBlank(message = "配置类型不能为空") String configType) {
        return emailPushConfigService.getConfigsByType(configType);
    }

    /**
     * 查询所有启用的配置
     *
     * @return 配置列表
     */
    @GetMapping("/getAllEnabled")
    public R<List<EmailPushConfig>> getAllEnabledConfigs() {
        return emailPushConfigService.getAllEnabledConfigs();
    }

    /**
     * 新增配置
     *
     * @param config 配置信息
     * @return 操作结果
     */
    @PostMapping("/add")
    public R<String> addConfig(@RequestBody @Validated EmailPushConfig config) {
        return emailPushConfigService.addConfig(config);
    }

    /**
     * 更新配置
     *
     * @param config 配置信息
     * @return 操作结果
     */
    @PostMapping("/update")
    public R<String> updateConfig(@RequestBody @Validated EmailPushConfig config) {
        return emailPushConfigService.updateConfig(config);
    }

    /**
     * 根据配置键更新配置值
     *
     * @param configKey   配置键
     * @param configValue 配置值
     * @param updateUser  更新人
     * @return 操作结果
     */
    @PostMapping("/updateValue")
    public R<String> updateConfigValue(@RequestParam("configKey") @NotBlank(message = "配置键不能为空") String configKey,
                                      @RequestParam("configValue") @NotBlank(message = "配置值不能为空") String configValue,
                                      @RequestParam(value = "updateUser", required = false) String updateUser) {
        return emailPushConfigService.updateConfigValue(configKey, configValue, updateUser);
    }

    /**
     * 删除配置
     *
     * @param id 配置ID
     * @return 操作结果
     */
    @PostMapping("/delete")
    public R<String> deleteConfig(@RequestParam("id") @NotNull(message = "配置ID不能为空") Long id) {
        return emailPushConfigService.deleteConfig(id);
    }

    /**
     * 批量更新配置
     *
     * @param configs 配置列表
     * @return 操作结果
     */
    @PostMapping("/batchUpdate")
    public R<String> batchUpdateConfigs(@RequestBody List<EmailPushConfig> configs) {
        return emailPushConfigService.batchUpdateConfigs(configs);
    }

    /**
     * 获取SMTP配置
     *
     * @return SMTP配置
     */
    @GetMapping("/getSmtpConfig")
    public R<Map<String, String>> getSmtpConfig() {
        Map<String, String> smtpConfig = emailPushConfigService.getSmtpConfig();
        return R.success(smtpConfig);
    }

    /**
     * 更新SMTP配置
     *
     * @param smtpConfig SMTP配置
     * @param updateUser 更新人
     * @return 操作结果
     */
    @PostMapping("/updateSmtpConfig")
    public R<String> updateSmtpConfig(@RequestBody Map<String, String> smtpConfig,
                                     @RequestParam(value = "updateUser", required = false) String updateUser) {
        return emailPushConfigService.updateSmtpConfig(smtpConfig, updateUser);
    }

    /**
     * 测试SMTP连接
     *
     * @return 测试结果
     */
    @PostMapping("/testSmtpConnection")
    public R<String> testSmtpConnection() {
        return emailPushConfigService.testSmtpConnection();
    }

    /**
     * 重置配置为默认值
     *
     * @param configKey 配置键
     * @return 操作结果
     */
    @PostMapping("/resetToDefault")
    public R<String> resetConfigToDefault(@RequestParam("configKey") @NotBlank(message = "配置键不能为空") String configKey) {
        return emailPushConfigService.resetConfigToDefault(configKey);
    }

    /**
     * 导入配置
     *
     * @param configs 配置列表
     * @return 操作结果
     */
    @PostMapping("/import")
    public R<String> importConfigs(@RequestBody List<EmailPushConfig> configs) {
        return emailPushConfigService.importConfigs(configs);
    }

    /**
     * 导出配置
     *
     * @param configType 配置类型
     * @return 配置列表
     */
    @GetMapping("/export")
    public R<List<EmailPushConfig>> exportConfigs(@RequestParam(value = "configType", required = false) String configType) {
        return emailPushConfigService.exportConfigs(configType);
    }

    /**
     * 刷新配置缓存
     *
     * @return 操作结果
     */
    @PostMapping("/refreshCache")
    public R<String> refreshConfigCache() {
        return emailPushConfigService.refreshConfigCache();
    }
}
