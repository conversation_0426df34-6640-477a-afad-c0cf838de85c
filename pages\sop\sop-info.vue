<!-- 工序信息 -->
<script setup>
import { onShow } from '@dcloudio/uni-app'
import { ref, onMounted, watch, provide } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap, proSeqMap } from '@/pages/sop/common/commonMap.js'
import OperationPopup from '@/pages/sop/info/operation-popup.vue'
import BrandPopup from '@/pages/sop/info/brand-popup.vue'
import AddInfoPopup from '@/pages/sop/info/add-info-popup.vue'
import DeleteInfoPopup from '@/pages/sop/info/delete-info-popup.vue'
import CopyPopup from '@/pages/sop/info/copy-popup.vue'
import InfoTypePopup from '@/pages/sop/info/info-type-popup.vue'
import InfoMaterialPopup from '@/pages/sop/info/info-material-popup.vue'
import InfoSkuPopup from '@/pages/sop/info/info-sku-popup.vue'
import LastNosPopup from '@/pages/sop/info/last-nos-popup.vue'
import OsNoPopup from '@/pages/sop/info/os-no-popup.vue'
import EditPopup from '@/pages/sop/info/edit-popup.vue'
import ResetFlowPopup from '@/pages/sop/info/reset-flow-popup.vue'
import PreviewPopup from '@/pages/sop/info/preview-popup.vue'
import ExportPopup from '@/pages/sop/info/export-popup.vue'
import PicturePopup from '@/pages/sop/common/picture-popup.vue'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync('loUserNo')
// 用户账号
const loginUser = uni.getStorageSync('loginUserName')

// 提示弹框
const tipPopup = ref()

// 制程
const operation = ref(uni.getStorageSync('sop-operation') ? uni.getStorageSync('sop-operation') : '6')
// 详细制程
const proSeq = ref(uni.getStorageSync('sop-proSeq') ? uni.getStorageSync('sop-proSeq') : '6A')
// 皮底制程列表
const leatherSoleList = ref(['8D', '8P', '8Q', '8R', '8S', '8T', '8U', '8Y'])
// 制程弹框
const operationPopup = ref()

// 品牌
const brand = ref(uni.getStorageSync('sop-brand') ? uni.getStorageSync('sop-brand') : 'AB')
// 品牌弹框
const brandPopup = ref()

// 型体编号
const model = ref('')
// 搜索型体
const searchModel = ref('')
// 输入框型体编号
const modelInput = ref('')
// 聚焦型体输入框
const focusModelInput = ref(false)
// 型体列表
const modelList = ref([])
// 可选型体列表
const modelOptionList = ref([])

// 输入框型体描述
const modelDescInput = ref('')
// 聚焦型体描述输入框
const focusModelDescInput = ref(false)

// 型体鞋图
const modelPicture = ref('')
// 图片弹框
const picturePopup = ref()

// 是否是自己做的资料
const isMine = ref(uni.getStorageSync('sop-isMine') ? uni.getStorageSync('sop-isMine') : false)
// 是否为审核人
const isAuditer = ref(false)

// 工序创建方式
const createType = ref(uni.getStorageSync('sop-createType') ? uni.getStorageSync('sop-createType') : '全部')
// 聚焦创建方式选择器
const focusCreateTypeSelector = ref(false)
// 工序创建方式列表
const createTypeList = ref(['全部', 'APP', 'ERP'])

// 楦头编号
const lastNos = ref('全部')
// Outsole
const osNo = ref('全部')

// 工序信息页数
const pageNo = ref(1)
// 工序信息页大小
const pageSize = ref(20)
// 加载状态
const loadType = ref('')

// 工序信息列表
const processInfoList = ref([])

// 工序信息下拉刷新状态
const processInfoTriggered = ref(false)

// 是否选择所有工序信息
const isSelectedAllProcessInfo = ref(false)

// 添加工序信息弹框
const addInfoPopup = ref()
// 删除工序信息弹框
const deleteInfoPopup = ref()
// 复制弹框
const copyPopup = ref()
// 工序信息生产类型弹框
const infoTypePopup = ref()
// 工序信息材质弹框
const infoMaterialPopup = ref()
// 工序信息 SKU 弹框
const infoSkuPopup = ref()
// 楦头编号弹框
const lastNosPopup = ref()
// Outsole 弹框
const osNoPopup = ref()
// 编辑弹框
const editPopup = ref()
// 重置工序流程弹框
const resetFlowPopup = ref()
// 预览弹框
const previewPopup = ref()
// 导出弹框
const exportPopup = ref()

// 返回
function back() {
  uni.navigateBack()
}

// 切换是否是自己做的资料
function changeIsMine() {
  isMine.value = !isMine.value
  uni.setStorageSync('sop-isMine', isMine.value)
}

// 是否有审核权限
function canAudit() {
  uni.request({
    url: urlPrefix + '/menu/buttonQuery',
    method: 'GET',
    data: {
      url: '/sop-info/unaudit',
      loginUser: loginUser
    }
  }).then(res => {
    isAuditer.value = res.data.data
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 获取型体列表
async function getModelList(param) {
  modelList.value = []
  modelOptionList.value = []
  await uni.request({
    url: urlPrefix + '/sop/getModelList',
    method: 'POST',
    data: {
      brand: param
    }
  }).then(res => {
    if (res.data.code) {
      modelList.value = res.data.data ? res.data.data : []
      if (param === 'TR') {
        modelOptionList.value = modelList.value.filter(item => !item.modelNo.startsWith('TB')).slice(0, 50)
      } else {
        modelOptionList.value = modelList.value.slice(0, 50)
      }
    } else {
      tipPopup.value.showTipPopup('warn', '暂无型体列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 选择型体
async function selectModel(param) {
  modelInput.value = param.modelNo
  modelDescInput.value = param.modelNo + ' | ' + param.modelDesc
  model.value = param.modelNo
}

// 选择型体描述
function selectModelDesc(param) {
  modelInput.value = param.modelNo
  modelDescInput.value = param.modelNo + ' | ' + param.modelDesc
  model.value = param.modelNo
}

// 清空型体输入框
function clearModelInput() {
  modelPicture.value = ''
  modelInput.value = ''
  modelDescInput.value = ''
  model.value = ''
}

// 清空型体描述输入框
function clearModelDescInput() {
  modelPicture.value = ''
  modelInput.value = ''
  modelDescInput.value = ''
  model.value = ''
}

// 获取鞋图
async function getModelPicture(param) {
  modelPicture.value = ''
  await uni.request({
    url: urlPrefix + '/sop/getModelPicture',
    method: 'POST',
    data: {
      model: param
    }
  }).then(res => {
    if (res.data.code) {
      modelPicture.value = res.data.data ? ('data:image/jpg;base64,' + res.data.data.modelPicture) : ''
    } else {
      tipPopup.value.showTipPopup('warn', '暂无鞋图数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 获取工序信息列表
async function getProcessInfoList(param1, param2, param3, param4) {
  isSelectedAllProcessInfo.value = false
  
  await uni.request({
    url: urlPrefix + '/sop/getProcessInfoList',
    method: 'POST',
    data: {
      operation: param1,
      brand: param2,
      model: searchModel.value,
      insUser: isMine.value ? user : '',
      createType: uni.getStorageSync('sop-createType') ? uni.getStorageSync('sop-createType') : '全部',
      lastNos: lastNos.value,
      osNo: osNo.value,
      pageNo: param3 ? param3 : 1,
      pageSize: param4 ? param4 : 20
    }
  }).then(res => {
    if (param3) {
      processInfoList.value.push(...(res.data.data ? res.data.data : []))
    } else {
      processInfoList.value = res.data.data ? res.data.data : []
      pageNo.value = 1
    }
    
    if (!res.data.data || res.data.data.length < pageSize.value) {
      loadType.value = 'no-more'
    } else {
      loadType.value = 'more'
    }
    
    pageNo.value++
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 滚动至底部
function scrollToLower() {
  if (loadType.value === 'loading' || loadType.value === 'no-more' || processInfoList.value.length < pageSize.value) {
    return
  }
  loadType.value = 'loading'
  getProcessInfoList(operation.value, brand.value, pageNo.value, pageSize.value)
}

// 工序信息下拉刷新
async function processInfoOnRefresh() {
  if (processInfoTriggered.value) {
    return
  }
  
  // if (!model.value) {
  //   processInfoTriggered.value = true
  //   setTimeout(() => {
  //     processInfoTriggered.value = false
  //     tipPopup.value.showTipPopup('warn', '请选择型体！')
  //   }, 500)
  //   return
  // }
  
  processInfoTriggered.value = true
  await getProcessInfoList(operation.value, brand.value)
  setTimeout(() => {
    processInfoTriggered.value = false
    tipPopup.value.showTipPopup('success', '列表已刷新！')
  }, 500)
}

// 可选工序信息个数
function canSelectProcessInfoCount() {
  let canSelectedCount = 0
  for (let item of processInfoList.value) {
    if (user === item.insUser && item.flowNumber === 0) {
      canSelectedCount++
    }
  }
  return canSelectedCount
}

// 已选工序信息个数
function selectedProcessInfoCount() {
  let selectedCount = 0
  for (let item of processInfoList.value) {
    if (item.isSelected) {
      selectedCount++
    }
  }
  return selectedCount
}

// 选择工序信息
function selectItemProcessInfo(param) {
  if (user !== param.insUser || param.flowNumber > 0) {
    return
  }
  
  param.isSelected = !param.isSelected
  
  if (selectedProcessInfoCount() === canSelectProcessInfoCount()) {
    isSelectedAllProcessInfo.value = true
  } else {
    isSelectedAllProcessInfo.value = false
  }
}

// 选择所有工序信息
function selectAllProcessInfo() {
  if (canSelectProcessInfoCount() === 0) {
    return
  }
  
  isSelectedAllProcessInfo.value = !isSelectedAllProcessInfo.value
  for (let item of processInfoList.value) {
    if (user === item.insUser && item.flowNumber === 0) {
      item.isSelected = isSelectedAllProcessInfo.value
    }
  }
}

// 添加工序信息
function addProcessInfo(param1, param2, param3, param4) {
  if (!param1) {
    tipPopup.value.showTipPopup('warn', '请选择型体！')
    return
  }
  
  addInfoPopup.value.showAddInfoPopup(param1, param2, param3, param4)
}

// 删除工序信息
async function deleteProcessInfo(param1, param2, param3, param4) {
  // if (!param1) {
  //   tipPopup.value.showTipPopup('warn', '请选择型体！')
  //   return
  // }
  
  let selectedProcessInfoList = []
  for (let item of param4) {
    if (item.isSelected) {
      selectedProcessInfoList.push({
        ...item,
        skuList: item.skuList.length > 0 ? item.skuList : []
      })
    }
  }
  if (selectedProcessInfoList.length === 0) {
    tipPopup.value.showTipPopup('warn', '请勾选要删除的选项！')
    return
  }
  
  deleteInfoPopup.value.showDeleteInfoPopup(param1, param2, param3, selectedProcessInfoList)
}

// 复制工序信息
function copyProcess(param) {
  let info = {
    ...param,
    brand: brand.value
  }
  copyPopup.value.showCopyPopup(info)
}

// 修改工序信息 SKU
function updateProcessSku(param) {
  let info = {
    ...param,
    brand: brand.value
  }
  infoSkuPopup.value.showInfoSkuPopup(info)
}

// 编辑工序
function editProcess(param) {
  if (param.cplFlag === 'Y' && !isAuditer.value) {
    tipPopup.value.showTipPopup('warn', '暂无解除已完成状态的权限！')
    return
  }
  
  let info = {
    ...param,
    brand: brand.value
  }
  
  editPopup.value.showEditPopup(info)
}

// 重置工序流程
function resetFlow(param) {
  if (param.cplFlag === 'Y') {
    tipPopup.value.showTipPopup('warn', '已完成的工序无法进行重置！')
    return
  }
  resetFlowPopup.value.showResetFlowPopup(param.model, param.operation, param.rtgCode, param.proSeq, brand.value)
}

// 进入预览工序流程页面
function toPreview(param) {
  let info = {
    ...param,
    modelDesc: '',
    brand: brand.value
  }
  
  if (info.proSeq === '1E' || info.proSeq === '1D') {
    previewPopup.value.showPreviewPopup(info)
  } else {
    uni.navigateTo({
      url: `/pages/sop/sop-preview?info=${JSON.stringify(info)}`,
      animationType: 'pop-in',
      animationDuration: 300
    })
  }
}

// 导出工序流程
function exportFlow(param) {
  exportPopup.value.showExportPopup(param)
}

// 聚焦工序创建方式选择器
function focusCreateType() {
  focusCreateTypeSelector.value = true
}

// 选择工序创建方式
function selectCreateType(param) {
  uni.setStorageSync('sop-createType', param)
  createType.value = param
}

// 隐藏选项
function hideOption() {
  if (focusCreateTypeSelector.value) {
    focusCreateTypeSelector.value = false
  }
}

// 同步加载中
async function syncLoading(...args) {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  for (let f of args) {
    await f
  }
  
  uni.hideLoading()
}

// 异步加载中
async function asyncLoading(...args) {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await Promise.all(args).finally(() => uni.hideLoading())
}

watch(operation, () => {
  asyncLoading(
    infoTypePopup.value.getInfoTypeList(operation.value),
    getProcessInfoList(operation.value, brand.value)
  )
})

watch(brand, () => {
  model.value = ''
  modelInput.value = ''
  modelDescInput.value = ''
  modelPicture.value = ''
  lastNos.value = '全部'
  osNo.value= '全部'
  processInfoList.value = []
  asyncLoading(
    getModelList(brand.value),
    lastNosPopup.value.getLastNosList(brand.value),
    osNoPopup.value.getOsNoList(brand.value),
    getProcessInfoList(operation.value, brand.value)
  )
})


let modelInputTimer = null
watch(modelInput, () => {
  if (modelInputTimer) {
    clearTimeout(modelInputTimer)
  }
  modelInputTimer = setTimeout(() => {
    modelOptionList.value = modelList.value.filter(item => item.modelNo.toUpperCase().includes(modelInput.value.toUpperCase())).slice(0, 50)
  }, 300)
})

let modelDescInputTimer = null
watch(modelDescInput, () => {
  if (modelDescInputTimer) {
    clearTimeout(modelDescInputTimer)
  }
  modelDescInputTimer = setTimeout(() => {
    modelOptionList.value = modelList.value.filter(item => (item.modelNo + ' | ' + item.modelDesc).toUpperCase().includes(modelDescInput.value.toUpperCase())).slice(0, 50)
  }, 300)
})

watch(model, () => {
  searchModel.value = model.value
  if (model.value) {
    asyncLoading(
      getModelPicture(model.value),
      getProcessInfoList(operation.value, brand.value),
      infoMaterialPopup.value.getInfoMaterialList(model.value, operation.value)
    )
  } else {
    asyncLoading(
      getProcessInfoList(operation.value, brand.value),
      infoMaterialPopup.value.getInfoMaterialList(model.value, operation.value)
    )
  }
})

watch([isMine, createType, lastNos, osNo], () => {
  asyncLoading(
    getProcessInfoList(operation.value, brand.value)
  )
})

onMounted(() => {
  asyncLoading(
    operationPopup.value.getOperationList(),
    brandPopup.value.getBrandList(),
    infoTypePopup.value.getInfoTypeList(operation.value),
    infoMaterialPopup.value.getInfoMaterialList(model.value, operation.value),
    lastNosPopup.value.getLastNosList(brand.value),
    osNoPopup.value.getOsNoList(brand.value),
    getModelList(brand.value),
    getProcessInfoList(operation.value, brand.value),
    canAudit()
  )
})

onShow(() => {
  getProcessInfoList(operation.value, brand.value)
  canAudit()
})

provide('operation', operation)
provide('proSeq', proSeq)
provide('brand', brand)
provide('processInfoList', processInfoList)
provide('searchModel', searchModel)
provide('isMine', isMine)
provide('lastNos', lastNos)
provide('osNo', osNo)
provide('getProcessInfoList', getProcessInfoList)
provide('isSelectedAllProcessInfo', isSelectedAllProcessInfo)
provide('isAuditer', isAuditer)
</script>

<template>
  <view @click="hideOption()" class="sop-info">
    <view class="top-bar flex-row-start-center">
      <view @click="back()" class="back button">
        <uni-icons type="back" size="30" color="steelblue"></uni-icons>
      </view>
      
      <view @click="operationPopup.showOperationPopup()" class="department button">
        {{ operationMap.get(operation) ? operationMap.get(operation) : '未知' }} - {{ proSeqMap.get(proSeq) ? proSeqMap.get(proSeq) : '未知' }}
      </view>
      
      <view @click="brandPopup.showBrandPopup()" class="brand button">
        {{ brand }}
      </view>
      
      <view v-show="!leatherSoleList.includes(proSeq)" class="model">
        <input
          v-model="modelInput"
          @focus="focusModelInput = true"
          @blur="focusModelInput = false"
          type="text"
          placeholder="请输入型体编号"
          class="model-input input"
          :style="{
            boxShadow: focusModelInput ? '0 0 5px blue' : '0 0 5px gray'
          }"
        />
        
        <view v-show="modelInput" @click="clearModelInput()" class="model-clear button">
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <!-- #ifdef APP -->
        <uni-transition
          :show="focusModelInput"
          mode-class="fade"
          class="model-option"
        >
        <!-- #endif -->
        <!-- #ifdef WEB -->
        <transition name="fade">
        <view v-show="focusModelInput" class="model-option">
        <!-- #endif -->
          <view
            v-for="item in modelOptionList"
            @click="selectModel(item)"
            class="model-option-item"
          >
            {{ item.modelNo }}
          </view>
          
          <view v-show="modelOptionList.length === 0" class="model-option-empty">
            暂无可选型体
          </view>
        <!-- #ifdef WEB -->
        </view>
        </transition>
        <!-- #endif -->
        <!-- #ifdef APP -->
        </uni-transition>
        <!-- #endif -->
      </view>
      
      <view v-show="leatherSoleList.includes(proSeq)" class="model-desc">
        <input
          v-model="modelDescInput"
          @focus="focusModelDescInput = true"
          @blur="focusModelDescInput = false"
          type="text"
          placeholder="请输入型体描述"
          class="model-desc-input input"
          :style="{
            boxShadow: focusModelDescInput ? '0 0 5px blue' : '0 0 5px gray'
          }"
        />
        
        <view v-show="modelDescInput" @click="clearModelDescInput()" class="model-desc-clear button">
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <!-- #ifdef APP -->
        <uni-transition
          :show="focusModelDescInput"
          mode-class="fade"
          class="model-desc-option"
        >
        <!-- #endif -->
        <!-- #ifdef WEB -->
        <transition name="fade">
        <view v-show="focusModelDescInput" class="model-desc-option">
        <!-- #endif -->
          <view
            v-for="item in modelOptionList"
            @click="selectModelDesc(item)"
            class="model-desc-option-item"
          >
            {{ item.modelNo + ' | ' + item.modelDesc }}
          </view>
          
          <view v-show="modelOptionList.length === 0" class="model-desc-option-empty">
            暂无可选型体描述
          </view>
        <!-- #ifdef WEB -->
        </view>
        </transition>
        <!-- #endif -->
        <!-- #ifdef APP -->
        </uni-transition>
        <!-- #endif -->
      </view>
      
      <view class="picture" :style="{ visibility: modelPicture.length > 0 ? 'visible' : 'hidden' }">
        <!-- #ifdef APP -->
        <uni-transition
          :show="modelPicture.length > 0"
          mode-class="fade"
          class="watermark"
        >
        <!-- #endif -->
        <!-- #ifdef WEB -->
        <transition name="fade">
        <view v-show="modelPicture.length > 0" class="watermark">
        <!-- #endif -->
          <img
            @click="picturePopup.showPicturePopup(modelPicture)"
            :src="modelPicture"
            alt=""
            class="button"
          />
        <!-- #ifdef WEB -->
        </view>
        </transition>
        <!-- #endif -->
        <!-- #ifdef APP -->
        </uni-transition>
        <!-- #endif -->
      </view>
      
      <view @click="addProcessInfo(model, operation, proSeq, brand)" class="add button">
        <uni-icons type="plusempty" size="30" color="green"></uni-icons>
      </view>
      
      <view @click="deleteProcessInfo(model, operation, brand, processInfoList)" class="delete button">
        <uni-icons type="trash" size="30" color="red"></uni-icons>
      </view>
      
      <view @click="changeIsMine()" class="person button" :style="{ backgroundColor: isMine ? '#ccc' : 'transparent' }">
        <uni-icons type="person" size="30" color="saddlebrown"></uni-icons>
      </view>
    </view>
    
    <view class="process-info">
      <!-- <scroll-view
        :scroll-x="true"
        :scroll-y="true"
        :refresher-enabled="true"
        :refresher-triggered="processInfoTriggered"
        :refresher-threshold="80"
        refresher-background="#fdf6e3"
        @refresherrefresh="processInfoOnRefresh()"
        class="process-info-table"
      > -->
      <scroll-view
        :scroll-x="true"
        :scroll-y="true"
        :lower-threshold="1"
        @scrolltolower="scrollToLower()"
        class="process-info-table"
      >
        <table>
          <thead>
            <tr>
              <th style="width: 50px; position: sticky; left: 0px; z-index: 2;">
                <checkbox
                  @click="selectAllProcessInfo()"
                  :checked="isSelectedAllProcessInfo"
                  :disabled="canSelectProcessInfoCount() === 0"
                  color="white"
                  activeBackgroundColor="violet"
                  borderColor="gray"
                  activeBorderColor="black"
                ></checkbox>
              </th>
              <th style="width: 160px; position: sticky; left: 52px; z-index: 2;">操作</th>
              <th style="width: 250px; position: sticky; left: 214px; z-index: 2;">型体信息</th>
              <th style="width: 120px; position: sticky; left: 466px; z-index: 2;">当前版本</th>
              <th v-show="operation === '1'" style="width: 250px;">配色</th>
              <th style="width: 250px;">类型材质</th>
              <th style="width: 200px;">
                <view class="create-info">
                  <text>创建信息</text>
                  
                  <view class="create-type-selector">
                    <view
                      @click.stop="focusCreateType()"
                      class="create-type-value"
                      :style="{
                        boxShadow: focusCreateTypeSelector ? '0 0 5px blue' : '0 0 5px gray'
                      }"
                    >
                      {{ createType }}
                    </view>
                    
                    <uni-transition
                      :show="focusCreateTypeSelector"
                      mode-class="fade"
                      class="create-type-option"
                    >
                      <view class="create-type-option-list">
                        <view
                          v-for="(item, index) in createTypeList"
                          :key="index"
                          @click="selectCreateType(item)"
                          class="create-type-option-item"
                          :style="{
                            color: createType === item ? 'red' : 'black'
                          }"
                        >
                          {{ item }}
                        </view>
                      </view>
                    </uni-transition>
                  </view>
                </view>
              </th>
              <th style="width: 180px;">
                <view class="last-nos">
                  <view class="last-nos-title">
                    楦头编号
                  </view>
                  
                  <view @click="lastNosPopup.showLastNosPopup(brand)" class="last-nos-value button">
                    {{ lastNos ? lastNos : '/' }}
                  </view>
                </view>
              </th>
              <th style="width: 180px;">
                <view class="outsole">
                  <view class="outsole-title">
                    Outsole
                  </view>
                  
                  <view @click="osNoPopup.showOsNoPopup(brand)" class="outsole-value button">
                    {{ osNo ? osNo : '/' }}
                  </view>
                </view>
              </th>
            </tr>
          </thead>
          
          <tbody v-show="processInfoList.length > 0">
            <tr
              v-for="(item, index) in processInfoList"
              :key="index"
              v-show="isMine ? user === item.insUser : true"
              :style="{
                backgroundColor: item.cplFlag === 'Y' ? '#eaf7fc' : (index % 2 === 0 ? '#edfbe4' : '#fdf6e3')
              }"
            >
              <td
                style="position: sticky; left: 0px; z-index: 1;"
                :style="{
                  backgroundColor: item.cplFlag === 'Y' ? '#eaf7fc' : (index % 2 === 0 ? '#edfbe4' : '#fdf6e3')
                }"
              >
                <checkbox
                  @click="selectItemProcessInfo(item)"
                  :checked="item.isSelected"
                  :disabled="user !== item.insUser || item.flowNumber > 0"
                  color="white"
                  activeBackgroundColor="violet"
                  borderColor="gray"
                  activeBorderColor="black"
                ></checkbox>
              </td>
              <td
                style="position: sticky; left: 52px; z-index: 1;"
                :style="{
                  backgroundColor: item.cplFlag === 'Y' ? '#eaf7fc' : (index % 2 === 0 ? '#edfbe4' : '#fdf6e3')
                }"
              >
                <view class="operate flex-row-start-center">
                  <view class="edit">
                    <view @click="editProcess(item)" class="button">
                      <uni-icons type="compose" size="30" color="darkorange"></uni-icons>
                    </view>
                    
                    <view v-show="item.cplFlag === 'Y'" class="complete">
                      <uni-icons type="checkbox-filled" size="20" color="#008080"></uni-icons>
                    </view>
                  </view>
                  
                  <view
                    @click="toPreview(item)"
                    class="preview button"
                    :style="{ visibility: item.flowNumber > 0 || item.proSeq === '1E' || item.proSeq === '1D' ? 'visible' : 'hidden' }"
                  >
                    <uni-icons type="eye" size="30" color="hotpink"></uni-icons>
                  </view>
                  
                  <view
                    @click="tipPopup.showTipPopup('warn', '确认版本功能正在开发中...')"
                    class="send button"
                    :style="{ visibility: true ? 'visible' : 'hidden' }"
                  >
                    <uni-icons type="paperplane" size="30" color="rgb(0, 200, 0)"></uni-icons>
                  </view>
                  
                  <view
                    @click="exportFlow(item)"
                    class="export button"
                    :style="{ visibility: item.flowNumber > 0 ? 'visible' : 'hidden' }"
                  >
                    <uni-icons type="download" size="30" color="cornflowerblue"></uni-icons>
                  </view>
                </view>
              </td>
              <td
                style="position: sticky; left: 214px; z-index: 1;"
                :style="{
                  backgroundColor: item.cplFlag === 'Y' ? '#eaf7fc' : (index % 2 === 0 ? '#edfbe4' : '#fdf6e3')
                }"
              >
                <view class="model-data">
                  <view class="model-no flex-row-center">
                    编号：
                    <view @click="copyProcess(item)" class="button">
                      {{ item.model }}
                    </view>
                  </view>
                  
                  <view class="model-desc flex-row-center">
                    描述：
                    <view class="value flex-row-center">
                      {{ item.modelDesc ? item.modelDesc : '/' }}
                    </view>
                  </view>
                  
                  <view class="operation flex-row-center">
                    制程：
                    <view class="value flex-row-center">
                      {{ proSeqMap.get(item.proSeq) ? proSeqMap.get(item.proSeq) : '/' }}
                    </view>
                  </view>
                </view>
              </td>
              <td
                style="position: sticky; left: 466px; z-index: 1;"
                :style="{
                  backgroundColor: item.cplFlag === 'Y' ? '#eaf7fc' : (index % 2 === 0 ? '#edfbe4' : '#fdf6e3')
                }"
              >
                <view class="edition flex-row-center">
                  <view @click="tipPopup.showTipPopup('warn', '版本对比功能正在开发中...')" class="button">
                    {{ item.edition ? item.edition : '/' }}
                  </view>
                </view>
              </td>
              <td
                v-show="operation === '1'"
                :style="{
                  backgroundColor: item.cplFlag === 'Y' ? '#eaf7fc' : (index % 2 === 0 ? '#edfbe4' : '#fdf6e3')
                }"
              >
                <view class="sku flex-row-center">
                  <view
                    v-show="user === item.insUser && item.cplFlag === 'N'"
                    @click="updateProcessSku(item)"
                    class="button"
                  >
                    <view v-show="item.skuList.length > 0" class="flex-column-center">
                      <view v-for="(i, index) in item.skuList">
                        {{ i ? `${i.colorDesc}` : '/' }}
                      </view>
                    </view>
                    
                    <view v-show="item.skuList.length === 0">
                      /
                    </view>
                  </view>
                  
                  <view v-show="user !== item.insUser || item.cplFlag === 'Y'">
                    <view v-show="item.skuList.length > 0">
                      <view v-for="i in item.skuList">
                        {{ i ? `${i.colorDesc}` : '/' }}
                      </view>
                    </view>
                    
                    <view v-show="item.skuList.length === 0">
                      /
                    </view>
                  </view>
                </view>
              </td>
              <td>
                <view v-show="user === item.insUser && item.cplFlag === 'N'" class="type-material flex-row-center">
                  <view class="type flex-row-center">
                    类型：
                    <view
                      @click="infoTypePopup.showInfoTypePopup(item)"
                      class="button"
                    >
                      {{ item.rtgType ? item.rtgType : '/' }}
                    </view>
                  </view>
                  
                  <view class="material flex-row-center">
                    材质：
                    <view
                      @click="infoMaterialPopup.showInfoMaterialPopup(item)"
                      class="button"
                    >
                      {{ item.material ? item.material : '/' }}
                    </view>
                  </view>
                </view>
                
                <view v-show="user !== item.insUser || item.cplFlag === 'Y'" class="type-material flex-row-center">
                  <view class="type flex-row-center">
                    类型：
                    <view class="value flex-row-center">
                      {{ item.rtgType ? item.rtgType : '/' }}
                    </view>
                  </view>
                  
                  <view class="material flex-row-center">
                    材质：
                    <view class="value flex-row-center">
                      {{ item.material ? item.material : '/' }}
                    </view>
                  </view>
                </view>
              </td>
              <td>
                <view class="flex-row-center">
                  <view class="create-info">
                    <view class="flow-number">
                      项次：{{ item.flowNumber }}
                    </view>
                    
                    <view class="create-user">
                      作者：{{ item.insName }}
                    </view>
                    
                    <view class="create-date">
                      日期：{{ item.insDate ? item.insDate.substring(0, 10).replace(new RegExp('-', 'g'), '/') : '' }}
                    </view>
                    
                    <view class="create-type">
                      方式：{{ item.createType ? item.createType : 'ERP' }}
                    </view>
                  </view>
                </view>
              </td>
              <td>
                {{ item.lastNos ? item.lastNos : '/' }}
              </td>
              <td>
                {{ item.osNo ? item.osNo : '/' }}
              </td>
            </tr>
          </tbody>
          
          <tbody v-show="processInfoList.length === 0">
            <tr>
              <td :colspan="operation !== '1' ? 8 : 9">暂无数据</td>
            </tr>
          </tbody>
        </table>
        
        <view class="load-more flex-row-center">
          <uni-load-more
            v-show="processInfoList.length >= pageSize"
            :status="loadType"
            iconType="circle"
            color="black"
            :contentText="{
              contentdown: '上拉显示更多',
              contentrefresh: '正在加载...',
              contentnomore: '没有更多数据了'
            }"
          ></uni-load-more>
        </view>
      </scroll-view>
    </view>
  </view>
  
  <operation-popup ref="operationPopup" />
  <brand-popup ref="brandPopup" />
  <add-info-popup ref="addInfoPopup" />
  <delete-info-popup ref="deleteInfoPopup" />
  <copy-popup ref="copyPopup" />
  <info-type-popup ref="infoTypePopup" />
  <info-material-popup ref="infoMaterialPopup" />
  <info-sku-popup ref="infoSkuPopup" />
  <last-nos-popup ref="lastNosPopup" />
  <os-no-popup ref="osNoPopup" />
  <edit-popup ref="editPopup" />
  <reset-flow-popup ref="resetFlowPopup" />
  <preview-popup ref="previewPopup" />
  <export-popup ref="exportPopup" />
  <picture-popup ref="picturePopup" />
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss">
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

page {
  width: 100%;
  height: 100%;
  padding: 2.5% 2% 1.5% 2%;
  background-color: #fdf6e3;
}

.sop-info {
  width: 100%;
  height: 100%;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 0 1px 5px #ddd;
  
  .top-bar {
    width: 100%;
    height: 50px;
    margin-bottom: 20px;
    
    .back, .picture {
      height: 50px;
      margin-right: 2%;
    }
    
    .back {
      width: 6%;
    }
    
    .picture {
      width: 9%;
    }
    
    .picture img {
      min-width: 50px;
      max-width: 100%;
      height: 50px;
    }
    
    .department, .brand {
      height: 50px;
      margin-right: 2%;
      font-size: 20px;
    }
    
    .department {
      width: 15%;
      color: slateblue;
    }
    
    .brand {
      width: 8%;
      color: blueviolet;
    }
    
    .add, .delete, .person {
      width: 6%;
      height: 50px;
      margin-right: 2%;
    }
    
    .person {
      margin-right: 0;
    }
    
    .model {
      width: 30%;
      height: 50px;
      margin-right: 2%;
      position: relative;
      
      .model-input {
        width: 100%;
        height: 100%;
        padding: 0 10px;
      }
      
      .model-clear {
        width: 60px;
        height: 40px;
        margin-top: 5px;
        position: absolute;
        top: 0;
        right: 5px;
      }
      
      .model-option {
        width: 100%;
        max-height: 250px;
        position: absolute;
        top: calc(100% + 10px);
        left: 0;
        border-radius: 10px;
        box-shadow: 0 0 5px gray;
        // backdrop-filter: blur(10px);
        background-color: #fdf6e3;
        z-index: 3;
        overflow: auto;
        
        // /* #ifdef WEB */
        // &::-webkit-scrollbar {
        //   display: none;
        // }
        // /* #endif */
        
        .model-option-item, .model-option-empty {
          width: 100%;
          min-height: 50px;
          padding: 5px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 20px;
          font-weight: bold;
          text-align: center;
          border-top: 1px solid #ddd;
        }
        
        .model-option-item {
          transition: all 0.1s linear;
          
          /* #ifdef APP */
          &:active {
            background-color: #ccc;
          }
          /* #endif */
          
          /* #ifdef WEB */
          cursor: pointer;
          &:hover {
            background-color: #ccc;
          }
          /* #endif */
        }
        
        .model-option-item:first-child, .model-option-empty {
          border: none;
        }
      }
    }
    
    .model-desc {
      width: 30%;
      height: 50px;
      margin-right: 2%;
      position: relative;
      
      .model-desc-input {
        width: 100%;
        height: 100%;
        padding: 0 10px;
      }
      
      .model-desc-clear {
        width: 60px;
        height: 40px;
        margin-top: 5px;
        position: absolute;
        top: 0;
        right: 5px;
      }
      
      .model-desc-option {
        width: 100%;
        max-height: 250px;
        position: absolute;
        top: calc(100% + 10px);
        left: 0;
        border-radius: 10px;
        box-shadow: 0 0 5px gray;
        // backdrop-filter: blur(10px);
        background-color: #fdf6e3;
        z-index: 3;
        overflow: auto;
        
        // /* #ifdef WEB */
        // &::-webkit-scrollbar {
        //   display: none;
        // }
        // /* #endif */
        
        .model-desc-option-item, .model-desc-option-empty {
          width: 100%;
          min-height: 50px;
          padding: 5px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 20px;
          font-weight: bold;
          text-align: center;
          border-top: 1px solid #ddd;
        }
        
        .model-desc-option-item {
          transition: all 0.1s linear;
          
          /* #ifdef APP */
          &:active {
            background-color: #ccc;
          }
          /* #endif */
          
          /* #ifdef WEB */
          cursor: pointer;
          &:hover {
            background-color: #ccc;
          }
          /* #endif */
        }
        
        .model-desc-option-item:first-child, .model-desc-option-empty {
          border: none;
        }
      }
    }
  }
  
  .process-info {
    width: 100%;
    height: calc(100% - 70px);
    
    .process-info-table {
      width: 100%;
      height: 100%;
      border-radius: 10px;
      box-shadow: 0 0 5px gray;
      overflow: hidden;
      
      &:deep(.uni-scroll-view) {
        // /* #ifdef WEB */
        // &::-webkit-scrollbar {
        //   display: none;
        // }
        // /* #endif */
      }
      
      table {
        width: fit-content;
        min-width: 100%;
        max-height: 100%;
        border-radius: 10px;
        border-spacing: 0;
        table-layout: fixed;
        
        tr {
          height: 60px;
        }
        
        th, td {
          text-align: center;
          border-right: 2px solid #ccc;
          border-bottom: 2px solid #ccc;
          word-break: break-all;
          box-sizing: content-box;
          
          &:last-child {
            border-right: none;
          }
          
          &:deep(.uni-checkbox-input) {
            width: 30px;
            height: 30px;
            margin: 0;
            border-width: 2px;
          }
        }
        
        th {
          height: 80px;
          position: sticky;
          top: 0;
          z-index: 1;
          background-color: #fdf6e3;
          /* #ifdef APP */
          border-top: 1px solid #ccc;
          /* #endif */
          
          .create-info {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            align-items: center;
            
            .create-type-selector {
              width: calc(100% - 20px);
              height: 32px;
              position: relative;
              
              .create-type-value {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 10px;
                cursor: pointer;
                transition: all 0.3s linear;
              }
              
              .create-type-option {
                width: 100%;
                position: absolute;
                top: calc(100% + 15px);
                right: 0;
                border-radius: 10px;
                filter: drop-shadow(0 0 5px gray);
                background-color: #fdf6e3;
                z-index: 3;
                
                &::before {
                  content: "";
                  position: absolute;
                  top: -9px;
                  left: calc(50% - 10px);
                  border-left: 10px solid transparent;
                  border-right: 10px solid transparent;
                  border-bottom: 10px solid #fdf6e3;
                  z-index: -1;
                }
                
                .create-type-option-list {
                  width: 100%;
                  border-radius: 10px;
                  overflow: auto;
                  
                  .create-type-option-item {
                    width: 100%;
                    height: 40px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-weight: bold;
                    text-align: center;
                    border-top: 1px solid #ccc;
                    transition: all 0.1s linear;
                    
                    /* #ifdef APP */
                    &:active {
                      background-color: #ccc;
                    }
                    /* #endif */
                    
                    /* #ifdef WEB */
                    cursor: pointer;
                    &:hover {
                      background-color: #ccc;
                    }
                    /* #endif */
                  }
                  
                  .create-type-option-item:first-child {
                    border: none;
                  }
                }
              }
            }
          }
          
          .last-nos, .outsole {
            width: 100%;
            height: 100%;
            padding: 5px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            
            .last-nos-title, .outsole-title {
              width: 100%;
              margin: 5px;
            }
            
            .last-nos-value, .outsole-value {
              width: calc(100% - 10px);
              margin: 5px;
              padding: 5px;
            }
          }
        }
        
        td {
          .operate {
            .edit, .reset, .preview, .export, .send {
              width: 60px;
              height: 40px;
              margin: 10px;
            }
            
            .edit {
              position: relative;
              
              .button {
                width: 100%;
                height: 100%;
              }
              
              .complete {
                position: absolute;
                right: -10px;
                top: -10px;
              }
            }
          }
          
          .model-no, .edition, .type, .material, .sku {
            width: 100%;
            min-height: 60px;
            padding: 5px;
            
            .button, .value {
              width: 180px;
              min-height: 40px;
              padding: 5px;
              word-break: break-all;
            }
          }
          
          .model-no {
            min-height: 50px;
          }
          
          .model-desc, .operation {
            padding: 5px;
            
            .value {
              width: 180px;
              word-break: break-all;
            }
          }
          
          .model-no .button {
            color: blue;
          }
          
          .type .button {
            color: seagreen;
          }
          
          .material .button {
            color: rebeccapurple;
          }
          
          .edition, .sku {
            padding: 10px;
            
            .button {
              width: 100%;
            }
          }
          
          .edition .button {
            color: coral;
          }
          
          .sku .button {
            color: darkgoldenrod;
          }
          
          .create-info {
            width: fit-content;
            margin: 5px;
            text-align: left;
            
            .flow-number, .create-user, .create-date, .create-type {
              margin: 5px 0;
            }
          }
        }
      }
      
      .load-more {
        width: 100%;
        
        &:deep(.uni-load-more__text) {
          font-size: 18px;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .watermark {
    position: relative;
    word-break: break-all;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    
    &:before {
      content: 'STELLA';
      position: absolute;
      top: 2px;
      left: 2px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      text-shadow: 0 0 2px black;
      z-index: 1;
      pointer-events: none;
    }
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
  
  .flex-row-start {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
  }
  
  .flex-row-start-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
  }
  
  .flex-row-evenly-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    align-items: center;
  }
  
  .flex-column-center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  
  /* #ifdef WEB */
  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.3s ease;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  
  .fade-enter-to,
  .fade-leave-from {
    opacity: 1;
  }
  /* #endif */
}
</style>