<template>
    <view class="container">
        <view class="back">
            <uni-icons @click="back" type="back" size="36"></uni-icons>
        </view>
		
		<view style="width: 100%; text-align: center; font-size: 24px;">
			<text>倉庫物料配套</text>
		</view>
		
		<view class="search">
			<text>出货时间</text>
			<uv-datetime-picker ref="datetimePickerSt" v-model="startTime" mode="datetime" @confirm="confirmSt"></uv-datetime-picker>
			<view class="inputDate">
				<uv-input disabled @click="openSt" v-model="startTimeText"></uv-input>
			</view>
			<uv-datetime-picker ref="datetimePickerEnd" v-model="endTime" mode="datetime" @confirm="confirmEnd"></uv-datetime-picker>
			<view class="inputDate">
				<uv-input disabled @click="openEnd" v-model="endTimeText"></uv-input>
			</view>
			<text style="margin-left: 25px;">品牌</text>
			<view class="inputDate">
				<uv-input v-model="brand"></uv-input>
			</view>
			
			<text style="margin-left: 25px;">样品类型</text>
			<view style="width: 20%;">
				<uni-data-select v-model="devType" :localdata="devTypes"
								 :clear="false"
								 emptyTips="請選擇"
								 style="width: 80%;margin-left: 16px;background-color: white;"></uni-data-select>
			</view>
			<uv-button @click="query()" style="margin-left: 25px;" type="primary" text="查询"></uv-button>
		</view>

        <view class="title">
            <table>
                <thead>
                <tr>
                    <th class="rowClass">样品单号</th>
                    <th class="rowClass">样品类型</th>
                    <th class="rowClass">鞋图</th>
                    <th class="rowClass2" style="width: 15px;">业务</th>
                    <th class="rowClass">型体</th>
                    <th class="rowClass">楦头<br>编号</th>
                    <th class="rowClass">派工日</th>
                    <th class="rowClass">出货日</th>
                    <th class="rowClass">订单量</th>
                    <th class="rowClass">面料<br>配套</th>
                    <th class="rowClass">副料<br>配套</th>
                    <th class="rowClass">底料<br>配套</th>
                    <th class="rowClass">异常状况推送</th>
                    <th class="rowClass">异常原因</th>
                </tr>
                </thead>
                <tbody>
					<template v-for="(item, index) in tableData" :key="item.id">
						<tr>
							<td class="rowClass" rowspan="2">{{ item.ord_no }}</td>
							<td class="rowClass" rowspan="2">{{ item.dev_type }}</td>
							<td class="rowClass" rowspan="2"><img :src="'data:image/jpg;base64,' + item.model_pic " alt="鞋图"/></td>
							<td class="rowClass2" rowspan="2">{{ item.dutyer }}</td>
							<td class="rowClass" rowspan="2">{{ item.model_no }}</td>
							<td class="rowClass" rowspan="2" style="white-space: pre-wrap;">{{ item.last_no }}</td>
							<td class="rowClass" rowspan="2">{{ item.wo_date }}</td>
							<td class="rowClass" rowspan="2">{{ item.shp_date }}</td>
							<td class="rowClass" rowspan="2">{{ item.tot_qty }}</td>
							<td style="text-decoration: underline;color: skyblue;" class="rowClass" rowspan="2" @click="viewDetail(item.ord_no)">{{ item.t1_flag }}</td>
							<td class="rowClass" rowspan="2">{{ item.t3_flag }}</td>
							<td class="rowClass" rowspan="2">{{ item.t2_flag }}</td>
							<td class="rowClass" rowspan="2">
								<uv-button type="primary" text="推送" @click="getUser('')"></uv-button>
							</td>
						</tr>
						<tr>
							<td class="rowClass">{{ item.u_f_status }}</td>
						</tr>
					</template>
                </tbody>
            </table>
        </view>
		
		<!--单击单行弹出界面-->
		<view class="updateSpecificationPopup">
		  <uni-popup
		    ref="updateSpecificationPopup"
		    type="center"
			style="height: 80%;">
		  
		    <view class="updateSpecificationBox">
		
		      <uni-title title="倉庫掃描明細" type="h2" align="center" ></uni-title>
			  <uni-icons type="closeempty" @click="backDrom" size="32" style="position: absolute;top: 25rpx;right: 1vw;"></uni-icons>
		      <view class="updateSpecificationData">
					<zb-table
						:show-header="true"
						:columns="column"
						:stripe="true"
						ref="zbTable"
						:fit="true"
						:border="true"
						:cell-style="cellStyle"
						:data="detailTableData"></zb-table>
		      </view>
			</view>
			</uni-popup>
		</view>
		
		<!-- 异常推送选中用户 -->
		<view class="updateSpecificationPopup">
		  <uni-popup
		    ref="userPopup"
		    type="center"
			style="height: 80%;">
		  
		    <view class="updateSpecificationBox">
		
		      <uni-title title="推送明細" type="h2" align="center" ></uni-title>
			  <uni-icons type="closeempty" @click="backDrom" size="32" style="position: absolute;top: 25rpx;right: 1vw;"></uni-icons>
		      <view class="updateSpecificationData">
					  <uni-easyinput :styles="{ color: '#2979FF', borderColor: '#2979FF' }" type="text" class="uni-input"
									 @input="getUser"  placeholder="輸入内容,最少3位字符"/>
					<zb-table
						:show-header="true"
						:columns="userTablecolumn"
						:stripe="true"
						ref="zbTable"
						:fit="true"
						:border="true"
						:cell-style="cellStyle"
						:data="userTableData"></zb-table>
		      </view>
			</view>
			</uni-popup>
		</view>
		
		<view class="left-bottom">
			<uni-pagination show-icon="true" :total="pageCount" :current="firstPageNo" :pageSize="firstPageSize"
				@change="firstChange"></uni-pagination>
		</view>
    </view>

</template>

<script setup>
import {
    onMounted,
    ref,
    reactive,
    watch
} from 'vue'
import {
    onPullDownRefresh
} from '@dcloudio/uni-app'
import {
    onShow
} from '@dcloudio/uni-app';
import urlPrefix from '@/pages/common/urlPrefix.js'

//第几页
const firstPageNo = ref(1)
const firstPageSize = ref(6)
const pageCount = ref(0)

//表单数据
const dataList = ref([])

const insUs = uni.getStorageSync("loUserNo")
const insName = uni.getStorageSync("loginUserName")

//日期选择
const endTime = ref(new Date().getTime())
const date = new Date();
date.setHours(date.getHours() - 24);
const startTime = ref(date.getTime())
const datetimePickerSt = ref()
const datetimePickerEnd = ref()
const startTimeText = ref('2024/06/03')
const endTimeText = ref('2024/06/03')

const cfStatus = ref(false);
const brand = ref()
const devType = ref()
const devTypes = ref([])
const fileType = ref()
const fileTypeList = ref([
	{'value':0,'text':'請選擇'},
	{'value':1,'text':'優先'},
	{'value':2,'text':'可配套生產'},
	{'value':3,'text':'只可面部生產'},
	{'value':4,'text':'只可裁斷生產'}
])

const cutComplType = ref()
const cutComplTypeList = ref([
	{'value':0,'text':'請選擇'},
	{'value':1,'text':'已完成'},
	{'value':2,'text':'未完成'}
])

const tableData = ref([]);

const updateSpecificationPopup = ref();
const userPopup = ref();
const column=ref([
          { name: 'mat_type', label: '倉位',emptyString:'--',width:150},
		  { name: 'sh_date', label: '出貨日期',emptyString:' ',width:150},
		  { name: 'ord_no', label: '樣品單號',width:150},
		  { name: 'brand_no', label: '品牌簡稱',emptyString:'/'},
		  { name: 'season_no', label: '季節序號',emptyString:'/'},
		  { name: 'phase', label: '階段',emptyString:'/'},
          { name: 'dev_type', label: '樣品類型',emptyString:'/'},
          { name: 'mat_seq', label: '品牌料號',emptyString:'/'},
          { name: 'req_qty', label: '需求數量',emptyString:'/'},
          { name: 'suom', label: '庫存單位',emptyString:'/'},
          { name: 'bat_qty', label: '庫存數量',emptyString:'/'},
          { name: 'shelf_pos', label: '存放架位',emptyString:'/'},
          { name: 'give_cdate', label: '交貨日期',emptyString:'/'},
          { name: 'str_date', label: '驗收日期',emptyString:'/'},
          { name: 'mat_desc', label: '材料描述',emptyString:'/'}
        ]);
		
const userTablecolumn=ref([
		{ name: '', type:'operation',label: '推送',renders:[
					 {
					   name:'推送',
					   type:'warn',
					   func:'dele'// func 代表子元素点击的事件 父元素接收的事件 父元素 @edit
					 }
		 ]},
		  { name: 'userId', label: '用户编号',emptyString:'--',width:150},
		  { name: 'userDesc', label: '用户名',emptyString:'--',width:150}
		]);

const detailTableData = ref([]);
const userTableData = ref([]);

//关闭弹窗
function backDrom() {
  detailTableData.value=[];
  userTableData.value=[];
  updateSpecificationPopup.value.close()
  userPopup.value.close()
}	

function openSt() {
	datetimePickerSt.value.open();
}

function confirmSt(e) {
	console.log('confirm', e);
	// 创建一个新的日期对象e
	var date = new Date(e.value);
	
	// 获取年、月、日
	var year = date.getFullYear();
	var month = date.getMonth() + 1; // 月份从0开始，需要加1
	var day = date.getDate();
	
	// 格式化日期为yyyy/MM/DD的样式
	var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
	
	startTimeText.value = formattedDate;
}

//判断颜色
function cellStyle({row, column, rowIndex, columnIndex}){
	if(row.key_flag == "Y"){
		return {
		  'background-color': 'red',
		  'color': 'white'
		};
	}
	// 如果不符合条件，返回空对象
	return {};
}


function openEnd() {
	datetimePickerEnd.value.open();
}

function confirmEnd(e) {
	// 创建一个新的日期对象e
	var date = new Date(e.value);
	
	// 获取年、月、日
	var year = date.getFullYear();
	var month = date.getMonth() + 1; // 月份从0开始，需要加1
	var day = date.getDate();
	
	// 格式化日期为yyyy/MM/DD的样式
	var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
	
	endTimeText.value = formattedDate;
}

const totalOrderQuantity = ref()

function query(){
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
	firstPageNo.value = 1;
	uni.request({
	    url: urlPrefix + "/materialmatch/query",
	    data: {
	        "pageNo": 1,
	        "pageSize": firstPageSize.value,
			"startTime": startTime.value,
			"endTime": endTime.value,
			"brand": brand.value,
			"devType": devType.value,
			"fileType": fileType.value,
			"cutComplType": cutComplType.value
	    },
	    method: "GET"
	}).then(res => {
		totalOrderQuantity.value = res.data.data.list[0].total_order_quantity;
	    tableData.value = res.data.data.list
		pageCount.value = res.data.data.total;
		uni.hideLoading();
	}).catch(err => {
	    console.log(err)
		uni.hideLoading();
	})
}

const currentOrdNo = ref();

//查看明细
function viewDetail(ord_no){
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
	currentOrdNo.value = ord_no;
	console.log(ord_no)
	uni.request({
	    url: urlPrefix + "/materialmatch/queryDetailTableData",
	    data: {
	        "ord_no": ord_no
	    },
	    method: "GET"
	}).then(res => {
	    detailTableData.value = res.data;
		uni.hideLoading();
	}).catch(err => {
	    console.log(err)
		uni.hideLoading();
	})
	updateSpecificationPopup.value.open()
}

function getUser(event){
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
	if(event.length > 0 && event.length < 3){
		return;
	}
	uni.request({
	   url: urlPrefix + "/user/getUser?userNo="+event+"",
	   method: "GET"
	 }).then(res => {
		userTableData.value.length = 0;
		const convertedArray = res.data.data.map(item => {
				userTableData.value.push({ userId: item.userId, userDesc: item.userDesc });
		});
		uni.hideLoading();
	 }).catch(err => {
	   console.log(err)
	   uni.hideLoading();
	 });
	 userPopup.value.open()
}

function manualClose(){
	uni.showModal({
		title: '提示',
		content: '确定要更新这条记录吗？',
		confirmColor: "#ff0000",
		success: function(res) {
			if (res.confirm) {
				console.log('用户点击确定');
				uni.request({
					url: urlPrefix + "/materialmatch/manualClose",
					data: {
						"ord_no": currentOrdNo.value
					},
					method: "POST"
				}).then(res => {
					if (res.statusCode != 200) {
						uni.showToast({
							title: res.data.message,
							icon: "error"
						});
					} else{
						updateSpecificationPopup.value.close();
						query();
					}
				}).catch(err => {
					console.log(err);
					uni.showToast({
						title: '操作失败..',
						icon: "error"
					});
				})
			} else if (res.cancel) {
				console.log('用户点击取消');
			}
		}
	});
	
}


//返回首页
function back() {
    let back = getCurrentPages();
    if(back && back.length > 1) {
    	uni.navigateBack({  
    	  delta: 1  
    	});  
    } else {  
    	history.back();  
    }
}

function prodInvestment(ordNo,itemNo){
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
	var text = {'ord_no':ordNo,'item_no':itemNo};
	uni.request({
		url: urlPrefix + "/materialmatch/update",
		data: text,
		method: "POST"
	}).then(res => {
		if(res.statusCode == 200){
			uni.showToast({
				title: res.data.data,
				icon: "success"
			});
			query();
		} else {
			uni.showToast({
				title: res.data.message,
				icon: "error"
			});
		} 
	}).catch(err => {
		console.log(err)
		uni.showToast({
			title: '保存数据失败..',
			icon: "error"
		});
	})
}

async function firstChange(e) {
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
    firstPageNo.value = e.current;
    await uni.request({
        url: urlPrefix + "/materialmatch/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
			"startTime": startTime.value,
			"endTime": endTime.value,
			"brand": brand.value,
			"devType": devType.value,
			"fileType": fileType.value,
			"cutComplType": cutComplType.value
        },
        method: "GET"
    }).then(res => {
        //console.log(res.data);
		totalOrderQuantity.value = res.data.data.list[0].total_order_quantity;
        tableData.value = res.data.data.list
		uni.hideLoading();
    }).catch(err => {
        console.log(err)
		uni.hideLoading();
    })
}

const tableRef = ref();


//获取数据
function queryAllDevType() {
    uni.request({
        url: urlPrefix + "/qdpt/queryAllDevType",
        method: "GET"
    }).then(res => {
        console.log(res.data.data);
		devTypes.value = res.data.data;
    }).catch(err => {
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

//获取数据
async function getData() {
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
	tableData.value = [];
    if (tableRef.value) {
        tableRef.value.clearSelection();
    }
    await uni.request({
        url: urlPrefix + "/materialmatch/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
            "startTime": startTime.value,
            "endTime": endTime.value,
            "brand": brand.value,
            "devType": devType.value
        },
        method: "GET"
    }).then(res => {
		totalOrderQuantity.value = res.data.data.list[0].total_order_quantity;
        dataList.value = res.data.data.list;
        pageCount.value = res.data.data.total;
		tableData.value = res.data.data.list;
		uni.hideLoading();
    }).catch(err => {
		uni.hideLoading();
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

//登录校验
function loginCheck(){
	if(uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == ''){
		uni.navigateTo({
			url: `/pages/login/login`,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}
}

onShow(async (props) => {
	loginCheck();
})

//预加载
onMounted(async () => {
	if(1 == 1){
		// 创建一个新的日期对象e
		var date = new Date();
		
		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		
		// 格式化日期为yyyy/MM/DD的样式
		var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
		
		endTimeText.value = formattedDate;
	}
	if(1 == 1){
		// 创建一个新的日期对象e
		var date = new Date();
		
		// 获取当前日期的天数
		var temp = date.getDate();
		
		// 将日期减去一天
		date.setDate(temp - 1);
		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		
		var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
		startTimeText.value = formattedDate;
	}
    await getData()
	queryAllDevType();
})

//预加载
onShow(async () => {
    // await getData();
})
</script>

<style lang="scss">
page {
    width: 100%;
    height: 100%;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;
}

.back {
	width: 50px;
	height: 50px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    position: absolute;
    left: 1.5%;
    top: 3%;
	border-radius: 50%;
	box-shadow: 0 0 5px gray;
    cursor: pointer;
    z-index: 1;
}


.search {

	display: flex;
	align-items: center;
	margin-left: 1%;
	margin-top: 2%;
	
	.inputDate{
		width: 12%;
		margin-left: 5px;
	}
	
	.search button{
		width: 20%;
	}
}

.container {
    width: 100%;
    height: 100%;
    padding: 1%;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0 0 1px 5px #dddddd;
    position: relative;
}

.right-top-top {
    display: flex;
}

.inpBr {
    width: 15%;
    margin-left: 10rpx;
}

.left-bottom {
    width: 100%;
    height: 10%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.uni-pagination__total {
    margin-right: 0.4375rem !important;
}

.uni-pagination__num-tag {
    min-width: 1.875rem !important;
    background-color: #F0F0F0 !important;
}

.page--active {
    color: white !important;
    background-color: deeppink !important;
}

.title {
    width: 100%;
    overflow-x: auto; /* 添加横向滚动条 */
    height: 78%;
    margin-bottom: 1%;
    margin-top: 1%;
    display: block;
	table-layout: fixed; /* 使用固定布局 */

}

.table-container {
    width: 100%;
    overflow-x: auto; /* 使表格容器可横向滚动 */
}

table {
	background-color: #fff;
	table-layout: fixed; /* 使用固定布局 */
    border-collapse: collapse;
    position: relative;
    border-radius: 5px;
    box-sizing: border-box;
}

tbody {
}

thead {
	background-color: #F0F0F0;
}

.rowClass {
	width: 150px;
    height: 2.5rem;
    border: 2px solid #ddd;
    padding: 8px;
    text-align: center;
    box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
    overflow: visible; /* 允许内容溢出 */
    white-space: nowrap; /* 禁止换行 */
}

.rowClass2 {
	width: 30px;
    height: 1rem;
    border: 2px solid #ddd;
    padding: 8px;
    text-align: center;
    box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
    // white-space: nowrap; /* 禁止换行 */
}

tr {
    display: table-row;
}

img {
    width: 50px;
    height: auto;
}

.left-bottom {
	width: 100%;
	height: 10%;
	display: flex;
	justify-content: center;
	align-items: center;
	
	
	.uni-pagination__total {
		margin-right: 0.4375rem !important;
	}
	
	.uni-pagination__num-tag {
		min-width: 1.875rem !important;
		background-color: #F0F0F0 !important;
	}
	
	.page--active {
		color: white !important;
		background-color: deeppink !important;
	}
}

.updateSpecificationPopup {
  .updateSpecificationBox {
	width: 96vw;
	height: 90vh;
	
	border-radius: 1vw;
	background-color: white;
	
	.updateSpecificationData {
	  width: 100%;
	  position: relative;
	  display: flex;
	  justify-content: center;
	  align-items: flex-start;
	  flex-wrap: wrap;
	  height: 85%;
	  
	  .updateSpecificationAttribute {
		width: 35%;
		margin-left: 15%;
		
		.uni-easyinput {
		  width: 70%;
		  margin-left: 1rem;
		}
		
		.uni-stat__select {
		  width: 70%;
		  margin-left: 1rem;
		}
		
		.uni-numbox {
		  margin-left: 1rem;
		}
		
		.uni-file-picker {
		  margin-left: 1rem;
		}
	  }
	}
  }
}
</style>