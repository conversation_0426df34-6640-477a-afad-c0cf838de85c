<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import urlPrefix from '@/pages/common/urlPrefix.js'

// 国际化
const { t } = useI18n()

// 生产环境
// const urlPrefix = "http://***********:8200"
const user = uni.getStorageSync("loUserNo")

// 开发环境
// const urlPrefix = "http://**********:8080"
// const user = "zqn"

const brand = ref('AB')
const brandList = ref([])
const brandPopup = ref()
// 滚动 id
const scrollId = ref('A')

const searchType = ref(true)
const shoe_last = ref('')
const shoeLastModelList = ref([])
const shoeLastModelListShow = ref(false)
const searchShoeLastModel = ref([])

const model_no = ref('')
const modelList = ref([])
const modelListShow = ref(false)
const searchInput = ref()
const searchModel = ref([])
const modelPicture = ref()
const modelPicturePopup = ref()
const modelData = reactive({
  factory: "",
  brand_no: "",
  season_no: "",
  model_no: "",
  model_desc: "",
  siz_type: "",
  bas_size: "",
  series: "",
  last_nos: "",
  upper_der: "",
  sole_der: ""
})

const showSizeTypeList = ref(false)
const sizeTypeList = ref([])
const showBaseSizeList = ref(false)

const previewDetail = reactive({
  model_no: "",
  siz_type: "",
  bas_size: 0
})
const previewPopup = ref()
const picture = ref('')
const info = ref()
const type1Number = ref(0)
const type2Number = ref(0)
const type1Title = ref([])
const type2Title = ref([])
const type1Remark = ref('')
const type2Remark = ref('')
const type1Count = ref()
const type2Count = ref()
const sizeType = ref(false)
const insertUser = ref('')
const insertDate = ref('')
const updateUser = ref('')
const updateDate = ref('')

const chkSignature = ref('')
const uppSignature = ref('')
const solSignature = ref('')
const signatureType = ref(0)
const signaturePopup = ref()

const specificationList = ref([])
const specificationPictureDetail = reactive({
  model_no: "",
  procs_type: 1,
  procs_pic: "",
  s_size: 0,
  e_size: 0,
  remark: "",
  ins_user: "",
  ins_date: "",
  upd_user: user,
  upd_date: ""
})
const specificationPicturePopup = ref()
const resetPicturePopup = ref()

const sizeOption = ref([])
const startSizeOption = ref([])
const endSizeOption = ref([])
const showStartSizeOption1 = ref(false)
const showStartSizeOption2 = ref(false)
const showEndSizeOption1 = ref(false)
const showEndSizeOption2 = ref(false)

const addSpecificationPopup = ref()
const addSpecificationDetail = reactive({
  model_no: "",
  procs_type: 1,
  procs_pic: "",
  s_size: 0,
  e_size: 0,
  remark: "",
  ins_user: user,
  ins_date: "",
  upd_user: user,
  upd_date: ""
})

const oldSpecificationRemark = ref('')

const deleteSpecificationPopup = ref()
const deleteSpecificationDetail = reactive({
  model_no: "",
  procs_type: 1
})

const partList = ref([])
const part1List = ref([])
const part2List = ref([])
const partTable = ref()
const partPopup = ref()

const showSelectPart = ref(false)
const selector = ref(0)

const option1List = ref([])
const option2List = ref([])
const option1Order = ref(new Map())
const option2Order = ref(new Map())
const option1Seq = ref(new Map())
const option2Seq = ref(new Map())

const addPartPopup = ref()
const addPart1NameList = ref([])
const addPart2NameList = ref([])
const addPartList = ref([])
const addPartDetail = reactive({
  model_no: "",
  procs_type: 1,
  part_seq: "01",
  part_name: "楦頭",
  part_spec: 0,
  add_per: 0,
  ins_user: user,
  ins_date: "",
  upd_user: user,
  upd_date: ""
})

const temporaryPartPopup = ref()
const temporaryPartDetail = reactive({
  model_no: "",
  procs_type: 1,
  part_seq: "99",
  part_name: "",
  part_spec: 0,
  add_per: 0,
  ins_user: user,
  ins_date: "",
  upd_user: user,
  upd_date: ""
})

const oldPartSpec = ref(0)
const oldAddPer = ref(0)

const deletePartPopup = ref()
const deletePartDetail = reactive({
  model_no: "",
  procs_type: 1,
  part_name: ""
})

const partIndexArray = ref([])
const batchDeletePartPopup = ref()
const batchDeletePartList = ref([])

const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')

// 监视输入框中的 model_no，更新搜索提示列表
watch(model_no, () => {
  searchModel.value = modelList.value.filter(item => item.model_no.includes(model_no.value.toUpperCase())).slice(
    0, 50)
})

// 监听打印报表页面的 size 类型
watch(sizeType, (newValue, oldValue) => {
  updateFullSize(newValue)
  if (newValue == true) {
    getCount2()
  } else {
    getCount1()
  }
})

// 根据楦头编号查询型体
function getShoeLastModelList() {
  uni.request({
    url: urlPrefix + "/first/getListByShoeLast",
    method: "POST",
    data: {
      brand_no: brand.value,
      shoe_last: shoe_last.value
    }
  }).then(res => {
    shoeLastModelList.value = res.data.data ? res.data.data : []
    // searchShoeLastModel.value = shoeLastModelList.value.slice(0, 50)
    searchShoeLastModel.value = shoeLastModelList.value
    shoeLastModelListShow.value = true
  }).catch(err => {
    console.log(err)
  })
}

// 返回上一页
function back() {
  uni.navigateBack({
    delta: 1,
    animationType: 'pop-out',
    animationDuration: 200
  })
}

// 获取品牌列表
function getBrands() {
  uni.request({
    url: urlPrefix + "/first/getBrandsPlus",
    method: "POST"
  }).then(res => {
    brandList.value = res.data.data ? res.data.data.filter(item => item.letter !== 'ALL') : []
  }).catch(err => {
    console.log(err)
  })
}

// 选择品牌
function select(param) {
  model_no.value = ""
  shoe_last.value = ""
  brand.value = param
  brandPopup.value.close()

  uni.request({
    url: urlPrefix + "/first/getList",
    method: "POST",
    data: {
      brand_no: brand.value
    }
  }).then(res => {
    modelList.value = res.data.data ? res.data.data : []
    searchModel.value = modelList.value.filter(item => item.model_no.includes(model_no.value.toUpperCase()))
      .slice(0, 50)
  }).catch(err => {
    console.log(err)
  })

  modelList.value = []

  modelData.factory = ""
  modelData.brand_no = ""
  modelData.season_no = ""
  modelData.model_no = ""
  modelData.model_desc = ""
  modelData.siz_type = ""
  modelData.bas_size = ""
  modelData.series = ""
  modelData.last_nos = ""
  modelData.upper_der = ""
  modelData.sole_der = ""

  modelPicture.value = ""

  specificationList.value = []

  partList.value = []

  selector.value = 0
}

// 获取型体图片
function getModelPicture(param) {
  modelPicture.value = ""
  uni.request({
    url: urlPrefix + "/first/getModelPicture",
    method: "POST",
    data: {
      model_no: param
    }
  }).then(res => {
    modelPicture.value = res.data.data ? res.data.data.model_pic : ""
  }).catch(err => {
    console.log(err)
  })
}

// 显示码别选项
function showSizeTypeOption() {
  showSizeTypeList.value = !showSizeTypeList.value

  showBaseSizeList.value = false
  showStartSizeOption1.value = false
  showStartSizeOption2.value = false
  showEndSizeOption1.value = false
  showEndSizeOption2.value = false
  showSelectPart.value = false
}

// 获取码别选项
function getSizeTypeOption() {
  uni.request({
    url: urlPrefix + "/first/getSizeTypeList",
    method: "POST",
    data: {
      brand_no: brand.value
    }
  }).then(res => {
    sizeTypeList.value = res.data.data ? res.data.data : []
  }).catch(err => {
    console.log(err)
  })
}

// 修改码别
function updateSizeType(param) {
  if (param == modelData.siz_type) {
    return
  }

  uni.request({
    url: urlPrefix + "/first/updateSizeType",
    method: "POST",
    data: {
      model_no: modelData.model_no,
      siz_type: param
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getSizeOption(param)
      let minSize = sizeOption.value[0]
      let maxSize = sizeOption.value[sizeOption.value.length - 1]

      const updateBaseSize = uni.request({
        url: urlPrefix + "/first/updateBaseSize",
        method: "POST",
        data: {
          model_no: modelData.model_no,
          bas_size: "",
          upd_user: user
        }
      })
      const updateStartSize = uni.request({
        url: urlPrefix + "/second/updateStartSize",
        method: "POST",
        data: {
          model_no: modelData.model_no,
          s_size: minSize,
          upd_user: user
        }
      })
      const updateEndSize = uni.request({
        url: urlPrefix + "/second/updateEndSize",
        method: "POST",
        data: {
          model_no: modelData.model_no,
          e_size: maxSize,
          upd_user: user
        }
      })

      Promise.all([updateBaseSize, updateStartSize, updateEndSize])
        .then(res => {
          getModel(modelData.model_no)
          getSpecification(modelData.model_no)
          showTip('success', t('码别修改成功！'))
        }).catch(err => {
          showTip('error', t('码别修改失败！'))
        })
    } else {
      showTip('error', t('码别修改失败！'))
    }
  }).catch(err => {
    showTip('error', t('码别修改失败！'))
  })
}

// 显示基本码选项
function showBaseSizeOption() {
  showBaseSizeList.value = !showBaseSizeList.value

  showSizeTypeList.value = false
  showStartSizeOption1.value = false
  showStartSizeOption2.value = false
  showEndSizeOption1.value = false
  showEndSizeOption2.value = false
  showSelectPart.value = false
}

// 修改基本码
function updateBaseSize(param) {
  if (param == modelData.bas_size) {
    return
  }

  uni.request({
    url: urlPrefix + "/first/updateBaseSize",
    method: "POST",
    data: {
      model_no: modelData.model_no,
      bas_size: param,
      upd_user: user
    }
  }).then(res => {
    if (res.data.code) {
      getModel(modelData.model_no)
      showTip('success', t('基本码修改成功！'))
    } else {
      showTip('error', t('基本码修改失败！'))
    }
  }).catch(err => {
    showTip('error', t('基本码修改失败！'))
  })
}

// 获取号码选项
function getSizeOption(param) {
  uni.request({
    url: urlPrefix + "/first/getSizeOption",
    method: "POST",
    data: {
      siz_type: param
    }
  }).then(res => {
    sizeOption.value = res.data.data ? res.data.data : []
  }).catch(err => {
    console.log(err)
  })
}

// 重置号码选项
function resetSizeOption() {
  startSizeOption.value = []
  endSizeOption.value = []

  for (let item of sizeOption.value) {
    startSizeOption.value.push({
      text: item,
      value: item,
      disable: false
    })

    endSizeOption.value.push({
      text: item,
      value: item,
      disable: false
    })
  }

  addSpecificationDetail.s_size = startSizeOption.value.length > 0 ? startSizeOption.value[0].value : 0
  addSpecificationDetail.e_size = endSizeOption.value.length > 0 ? endSizeOption.value[endSizeOption.value.length - 1].value : 0
}

// 获取型体数据
function getModel(param) {
  uni.request({
    url: urlPrefix + "/first/getModel",
    method: "POST",
    data: {
      model_no: param
    }
  }).then(res => {
    let data = res.data.data ? res.data.data : {}
    modelData.factory = data.factory,
    modelData.brand_no = data.brand_no,
    modelData.season_no = data.season_no,
    modelData.model_no = data.model_no,
    modelData.model_desc = data.model_desc,
    modelData.siz_type = data.siz_type,
    modelData.bas_size = data.bas_size,
    modelData.series = data.series,
    modelData.last_nos = data.last_nos,
    modelData.upper_der = data.upper_der,
    modelData.sole_der = data.sole_der

    getSizeTypeOption()
    getSizeOption(modelData.siz_type)
  }).catch(err => {
    console.log(err)
  })
}

// 获取规格数据
async function getSpecification(param) {
  await uni.request({
    url: urlPrefix + "/second/getSpecification",
    method: "POST",
    data: {
      model_no: param
    }
  }).then(res => {
    specificationList.value = res.data.data ? res.data.data : []
  }).catch(err => {
    console.log(err)
  })
}

// 自动添加规格数据
async function autoAddSpecification() {
  if (specificationList.value.length > 0) {
    showTip('warn', t('请先清空规格数据！'))
    return
  }
  
  await uni.request({
    url: urlPrefix + "/second/addSpecification",
    method: "POST",
    data: {
      model_no: modelData.model_no,
      procs_type: 1,
      procs_pic: "",
      s_size: sizeOption.value[0],
      e_size: sizeOption.value[sizeOption.value.length - 1],
      remark: "",
      ins_user: user,
      ins_date: "",
      upd_user: user,
      upd_date: ""
    }
  })
  
  await uni.request({
    url: urlPrefix + "/second/addSpecification",
    method: "POST",
    data: {
      model_no: modelData.model_no,
      procs_type: 2,
      procs_pic: "",
      s_size: sizeOption.value[0],
      e_size: sizeOption.value[sizeOption.value.length - 1],
      remark: "",
      ins_user: user,
      ins_date: "",
      upd_user: user,
      upd_date: ""
    }
  })
  
  await getSpecification(modelData.model_no)
  
  showTip('success', t('规格数据自动添加成功！'))
}

// 获取部位数据
function getPart(param) {
  selector.value = 0
  partIndexArray.value = []
  partTable.value.clearSelection()

  uni.request({
    url: urlPrefix + "/third/getPart",
    method: "POST",
    data: {
      model_no: param
    }
  }).then(res => {
    part1List.value = res.data.data ? res.data.data.part1List : []
    part2List.value = res.data.data ? res.data.data.part2List : []
    partList.value = part1List.value.concat(part2List.value)
  }).catch(err => {
    console.log(err)
  })
}

// 根据型体编号进行搜索
async function search(param) {
  model_no.value = param
  getModelPicture(param)
  await getModel(param)
  getSpecification(param)
  getPart(param)
}

// 获取报表型体图片
function getPicture() {
  uni.request({
    url: urlPrefix + "/report/getPicture",
    method: "POST",
    data: {
      model_no: previewDetail.model_no
    }
  }).then(res => {
    picture.value = res.data.data ? res.data.data.procs_pic : ""
  }).catch(err => {
    console.log(err)
  })
}

// 获取报表信息
function getInfo() {
  uni.request({
    url: urlPrefix + "/report/getInfo",
    method: "POST",
    data: {
      model_no: previewDetail.model_no
    }
  }).then(res => {
    info.value = res.data.data ? res.data.data : {}
  }).catch(err => {
    console.log(err)
  })
}

// 获取报表部位码
function getTitle() {
  uni.request({
    url: urlPrefix + "/report/getTitle",
    method: "POST",
    data: {
      model_no: previewDetail.model_no,
      siz_type: previewDetail.siz_type,
      bas_size: previewDetail.bas_size
    }
  }).then(res => {
    let data = res.data.data ? res.data.data : []
    for (let i = 0; i < data.length; i++) {
      for (let key in data[i]) {
        if (data[i].procs_type === "1") {
          if (key.startsWith("size") && key.substring(4) <= '25' && data[i][key]) {
            type1Number.value++
            type1Title.value.push(data[i][key])
          }
          if (key === "remark") {
            type1Remark.value = data[i][key]
          }
        }
        if (data[i].procs_type === "2") {
          if (key.startsWith("size") && key.substring(4) <= '25' && data[i][key]) {
            type2Number.value++
            type2Title.value.push(data[i][key])
          }
          if (key === "remark") {
            type2Remark.value = data[i][key]
          }
        }
      }
    }
  }).catch(err => {
    console.log(err)
  })
}

// 获取报表部位规格（半码）
function getCount1() {
  uni.request({
    url: urlPrefix + "/report/getCount1",
    method: "POST",
    data: {
      model_no: previewDetail.model_no,
      siz_type: previewDetail.siz_type,
      bas_size: previewDetail.bas_size,
      procs_type: 1
    }
  }).then(res => {
    type1Count.value = res.data.data ? res.data.data : {}
  }).catch(err => {
    console.log(err)
  })

  uni.request({
    url: urlPrefix + "/report/getCount1",
    method: "POST",
    data: {
      model_no: previewDetail.model_no,
      siz_type: previewDetail.siz_type,
      bas_size: previewDetail.bas_size,
      procs_type: 2
    }
  }).then(res => {
    type2Count.value = res.data.data ? res.data.data : {}
  }).catch(err => {
    console.log(err)
  })
}

// 获取报表部位规格（整码）
function getCount2() {
  uni.request({
    url: urlPrefix + "/report/getCount2",
    method: "POST",
    data: {
      model_no: previewDetail.model_no,
      siz_type: previewDetail.siz_type,
      bas_size: previewDetail.bas_size,
      procs_type: 1
    }
  }).then(res => {
    type1Count.value = res.data.data ? res.data.data : {}
  }).catch(err => {
    console.log(err)
  })

  uni.request({
    url: urlPrefix + "/report/getCount2",
    method: "POST",
    data: {
      model_no: previewDetail.model_no,
      siz_type: previewDetail.siz_type,
      bas_size: previewDetail.bas_size,
      procs_type: 2
    }
  }).then(res => {
    type2Count.value = res.data.data ? res.data.data : {}
  }).catch(err => {
    console.log(err)
  })
}

// 获取建表人信息
function getInsertInfo() {
  uni.request({
    url: urlPrefix + "/report/getInsertInfo",
    method: "POST",
    data: {
      model_no: previewDetail.model_no
    }
  }).then(res => {
    insertUser.value = res.data.data ? res.data.data.ins_user : ""
    insertDate.value = res.data.data ? res.data.data.ins_date : ""
    updateUser.value = res.data.data ? res.data.data.upd_user : ""
    updateDate.value = res.data.data ? res.data.data.upd_date : ""
  }).catch(err => {
    console.log(err)
  })
}

// 获取签名
function getSignature() {
  uni.request({
    url: urlPrefix + "/report/getSignature",
    method: "POST",
    data: {
      model_no: previewDetail.model_no
    }
  }).then(res => {
    chkSignature.value = res.data.data ? res.data.data.chk_pic : ""
    uppSignature.value = res.data.data ? res.data.data.upp_pic : ""
    solSignature.value = res.data.data ? res.data.data.sol_pic : ""
  }).catch(err => {
    console.log(err)
  })
}

// 获取整码、半码
async function getFullSize() {
  await uni.request({
    url: urlPrefix + "/report/getFullSize",
    method: "POST",
    data: {
      model_no: previewDetail.model_no
    }
  }).then(res => {
    sizeType.value = res.data.data ? res.data.data : false
  }).catch(err => {
    console.log(err)
  })
}

// 切换整码、半码
function updateFullSize(param) {
  uni.request({
    url: urlPrefix + "/report/updateFullSize",
    method: "POST",
    data: {
      model_no: modelData.model_no,
      full_size: param
    }
  }).then(res => {
    console.log(res)
  }).catch(err => {
    console.log(err)
  })
}

// 显示报表
async function showPreview(param) {
  previewDetail.model_no = param.model_no
  previewDetail.siz_type = param.siz_type
  previewDetail.bas_size = param.bas_size

  previewPopup.value.open()

  getPicture()
  getInfo()
  getTitle()
  getInsertInfo()
  getSignature()
  await getFullSize()
  if (sizeType.value) {
    getCount2()
  } else {
    getCount1()
  }
}

// 关闭报表
function closePreview() {
  picture.value = ""
  info.value = null
  type1Number.value = 0
  type2Number.value = 0
  type1Title.value = []
  type2Title.value = []
  type1Count.value = null
  type2Count.value = null
  type1Remark.value = ""
  type2Remark.value = ""

  insertUser.value = ""
  insertDate.value = ""
  updateUser.value = ""
  updateDate.value = ""
  chkSignature.value = ""
  uppSignature.value = ""
  solSignature.value = ""

  previewPopup.value.close()
}

// 扫码签名
function signature(param) {
  let url = urlPrefix
  if (param == 1) {
    url += '/report/saveChkSignature'
  } else if (param == 2) {
    url += '/report/saveUppSignature'
  } else if (param == 3) {
    url += '/report/saveSolSignature'
  } else {
    return
  }

  uni.scanCode({
    onlyFromCamera: true,
    scanType: ['qrCode'],
    success: (res) => {
      uni.request({
        url: url,
        method: "POST",
        data: {
          model_no: previewDetail.model_no,
          user_id: res.result,
          ins_user: insertUser.value,
          ins_date: insertDate.value,
          upd_user: updateUser.value,
          upd_date: updateDate.value
        }
      }).then(res => {
        if (res.data.code) {
          uni.showToast({
            icon: 'success',
            title: t('签名成功！')
          })
          getSignature()
        } else {
          uni.showToast({
            icon: 'error',
            title: t('签名失败！')
          })
        }
      }).catch(err => {
        uni.showToast({
          icon: 'error',
          title: t('签名失败！')
        })
      })
    }
  })

  // 跳转至签名界面
  // uni.navigateTo({
  //   url: `/pages/index/signature?model_no=${previewDetail.model_no}&type=${param}`
  // })
}

// 显示签名
function showSignature(param) {
  signatureType.value = param

  signaturePopup.value.open()
}

// 重置签名
function resetSignature() {
  if (signatureType.value === 1) {
    uni.request({
      url: urlPrefix + '/report/resetChkSignature',
      method: "POST",
      data: {
        model_no: previewDetail.model_no
      }
    }).then(res => {
      if (res.data.code) {
        signaturePopup.value.close()
        chkSignature.value = ""

        uni.showToast({
          title: t('签名重置成功！'),
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: t('签名重置失败！'),
          icon: 'error'
        })
      }
    }).catch(err => {
      uni.showToast({
        title: t('签名重置失败！'),
        icon: 'error'
      })
    })
  } else if (signatureType.value === 2) {
    uni.request({
      url: urlPrefix + '/report/resetUppSignature',
      method: "POST",
      data: {
        model_no: previewDetail.model_no
      }
    }).then(res => {
      if (res.data.code) {
        signaturePopup.value.close()
        uppSignature.value = ""

        uni.showToast({
          title: t('签名重置成功！'),
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: t('签名重置失败！'),
          icon: 'error'
        })
      }
    }).catch(err => {
      uni.showToast({
        title: t('签名重置失败！'),
        icon: 'error'
      })
    })
  } else if (signatureType.value === 3) {
    uni.request({
      url: urlPrefix + '/report/resetSolSignature',
      method: "POST",
      data: {
        model_no: previewDetail.model_no
      }
    }).then(res => {
      if (res.data.code) {
        signaturePopup.value.close()
        solSignature.value = ""

        uni.showToast({
          title: t('签名重置成功！'),
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: t('签名重置失败！'),
          icon: 'error'
        })
      }
    }).catch(err => {
      uni.showToast({
        title: t('签名重置失败！'),
        icon: 'error'
      })
    })
  } else {
    uni.showToast({
      title: t('签名重置失败！'),
      icon: 'error'
    })
  }
}

// 报表打印
function print() {
  //#ifdef APP-PLUS
  plus.runtime.openURL(urlPrefix + "/index.html?model_no=" + previewDetail.model_no, function() {
    showTip('warn', t('请先安装 Edge 浏览器！'))
  }, 'com.microsoft.emmx');
  //#endif

  //#ifdef H5
  window.location.href = urlPrefix + "/index.html?model_no=" + previewDetail.model_no
  //#endif
}

// 保存型体图片
function saveImage() {
  //#ifdef APP-PLUS
  let base64 = 'data:image/jpg;base64,' + modelPicture.value
  let bitmap = new plus.nativeObj.Bitmap("base64")

  bitmap.loadBase64Data(base64, res => {
    let filePath = modelData.model_no + "-" + (new Date()).getTime() + ".jpg"
    bitmap.save(filePath, {
      overwrite: true, // 是否覆盖
      format: 'jpg', // 图片格式
      quality: 100 // 图片清晰度
    }, res => {
      uni.saveImageToPhotosAlbum({
        filePath: filePath,
        success: () => {
          uni.showToast({
            title: t('图片保存成功！'),
            icon: 'success'
          })
          bitmap.clear()
        },
        fail: () => {
          uni.showToast({
            title: t('图片保存失败！'),
            icon: 'error'
          })
          bitmap.clear()
        }
      })
    }, err => {
      uni.showToast({
        title: t('图片保存失败！'),
        icon: 'error'
      })
      bitmap.clear()
    })
  }, err => {
    uni.showToast({
      title: t('图片保存失败'),
      icon: 'error'
    })
    bitmap.clear()
  })
  //#endif

  //#ifdef H5
  let link = document.createElement("a")
  link.download = modelData.model_no + ".jpg"
  link.href = 'data:image/jpg;base64,' + modelPicture.value
  link.click()
  //#endif
}

// 提示信息
function showTip(type, message) {
  tipType.value = type
  tipMessage.value = message
  tipPopup.value.open()
}

// 显示添加规格弹框
function showAddSpecification() {
  addSpecificationDetail.model_no = modelData.model_no

  resetSizeOption()

  addSpecificationPopup.value.open()
}

// 添加规格上传图片
function addSpecificationUpload(e) {
  //#ifdef APP-PLUS
  plus.io.resolveLocalFileSystemURL(e.tempFiles[0].path, function(entry) {
    //读取文件  
    entry.file(function(file) {
      let reader = new plus.io.FileReader()
      reader.readAsDataURL(file) // 以URL格式读取文件
      reader.onload = function() {
        let base64 = reader.result.split(',')[1] // 获取base64字符串
        let arrayBuffer = uni.base64ToArrayBuffer(base64) // 转换为arrayBuffer格式
        let array = Array.from(new Uint8Array(arrayBuffer))
        addSpecificationDetail.procs_pic = array
      }
    })
  })
  //#endif

  //#ifdef H5
  let file = e.tempFiles[0].file
  let reader = new FileReader()
  reader.readAsArrayBuffer(file)
  reader.onload = function() {
    let array = Array.from(new Uint8Array(reader.result))
    addSpecificationDetail.procs_pic = array
  }
  //#endif
}

// 提交添加规格数据
function submitAddSpecification() {
  if (Number(addSpecificationDetail.s_size) > Number(addSpecificationDetail.e_size)) {
    showTip('warn', t('起始号码不能大于终止号码！'))
    return
  }

  if (Number(addSpecificationDetail.e_size) < Number(addSpecificationDetail.s_size)) {
    showTip('warn', t('终止号码不能小于起始号码！'))
    return
  }

  uni.request({
    url: urlPrefix + "/second/addSpecification",
    method: "POST",
    data: addSpecificationDetail
  }).then(res => {
    if (res.data.code) {
      showTip('success', res.data.data)
      getSpecification(modelData.model_no)
    } else {
      showTip('error', res.data.msg)
    }
  }).catch(err => {
    console.log(err)
  })

  addSpecificationDetail.model_no = ""
  addSpecificationDetail.procs_type = 1
  addSpecificationDetail.procs_pic = ""
  addSpecificationDetail.s_size = 0
  addSpecificationDetail.e_size = 0
  addSpecificationDetail.remark = ""

  resetSizeOption()

  addSpecificationPopup.value.close()
}

// 关闭添加规格弹框
function closeAddSpecification() {
  addSpecificationDetail.model_no = ""
  addSpecificationDetail.procs_type = 1
  addSpecificationDetail.procs_pic = ""
  addSpecificationDetail.s_size = 0
  addSpecificationDetail.e_size = 0
  addSpecificationDetail.remark = ""

  resetSizeOption()

  addSpecificationPopup.value.close()
}

// 显示起始号码选项
function showStartSizeOption(param) {
  showSizeTypeList.value = false
  showBaseSizeList.value = false
  showSelectPart.value = false
  showEndSizeOption1.value = false
  showEndSizeOption2.value = false

  if (param.procs_type == 1) {
    showStartSizeOption1.value = !showStartSizeOption1.value
    showStartSizeOption2.value = false
  } else {
    showStartSizeOption2.value = !showStartSizeOption2.value
    showStartSizeOption1.value = false
  }
}

// 修改规格起始号码
function updateSpecificationStartSize(item, size) {
  if (Number(size) != Number(item.s_size)) {
    if (Number(size) > Number(item.e_size)) {
      showTip('warn', t('起始号码不能大于终止号码！'))
      return
    }

    uni.request({
      url: urlPrefix + "/second/updateStartSize",
      method: "POST",
      data: {
        model_no: item.model_no,
        s_size: size,
        upd_user: user
      }
    }).then(res => {
      if (res.data.code) {
        showTip('success', t('起始号码修改成功！'))
        getSpecification(modelData.model_no)
      } else {
        showTip('error', t('起始号码修改失败！'))
      }
    }).catch(err => {
      console.log(err)
    })
  }
}

// 显示终止号码选项
function showEndSizeOption(param) {
  showSizeTypeList.value = false
  showBaseSizeList.value = false
  showSelectPart.value = false
  showStartSizeOption1.value = false
  showStartSizeOption2.value = false

  if (param.procs_type == 1) {
    showEndSizeOption1.value = !showEndSizeOption1.value
    showEndSizeOption2.value = false
  } else {
    showEndSizeOption2.value = !showEndSizeOption2.value
    showEndSizeOption1.value = false
  }
}

// 修改规格终止号码
function updateSpecificationEndSize(item, size) {
  if (Number(size) != Number(item.e_size)) {
    if (Number(size) < Number(item.s_size)) {
      showTip('warn', t('终止号码不能小于起始号码！'))
      return
    }

    uni.request({
      url: urlPrefix + "/second/updateEndSize",
      method: "POST",
      data: {
        model_no: item.model_no,
        e_size: size,
        upd_user: user
      }
    }).then(res => {
      if (res.data.code) {
        showTip('success', t('终止号码修改成功！'))
        getSpecification(modelData.model_no)
      } else {
        showTip('error', t('终止号码修改失败！'))
      }
    }).catch(err => {
      console.log(err)
    })
  }
}

// 聚焦规格备注
function focusSpecificationRemark(param) {
  oldSpecificationRemark.value = param.remark
}

// 修改规格备注
function updateSpecificationRemark(param) {
  param.remark = param.remark.trim()

  if (param.remark != oldSpecificationRemark.value) {
    param.upd_user = user

    uni.request({
      url: urlPrefix + "/second/updateSpecification",
      method: "POST",
      data: param
    }).then(res => {
      if (res.data.code) {
        showTip('success', t('备注修改成功！'))
        getSpecification(modelData.model_no)
      } else {
        showTip('error', t('备注修改失败！'))
      }
    }).catch(err => {
      console.log(err)
    })
  }
}

// 上传图片
function uploadPicture(param) {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      //#ifdef APP-PLUS
      plus.io.resolveLocalFileSystemURL(res.tempFiles[0].path, function(entry) {
        //读取文件  
        entry.file(function(file) {
          let reader = new plus.io.FileReader()
          reader.readAsDataURL(file) // 以URL格式读取文件
          reader.onload = function() {
            let base64 = reader.result.split(',')[1] // 获取base64字符串
            let arrayBuffer = uni.base64ToArrayBuffer(base64) // 转换为arrayBuffer格式
            let array = Array.from(new Uint8Array(arrayBuffer))

            param.procs_pic = array
            param.upd_user = user

            uni.request({
              url: urlPrefix + "/second/updateSpecification",
              method: "POST",
              data: param
            }).then(res => {
              if (res.data.code) {
                uni.showToast({
                  title: t('图片上传成功！'),
                  icon: 'success'
                })
                getSpecification(modelData.model_no)
              } else {
                uni.showToast({
                  title: t('图片上传失败！'),
                  icon: 'error'
                })
              }
            }).catch(err => {
              console.log(err)
            })
          }
        })
      })
      //#endif

      //#ifdef H5
      let file = res.tempFiles[0]
      console.log(file)
      let reader = new FileReader()
      reader.readAsArrayBuffer(file)
      reader.onload = function() {
        let array = Array.from(new Uint8Array(reader.result))

        param.procs_pic = array
        param.upd_user = user

        uni.request({
          url: urlPrefix + "/second/updateSpecification",
          method: "POST",
          data: param
        }).then(res => {
          if (res.data.code) {
            uni.showToast({
              title: t('图片上传成功！'),
              icon: 'success'
            })
            getSpecification(modelData.model_no)
          } else {
            uni.showToast({
              title: t('图片上传失败！'),
              icon: 'error'
            })
          }
        }).catch(err => {
          console.log(err)
        })
      }
      //#endif
    }
  })
}

// 显示规格图片
function showSpecificationPicture(param) {
  specificationPictureDetail.model_no = param.model_no
  specificationPictureDetail.procs_type = param.procs_type
  specificationPictureDetail.procs_pic = param.procs_pic
  specificationPictureDetail.s_size = param.s_size
  specificationPictureDetail.e_size = param.e_size
  specificationPictureDetail.remark = param.remark
  specificationPictureDetail.ins_user = param.ins_user
  specificationPictureDetail.ins_date = param.ins_date
  specificationPictureDetail.upd_user = param.upd_user
  specificationPictureDetail.upd_date = param.upd_date
  specificationPicturePopup.value.open()
}

// 修改规格图片
function updatePicture(param) {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      //#ifdef APP-PLUS
      plus.io.resolveLocalFileSystemURL(res.tempFiles[0].path, function(entry) {
        //读取文件  
        entry.file(function(file) {
          let reader = new plus.io.FileReader()
          reader.readAsDataURL(file) // 以URL格式读取文件
          reader.onload = function() {
            let base64 = reader.result.split(',')[1] // 获取base64字符串
            let arrayBuffer = uni.base64ToArrayBuffer(base64) // 转换为arrayBuffer格式
            let array = Array.from(new Uint8Array(arrayBuffer))

            param.procs_pic = array
            param.upd_user = user

            uni.request({
              url: urlPrefix + "/second/updateSpecification",
              method: "POST",
              data: param
            }).then(res => {
              if (res.data.code) {
                uni.showToast({
                  title: t('图片修改成功！'),
                  icon: 'success'
                })
                getSpecification(modelData.model_no)
                specificationPicturePopup.value.close()
              } else {
                uni.showToast({
                  title: t('图片修改失败！'),
                  icon: 'error'
                })
              }
            }).catch(err => {
              console.log(err)
            })
          }
        })
      })
      //#endif

      //#ifdef H5
      let file = res.tempFiles[0]
      console.log(file)
      let reader = new FileReader()
      reader.readAsArrayBuffer(file)
      reader.onload = function() {
        let array = Array.from(new Uint8Array(reader.result))

        param.procs_pic = array
        param.upd_user = user

        uni.request({
          url: urlPrefix + "/second/updateSpecification",
          method: "POST",
          data: param
        }).then(res => {
          if (res.data.code) {
            uni.showToast({
              title: t('图片修改成功！'),
              icon: 'success'
            })
            getSpecification(modelData.model_no)
            specificationPicturePopup.value.close()
          } else {
            uni.showToast({
              title: t('图片修改失败！'),
              icon: 'error'
            })
          }
        }).catch(err => {
          console.log(err)
        })
      }
      //#endif
    }
  })
}

// 重置规格图片
function resetPicture(param) {
  param.procs_pic = ""
  param.upd_user = user

  uni.request({
    url: urlPrefix + "/second/updateSpecification",
    method: "POST",
    data: param
  }).then(res => {
    if (res.data.code) {
      uni.showToast({
        title: t('图片重置成功！'),
        icon: 'success'
      })
      getSpecification(modelData.model_no)
      specificationPicturePopup.value.close()
    } else {
      uni.showToast({
        title: t('图片重置失败！'),
        icon: 'error'
      })
    }
  }).catch(err => {
    console.log(err)
  })
}

// 显示删除规格弹框
function showDeleteSpecification(param) {
  deleteSpecificationDetail.model_no = param.model_no
  deleteSpecificationDetail.procs_type = param.procs_type
  deleteSpecificationPopup.value.open()
}

// 删除规格数据
function submitDeleteSpecification() {
  uni.request({
    url: urlPrefix + "/second/deleteSpecification",
    method: "POST",
    data: deleteSpecificationDetail
  }).then(res => {
    if (res.data.code) {
      showTip('success', res.data.data)
      getSpecification(modelData.model_no)
    } else {
      showTip('error', res.data.msg)
    }
  }).catch(err => {
    console.log(err)
  })
}

// 关闭删除规格弹框
function closeDeleteSpecification() {
  deleteSpecificationDetail.model_no = ""
  deleteSpecificationDetail.procs_type = ""
  deleteSpecificationPopup.value.close()
}

// 获取部位选项
function getOption() {
  uni.request({
    url: urlPrefix + "/third/getOption",
    method: "POST"
  }).then(res => {
    option1List.value = res.data.data ? res.data.data.option1List : []
    for (let item of option1List.value) {
      option1Order.value.set(item.column_no, item.column_order < 10 ? ("0" + item.column_order) : item
        .column_order)
      option1Seq.value.set(item.column_no, item.column_seq)
    }

    option2List.value = res.data.data ? res.data.data.option2List : []
    for (let item of option2List.value) {
      option2Order.value.set(item.column_no, item.column_order < 10 ? ("0" + item.column_order) : item
        .column_order)
      option2Seq.value.set(item.column_no, item.column_seq)
    }
  }).catch(err => {
    console.log(err)
  })
}

// 显示部位选项
function showPartOption() {
  showSelectPart.value = !showSelectPart.value

  showSizeTypeList.value = false
  showBaseSizeList.value = false
  showStartSizeOption1.value = false
  showStartSizeOption2.value = false
  showEndSizeOption1.value = false
  showEndSizeOption2.value = false
}

// 筛选全部部位数据
function selectAllPartList() {
  selector.value = 0
  showSelectPart.value = false
  partList.value = part1List.value.concat(part2List.value)
}

// 筛选未贴底部位数据
function selectPart1List() {
  selector.value = 1
  showSelectPart.value = false
  partList.value = part1List.value
}

// 筛选已贴底部位数据
function selectPart2List() {
  selector.value = 2
  showSelectPart.value = false
  partList.value = part2List.value
}

// 显示添加部位弹框
function showAddPart() {
  addPartDetail.model_no = modelData.model_no
  addPartPopup.value.open()
}

// 获取未贴底的部位选项
function getPart1NameList() {
  let nameList = []

  for (let item of option1List.value) {
    nameList.push({
      text: (item.column_order < 10 ? ("0" + item.column_order) : item.column_order) + " " + item.column_no,
      value: item.column_no,
      disable: false
    })
  }
  
  let procsTypeList = specificationList.value.map(item => item.procs_type)
  if (procsTypeList.length > 0 && procsTypeList.includes(1)) {
    for (let n of nameList) {
      for (let p of part1List.value) {
        if (n.value == p.part_name) {
          n.disable = true
        }
      }
    }
  } else {
    for (let n of nameList) {
      n.disable = true
    }
  }

  return nameList
}

// 获取已贴底的部位选项
function getPart2NameList() {
  let nameList = []

  for (let item of option2List.value) {
    nameList.push({
      text: (item.column_order < 10 ? ("0" + item.column_order) : item.column_order) + " " + item.column_no,
      value: item.column_no,
      disable: false
    })
  }
  
  let procsTypeList = specificationList.value.map(item => item.procs_type)
  if (procsTypeList.length > 0 && procsTypeList.includes(2)) {
    for (let n of nameList) {
      for (let p of part2List.value) {
        if (n.value == p.part_name) {
          n.disable = true
        }
      }
    }
  } else {
    for (let n of nameList) {
      n.disable = true
    }
  }

  return nameList
}

// 添加部位数据
function submitAddPart() {
  for (let item of addPart1NameList.value) {
    addPartList.value.push({
      model_no: addPartDetail.model_no,
      procs_type: 1,
      part_seq: option1Seq.value.get(item),
      part_name: item,
      part_spec: addPartDetail.part_spec,
      add_per: addPartDetail.add_per,
      ins_user: addPartDetail.ins_user,
      ins_date: addPartDetail.ins_date,
      upd_user: addPartDetail.upd_user,
      upd_date: addPartDetail.upd_date
    })
  }

  for (let item of addPart2NameList.value) {
    addPartList.value.push({
      model_no: addPartDetail.model_no,
      procs_type: 2,
      part_seq: option2Seq.value.get(item),
      part_name: item,
      part_spec: addPartDetail.part_spec,
      add_per: addPartDetail.add_per,
      ins_user: addPartDetail.ins_user,
      ins_date: addPartDetail.ins_date,
      upd_user: addPartDetail.upd_user,
      upd_date: addPartDetail.upd_date
    })
  }

  if (addPartList.value.length === 0) {
    showTip('warn', t('请选择部位名称！'))
    return
  }

  uni.request({
    url: urlPrefix + "/third/addPart",
    method: "POST",
    data: addPartList.value
  }).then(res => {
    if (res.data.code) {
      showTip('success', res.data.data)
      getPart(modelData.model_no)
    } else {
      showTip('error', res.data.msg)
    }
  }).catch(err => {
    console.log(err)
  }).finally(() => {
    addPart1NameList.value = []
    addPart2NameList.value = []
    addPartList.value = []

    addPartDetail.model_no = ""
    addPartDetail.procs_type = 1
    addPartDetail.part_seq = "01"
    addPartDetail.part_name = "楦頭"
    addPartDetail.part_spec = 0
    addPartDetail.add_per = 0

    addPartPopup.value.close()
  })
}

// 关闭添加部位弹框
function closeAddPart() {
  addPart1NameList.value = []
  addPart2NameList.value = []
  addPartList.value = []

  addPartDetail.model_no = ""
  addPartDetail.procs_type = 1
  addPartDetail.part_seq = "01"
  addPartDetail.part_name = "楦頭"
  addPartDetail.part_spec = 0
  addPartDetail.add_per = 0

  addPartPopup.value.close()
}

// 聚焦部位规格
function focusPartSpec(param) {
  oldPartSpec.value = param.part_spec
}

// 修改部位规格
function updatePartSpec(param) {
  if (param.part_spec != oldPartSpec.value) {
    param.upd_user = user

    uni.request({
      url: urlPrefix + "/third/updatePart",
      method: "POST",
      data: param
    }).then(res => {
      if (res.data.code) {
        showTip('success', t('部位规格修改成功！'))
        getPart(modelData.model_no)
      } else {
        showTip('error', t('部位规格修改失败！'))
      }
    }).catch(err => {
      console.log(err)
    })
  }
}

// 聚焦级放数
function focusAddPer(param) {
  oldAddPer.value = param.add_per
}

// 修改级放数
function updateAddPer(param) {
  if (param.add_per != oldAddPer.value) {
    param.upd_user = user

    uni.request({
      url: urlPrefix + "/third/updatePart",
      method: "POST",
      data: param
    }).then(res => {
      if (res.data.code) {
        showTip('success', t('级放数修改成功！'))
        getPart(modelData.model_no)
      } else {
        showTip('error', t('级放数修改失败！'))
      }
    }).catch(err => {
      console.log(err)
    })
  }
}

// 显示删除部位弹框
function showDeletePart(param) {
  deletePartDetail.model_no = param.model_no
  deletePartDetail.procs_type = param.procs_type
  deletePartDetail.part_name = param.part_name

  deletePartPopup.value.open()
}

// 删除部位数据
function submitDeletePart() {
  uni.request({
    url: urlPrefix + "/third/deletePart",
    method: "POST",
    data: deletePartDetail
  }).then(res => {
    if (res.data.code) {
      showTip('success', res.data.data)
      getPart(modelData.model_no)
    } else {
      showTip('error', res.data.msg)
    }
  }).catch(err => {
    console.log(err)
  })
}

// 关闭删除部位弹框
function closeDeletePart() {
  deletePartDetail.model_no = ""
  deletePartDetail.procs_type = 1
  deletePartDetail.part_name = ""

  deletePartPopup.value.close()
}

// 获取删除的部位下标数组
function getPartIndexArray(param) {
  partIndexArray.value = param.detail.index
}

// 显示批量删除部位弹框
function showBatchDeletePart() {
  if (partIndexArray.value.length > 0) {
    batchDeletePartList.value = []
    for (let i = 0; i < partIndexArray.value.length; i++) {
      batchDeletePartList.value.push({
        model_no: partList.value[partIndexArray.value[i]].model_no,
        procs_type: partList.value[partIndexArray.value[i]].procs_type,
        part_name: partList.value[partIndexArray.value[i]].part_name
      })
    }
    batchDeletePartPopup.value.open()
  } else {
    showTip('warn', t('请选择要删除的部位数据！'))
  }
}

// 批量删除部位数据
function submitBatchDeletePart() {
  uni.request({
    url: urlPrefix + "/third/batchDeletePart",
    method: "POST",
    data: batchDeletePartList.value
  }).then(res => {
    if (res.data.code) {
      showTip('success', res.data.data)
      getPart(modelData.model_no)
    } else {
      showTip('error', res.data.msg)
    }
  })
}

// 关闭批量删除部位弹框
function closeBatchDeletePart() {
  batchDeletePartList.value = []
  batchDeletePartPopup.value.close()
}

// 显示临时部位弹框
function showTemporaryPart() {
  temporaryPartDetail.model_no = modelData.model_no
  temporaryPartPopup.value.open()
}

// 添加临时部位
function submitTemporaryPart() {
  if (temporaryPartDetail.part_name.trim().length == 0) {
    showTip('warn', t('部位名称不能为空！'))
    return
  }

  uni.request({
    url: urlPrefix + "/third/addTemporaryPart",
    method: "POST",
    data: temporaryPartDetail
  }).then(res => {
    if (res.data.code) {
      showTip('success', res.data.data)
      getPart(modelData.model_no)
    } else {
      showTip('error', res.data.msg)
    }
  }).catch(err => {
    console.log(err)
  }).finally(() => {
    temporaryPartDetail.model_no = ""
    temporaryPartDetail.procs_type = 1
    temporaryPartDetail.part_name = ""
    temporaryPartDetail.part_spec = 0
    temporaryPartDetail.add_per = 0

    temporaryPartPopup.value.close()
  })
}

// 关闭临时部位弹框
function closeTemporaryPart() {
  temporaryPartDetail.model_no = ""
  temporaryPartDetail.procs_type = 1
  temporaryPartDetail.part_name = ""
  temporaryPartDetail.part_spec = 0
  temporaryPartDetail.add_per = 0

  temporaryPartPopup.value.close()
}

// 隐藏列表
function hidden() {
  shoeLastModelListShow.value = false
  showSizeTypeList.value = false
  showBaseSizeList.value = false
  showSelectPart.value = false
  showStartSizeOption1.value = false
  showStartSizeOption2.value = false
  showEndSizeOption1.value = false
  showEndSizeOption2.value = false
}

onMounted(() => {
  getBrands()
  select(brand.value)
  getOption()
})
</script>

<template>
  <scroll-view :scroll-y="true" @click="hidden" class="container">
    <view class="back">
      <uni-icons @click="back" type="back" size="36"></uni-icons>
    </view>

    <view class="search">
      <button class="search-brand" @click="scrollId = 'A'; brandPopup.open()">
        {{ brand }}
      </button>

      <button class="search-type" @click="searchType = !searchType">
        {{ searchType ? t('型体') : t('楦头') }}
      </button>

      <input
        ref="searchInput"
        v-show="searchType"
        v-model="model_no"
        class="search-input"
        type="text"
        :placeholder="t('请输入型体编号')"
        @focus="modelListShow = true"
        @blur="modelListShow = false"
      />

      <input
        v-show="!searchType"
        v-model="shoe_last"
        type="text"
        :placeholder="t('请输入楦头编号')"
        class="search-input-shoe-last"
      />

      <view
        v-show="model_no.length > 0 && searchType"
        @click="model_no = ''"
        class="search-clear"
      >
        <uni-icons type="clear" size="34"></uni-icons>
      </view>

      <view
        v-show="shoe_last.length > 0 && !searchType"
        @click="shoe_last = ''"
        class="search-clear-shoe-last"
      >
        <uni-icons type="clear" size="34"></uni-icons>
      </view>

      <view
        v-show="!searchType"
        @click="getShoeLastModelList"
        class="search-icon-shoe-last"
        :style="{ marginLeft: shoe_last.length > 0 ? '11px' : '0' }"
      >
        <uni-icons type="search" size="48"></uni-icons>
      </view>

      <transition name="list">
        <view v-show="modelListShow" class="search-list">
          <view class="search-box">
            <view
              v-for="(item, index) in searchModel"
              :key="index"
              @click="search(item.model_no)"
              class="search-item"
              :style="{ border: searchModel.length === 1 ? 'none' : '' }"
            >
              {{ item.model_no }}
            </view>
            <view v-show="searchModel.length === 0" class="search-item" style="border: none;">
              {{ t('暂无该型体数据') }}
            </view>
          </view>
        </view>
      </transition>

      <transition name="list">
        <view v-show="shoeLastModelListShow" class="search-list-shoe-last">
          <view class="search-box-shoe-last">
            <view
              v-for="(item, index) in searchShoeLastModel"
              :key="index"
              @click="search(item.model_no)"
              class="search-item-shoe-last"
              :style="{ border: shoeLastModelList.length === 1 ? 'none' : '' }"
            >
              {{ item.model_no }}
            </view>
            <view v-show="shoeLastModelList.length === 0" class="search-item-shoe-last" style="border: none;">
              {{ t('暂无型体数据') }}
            </view>
          </view>
        </view>
      </transition>
    </view>

    <view class="model">
      <view v-if="modelPicture" class="model-picture">
        <image
          :src="'data:image/jpg;base64,' + modelPicture"
          mode="aspectFit"
          @click="modelPicturePopup.open()"
          style="height: 70px; width: 120px; cursor: pointer;"
        ></image>
      </view>

      <table>
        <thead>
          <tr>
            <th>{{ t('操作') }}</th>
            <th>{{ t('生产工厂') }}</th>
            <th>{{ t('品牌') }}</th>
            <th>{{ t('季节编号') }}</th>
            <th>{{ t('型体编号') }}</th>
            <th>{{ t('型体描述') }}</th>
            <th>{{ t('码别') }}</th>
            <th>{{ t('基本码') }}</th>
            <th>{{ t('系列') }}</th>
            <th>{{ t('楦头') }}</th>
            <th>{{ t('面版师') }}</th>
            <th>{{ t('底版师') }}</th>
          </tr>
        </thead>

        <tbody v-if="modelData.model_no.length > 0">
          <tr>
            <td>
              <button @click="showPreview(modelData)" class="model-button">
                {{ t('预览') }}
              </button>
            </td>
            <td>{{ modelData.factory }}</td>
            <td>{{ modelData.brand_no }}</td>
            <td>{{ modelData.season_no }}</td>
            <td>{{ modelData.model_no }}</td>
            <td>{{ modelData.model_desc ? modelData.model_desc : t('暂无数据') }}</td>
            <td class="model-option">
              <view @click.stop="showSizeTypeOption" class="model-option-value">
                {{ modelData.siz_type ? modelData.siz_type : t('请选择') }}
              </view>

              <view v-show="showSizeTypeList" class="model-option-select">
                <view class="model-option-list">
                  <view
                    v-for="sizeType in sizeTypeList"
                    @click="updateSizeType(sizeType)"
                    class="model-option-item"
                    :style="{ border: sizeTypeList.length === 1 ? 'none' : '' }"
                  >
                    {{ sizeType }}
                  </view>
                </view>
              </view>
            </td>
            <td class="model-option">
              <view @click.stop="showBaseSizeOption" class="model-option-value" style="font-size: 20px;">
                {{ modelData.bas_size ? modelData.bas_size : t('请选择') }}
              </view>

              <view v-show="showBaseSizeList" class="model-option-select">
                <view class="model-option-list">
                  <view
                    v-for="baseSize in sizeOption"
                    @click="updateBaseSize(baseSize)"
                    class="model-option-item"
                    style="font-size: 18px;"
                  >
                    {{ baseSize }}
                  </view>
                </view>
              </view>
            </td>
            <td>{{ modelData.series ? modelData.series : t('暂无数据') }}</td>
            <td>{{ modelData.last_nos ? modelData.last_nos : t('暂无数据') }}</td>
            <td>{{ modelData.upper_der ? modelData.upper_der : t('暂无数据') }}</td>
            <td>{{ modelData.sole_der ? modelData.sole_der : t('暂无数据') }}</td>
          </tr>
        </tbody>

        <tbody v-else>
          <tr>
            <td colspan="12">{{ t('暂无数据') }}</td>
          </tr>
        </tbody>
      </table>
    </view>

    <view class="specification">
      <view class="specification-top">
        <uni-title type="h1" :title="t('规格类型')" align="center"></uni-title>
      </view>

      <view class="specification-center">
        <button
          v-show="modelData.model_no.length > 0"
          @click="autoAddSpecification"
          class="specification-button"
          style="width: 100px; margin: 0 6px; background-color: #f3a73f;"
        >
          {{ t('自动添加') }}
        </button>
        
        <button
          v-show="modelData.model_no.length > 0"
          @click="showAddSpecification"
          class="specification-button"
          style="width: 100px; margin: 0 6px; background-color: #18bc37;"
        >
          {{ t('手动添加') }}
        </button>
      </view>

      <view class="specification-bottom">
        <table>
          <thead>
            <tr>
              <th>{{ t('型体编号') }}</th>
              <th>{{ t('规格类型') }}</th>
              <th>{{ t('起始号码') }}</th>
              <th>{{ t('终止号码') }}</th>
              <th>{{ t('图片') }}</th>
              <th>{{ t('备注') }}</th>
              <th>{{ t('操作') }}</th>
            </tr>
          </thead>

          <tbody v-if="specificationList.length > 0">
            <tr v-for="item in specificationList" :key="item.procs_type" style="background-color: #fdf6e3;">
              <td>{{ item.model_no }}</td>
              <td>{{ item.procs_type === 1 ? t('未贴底') : t('已贴底') }}</td>
              <td @click.stop="showStartSizeOption(item)" class="specification-screen">
                <view class="specification-size">
                  {{ item.s_size }}
                </view>

                <view
                  v-show="item.procs_type == 1 ? showStartSizeOption1 : showStartSizeOption2"
                  class="specification-size-option"
                >
                  <view class="specification-select">
                    <view
                      v-for="size in sizeOption"
                      @click="updateSpecificationStartSize(item, size)"
                      class="specification-select-item"
                    >
                      {{ size }}
                    </view>
                  </view>
                </view>
              </td>
              <td @click.stop="showEndSizeOption(item)" class="specification-screen">
                <view class="specification-size">
                  {{ item.e_size }}
                </view>

                <view
                  v-show="item.procs_type == 1 ? showEndSizeOption1 : showEndSizeOption2"
                  class="specification-size-option"
                >
                  <view class="specification-select">
                    <view
                      v-for="size in sizeOption"
                      @click="updateSpecificationEndSize(item, size)"
                      class="specification-select-item"
                    >
                      {{ size }}
                    </view>
                  </view>
                </view>
              </td>
              <td>
                <image
                  v-if="item.procs_pic"
                  :src="'data:image/jpg;base64,' + item.procs_pic"
                  @click="showSpecificationPicture(item)"
                  mode="aspectFit"
                  style="height: 50px; width: 100px; cursor: pointer;"
                ></image>

                <button
                  v-else
                  @click="uploadPicture(item)"
                  class="specification-button"
                  style="width: 100px; background-color: slateblue;"
                >
                  {{ t('上传图片') }}
                </button>
              </td>
              <td style="display: flex; justify-content: center; align-items: center;">
                <textarea
                  v-model="item.remark"
                  :placeholder="t('请输入备注内容')"
                  @focus="focusSpecificationRemark(item)"
                  @blur="updateSpecificationRemark(item)"
                  class="specification-remark"
                ></textarea>
              </td>
              <td>
                <button
                  @click="showDeleteSpecification(item)"
                  class="specification-button"
                  style="background-color: #e43d33;"
                >
                  {{ t('删除') }}
                </button>
              </td>
            </tr>
          </tbody>

          <tbody v-else>
            <tr>
              <td colspan="7">{{ t('暂无数据') }}</td>
            </tr>
          </tbody>
        </table>
      </view>
    </view>

    <view class="part">
      <view class="part-top">
        <uni-title type="h1" :title="t('部位明细')" align="center"></uni-title>
      </view>

      <view class="part-center">
        <button
          v-show="modelData.model_no.length > 0"
          @click="showBatchDeletePart"
          class="part-button"
          style="width: 100px; margin: 0 6px; background-color: #e43d33;"
        >
          {{ t('批量删除') }}
        </button>
        <button
          v-show="modelData.model_no.length > 0"
          @click="showTemporaryPart"
          class="part-button"
          style="width: 100px; margin: 0 6px; background-color: #f3a73f;"
        >
          {{ t('临时部位') }}
        </button>
        <button
          v-show="modelData.model_no.length > 0"
          @click="showAddPart"
          class="part-button"
          style="width: 100px; margin: 0 6px; background-color: #18bc37;"
        >
          {{ t('添加部位') }}
        </button>
      </view>

      <view class="part-bottom">
        <uni-table
          ref="partTable"
          :stripe="false"
          type="selection"
          :emptyText="t('暂无数据')"
          @selection-change="getPartIndexArray"
        >
          <uni-tr style="background-color: #fdf6e3;">
            <uni-th align="center" style="color: black; font-size: 18px;">{{ t('型体编号') }}</uni-th>
            <uni-th
              @click.stop="showPartOption"
              align="center"
              class="screen"
              style="color: black; font-size: 18px;"
            >
              {{ t('规格类型') }}

              <transition name="down">
                <view v-show="showSelectPart" class="select">
                  <view
                    @click.stop="selectAllPartList"
                    class="selectItem"
                    :style="{
                      color: selector == 0 ? 'white' : 'black',
                      backgroundColor: selector == 0 ? 'violet' : 'pink'
                    }"
                  >
                    {{ t('全部') }}
                  </view>
                  <view
                    @click.stop="selectPart1List"
                    class="selectItem"
                    :style="{
                      color: selector == 1 ? 'white' : 'black',
                      backgroundColor: selector == 1 ? 'violet' : 'pink'
                    }"
                  >
                    {{ t('未贴底') }}
                  </view>
                  <view
                    @click.stop="selectPart2List"
                    class="selectItem"
                    :style="{
                      color: selector == 2 ? 'white' : 'black',
                      backgroundColor: selector == 2 ? 'violet' : 'pink'
                    }"
                  >
                    {{ t('已贴底') }}
                  </view>
                </view>
              </transition>
            </uni-th>
            <uni-th align="center" style="color: black; font-size: 18px;">{{ t('部位顺序') }}</uni-th>
            <uni-th align="center" style="color: black; font-size: 18px;">{{ t('部位名称') }}</uni-th>
            <uni-th align="center" style="color: black; font-size: 18px;">{{ t('部位规格') }}</uni-th>
            <uni-th align="center" style="color: black; font-size: 18px;">{{ t('级放数') }}</uni-th>
            <uni-th align="center" style="color: black; font-size: 18px;">{{ t('操作') }}</uni-th>
          </uni-tr>

          <template v-if="partList.length > 0">
            <uni-tr v-for="(item, index) in partList" :key="index" style="background-color: #fdf6e3;">
              <uni-td align="center" style="color: black; font-size: 18px;">
                {{ item.model_no }}
              </uni-td>
              <uni-td align="center" style="color: black; font-size: 18px;">
                {{ item.procs_type === 1 ? t('未贴底') : t('已贴底') }}
              </uni-td>
              <uni-td align="center" style="color: black; font-size: 18px;">
                {{ (item.procs_type === 1 ? option1Order.get(item.part_name) : option2Order.get(item.part_name)) ? (item.procs_type === 1 ? option1Order.get(item.part_name) : option2Order.get(item.part_name)) : "-" }}
              </uni-td>
              <uni-td align="center" style="color: black; font-size: 18px;">
                {{ item.part_name }}
              </uni-td>
              <uni-td align="center" style="color: black; font-size: 18px;">
                <input
                  type="number"
                  v-model="item.part_spec"
                  @focus="focusPartSpec(item)"
                  @blur="updatePartSpec(item)"
                />
              </uni-td>
              <uni-td align="center" style="color: black; font-size: 18px;">
                <input type="digit" v-model="item.add_per" @focus="focusAddPer(item)" @blur="updateAddPer(item)" />
              </uni-td>
              <uni-td align="center">
                <view style="display: flex; justify-content: space-evenly; align-items: center;">
                  <button @click="showDeletePart(item)" class="part-button" style="background-color: #e43d33;">
                    {{ t('删除') }}
                  </button>
                </view>
              </uni-td>
            </uni-tr>
          </template>
        </uni-table>
      </view>
    </view>
  </scroll-view>

  <view class="brand-popup">
    <uni-popup ref="brandPopup" type="center">
      <view class="brand-navbar-list">
        <view
          v-for="(item, index) in brandList"
          :key="index"
          v-show="item.data.length > 0"
          @click="scrollId = item.letter"
          class="brand-navbar"
          :style="{
            color: item.letter === scrollId ? 'white' : 'black',
            backgroundColor: item.letter === scrollId ? '#b6b7a4' : '#e6e6fa'
          }"
        >
          {{ item.letter }}
        </view>
      </view>
      
      <scroll-view
        :scroll-y="true"
        :scroll-into-view="scrollId"
        :scroll-with-animation="true"
        :show-scrollbar="false"
        class="brand-box"
      >
        <view v-for="(item, index) in brandList" :key="index" v-show="item.data.length > 0" class="brand-part">
          <view class="brand-initial" :id="item.letter">{{ item.letter }}</view>
          <view v-for="(brand, index) in item.data" :key="index" @click="select(brand)" class="brand">
            {{ brand }}
          </view>
        </view>
      </scroll-view>
    </uni-popup>
  </view>

  <view class="model-picture-popup">
    <uni-popup ref="modelPicturePopup" type="center">
      <view class="watermark">
        <img :src="'data:image/jpg;base64,' + modelPicture" alt="" style="max-width: 800px; max-height: 400px;">
      </view>

      <button @click="saveImage" style="margin-top: 10px;">{{ t('保存到相册') }}</button>
    </uni-popup>
  </view>

  <view class="preview-popup">
    <uni-popup ref="previewPopup" type="center" :mask-click="false">
      <view class="preview">
        <view v-if="info" class="preview-box">
          <view class="back">
            <uni-icons @click="closePreview" type="back" size="36"></uni-icons>
          </view>

          <view class="title">
            <h1>{{ t('数据规格表') }}</h1>
          </view>

          <view class="base-size">
            <text>
              {{ info.bas_size ? info.bas_size : 0 }}#
            </text>
          </view>

          <view class="size-type">
            <text>{{ t('半码') }}</text>
            <switch
              :checked="sizeType"
              @change="sizeType = !sizeType"
              color="orange"
              style="margin: 0 20px 0 25px; transform: scale(1.5); transform-origin: center;"
            />
            <text>{{ t('整码') }}</text>
          </view>

          <view class="print">
            <button @click="print()">{{ t('打印') }}</button>
          </view>

          <view class="picture">
            <view class="watermark">
              <img :src="'data:image/jpg;base64,' + picture" alt="" style="min-height: 120px; max-height: 160px;">
            </view>
          </view>

          <view class="info">
            <text style="display: block; width: 100%;">
              {{ t('客户：') }}{{ info.brand_no}}{{ info.series ? ("（" + info.series + "）") : "" }}
            </text>
            <text>
              {{ t('型体：') }}{{ info.model_no }}&emsp;{{ info.model_desc ? info.model_desc : "" }}
            </text>
            <text>
              {{ t('结构：') }}{{ info.constr ? info.constr : t('暂无数据') }}
            </text>
            <text>
              {{ t('楦头：') }}{{ info.last_nos ? info.last_nos : t('暂无数据') }}
            </text>
          </view>

          <hr>

          <view class="detail">
            <text>{{ t('未贴底：空楦数据') }}</text>

            <table v-if="type1Title.length > 0">
              <thead>
                <tr>
                  <th style="width: 10%"></th>
                  <th v-for="(value, key, index) in type1Title" :key="index">
                    {{ value }}
                  </th>
                  <th style="width: 4%;">{{ t('级放') }}</th>
                </tr>
              </thead>

              <tbody v-if="type1Count">
                <tr v-for="(item, index) in type1Count" :key="index">
                  <td style="width: 10%">{{ item.part_name }}</td>
                  <template v-for="(value, key, index) in item">
                    <td
                      v-if="key.startsWith('size') && key.substring(4) <= type1Number && key.substring(4) <= '25'"
                      align="center"
                    >
                      <!--{{ value ? (Math.trunc(value * 10) / 10) : value }}-->
                      {{ (value % 1 === 0.25 || value % 1 === -0.75) ? (value - 0.25) : ((value % 1 === 0.75 || value % 1 === -0.25) ? (value + 0.25) : value) }}
                    </td>
                  </template>
                  <td style="width: 4%;">{{ item.add_per }}</td>
                </tr>
              </tbody>

              <tbody v-else>
                <tr>
                  <td>{{ t('暂无数据') }}</td>
                </tr>
              </tbody>
            </table>

            <table v-else>
              <thead>
                <tr>
                  <th>{{ t('暂无数据') }}</th>
                </tr>
              </thead>
            </table>

            <view style="margin-bottom: 1%; font-weight: bold;">
              {{ type1Remark ? t('备注：') + type1Remark : "" }}
            </view>

            <text>{{ t('已贴底：') }}</text>

            <table v-if="type2Title.length > 0">
              <thead>
                <tr>
                  <th style="width: 10%"></th>
                  <th v-for="(value, key, index) in type2Title" :key="index">
                    {{ value }}
                  </th>
                  <th style="width: 4%">{{ t('级放') }}</th>
                </tr>
              </thead>

              <tbody v-if="type2Count">
                <tr v-for="(item, index) in type2Count" :key="index">
                  <td style="width: 10%">{{ item.part_name }}</td>
                  <template v-for="(value, key, index) in item">
                    <td
                      v-if="key.startsWith('size') && key.substring(4) <= type2Number && key.substring(4) <= '25'"
                      align="center"
                    >
                      <!--{{ value ? (Math.trunc(value * 10) / 10) : value }}-->
                      {{ (value % 1 === 0.25 || value % 1 === -0.75) ? (value - 0.25) : ((value % 1 === 0.75 || value % 1 === -0.25) ? (value + 0.25) : value) }}
                    </td>
                  </template>
                  <td style="width: 4%">{{ item.add_per }}</td>
                </tr>
              </tbody>

              <tbody v-else>
                <tr>
                  <td>{{ t('暂无数据') }}</td>
                </tr>
              </tbody>
            </table>

            <table v-else>
              <tr>
                <th>{{ t('暂无数据') }}</th>
              </tr>
            </table>

            <view style="margin-bottom: 1%; font-weight: bold;">
              {{ type2Remark ? (t('备注：') + type2Remark) : "" }}
            </view>
          </view>

          <hr>

          <view class="sign">
            <view class="sign-item">
              {{ t('制表日期：') }}{{ insertDate ? insertDate.substring(0, 4) + "/" + insertDate.substring(5, 7) + "/" + insertDate.substring(8, 10) : "暂无数据" }}
            </view>

            <view class="sign-item">
              {{ t('核准：') }}

              <view v-if="chkSignature.length === 0" @click="signature(1)" class="sign-item-button">
                {{ t('签名') }}
              </view>

              <image
                v-else :src="'data:image/jpg;base64,' + chkSignature"
                @click="showSignature(1)"
                mode="aspectFit"
                class="sign-item-image"
              ></image>
            </view>

            <view class="sign-item">
              {{ t('面版师：') }}

              <view v-if="uppSignature.length === 0" @click="signature(2)" class="sign-item-button">
                {{ t('签名') }}
              </view>

              <image
                v-else
                :src="'data:image/jpg;base64,' + uppSignature"
                @click="showSignature(2)"
                mode="aspectFit"
                class="sign-item-image"
              ></image>
            </view>

            <view class="sign-item">
              {{ t('底版师：') }}

              <view v-if="solSignature.length === 0" @click="signature(3)" class="sign-item-button">
                {{ t('签名') }}
              </view>

              <image
                v-else
                :src="'data:image/jpg;base64,' + solSignature"
                @click="showSignature(3)"
                mode="aspectFit"
                class="sign-item-image"
              ></image>
            </view>

            <view class="sign-item">
              {{ t('制表人：') }}{{ insertUser ? insertUser : t('暂无数据') }}
            </view>
          </view>
        </view>

        <view v-else style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;">
          <uni-icons type="spinner-cycle" size="50" class="loading"></uni-icons>
        </view>
      </view>
    </uni-popup>
  </view>

  <view class="signature-popup">
    <uni-popup ref="signaturePopup" type="center">
      <view class="watermark">
        <img
          :src="'data:image/jpg;base64,' + (signatureType === 1 ? chkSignature : (signatureType === 2 ? uppSignature : solSignature))"
          alt=""
        >
      </view>

      <button @click="resetSignature" style="margin-top: 10px;">{{ t('重置签名') }}</button>
    </uni-popup>
  </view>

  <view class="add-specification-popup">
    <uni-popup ref="addSpecificationPopup" type="center" :mask-click="false">
      <view class="add-specification">
        <view class="add-specification-box">
          <view class="back">
            <uni-icons @click="closeAddSpecification" type="back" size="36"></uni-icons>
          </view>

          <view class="submit">
            <uni-icons @click="submitAddSpecification" type="checkmarkempty" size="36"></uni-icons>
          </view>

          <view class="title">
            {{ t('添加规格') }}
          </view>

          <view class="add-specification-data">
            <uni-section :title="t('型体编号')" titleFontSize="20px" type="circle" class="add-specification-attribute">
              <uni-easyinput :value="addSpecificationDetail.model_no" disabled></uni-easyinput>
            </uni-section>

            <uni-section
              :title="t('规格类型')"
              titleFontSize="20px"
              type="line"
              class="add-specification-attribute"
              style="margin-left: 0;"
            >
              <uni-data-checkbox
                v-model="addSpecificationDetail.procs_type"
                mode="tag"
                :localdata="[
                  {
                    text: t('未贴底'),
                    value: 1
                  },
                  {
                    text: t('已贴底'),
                    value: 2
                  }
                ]"
              ></uni-data-checkbox>
            </uni-section>

            <uni-section :title="t('起始号码')" titleFontSize="20px" type="line" class="add-specification-attribute">
              <uni-data-select
                v-model="addSpecificationDetail.s_size"
                :localdata="startSizeOption"
                :clear="false"
              ></uni-data-select>
            </uni-section>

            <uni-section
              :title="t('终止号码')"
              titleFontSize="20px"
              type="line"
              class="add-specification-attribute"
              style="margin-left: 0;"
            >
              <uni-data-select
                v-model="addSpecificationDetail.e_size"
                :localdata="endSizeOption"
                :clear="false"
              ></uni-data-select>
            </uni-section>

            <uni-section :title="t('图片')" titleFontSize="20px" type="line" class="add-specification-attribute">
              <uni-file-picker return-type="object" @select="addSpecificationUpload"></uni-file-picker>
            </uni-section>

            <uni-section
              :title="t('备注')"
              titleFontSize="20px"
              type="line"
              class="add-specification-attribute"
              style="width: 50%; margin-left: 0%;"
            >
              <textarea
                v-model="addSpecificationDetail.remark"
                :placeholder="t('请输入备注内容')"
                class="add-specification-remark"
              ></textarea>
            </uni-section>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>

  <view class="specification-picture-popup">
    <uni-popup ref="specificationPicturePopup" type="center">
      <view class="watermark">
        <img
          :src="'data:image/jpg;base64,' + specificationPictureDetail.procs_pic"
          alt=""
          style="max-width: 800px; max-height: 400px;"
        >
      </view>

      <button @click="updatePicture(specificationPictureDetail)" style="margin-top: 10px;">{{ t('修改图片') }}</button>
      <button @click="resetPicturePopup.open()" style="margin-top: 10px;">{{ t('重置图片') }}</button>
    </uni-popup>
  </view>

  <view class="reset-picture-popup">
    <uni-popup ref="resetPicturePopup" type="dialog">
      <uni-popup-dialog
        type="info"
        mode="base"
        :title="t('提示')"
        :content="t('你确定要重置图片吗？')"
        :confirmText="t('确定')"
        :cancelText="t('取消')"
        @confirm="resetPicture(specificationPictureDetail)"
        @close="resetPicturePopup.close()"
      ></uni-popup-dialog>
    </uni-popup>
  </view>

  <view class="delete-specification-popup">
    <uni-popup ref="deleteSpecificationPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        mode="base"
        :title="t('提示')"
        :content="t('你确定要删除该规格数据吗？')"
        :confirmText="t('确定')"
        :cancelText="t('取消')"
        @confirm="submitDeleteSpecification"
        @close="closeDeleteSpecification"
      ></uni-popup-dialog>
    </uni-popup>
  </view>

  <view class="add-part-popup">
    <uni-popup ref="addPartPopup" type="center" :mask-click="false">
      <view class="add-part">
        <view class="add-part-box">
          <view class="back">
            <uni-icons @click="closeAddPart" type="back" size="36"></uni-icons>
          </view>

          <view class="submit">
            <uni-icons @click="submitAddPart" type="checkmarkempty" size="36"></uni-icons>
          </view>

          <view class="title">
            {{ t('添加部位') }}
          </view>

          <view class="add-part-data">
            <uni-section :title="t('型体编号')" titleFontSize="20px" type="circle" class="add-part-attribute">
              <uni-easyinput :value="addPartDetail.model_no" disabled />
            </uni-section>

            <uni-section :title="t('部位规格')" titleFontSize="20px" type="line" class="add-part-attribute">
              <uni-number-box
                v-model="addPartDetail.part_spec"
                :min="0"
                :max="500"
                color="black"
                background="white"
              />
            </uni-section>

            <uni-section
              :title="t('级放数')"
              titleFontSize="20px"
              type="line"
              class="add-part-attribute"
              style="margin-left: 0;"
            >
              <uni-number-box
                v-model="addPartDetail.add_per"
                :min="0"
                :max="10"
                :step="0.5"
                color="black"
                background="white"
              />
            </uni-section>

            <uni-section
              :title="t('部位名称（未贴底）')"
              titleFontSize="20px"
              type="line"
              class="add-part-attribute"
              style="margin-left: 3%;"
            >
              <uni-data-checkbox
                v-model="addPart1NameList"
                multiple
                mode="button"
                :localdata="getPart1NameList()"
              ></uni-data-checkbox>
            </uni-section>

            <uni-section
              :title="t('部位名称（已贴底）')"
              titleFontSize="20px"
              type="line"
              class="add-part-attribute"
              style="margin-left: 3%;"
            >
              <uni-data-checkbox
                v-model="addPart2NameList"
                multiple
                mode="button"
                :localdata="getPart2NameList()"
              ></uni-data-checkbox>
            </uni-section>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>

  <view class="temporary-part-popup">
    <uni-popup ref="temporaryPartPopup" type="center" :mask-click="false">
      <view class="temporary-part">
        <view class="temporary-part-box">
          <view class="back">
            <uni-icons @click="closeTemporaryPart" type="back" size="36"></uni-icons>
          </view>

          <view class="submit">
            <uni-icons @click="submitTemporaryPart" type="checkmarkempty" size="36"></uni-icons>
          </view>

          <view class="title">
            {{ t('临时部位') }}
          </view>

          <view class="temporary-part-data">
            <uni-section :title="t('型体编号')" titleFontSize="20px" type="circle" class="temporary-part-attribute">
              <uni-easyinput :value="temporaryPartDetail.model_no" disabled></uni-easyinput>
            </uni-section>

            <uni-section
              :title="t('规格类型')"
              titleFontSize="20px"
              type="line"
              class="temporary-part-attribute"
              style="margin-left: 0;"
            >
              <uni-data-checkbox
                v-model="temporaryPartDetail.procs_type"
                mode="tag"
                :localdata="[
                  {
                    text: t('未贴底'),
                    value: 1
                  },
                  {
                    text: t('已贴底'),
                    value: 2
                  }
                ]"
              ></uni-data-checkbox>
            </uni-section>

            <uni-section
              :title="t('部位名称')"
              titleFontSize="20px"
              type="line"
              class="temporary-part-attribute"
              style="width: 100%;"
            >
              <uni-easyinput v-model="temporaryPartDetail.part_name" style="width: 40%;"></uni-easyinput>
            </uni-section>

            <uni-section :title="t('部位规格')" titleFontSize="20px" type="line" class="temporary-part-attribute">
              <uni-number-box
                v-model="temporaryPartDetail.part_spec"
                :min="0"
                :max="500"
                color="black"
                background="white"
              />
            </uni-section>

            <uni-section
              :title="t('级放数')"
              titleFontSize="20px"
              type="line"
              class="temporary-part-attribute"
              style="margin-left: 0;"
            >
              <uni-number-box
                v-model="temporaryPartDetail.add_per"
                :min="0"
                :max="10"
                :step="0.5"
                color="black"
                background="white"
              />
            </uni-section>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>

  <view class="delete-part-popup">
    <uni-popup ref="deletePartPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        mode="base"
        :title="t('提示')"
        :content="t('你确定要删除该部位数据吗？')"
        :confirmText="t('确定')"
        :cancelText="t('取消')"
        @confirm="submitDeletePart"
        @close="closeDeletePart"
      ></uni-popup-dialog>
    </uni-popup>
  </view>

  <view class="batch-delete-part-popup">
    <uni-popup ref="batchDeletePartPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        mode="base"
        :title="t('提示')"
        :content="t('你确定要删除选中的部位数据吗？')"
        :confirmText="t('确定')"
        :cancelText="t('取消')"
        @confirm="submitBatchDeletePart"
        @close="closeBatchDeletePart"
      ></uni-popup-dialog>
    </uni-popup>
  </view>

  <view class="tip-popup">
    <uni-popup ref="tipPopup" type="message">
      <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
    </uni-popup>
  </view>
</template>

<style lang="scss">
page {
  width: 100%;
  height: 100%;
  padding: 2.5% 2% 1.5% 2%;
  box-sizing: border-box;
  background-color: #fdf6e3;
}

.container {
  width: 100%;
  height: 100%;
  padding: 1%;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0 0 1px 5px #dddddd;
  position: relative;

  .back {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    position: absolute;
    border-radius: 50%;
    box-shadow: 0 0 5px gray;
    left: 1.5%;
    width: 50px;
    height: 50px;
    top: 3%;
    cursor: pointer;
    z-index: 1;
  }

  .search {
    width: 100%;
    min-height: 10%;
    margin-bottom: 1%;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: relative;

    .search-brand {
      width: 10%;
      margin-left: 10%;
      margin-right: 0.5%;
      background: linear-gradient(to right bottom, orangered, pink);
      color: white;
      font-size: 24px;
      font-weight: bold;
      border-radius: 10px;
    }

    .search-type {
      width: 8%;
      margin-left: 0.5%;
      margin-right: 1%;
      background-color: #333;
      color: white;
      font-size: 24px;
      font-weight: bold;
      border-radius: 10px;
    }

    .search-input {
      width: 40%;
      padding: 10px;
      border: 2px solid gray;
      font-size: 24px;
      color: black;
      background-color: white;
      border-radius: 5px;

      &:hover {
        border: 2px solid black;
      }
    }

    .search-input-shoe-last {
      width: 35%;
      padding: 10px;
      border: 2px solid gray;
      font-size: 24px;
      color: black;
      background-color: white;
      border-radius: 5px;

      &:hover {
        border: 2px solid black;
      }
    }

    .search-clear {
      margin-left: -45px;
      margin-top: 2px;
      cursor: pointer;
      z-index: 1;
    }

    .search-clear-shoe-last {
      margin-left: -45px;
      margin-top: 2px;
      cursor: pointer;
      z-index: 1;
    }

    .search-icon-shoe-last {
      width: 5%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      z-index: 1;
    }

    .search-list {
      width: calc(40% + 20px + 4px);
      height: 270px;
      position: absolute;
      left: 30%;
      top: 120%;
      z-index: 1;

      .search-box {
        max-height: 270px;
        text-align: center;
        border-radius: 5px;
        color: white;
        font-size: 18px;
        font-weight: bold;
        background-color: gray;
        overflow: auto;

        .search-item {
          box-sizing: border-box;
          padding: 15px 0;
          border-bottom: 1px solid white;
          cursor: pointer;

          &:hover {
            background-color: #aaaaaa;
          }
        }
      }

      &::before {
        content: "";
        position: absolute;
        left: calc(50% - 10px);
        top: -10px;
        border-top: 0px solid transparent;
        border-left: 10px solid transparent;
        border-bottom: 10px solid gray;
        border-right: 10px solid transparent;
      }
    }

    .search-list-shoe-last {
      width: calc(35% + 20px + 4px);
      height: 270px;
      position: absolute;
      left: 30%;
      top: 120%;
      z-index: 1;

      .search-box-shoe-last {
        max-height: 270px;
        text-align: center;
        border-radius: 5px;
        color: white;
        font-size: 18px;
        font-weight: bold;
        background-color: gray;
        overflow: auto;

        .search-item-shoe-last {
          box-sizing: border-box;
          padding: 15px 0;
          border-bottom: 1px solid white;
          cursor: pointer;

          &:hover {
            background-color: #aaaaaa;
          }
        }
      }

      &::before {
        content: "";
        position: absolute;
        left: calc(50% - 10px);
        top: -10px;
        border-top: 0px solid transparent;
        border-left: 10px solid transparent;
        border-bottom: 10px solid gray;
        border-right: 10px solid transparent;
      }
    }
  }

  .model {
    width: 100%;
    min-height: 15%;
    overflow-x: auto;
    overflow-y: hidden;
    padding-bottom: 160px;
    margin-bottom: calc(-160px + 1%);

    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }

    table {
      width: 130%;
      font-size: 18px;
      text-align: center;

      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none;
      }

      th {
        padding: 12px 10px;
        border-bottom: 1px solid #ebeef5;
      }

      td {
        padding: 8px 10px;
        border-bottom: 1px solid #ebeef5;
      }
    }

    .model-picture {
      position: absolute;
      left: calc(75% + 20px + 4px);
      top: 0;
    }

    .model-button {
      width: 80px;
      color: white;
      background-color: #2979ff;
      font-weight: bold;
    }

    .model-option {
      position: relative;

      .model-option-value {
        width: 80px;
        height: 40px;
        margin: auto;
        border: 2px solid gray;
        border-radius: 5px;
        background-color: white;
        font-weight: bold;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }

      .model-option-select {
        .model-option-list {
          width: 100%;
          max-height: 140px;
          overflow: auto;
          background-color: whitesmoke;
          filter: drop-shadow(0 0 1px black);
          border-bottom: 1px solid whitesmoke;
          border-radius: 5px;
          position: absolute;
          left: 0;
          top: 70px;
          z-index: 10;

          .model-option-item {
            padding: 5%;
            font-weight: bold;
            border-bottom: 1px solid gray;
          }
        }

        &:before {
          content: "";
          position: absolute;
          left: calc(50% - 10px);
          bottom: -10px;
          filter: drop-shadow(0 0 1px black);
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          border-bottom: 10px solid whitesmoke;
        }
      }
    }
  }

  .specification {
    width: 100%;
    min-height: 25%;
    margin-bottom: 1%;

    .specification-top {
      width: 100%;

      .uni-title__box {
        padding: 2px 0;
      }
    }

    .specification-center {
      float: right;
      margin-right: 30px;
      display: flex;
      justify-content: space-around;
      align-items: center;
    }

    .specification-bottom {
      table {
        width: 100%;
        font-size: 18px;
        text-align: center;

        th {
          padding: 12px 10px;
          border-bottom: 1px solid #ebeef5;
        }

        td {
          padding: 8px 10px;
          border-bottom: 1px solid #ebeef5;
        }
      }

      .specification-screen {
        position: relative;

        .specification-size {
          width: 80px;
          height: 40px;
          margin: auto;
          border: 2px solid gray;
          border-radius: 5px;
          background-color: white;
          font-size: 20px;
          font-weight: bold;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }

        .specification-size-option {
          .specification-select {
            width: 100%;
            max-height: 140px;
            overflow: auto;
            background-color: whitesmoke;
            filter: drop-shadow(0 0 1px black);
            border-bottom: 1px solid whitesmoke;
            border-radius: 5px;
            position: absolute;
            left: 0;
            top: 80px;
            z-index: 1;

            .specification-select-item {
              padding: 5%;
              font-size: 18px;
              font-weight: bold;
              border-bottom: 1px solid gray;
            }
          }

          &:before {
            content: "";
            position: absolute;
            left: calc(50% - 10px);
            bottom: 0;
            filter: drop-shadow(0 0 1px black);
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid whitesmoke;
          }
        }
      }

      .specification-remark {
        width: 200px;
        height: 50px;
        padding: 5px;
        border-radius: 10px;
        border: 2px solid gray;
        background-color: white;
        text-align: left;
        font-size: 20px;

        &:hover {
          border: 2px solid black;
        }
      }
    }

    .specification-button {
      width: 80px;
      color: white;
      font-weight: bold;
    }
  }

  .part {
    width: 100%;
    min-height: 20%;

    .part-top {
      width: 100%;

      .uni-title__box {
        padding: 2px 0;
      }
    }

    .part-center {
      float: right;
      margin-right: 30px;
      display: flex;
      justify-content: space-around;
      align-items: center;
    }

    .part-bottom {
      .uni-table-scroll {
        min-height: 250px;
      }

      .screen {
        background-color: pink;
        border-radius: 10px;
        cursor: pointer;
        position: relative;

        .select {
          width: 100%;
          background-color: pink;
          border-radius: 10px;
          position: absolute;
          left: 0;
          top: 65px;
          z-index: 1;

          &:before {
            content: "";
            position: absolute;
            left: calc(50% - 10px);
            top: -10px;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid pink;
          }

          .selectItem {
            padding: 5%;
            border-radius: 10px;

            &:hover {
              color: white;
              background-color: deeppink;
            }
          }
        }
      }

      &:deep(.uni-table-text) {
        font-size: 18px;
        color: black;
        background-color: #fdf6e3;
      }

      input {
        width: 80px;
        height: 40px;
        margin-left: calc(50% - 40px - 2px);
        border: 2px solid gray;
        border-radius: 5px;
        font-size: 20px;
        font-weight: bold;
        background-color: white;

        &:hover {
          color: black;
          border: 2px solid black;
        }
      }
    }

    .part-button {
      width: 80px;
      color: white;
      font-weight: bold;
    }
  }
}

.brand-popup {
  .brand-navbar-list {
    width: 80vw;
    height: 50px;
    margin-bottom: 10px;
    background-color: #e6e6fa;
    border-radius: 25px;
    box-shadow: 0 0 5px white;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-shrink: 0;
    overflow-x: auto;
    overflow-y: hidden;
    
    /* #ifdef WEB */
    &::-webkit-scrollbar {
      display: none;
    }
    /* #endif */
    
    .brand-navbar {
      width: 40px;
      height: 40px;
      font-size: 24px;
      font-weight: bold;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      transition: all 0.25s ease;
      /* #ifdef WEB */
      cursor: pointer;
      /* #endif */
      
      /* #ifdef APP */
      &:active {
        background-color: #ccc !important;
      }
      /* #endif */
      
      /* #ifdef WEB */
      &:hover {
        background-color: #ccc !important;
      }
      /* #endif */
    }
  }
  
  .brand-box {
    width: 80vw;
    height: 80vh;
    border-radius: 10px;
    background-color: #fdf6e3;
    box-shadow: 0 0 5px white;
    overflow: auto;

    .brand-part {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;

      .brand-initial {
        width: 100%;
        padding: 10px;
        font-size: 32px;
        font-weight: bold;
      }

      .brand {
        width: 18%;
        height: 8vh;
        margin: 1%;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        border-radius: 10px;
        cursor: pointer;
        font-size: 24px;
        font-weight: bold;
        color: purple;
        box-shadow: 0 0 5px gray;
        transition: all 0.1s linear;
        
        &:active {
          box-shadow: 0 0 2px gray;
          transform: scale(0.98);
        }
        
        /* #ifdef APP */
        &:active {
          background-color: #ccc;
        }
        /* #endif */
        
        /* #ifdef WEB */
        &:hover {
          background-color: #ccc;
        }
        /* #endif */
      }
    }
  }
}

.preview-popup {
  .preview {
    width: 100vw;
    height: 100vh;
    overflow: auto;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;

    hr {
      border: 3px solid darkviolet;
      border-radius: 3px;
      margin-bottom: 0.5%;
    }

    text {
      font-weight: bold;
      margin-bottom: 0.5%;
    }

    table {
      width: 100%;
      margin: 0.5% 0;
      border-spacing: 0;
      border: 1px solid black;
      overflow: auto;

      th {
        width: 3%;
        border: 1px solid black;
        text-align: center;
        box-sizing: border-box;
        font-size: 14px;
      }

      td {
        width: 3%;
        border: 1px solid black;
        text-align: center;
        box-sizing: border-box;
        font-size: 14px;
      }
    }

    .preview-box {
      width: 100%;
      min-height: 100%;
      padding: 2%;
      border-radius: 10px;
      box-shadow: 0 0 1px 5px #dddddd;
      box-sizing: border-box;
      position: relative;

      .back {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        position: absolute;
        left: 2.5%;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        box-shadow: 0 0 5px gray;
        top: 4.5%;
        cursor: pointer;
        z-index: 1;
      }

      .title {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 2%;
      }

      .base-size {
        padding: 1%;
        position: absolute;
        top: 10%;
        left: 6%;
        font-size: 20px;
        font-weight: bold;
        border: 2px solid black;
      }

      .size-type {
        position: absolute;
        top: 3%;
        left: 15%;
        font-size: 24px;
        font-weight: bold;
      }

      .print {
        position: absolute;
        top: 10%;
        right: 10%;
        font-size: 20px;
        font-weight: bold;
      }

      .picture {
        height: 160px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 1%;
      }

      .info {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5%;
      }

      .detail {
        min-height: 40vh;
      }

      .sign {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .sign-item {
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: bold;

          .sign-item-button {
            width: 100px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            background-color: brown;
            border-radius: 10px;
            cursor: pointer;

            &:hover {
              opacity: 0.8;
            }
          }

          .sign-item-image {
            width: 100px;
            height: 50px;
            border-radius: 10px;
            cursor: pointer;
          }
        }
      }
    }

    .loading {
      animation: 1.5s linear infinite loading;
    }

    @keyframes loading {
      from {
        transform: rotateZ(0);
      }

      to {
        transform: rotateZ(360deg);
      }
    }
  }
}

.add-specification-popup {
  .add-specification {
    width: 100vw;
    height: 100vh;
    overflow: auto;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;

    .add-specification-box {
      width: 100%;
      min-height: 100%;
      border-radius: 10px;
      box-shadow: 0 0 1px 5px #dddddd;
      box-sizing: border-box;
      position: relative;

      .back {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        position: absolute;
        left: 2.5%;
        width: 50px;
        height: 50px;
        top: 4.5%;
        cursor: pointer;
        z-index: 1;
        border-radius: 50%;
        box-shadow: 0 0 5px gray;
      }

      .submit {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        position: absolute;
        right: 2.5%;
        top: 4.5%;
        cursor: pointer;
        z-index: 1;
      }

      .title {
        margin-bottom: 1%;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        padding: 16px;
      }

      .add-specification-data {
        width: 100%;
        position: relative;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;

        .add-specification-attribute {
          width: 35%;
          margin-left: 15%;
          margin-bottom: 8%;
          background-color: #fdf6e3;

          .uni-easyinput {
            width: 80%;
            margin-left: 16px;

            &:deep(.uni-easyinput__content-input) {
              height: 40px;
              font-size: 20px;
              font-weight: bold;
            }
          }

          .uni-data-checklist {
            margin-left: 16px;

            &:deep(.checklist-box) {
              margin-top: 0px;
            }

            &:deep(.checklist-text) {
              padding: 8px;
              font-size: 20px;
              font-weight: bold;
            }
          }

          .uni-stat__select {
            width: 80%;
            margin-left: 16px;

            &:deep(.uni-select) {
              height: 40px;
              font-size: 20px;
              font-weight: bold;
              background-color: white;
            }

            &:deep(.uni-scroll-view-content) {
              height: 200px;
            }

            &:deep(.uni-icons) {
              margin-left: -25px;
              line-height: 26px;
            }

            &:deep(.uni-select__selector-item) {
              font-size: 18px;
              padding: 2px 10px;
            }
          }

          .uni-file-picker {
            margin-left: 16px;

            &:deep(.file-picker__box-content) {
              border-color: black;
            }

            &:deep(.icon-add) {
              background-color: black;
            }
          }

          .add-specification-remark {
            width: 80%;
            height: 80px;
            padding: 10px;
            margin-left: 16px;
            border-radius: 10px;
            border: 2px solid gray;
            background-color: white;
            text-align: left;
            font-size: 20px;
          }
        }
      }
    }
  }
}

.add-part-popup {
  .add-part {
    width: 100vw;
    height: 100vh;
    overflow: auto;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;

    .add-part-box {
      width: 100%;
      min-height: 100%;
      border-radius: 10px;
      box-shadow: 0 0 1px 5px #dddddd;
      box-sizing: border-box;
      position: relative;

      .back {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        position: absolute;
        left: 2.5%;
        width: 50px;
        height: 50px;
        top: 4.5%;
        border-radius: 50%;
        box-shadow: 0 0 5px gray;
        cursor: pointer;
        z-index: 1;
      }

      .submit {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        position: absolute;
        right: 2.5%;
        top: 4.5%;
        cursor: pointer;
        z-index: 1;
      }

      .title {
        margin-bottom: 1%;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        padding: 16px;
      }

      .add-part-data {
        width: 100%;
        position: relative;
        display: flex;
        justify-content: space-evenly;
        align-items: flex-start;
        flex-wrap: wrap;

        .add-part-attribute {
          margin-bottom: 1%;
          background-color: #fdf6e3;

          .uni-easyinput {
            margin-left: 16px;

            &:deep(.uni-easyinput__content-input) {
              height: 40px;
              font-size: 20px;
              font-weight: bold;
            }
          }

          .uni-data-checklist {
            margin-left: 16px;

            &:deep(.checklist-box) {
              margin-top: 0px;
              width: 21%;
            }

            &:deep(.checklist-text) {
              padding: 5px;
              font-size: 18px;
              font-weight: bold;
            }
          }

          .uni-numbox {
            margin-left: 16px;
            height: 40px;

            &:deep(.uni-numbox-btns) {
              width: 40px;
              box-sizing: border-box;

              .uni-numbox--text {
                font-weight: bold;
              }
            }

            &:deep(.uni-numbox__value) {
              width: 60px;
              height: 40px;
              font-size: 20px;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
}

.temporary-part-popup {
  .temporary-part {
    width: 100vw;
    height: 100vh;
    overflow: auto;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;

    .temporary-part-box {
      width: 100%;
      min-height: 100%;
      border-radius: 10px;
      box-shadow: 0 0 1px 5px #dddddd;
      box-sizing: border-box;
      position: relative;

      .back {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        position: absolute;
        left: 2.5%;
        width: 50px;
        height: 50px;
        top: 4.5%;
        border-radius: 50%;
        box-shadow: 0 0 5px gray;
        cursor: pointer;
        z-index: 1;
      }

      .submit {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        position: absolute;
        right: 2.5%;
        top: 4.5%;
        cursor: pointer;
        z-index: 1;
      }

      .title {
        margin-bottom: 1%;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        padding: 16px;
      }

      .temporary-part-data {
        width: 100%;
        position: relative;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;

        .temporary-part-attribute {
          width: 35%;
          margin-left: 15%;
          margin-bottom: 8%;
          background-color: #fdf6e3;

          .uni-easyinput {
            width: 80%;
            margin-left: 16px;

            &:deep(.uni-easyinput__content-input) {
              height: 40px;
              font-size: 20px;
              font-weight: bold;
            }
          }

          .uni-data-checklist {
            margin-left: 16px;

            &:deep(.checklist-box) {
              margin-top: 0px;
            }

            &:deep(.checklist-text) {
              padding: 8px;
              font-size: 20px;
              font-weight: bold;
            }
          }

          .uni-numbox {
            margin-left: 16px;
            height: 40px;

            &:deep(.uni-numbox-btns) {
              width: 40px;
              box-sizing: border-box;

              .uni-numbox--text {
                font-weight: bold;
              }
            }

            &:deep(.uni-numbox__value) {
              width: 60px;
              height: 40px;
              font-size: 20px;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
}

.tip-popup {
  &:deep(.uni-popup-message-text) {
    font-size: 18px;
    font-weight: bold;
  }
}

.model-picture-popup,
.specification-picture-popup,
.signature-popup {
  .watermark {
    &::before {
      font-size: 36px;
    }
  }
}

.watermark {
  position: relative;
  overflow: hidden;

  &::before {
    content: "STELLA";
    position: absolute;
    right: 5px;
    top: 5px;
    color: #ddd;
    font-size: 24px;
    font-weight: bold;
    z-index: 1;
    pointer-events: none;
  }
}

button:hover {
  opacity: 0.8;
}

.list-enter-active,
.list-leave-active {
  transition: all 0.25s ease-out;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
}

.list-enter-to,
.list-leave-from {
  opacity: 1;
}

.down-enter-active,
.down-leave-active {
  transition: all 0.1s ease-out;
}

.down-enter-from,
.down-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}

.down-enter-to,
.down-leave-from {
  transform: translateY(0);
  opacity: 1;
}
</style>