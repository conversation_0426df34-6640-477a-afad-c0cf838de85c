-- 邮件推送日志表
-- 用于记录邮件发送历史和状态跟踪

CREATE TABLE pcc_email_push_log (
    id NUMBER(10) PRIMARY KEY,                    -- 主键ID
    batch_id VARCHAR2(50),                       -- 批次ID（同一批发送的邮件使用相同批次ID）
    
    -- 邮件基本信息
    email_type VARCHAR2(50) NOT NULL,            -- 邮件类型 (QUOTE_V1, QUOTE_V2, QUOTE_V3, QUOTE_V4, QUOTE_V5, ESTIMATE_Z, ESTIMATE_ZZ, P_VERSION, UPDATE_NOTIFY)
    subject VARCHAR2(500),                       -- 邮件主题
    content CLOB,                                -- 邮件内容
    
    -- 收发件人信息
    from_email VARCHAR2(200),                    -- 发件人邮箱
    to_email VARCHAR2(200) NOT NULL,            -- 收件人邮箱
    cc_email VARCHAR2(1000),                    -- 抄送邮箱（多个用逗号分隔）
    bcc_email VARCHAR2(1000),                   -- 密送邮箱（多个用逗号分隔）
    
    -- 关联信息
    user_no VARCHAR2(20),                       -- 收件人工号
    dept_name VARCHAR2(100),                    -- 收件人部门
    business_id VARCHAR2(100),                  -- 业务ID（如订单号、报价单号等）
    business_type VARCHAR2(50),                 -- 业务类型
    
    -- 发送状态
    send_status VARCHAR2(20) DEFAULT 'PENDING', -- 发送状态 (PENDING-待发送, SENDING-发送中, SUCCESS-成功, FAILED-失败, RETRY-重试中)
    send_time DATE,                             -- 发送时间
    retry_count NUMBER(3) DEFAULT 0,            -- 重试次数
    max_retry NUMBER(3) DEFAULT 3,              -- 最大重试次数
    next_retry_time DATE,                       -- 下次重试时间
    
    -- 错误信息
    error_code VARCHAR2(50),                    -- 错误代码
    error_message VARCHAR2(2000),               -- 错误信息
    
    -- 附件信息
    attachment_info CLOB,                       -- 附件信息（JSON格式存储）
    
    -- 审计字段
    create_user VARCHAR2(20),                   -- 创建人
    create_date DATE DEFAULT SYSDATE,          -- 创建时间
    update_user VARCHAR2(20),                  -- 更新人
    update_date DATE DEFAULT SYSDATE,          -- 更新时间
    
    -- 约束
    CONSTRAINT chk_email_send_status CHECK (send_status IN ('PENDING', 'SENDING', 'SUCCESS', 'FAILED', 'RETRY')),
    CONSTRAINT chk_email_type CHECK (email_type IN ('QUOTE_V1', 'QUOTE_V2', 'QUOTE_V3', 'QUOTE_V4', 'QUOTE_V5', 'ESTIMATE_Z', 'ESTIMATE_ZZ', 'P_VERSION', 'UPDATE_NOTIFY'))
);

-- 创建索引
CREATE INDEX idx_pcc_email_log_batch ON pcc_email_push_log(batch_id);
CREATE INDEX idx_pcc_email_log_type ON pcc_email_push_log(email_type);
CREATE INDEX idx_pcc_email_log_status ON pcc_email_push_log(send_status);
CREATE INDEX idx_pcc_email_log_user ON pcc_email_push_log(user_no);
CREATE INDEX idx_pcc_email_log_business ON pcc_email_push_log(business_id);
CREATE INDEX idx_pcc_email_log_send_time ON pcc_email_push_log(send_time);
CREATE INDEX idx_pcc_email_log_create_date ON pcc_email_push_log(create_date);
CREATE INDEX idx_pcc_email_log_retry ON pcc_email_push_log(next_retry_time, send_status);

-- 创建序列
CREATE SEQUENCE seq_pcc_email_push_log START WITH 1 INCREMENT BY 1;

-- 添加注释
COMMENT ON TABLE pcc_email_push_log IS '邮件推送日志表';
COMMENT ON COLUMN pcc_email_push_log.id IS '主键ID';
COMMENT ON COLUMN pcc_email_push_log.batch_id IS '批次ID（同一批发送的邮件使用相同批次ID）';
COMMENT ON COLUMN pcc_email_push_log.email_type IS '邮件类型';
COMMENT ON COLUMN pcc_email_push_log.subject IS '邮件主题';
COMMENT ON COLUMN pcc_email_push_log.content IS '邮件内容';
COMMENT ON COLUMN pcc_email_push_log.from_email IS '发件人邮箱';
COMMENT ON COLUMN pcc_email_push_log.to_email IS '收件人邮箱';
COMMENT ON COLUMN pcc_email_push_log.cc_email IS '抄送邮箱';
COMMENT ON COLUMN pcc_email_push_log.bcc_email IS '密送邮箱';
COMMENT ON COLUMN pcc_email_push_log.user_no IS '收件人工号';
COMMENT ON COLUMN pcc_email_push_log.dept_name IS '收件人部门';
COMMENT ON COLUMN pcc_email_push_log.business_id IS '业务ID';
COMMENT ON COLUMN pcc_email_push_log.business_type IS '业务类型';
COMMENT ON COLUMN pcc_email_push_log.send_status IS '发送状态';
COMMENT ON COLUMN pcc_email_push_log.send_time IS '发送时间';
COMMENT ON COLUMN pcc_email_push_log.retry_count IS '重试次数';
COMMENT ON COLUMN pcc_email_push_log.max_retry IS '最大重试次数';
COMMENT ON COLUMN pcc_email_push_log.next_retry_time IS '下次重试时间';
COMMENT ON COLUMN pcc_email_push_log.error_code IS '错误代码';
COMMENT ON COLUMN pcc_email_push_log.error_message IS '错误信息';
COMMENT ON COLUMN pcc_email_push_log.attachment_info IS '附件信息';
COMMENT ON COLUMN pcc_email_push_log.create_user IS '创建人';
COMMENT ON COLUMN pcc_email_push_log.create_date IS '创建时间';
COMMENT ON COLUMN pcc_email_push_log.update_user IS '更新人';
COMMENT ON COLUMN pcc_email_push_log.update_date IS '更新时间';
