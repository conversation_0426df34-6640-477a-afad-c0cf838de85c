<template>
	<view class="container">
	 <view class="right-top-top">
		<uni-icons  class="back" @click="back" type="back" size="36" style="margin-left:1vw;margin-top:10rpx;"></uni-icons>
		<view class="uni-padding-wrap" style="margin-top:1.22vw;margin-left: 5vw;">
			<view>
				<radio-group>	
				<label class="radio"><radio @click="radioChangeOn(0)" value="0" :checked="cxStatus === 0"/>{{ t('全部') }}</label>
				<label class="radio"><radio @click="radioChangeOn(1)" value="1" :checked="cxStatus === 1"/>{{ t('A线') }}</label>
				<label class="radio"><radio @click="radioChangeOn(2)" value="2" :checked="cxStatus === 2"/>{{ t('B线') }}</label>
				</radio-group>
			</view>
		</view>
		<uni-title
		  type="h2"
		  :title="t('已配套待投入')"
		  align="left"
		  style="margin-top:-0.7vw;margin-left: 15vw;"
		></uni-title>
	</view>


	  <view style="height: 79%;margin-top:20rpx;">
		<zb-table
			:show-header="true"
			:columns="column"
			:stripe="true"
			:fit="true"
			ref="zbTable"
			show-summary
			:sum-text="t('合计')"
			@rowClick="rowClick"
			:summary-method="getSummaries"
			:border="true"
			:cell-style="cellStyle"

			:data="dataList"></zb-table>
			
			<view class="left-bottom">
				<uni-pagination
				  show-icon="true"
				  :total="pageCount"
				  :current="firstPageNo"
				  :pageSize="firstPageSize"
				  @change="firstChange"
				></uni-pagination>
			</view>
	</view>
	
	<!--单击单行弹出详情-->
	<view class="modelPopup">
	  <uni-popup ref="modelPopup" type="center">
		
	    <view v-if="modelData" class="modelBox">
	      <uni-title
	        :title="t('单号详情')"
	        type="h2"
	        align="center"
	      ></uni-title>
	      
	      <view class="modelData">
	        <uni-section :title="t('样品单号')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.ord_no }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('型体代号')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.model_no }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('阶段')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.phase }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('责任部门')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.pb_dept }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('异常说明')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.pb_desc ? modelData.pb_desc : "--" }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('问题说明1')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.pb_desc1 ? modelData.pb_desc1 : "--" }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('异常地点')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.pb_addr ? modelData.pb_addr : "--" }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('问题数量')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.pb_qty ? modelData.pb_qty : "--" }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('样品类型')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.dev_type ? modelData.dev_type : "--" }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('解决方法')" titleFontSize="1.25rem" type="line" class="modelAttribute">
			  <uni-easyinput type="textarea" disabled style="font-size: 1rem; margin-left: 1rem;width:80%;" :value="modelData.methoed"></uni-easyinput>
	        </uni-section>
	        
	        <uni-section :title="t('结案日期')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.cl_date ? modelData.cl_date : "--" }}
	          </text>
	        </uni-section>
			
			<uni-section :title="t('结案状态')" titleFontSize="1.25rem" type="line" class="modelAttribute">
			  <uv-switch :value="modelData.cl_flag=='Y'?true:false" disabled style="margin-left: 1rem;"></uv-switch>
			</uni-section>
	      </view>
	    </view>
	  </uni-popup>
	</view>
	<!--单击单行弹出修改界面-->
	<view class="updateSpecificationPopup">
	  <uni-popup
	    ref="updateSpecificationPopup"
	    type="center"
	    
	  >
		<view class="updateSpecificationBox">
	      <uni-title
	        :title="t('查看详情')"
	        type="h2"
	        align="center"
	      ></uni-title>
	      <view class="updateSpecificationData">
			
			<uni-section :title="t('出货日')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.shp_date }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('型体编号')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.model_no }}
			  </text>
			</uni-section>
				
			<uni-section :title="t('样品单号')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.ord_no }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('样品类型')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.dev_type }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('双数')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.tot_qty }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('鞋面')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.ushelf_no }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('大底')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.bshelf_no6 }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('中底')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.bshelf_no2 }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('中底皮')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.bshelf_no1 }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('包粘')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.bshelf_no3}}
			  </text>
			</uni-section> 
					
	      </view>
	    </view>
	  </uni-popup>
	</view>
	
	<view class="uwrap" style="margin-top: 1vw;">
		<text style="margin-right: 1vw;float: right;color: darkgrey;">{{ t('注:红色字-未保存或保存双数小于总数') }}</text>
	</view>
	</view>
</template>

<script setup>
  import { onMounted,ref, reactive } from 'vue'
  import { onPullDownRefresh } from '@dcloudio/uni-app'
  import { useI18n } from 'vue-i18n'
  import urlPrefix from '@/pages/common/urlPrefix.js'
  
  // 国际化
  const { t } = useI18n()
  
	// const urlPrefix = "http://***********:8200";
	//const urlPrefix = "http://**********:8080	"   ***********:8200;
	const ypdh=ref()
	const zrbm=ref()
	const ycsm=ref()
	const ycdd=ref()
	const wtsl=ref(0)
	const selectWt=ref()
	const wtsm1=ref([])
	const switchDay=ref(false)
	//第几页
	const firstPageNo=ref(1)
	const firstPageSize=ref(20)
	const pageCount=ref(0)
	
	//表单数据
	const dataList=ref([])
	
	//扫描状态
	const brandCode=ref(0)
	
	//切换状态
	const cxStatus = ref(0)
	
	//详情弹窗
	const modelData = ref()
	const modelPopup = ref()
	
	const insUs=uni.getStorageSync("loUserNo")
	const insName=uni.getStorageSync("loginUserName")
	const iuser=ref("")
	
	//修改弹窗
	const updateSpecificationPopup = ref()
	const updateSpecificationDetail = reactive({
	  ord_no: "",
	  pb_dept: "",
	  pb_addr: "",
	  methoed: "",
	  cl_flag: "",
	  upd_user: insUs,
	  ins_date: ""
	})
	
	//删除参数
	const deleSpecificationDetail = reactive({
	  ord_no: "",
	  pb_dept: "",
	  pb_addr: "",
	  upd_user: insUs,
	  ins_date: ""
	}) 
	
	let timeout = null;
	//点击线别
	async function radioChangeOn(v){
		cxStatus.value=v
		dataList.value=[]
		if (timeout !== null) {
			clearTimeout(timeout);
			timeout = null;
		}else{
			timeout = setTimeout(async function(){
				await getPageCount()
				await getData()
				timeout = null
			},800);
		}
		console.log(cxStatus.value)
	}
	
	//下拉刷新
	 onPullDownRefresh (()=>{	//通过 onPullDownRefresh  可以监听到下拉刷新的动作
			uni.startPullDownRefresh({
				success(){
					//console.log(urlPrefix)
					getPageCount()
					getData()
					uni.stopPullDownRefresh()	//停止当前页面下拉刷新。
				},fail(){}
			})
	    }) 
		
	//表尾合计
	function getSummaries(param){
	  const { columns, data } = param;
	  const sums = [];
	  columns.forEach((column, index) => {
	    if (index === 0) {
	      sums[index] = t('合计');
	      return;
	    }		
	    if(column.name==='tot_qty'||column.name==='dp1_qty'||column.name==='pat_qty'||column.name==='p1_qty'){
	      const values = data.map(item => Number(item[column.name]));
	      if (!values.every(value => isNaN(value))) {
	        sums[index] = values.reduce((prev, curr) => {
	          const value = Number(curr);
	          if (!isNaN(value)) {
	            return prev + curr;
	          } else {
	            return prev;
	          }
	        }, 0);
	        sums[index] += '  ';
	      }
	    }else{
	      sums[index] = ' ';
	    }
	  });
	  return sums;
	}	
	
	//保存data
	const saveData = reactive({
	  "ord_no": "",
	  "pb_qty": "",
	  "pb_desc1":"",
	  "pb_dept": "",
	  "pb_desc": "",
	  "pb_addr": "",
	  "ins_user": insUs
	})
    const stus=ref(0)
	
	//修改保存
	// async function submitUpdateSpecification() {
		
	// 	updateSpecificationDetail.pb_dept = updateSpecificationDetail.pb_dept.split("-")[0]
	// 	updateSpecificationDetail.pb_addr = updateSpecificationDetail.pb_addr.split("-")[0]
	//   await uni.request({
	//     url: urlPrefix + "/scan/updateException",
	//     method: "POST",
	//     data: updateSpecificationDetail
	//   }).then(res => {
	// 	  console.log(res.data)
	//     if (res.data.code) {
	//       uni.showToast({
	//       	title: "修改成功"
	//       });
	// 	  getPageCount();
	// 	  getData();
	//     } else {
	//       uni.showToast({
	//       	title: "修改失败!!",
	//       	icon:"error"
	//       });
	//     }
	//   }).catch(err => {
	//     console.log(err)
	//   })
	  
	//   updateSpecificationPopup.value.close()
	// }
	
	const column=ref([
        { name: 'shp_date', label: t('出货日'),emptyString:'--' },
		{ name: 'ord_no', label: t('样品单号')},
		{ name: 'dev_type', label: t('样品类型')},
		{ name: 'model_pic', label: t('鞋图'),type:"img",align: "center" },
        { name: 'model_no', label: t('型体编号')},
		{ name: 'last_no', label: t('楦头编号') },
		{ name: 'status', label: t('楦头'),emptyString:' '},
        { name: 'tot_qty', label: t('开单数'),emptyString:' ',sorter:true},
		{ name: 'pat_qty', label: t('待配送'),emptyString:' ',sorter:true},
		{ name: 'dp1_qty', label: t('待投入'),emptyString:' ',sorter:true},
		{ name: 'p1_qty', label: t('已投入'),emptyString:' ',sorter:true},
        //{ name: 'pat_flag', label: '配送狀態'},
        { name: 'mat_date', label: t('配套时间')},
       // { name: 'pat_date', label: '配送时间'},
		// { name: 'ushelf_no', label: '鞋面  ',emptyString:'/'},
		// { name: 'bshelf_no3', label: '包粘  ',emptyString:'/'},
		// { name: 'bshelf_no6', label: '大底  ',emptyString:'/'},
		// { name: 'bshelf_no2', label: '中底  ',emptyString:'/'},
		// { name: 'bshelf_no1', label: '中底皮  ',emptyString:'/'},
        ]);

    //判断颜色
	function cellStyle({row, column, rowIndex, columnIndex}){
		if(columnIndex === 6){
			return{
				color:'red'
			}
		}
		// if(row.b1_flag==='N'&&column.name==='bshelf_no1'&&columnIndex === 12){
		// 	return{
		// 		color:'red'
		// 	}
		// }
		
		// if(row.b2_flag==='N'&&column.name==='bshelf_no2'&&columnIndex === 11){
		// 	return{
		// 		color:'red'
		// 	}
		// }
		
		// if(row.b3_flag==='N'&&column.name==='bshelf_no3'&&columnIndex === 9){
		// 	return{
		// 		color:'red'
		// 	}
		// }
		
		// if(row.b6_flag==='N'&&column.name==='bshelf_no6'&&columnIndex === 10){
		// 	return{
		// 		color:'red'
		// 	}
		// }
		
		// if(row.u_flag==='N'&&column.name==='ushelf_no'&&columnIndex === 8){
		// 	return{
		// 		color:'red'
		// 	}
		// }
      }
	
	function xlGetData(e){
		//console.log(e)
		updateSpecificationDetail.ord_no = e.ord_no
		updateSpecificationDetail.pb_dept = e.pb_dept
		updateSpecificationDetail.pb_addr = e.pb_addr
		updateSpecificationDetail.methoed = e.methoed
		updateSpecificationDetail.cl_flag = e.cl_flag
		updateSpecificationDetail.ins_date = e.ins_date
		updateSpecificationPopup.value.open()
	}
	
	//返回首页
	function back() {
	  uni.navigateBack({
	  	delta: 1,
	  	animationType: 'pop-out',
	  	animationDuration: 200
	  })
	}
			
	async function firstChange(e){
		firstPageNo.value=e.current;
		await uni.request({
			   url: urlPrefix + "/match/getWaitInput",
			   data:{
					"page_no": firstPageNo.value,
					"page_size": firstPageSize.value,
					"status":cxStatus.value
				},
			   method: "POST"
			 }).then(res => {
				//console.log(res.data);
				dataList.value = res.data.data
			 }).catch(err => {
			   console.log(err)
			 })
	}	
	
	//点击详情
	function rowClick(e){
		updateSpecificationDetail.ord_no = e.ord_no
		updateSpecificationDetail.model_no = e.model_no
		updateSpecificationDetail.pat_flag = e.pat_flag
		updateSpecificationDetail.dev_type = e.dev_type
		updateSpecificationDetail.tot_qty = e.tot_qty
		updateSpecificationDetail.shp_date = e.shp_date
		updateSpecificationDetail.ushelf_no = e.ushelf_no
		updateSpecificationDetail.bshelf_no6 = e.bshelf_no6
		updateSpecificationDetail.bshelf_no2 = e.bshelf_no2
		updateSpecificationDetail.bshelf_no1 = e.bshelf_no1
		updateSpecificationDetail.bshelf_no3 = e.bshelf_no3
		updateSpecificationPopup.value.open()
	}	

	//获取数据
	async function getData(){
		//console.log(iuser.value);
		await uni.request({
			   url: urlPrefix + "/match/getWaitInput",
			   data:{
					"page_no": firstPageNo.value,
					"page_size": firstPageSize.value,
					"status": cxStatus.value
				},
			   method: "POST"
			 }).then(res => {
				dataList.value = res.data.data
				for(let i=0;i<dataList.value.length;i++){
					dataList.value[i].model_pic='data:image/png;base64,'+dataList.value[i].model_pic
				}
			 }).catch(err => {
			   console.log(err)
			   uni.showToast({
			   	title: t('获取数据失败！'),
				icon:"error"
			   });
			 })
	}
	
	//获取总行数
	async function getPageCount(){
		//console.log(1);
		await uni.request({
			   url: urlPrefix + "/match/getInputCount",
			   data:{	
					"status":cxStatus.value
				},
			   method: "POST"
			 }).then(res => {
				//console.log(res.data);
				pageCount.value = res.data.data
				firstPageSize.value = res.data.data
			 }).catch(err => {
			   console.log(err)
			 })
	}
	//预加载
	onMounted(async () => {
	  await getPageCount()
	  await getData()
	  //await search()
	})
	
</script>

<style lang="scss">
	.radio{
		margin-left: 1.5vw;
		font-size: 15px;
		font-weight:480;
	}
	page {
	  width: 100%;
	  height: 100%;
	  padding: 2.5% 2% 1.5% 2%;
	  box-sizing: border-box;
	  background-color: white;
	}
	.container {
		width: 100%;
	height: 100%;
	box-sizing: border-box;
	border-radius: 10px;
	box-shadow: 0 0 1px 5px #dddddd;
	}
	
	::v-deep{
	  .uni-card{
	    margin: 8px!important;
	    padding: 0!important;
	    .uni-card__content{
	      padding: 0!important;
	    }
	  }
	}
	.right-top-top{
		display: flex;
	}
.propertyBox{
	margin-top: 2rpx;
	flex-flow: row;
	justify-content: flex-start;
	display: flex;
	position: -webkit-sticky;
	top: var(--window-top);
}
.inpBr{
	width: 15%;
	margin-left:10rpx;
}
	.left-bottom {
	width: 100%;
	height: 10%;
	display: flex;
	justify-content: center;
	align-items: center;

	.uni-pagination__total {
	  margin-right: 0.4375rem !important;
	}

	.uni-pagination__num-tag {
	  min-width: 1.875rem !important;
	  background-color: #F0F0F0 !important;
	}

	.page--active {
	  color: white !important;
	  background-color: deeppink !important;
	}
	}
	
	.modelPopup {
	  .modelBox {
	    width: 75vw;
	    height: 90vh;
	    border-radius: 1vw;
	    background-color: white;
	    
	    .modelData {
	      width: 100%;
	      height: 90%;
	      display: flex;
	      justify-content: center;
	      align-items: flex-start;
	      flex-wrap: wrap;
	      
	      .modelAttribute {
	        width: 30%;
	        margin-left: 3.3%;
	      }
	    }
	  }
	}
	
	.updateSpecificationPopup {
	  .updateSpecificationBox {
	    width: 75vw;
	    height: 90vh;
	    border-radius: 1vw;
	    background-color: white;
	    
	    .updateSpecificationData {
	      width: 100%;
	      height: 100%;
	      position: relative;
	      display: flex;
	      justify-content: center;
	      align-items: flex-start;
	      flex-wrap: wrap;
	      
	      .updateSpecificationAttribute {
	        width: 35%;
	        margin-left: 15%;
	        
	        .uni-easyinput {
	          width: 70%;
	          margin-left: 1rem;
	        }
	        
	        .uni-stat__select {
	          width: 70%;
	          margin-left: 1rem;
	        }
	        
	        .uni-numbox {
	          margin-left: 1rem;
	        }
	        
	        .uni-file-picker {
	          margin-left: 1rem;
	        }
	      }
	    }
	  }
	}
	
	.submit {
	  display: flex;
	  justify-content: center;
	  align-items: center;
	}

	.back {
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		position: absolute;
		left: 1.5%;
		width: 50px;
		height: 50px;
		top: 6%;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		cursor: pointer;
		z-index: 1;
	}
</style>
