package com.zqn.email.mapper;

import com.zqn.email.entity.EmailPushLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送日志Mapper接口
 * @date 2025/01/22 10:00
 */
@Mapper
public interface EmailPushLogMapper {

    /**
     * 插入邮件推送日志
     *
     * @param log 日志信息
     * @return 影响行数
     */
    int insert(EmailPushLog log);

    /**
     * 批量插入邮件推送日志
     *
     * @param logs 日志列表
     * @return 影响行数
     */
    int batchInsert(@Param("logs") List<EmailPushLog> logs);

    /**
     * 根据ID更新邮件推送日志
     *
     * @param log 日志信息
     * @return 影响行数
     */
    int updateById(EmailPushLog log);

    /**
     * 根据ID查询邮件推送日志
     *
     * @param id 日志ID
     * @return 日志信息
     */
    EmailPushLog selectById(@Param("id") Long id);

    /**
     * 根据批次ID查询邮件推送日志列表
     *
     * @param batchId 批次ID
     * @return 日志列表
     */
    List<EmailPushLog> selectByBatchId(@Param("batchId") String batchId);

    /**
     * 根据业务ID查询邮件推送日志列表
     *
     * @param businessId 业务ID
     * @return 日志列表
     */
    List<EmailPushLog> selectByBusinessId(@Param("businessId") String businessId);

    /**
     * 根据发送状态查询邮件推送日志列表
     *
     * @param sendStatus 发送状态
     * @return 日志列表
     */
    List<EmailPushLog> selectBySendStatus(@Param("sendStatus") String sendStatus);

    /**
     * 查询需要重试的邮件日志
     *
     * @return 日志列表
     */
    List<EmailPushLog> selectRetryLogs();

    /**
     * 根据条件查询邮件推送日志列表
     *
     * @param emailType   邮件类型
     * @param sendStatus  发送状态
     * @param userNo      用户工号
     * @param businessId  业务ID
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return 日志列表
     */
    List<EmailPushLog> selectByCondition(@Param("emailType") String emailType,
                                        @Param("sendStatus") String sendStatus,
                                        @Param("userNo") String userNo,
                                        @Param("businessId") String businessId,
                                        @Param("startDate") Date startDate,
                                        @Param("endDate") Date endDate);

    /**
     * 统计邮件发送情况
     *
     * @param emailType  邮件类型
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @return 统计结果
     */
    List<EmailPushLog> selectSendStatistics(@Param("emailType") String emailType,
                                           @Param("startDate") Date startDate,
                                           @Param("endDate") Date endDate);

    /**
     * 删除指定日期之前的日志
     *
     * @param beforeDate 指定日期
     * @return 影响行数
     */
    int deleteBeforeDate(@Param("beforeDate") Date beforeDate);

    /**
     * 根据条件统计日志数量
     *
     * @param emailType   邮件类型
     * @param sendStatus  发送状态
     * @param userNo      用户工号
     * @param businessId  业务ID
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return 数量
     */
    int countByCondition(@Param("emailType") String emailType,
                        @Param("sendStatus") String sendStatus,
                        @Param("userNo") String userNo,
                        @Param("businessId") String businessId,
                        @Param("startDate") Date startDate,
                        @Param("endDate") Date endDate);
}
