<template>
  <view class="container">
    <view class="header">
      <view class="header-left">
        <button class="back-btn" @click="goBack">
          <text class="back-icon">←</text>
          <text class="back-text">返回</text>
        </button>
      </view>
      <view class="header-center">
        <text class="title">制鞋学员一览表</text>
      </view>
      <view class="header-right">
        <text class="date">{{ currentDate }}</text>
        <button class="export-btn" @click="showExportDialog">
          <text class="export-icon">📊</text>
          <text class="export-text">导出</text>
        </button>
        <button class="add-btn" @click="addRecord">
          <text class="add-icon">+</text>
          <text class="add-text">新增</text>
        </button>
      </view>
    </view>
    
    <view class="content">
      <!-- 筛选区域 -->
      <view class="filter-section">
        <view class="filter-row">
          <view class="filter-items">
            <view class="filter-item">
              <text class="filter-label">日期范围:</text>
              <view class="date-range-container">
                <picker mode="date" :value="filterStartDate" @change="onFilterStartDateChange">
                  <view class="picker-text">{{ filterStartDate || '开始日期' }}</view>
                </picker>
                <text class="date-separator">至</text>
                <picker mode="date" :value="filterEndDate" @change="onFilterEndDateChange">
                  <view class="picker-text">{{ filterEndDate || '结束日期' }}</view>
                </picker>
              </view>
            </view>
            <view class="filter-item">
              <text class="filter-label">单号搜索:</text>
              <input 
                type="text" 
                v-model="filterOrderNo" 
                placeholder="请输入单号" 
                class="filter-input"
                @input="filterRecords"
              />
            </view>
            <view class="filter-item">
              <text class="filter-label">学员筛选:</text>
              <picker 
                mode="selector" 
                :range="studentOptions" 
                :value="selectedStudentIndex"
                @change="onStudentChange">
                <view class="picker-text">{{ selectedStudentIndex >= 0 ? studentOptions[selectedStudentIndex] : '选择学员' }}</view>
              </picker>
            </view>
          </view>
          <button class="clear-filter-btn" @click="clearFilters">清空筛选</button>
        </view>
      </view>
      
      <!-- 记录列表 -->
      <view class="record-list">
        <scroll-view 
          class="table-container" 
          scroll-x="true" 
          scroll-y="true"
          :enable-back-to-top="false"
          :refresher-enabled="true"
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
          @scrolltolower="onScrollToLower"
          :lower-threshold="100">
          <div v-if="isLoading && currentPage === 1" class="loading-container">
            <text class="loading-text">正在加载学员记录...</text>
          </div>
          <table v-else class="record-table">
            <thead class="table-header-container">
              <tr class="table-header">
                <th class="th th-date">日期</th>
                <th class="th th-shoe-pic">鞋图</th>
                <th class="th th-order-no">样品单号</th>
                <th class="th th-ord-qty">订单数量</th>
                <th class="th th-model-no">鞋型</th>
                <th class="th th-student">学员</th>
                <th class="th th-action-pics">实物动作</th>
                <th class="th th-improve-pics">改善动作</th>
                <th class="th th-product-pics">成品</th>
                <th class="th th-student-thoughts">学员感想</th>
                <th class="th th-communication">教官评语</th>
                <th class="th th-learned">动作学会</th>
                <th class="th th-actions">操作</th>
              </tr>
            </thead>
            <tbody class="table-body">
              <tr 
                v-for="(record, index) in recordList" 
                :key="record.id || index" 
                class="table-row">
                <td class="td td-date">{{ record.recordDate }}</td>
                <td class="td td-shoe-pic">
                  <image 
                    v-if="record.shoePic" 
                    :src="getImageUrl(record.shoePic, 'base64')"
                    mode="aspectFit" 
                    class="shoe-pic"
                    @click="previewImage(record.shoePic, 'base64')"
                  />
                  <text v-else class="no-image">无图片</text>
                </td>
                <td class="td td-order-no">{{ record.orderNo || '-' }}</td>
                <td class="td td-ord-qty">{{ record.ordQty || '-' }}</td>
                <td class="td td-model-no">{{ record.modelNo || '-' }}</td>
                <td class="td td-student">{{ record.student || '-' }}</td>
                <td class="td td-action-pics">
                  <view class="pic-list" v-if="record.actionPics && record.actionPics.length > 0">
                    <image 
                      v-for="(pic, picIndex) in record.actionPics.slice(0, 3)" 
                      :key="picIndex"
                      :src="getImageUrl(pic, 'file')"
                      mode="aspectFit" 
                      class="action-pic"
                      @click="previewImage(pic, 'file')"
                    />
                    <text v-if="record.actionPics.length > 3" class="more-pics">+{{ record.actionPics.length - 3 }}</text>
                  </view>
                  <text v-else class="no-image">无图片</text>
                </td>
                <td class="td td-improve-pics">
                  <view class="pic-list" v-if="record.improvePics && record.improvePics.length > 0">
                    <image 
                      v-for="(pic, picIndex) in record.improvePics.slice(0, 3)" 
                      :key="picIndex"
                      :src="getImageUrl(pic, 'file')"
                      mode="aspectFit" 
                      class="improve-pic"
                      @click="previewImage(pic, 'file')"
                    />
                    <text v-if="record.improvePics.length > 3" class="more-pics">+{{ record.improvePics.length - 3 }}</text>
                  </view>
                  <text v-else class="no-image">无图片</text>
                </td>
                <td class="td td-product-pics">
                  <view class="pic-list" v-if="record.productPics && record.productPics.length > 0">
                    <image 
                      v-for="(pic, picIndex) in record.productPics.slice(0, 3)" 
                      :key="picIndex"
                      :src="getImageUrl(pic, 'file')"
                      mode="aspectFit" 
                      class="product-pic"
                      @click="previewImage(pic, 'file')"
                    />
                    <text v-if="record.productPics.length > 3" class="more-pics">+{{ record.productPics.length - 3 }}</text>
                  </view>
                  <text v-else class="no-image">无图片</text>
                </td>
                <td class="td td-student-thoughts">
                  <view class="text-content" :title="record.studentThoughts">
                    {{ record.studentThoughts || '-' }}
                  </view>
                </td>
                <td class="td td-communication">
                  <view class="text-content" :title="record.communicationIssues">
                    {{ record.communicationIssues || '-' }}
                  </view>
                </td>
                <td class="td td-learned">
                  <view 
                    class="learned-status" 
                    :class="getLeariedStatusClass(record.learned)"
                    :key="`learned-${record.id}-${record.learned}`"
                  >
                    {{ record.learned ? '是' : '否' }}
                  </view>
                </td>
                <td class="td td-actions">
                  <button class="view-btn" @click="viewRecord(record)">查看</button>
                  <button class="edit-btn" @click="editRecord(record)">编辑</button>
                  <button class="delete-btn" @click="deleteRecord(record)">删除</button>
                </td>
              </tr>
              <tr v-if="!isLoading && recordList.length === 0">
                <td colspan="13" class="empty-message">
                  <text>暂无学员记录</text>
                </td>
              </tr>
            </tbody>
          </table>
          
          <!-- 加载更多提示 -->
          <view v-if="isLoadingMore" class="loading-more">
            <text class="loading-more-text">正在加载更多...</text>
          </view>
          
          <!-- 没有更多数据提示 -->
          <view v-if="!hasMore && recordList.length > 0" class="no-more">
            <text class="no-more-text">没有更多数据了</text>
          </view>
        </scroll-view>
      </view>
    </view>
    
    <!-- 图片预览组件 -->
    <image-popup
      v-model:show="popupVisible"
      :imageUrl="previewImageUrl"
    />
    
    <!-- 导出确认对话框 -->
    <uni-popup ref="exportPopup" type="center" :mask-click="false">
      <view class="export-dialog">
        <view class="export-dialog-header">
          <text class="export-dialog-title">导出制鞋学员一览表</text>
        </view>
        <view class="export-dialog-content">
          <text class="export-dialog-desc">确定要导出当前筛选条件下的学员记录吗？</text>
          <view class="export-filter-info">
            <text v-if="filterStartDate && filterEndDate" class="filter-info-item">日期: {{ filterStartDate }} 至 {{ filterEndDate }}</text>
            <text v-if="filterOrderNo" class="filter-info-item">单号: {{ filterOrderNo }}</text>
            <text v-if="selectedStudentIndex >= 0" class="filter-info-item">学员: {{ studentOptions[selectedStudentIndex] }}</text>
            <text v-if="!filterStartDate && !filterEndDate && !filterOrderNo && selectedStudentIndex < 0" class="filter-info-item">全部记录</text>
          </view>
        </view>
        <view class="export-dialog-actions">
          <button class="export-cancel-btn" @click="cancelExport">取消</button>
          <button class="export-confirm-btn" @click="exportToExcel" :disabled="isExporting">
            {{ isExporting ? '导出中...' : '确定导出' }}
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import ImagePopup from '@/components/ImagePopup.vue';
import { getStudentRecords, deleteStudentRecord } from '@/api/student-record';
import urlPrefix from '@/pages/common/urlPrefix.js';

export default {
  onLoad() {
    uni.setNavigationBarTitle({
      title: '制鞋学员一览表'
    });
    
    this.formatCurrentDate();
    this.loadRecords();
  },
  
  onShow() {
    // 不自动刷新数据，让用户可以继续查看之前的数据位置
    // 如果需要刷新，用户可以手动下拉刷新或点击刷新按钮
  },
  
  components: {
    ImagePopup
  },
  
  data() {
    return {
      currentDate: '',
      filterStartDate: '',
      filterEndDate: '',
      filterOrderNo: '',
      recordList: [],
      isLoading: false,
      isLoadingMore: false,
      isRefreshing: false,
      currentPage: 1,
      pageSize: 20,
      hasMore: true,
      total: 0,
      previewImageUrl: '',
      popupVisible: false,
      studentOptions: ['阮氏惠', '阮氏香', '阮秋容', '范氏李'],
      selectedStudentIndex: -1,
      // 导出相关
      exportDialogVisible: false,
      isExporting: false
    }
  },
  
  methods: {
    formatCurrentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      this.currentDate = `${year}年${month}月${day}日`;
    },
    
    async loadRecords(isRefresh = false) {
      // 如果是刷新，重置分页参数
      if (isRefresh) {
        this.currentPage = 1;
        this.hasMore = true;
        this.recordList = [];
      }
      
      // 如果没有更多数据，直接返回
      if (!this.hasMore && !isRefresh) {
        return;
      }
      
      if (this.currentPage === 1) {
        this.isLoading = true;
      } else {
        this.isLoadingMore = true;
      }
      
      try {
        const params = {
          page: this.currentPage,
          size: this.pageSize
        };
        
        if (this.filterStartDate && this.filterEndDate) {
          params.startDate = this.filterStartDate;
          params.endDate = this.filterEndDate;
        }
        
        if (this.filterOrderNo) {
          params.orderNo = this.filterOrderNo;
        }
        
        if (this.selectedStudentIndex >= 0) {
          params.student = this.studentOptions[this.selectedStudentIndex];
        }
        
        const res = await getStudentRecords(params);
        
        if (res.code === 1 && res.data) {
          let newRecords = [];
          let total = 0;
          
          if (res.data.records) {
            newRecords = res.data.records;
            total = res.data.total || res.data.records.length;
          } else if (Array.isArray(res.data)) {
            newRecords = res.data;
            total = res.data.length;
          }
          
          if (this.currentPage === 1) {
            this.recordList = newRecords;
          } else {
            this.recordList = [...this.recordList, ...newRecords];
          }
          
          this.total = total;
          
          // 判断是否还有更多数据
          if (newRecords.length < this.pageSize || this.recordList.length >= total) {
            this.hasMore = false;
          } else {
            this.hasMore = true;
          }
          
          // 确保DOM更新完成后再执行后续操作
          this.$nextTick(() => {
            // 强制触发响应式更新，确保样式正确显示
            this.$forceUpdate();
          });
          
        } else {
          if (this.currentPage === 1) {
            this.recordList = [];
          }
          uni.showToast({
            title: res.message || '获取学员记录失败',
            icon: 'none'
          });
        }
        
      } catch (error) {
        console.error('获取学员记录失败:', error);
        uni.showToast({
          title: '获取学员记录失败',
          icon: 'none'
        });
        if (this.currentPage === 1) {
          this.recordList = [];
        }
      } finally {
        this.isLoading = false;
        this.isLoadingMore = false;
        this.isRefreshing = false;
      }
    },
    
    // 滚动到底部时加载更多
    async onScrollToLower() {
      if (this.isLoadingMore || !this.hasMore) {
        return;
      }
      
      this.currentPage++;
      await this.loadRecords();
    },
    
    // 下拉刷新
    async onRefresh() {
      this.isRefreshing = true;
      await this.loadRecords(true);
    },
    
    async filterRecords() {
      // 重置分页参数，重新搜索
      this.currentPage = 1;
      this.hasMore = true;
      await this.loadRecords(true);
    },
    
    onFilterStartDateChange(e) {
      this.filterStartDate = e.detail.value;
      this.filterRecords();
    },
    
    onFilterEndDateChange(e) {
      this.filterEndDate = e.detail.value;
      this.filterRecords();
    },
    
    clearFilters() {
      this.filterStartDate = '';
      this.filterEndDate = '';
      this.filterOrderNo = '';
      this.selectedStudentIndex = -1;
      this.filterRecords();
    },
    
    getImageUrl(imageValue, type = 'base64') {
      if (!imageValue) return '';
      
      if (type === 'base64') {
        if (imageValue.startsWith('data:')) {
          return imageValue;
        }
        return `data:image/png;base64,${imageValue}`;
      }
      
      if (type === 'file') {
        return `${urlPrefix}/api/files/get/?url=${imageValue}`;
      }
      
      return imageValue;
    },
    
    previewImage(imageValue, type = 'base64') {
      if (!imageValue) return;
      this.previewImageUrl = this.getImageUrl(imageValue, type);
      this.popupVisible = true;
    },
    
    addRecord() {
      let url = '/pages/student-record/edit';
      // 如果有选中的学员，传递到编辑页面
      if (this.selectedStudentIndex >= 0) {
        const selectedStudent = this.studentOptions[this.selectedStudentIndex];
        url += `?student=${encodeURIComponent(selectedStudent)}`;
      }
      uni.navigateTo({
        url: url
      });
    },
    
    viewRecord(record) {
      uni.navigateTo({
        url: `/pages/student-record/view?id=${record.id}`
      });
    },
    
    editRecord(record) {
      uni.navigateTo({
        url: `/pages/student-record/edit?id=${record.id}`
      });
    },
    
    async deleteRecord(record) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条学员记录吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await deleteStudentRecord(record.id);
              
              if (result.code === 1) {
                // 删除成功后刷新当前页面
                await this.loadRecords(true);
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
              } else {
                uni.showToast({
                  title: result.message || '删除失败',
                  icon: 'none'
                });
              }
            } catch (error) {
              console.error('删除记录失败:', error);
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    
    goBack() {
      let back = getCurrentPages();
      if (back && back.length > 1) {
        uni.navigateBack({
          delta: 1
        });
      } else {
        uni.reLaunch({
          url: '/pages/menu/menu'
        });
      }
    },
    
    getLeariedStatusClass(learned) {
      if (learned) {
        return 'learned-yes';
      } else {
        return 'learned-no';
      }
    },
    
    onStudentChange(e) {
      this.selectedStudentIndex = e.detail.value;
      this.filterRecords();
    },
    
    // 显示导出对话框
    showExportDialog() {
      this.$refs.exportPopup.open();
    },
    
    // 导出Excel
    async exportToExcel() {
      if (this.isExporting) return;
      
      this.isExporting = true;
      this.$refs.exportPopup.close();
      
      try {
        uni.showLoading({
          title: '导出中...',
          mask: true
        });
        
        // 构建导出参数
        const params = {};
        if (this.filterStartDate && this.filterEndDate) {
          params.startDate = this.filterStartDate;
          params.endDate = this.filterEndDate;
        }
        if (this.filterOrderNo) {
          params.orderNo = this.filterOrderNo;
        }
        if (this.selectedStudentIndex >= 0) {
          params.student = this.studentOptions[this.selectedStudentIndex];
        }
        
        // 构建下载URL
        let downloadUrl = `${urlPrefix}/student-record/export/excel`;
        
        // 添加查询参数
        const queryParts = [];
        if (params.startDate) {
          queryParts.push(`startDate=${encodeURIComponent(params.startDate)}`);
        }
        if (params.endDate) {
          queryParts.push(`endDate=${encodeURIComponent(params.endDate)}`);
        }
        if (params.orderNo) {
          queryParts.push(`orderNo=${encodeURIComponent(params.orderNo)}`);
        }
        if (params.student) {
          queryParts.push(`student=${encodeURIComponent(params.student)}`);
        }
        
        if (queryParts.length > 0) {
          downloadUrl += `?${queryParts.join('&')}`;
        }
        
        // #ifdef WEB
        // Web端直接构建下载URL
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `制鞋学员一览表_${this.currentDate}.xlsx`;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        uni.showToast({
          title: '导出成功',
          icon: 'success'
        });
        // #endif
        
        // #ifdef APP
        // App端使用uni.downloadFile
        uni.downloadFile({
          url: downloadUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              uni.showToast({
                title: '导出成功',
                icon: 'success'
              });
              setTimeout(() => {
                uni.openDocument({
                  filePath: res.tempFilePath,
                  success: () => {
                    console.log('打开文档成功');
                  },
                  fail: (err) => {
                    console.error('打开文档失败:', err);
                  }
                });
              }, 1000);
            } else {
              uni.showToast({
                title: '导出失败',
                icon: 'none'
              });
            }
          },
          fail: (err) => {
            console.error('下载失败:', err);
            uni.showToast({
              title: '导出失败',
              icon: 'none'
            });
          }
        });
        // #endif
        
      } catch (error) {
        console.error('导出失败:', error);
        uni.showToast({
          title: '导出失败',
          icon: 'none'
        });
      } finally {
        this.isExporting = false;
        uni.hideLoading();
      }
    },
    
    // 取消导出
    cancelExport() {
      this.$refs.exportPopup.close();
    }
  }
}
</script>

<style>
.container {
  padding: 20px;
  height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f9fafb;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
  padding: 10px 5px;
  background: linear-gradient(135deg, #fdf6e3, #eee8d5);
  color: #657b83;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-left {
  width: 10%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 2%;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-right {
  width: 20%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  margin-left: 2%;
  gap: 15px;
  flex-wrap: nowrap;
}

.back-btn, .add-btn {
  display: flex;
  align-items: center;
  background-color: rgba(101, 123, 131, 0.2);
  border: none;
  color: #657b83;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  flex-shrink: 0;
}

.back-btn:hover, .add-btn:hover {
  background-color: rgba(101, 123, 131, 0.3);
}

.add-btn {
  background-color: rgba(147, 161, 161, 0.3);
}

.add-btn:hover {
  background-color: rgba(147, 161, 161, 0.4);
}

.export-btn {
  display: flex;
  align-items: center;
  background-color: rgba(108, 117, 125, 0.2);
  border: none;
  color: #6c757d;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  flex-shrink: 0;
}

.export-btn:hover {
  background-color: rgba(108, 117, 125, 0.3);
}

.back-icon, .add-icon {
  font-size: 18px;
  margin-right: 5px;
}

.refresh-icon {
  font-size: 16px;
  margin-right: 5px;
}

.export-icon {
  font-size: 16px;
  margin-right: 5px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 1px;
  text-align: center;
}

.date {
  font-size: 16px;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 5px 10px;
  border-radius: 4px;
  white-space: nowrap;
  flex-shrink: 0;
}

.content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.filter-section {
  margin-bottom: 15px;
  background-color: white;
  border-radius: 8px;
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.filter-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.filter-items {
  display: flex;
  align-items: center;
  gap: 30px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #657b83;
  white-space: nowrap;
}

.filter-input {
  width: 140px;
  height: 32px;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0 10px;
  font-size: 13px;
  background-color: #f5f7fa;
}

.filter-input:focus {
  border-color: #93a1a1;
  box-shadow: 0 0 0 3px rgba(147, 161, 161, 0.1);
  outline: none;
}

.picker-text {
  padding: 6px 10px;
  background-color: #fdf6e3;
  border-radius: 6px;
  font-size: 13px;
  border: 1px solid #eee8d5;
  color: #657b83;
  cursor: pointer;
  min-width: 110px;
}

.date-range-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-separator {
  font-size: 14px;
  color: #657b83;
  font-weight: 500;
}

.clear-filter-btn {
  background-color: #eee8d5;
  color: #657b83;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: auto;
  flex-shrink: 0;
}

.clear-filter-btn:hover {
  background-color: #d0d5c4;
}

.record-list {
  flex: 1;
  overflow: hidden;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  border: 1px solid #e0e7ff;
  border-radius: 8px;
  position: relative;
  max-height: 100%;
  width: 100%;
  height: 100%;
}

.record-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  min-width: 1760px;
  display: table;
}

.table-header-container {
  position: sticky;
  top: 0;
  z-index: 10;
  background: linear-gradient(to bottom, #fdf6e3, #eee8d5);
}

.table-header {
  background: linear-gradient(to bottom, #fdf6e3, #eee8d5);
}

.table-body {
  background-color: white;
  display: table-row-group;
}

.table-row {
  border-bottom: 1px solid #eee8d5;
  transition: background-color 0.15s;
}

.table-row:nth-child(even) {
  background-color: #faf8f0;
}

.table-row:hover {
  background-color: #f5f5eb;
}

.th, .td {
  padding: 12px 8px;
  text-align: center;
  border-right: 1px solid #eee8d5;
  vertical-align: middle;
  box-sizing: border-box;
}

.th {
  color: #657b83;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.th:last-child, .td:last-child {
  border-right: none;
}

/* 列宽设置 */
.th-date, .td-date {
  width: 100px;
  min-width: 100px;
}

.th-shoe-pic, .td-shoe-pic {
  width: 80px;
  min-width: 80px;
}

.th-order-no, .td-order-no {
  width: 120px;
  min-width: 120px;
}

.th-ord-qty, .td-ord-qty {
  width: 100px;
  min-width: 100px;
}

.th-model-no, .td-model-no {
  width: 100px;
  min-width: 100px;
}

.th-student, .td-student {
  width: 100px;
  min-width: 100px;
}

.th-action-pics, .td-action-pics,
.th-improve-pics, .td-improve-pics,
.th-product-pics, .td-product-pics {
  width: 150px;
  min-width: 150px;
}

.th-student-thoughts, .td-student-thoughts,
.th-communication, .td-communication {
  width: 200px;
  min-width: 200px;
}

.th-learned, .td-learned {
  width: 80px;
  min-width: 80px;
}

.th-actions, .td-actions {
  width: 180px;
  min-width: 180px;
}

.shoe-pic {
  width: 50px;
  height: 50px;
  object-fit: contain;
  cursor: pointer;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.shoe-pic:hover {
  transform: scale(1.1);
}

.pic-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.action-pic, .improve-pic, .product-pic {
  width: 40px;
  height: 40px;
  object-fit: contain;
  cursor: pointer;
  border-radius: 3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.action-pic:hover, .improve-pic:hover, .product-pic:hover {
  transform: scale(1.1);
}

.more-pics {
  font-size: 12px;
  color: #93a1a1;
  font-weight: 500;
}

.no-image {
  color: #8c9db5;
  font-size: 12px;
  font-style: italic;
}

.text-content {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: help;
  text-align: left;
  padding: 0 8px;
}

.learned-status {
  padding: 4px 12px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 14px;
}

.learned-yes {
  background-color: #d4edda;
  color: #155724;
}

.learned-no {
  background-color: #f8d7da;
  color: #721c24;
}

.view-btn, .edit-btn, .delete-btn {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  margin: 2px;
}

.view-btn {
  background-color: #6c757d;
  color: white;
}

.view-btn:hover {
  background-color: #5a6268;
}

.edit-btn {
  background-color: #93a1a1;
  color: white;
}

.edit-btn:hover {
  background-color: #7a8a8a;
}

.delete-btn {
  background-color: #dc3545;
  color: white;
}

.delete-btn:hover {
  background-color: #c82333;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  font-size: 16px;
  color: #8c9db5;
  font-style: italic;
}

.empty-message {
  padding: 30px;
  text-align: center;
  color: #8c9db5;
  font-size: 15px;
  font-style: italic;
}

.table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background-color: #c1c9d6;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background-color: #a1a9b6;
}

.table-container {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

@media screen and (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .filter-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
  }
  
  .filter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .filter-input, .picker-text {
    width: 100%;
  }
  
  .date-range-container {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }
  
  .date-separator {
    text-align: center;
    font-size: 12px;
  }
  
  .clear-filter-btn {
    align-self: flex-end;
    margin-left: 0;
    margin-top: 10px;
  }
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #f9fafb;
}

.loading-more-text {
  font-size: 14px;
  color: #93a1a1;
  font-style: italic;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
  background-color: #f9fafb;
  border-top: 1px solid #eee8d5;
}

.no-more-text {
  font-size: 13px;
  color: #8c9db5;
  font-style: italic;
}

/* 导出对话框样式 */
.export-dialog {
  background-color: white;
  border-radius: 12px;
  padding: 0;
  min-width: 320px;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.export-dialog-header {
  padding: 20px 20px 10px 20px;
  border-bottom: 1px solid #eee8d5;
}

.export-dialog-title {
  font-size: 18px;
  font-weight: bold;
  color: #657b83;
  text-align: center;
}

.export-dialog-content {
  padding: 20px;
}

.export-dialog-desc {
  font-size: 16px;
  color: #657b83;
  margin-bottom: 15px;
  text-align: center;
}

.export-filter-info {
  background-color: #fdf6e3;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #eee8d5;
}

.filter-info-item {
  display: block;
  font-size: 14px;
  color: #93a1a1;
  margin-bottom: 5px;
}

.filter-info-item:last-child {
  margin-bottom: 0;
}

.export-dialog-actions {
  display: flex;
  justify-content: space-between;
  padding: 15px 20px 20px 20px;
  gap: 15px;
}

.export-cancel-btn, .export-confirm-btn {
  flex: 1;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 16px;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.export-cancel-btn {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.export-cancel-btn:hover {
  background-color: #e9ecef;
}

.export-confirm-btn {
  background-color: #93a1a1;
  color: white;
}

.export-confirm-btn:hover {
  background-color: #7a8a8a;
}

.export-confirm-btn:disabled {
  background-color: #dee2e6;
  color: #6c757d;
  cursor: not-allowed;
}
</style>