<template>
    <view style="background-color: #F0F0F0;">


        <view id="box" style="height:calc(100vh - var(--window-bottom) - var(--status-bar-height));"
              @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd">
            <view>
                <uv-navbar @leftClick="showDrawer('showSetting')" @rightClick="dialogToggle"
                           :leftText="userDesc" leftIcon="account" rightIcon="backspace"
                           leftIconSize="35px" :title="this.$t('首页')" right
                           titleStyle="font-size: 22px;font-weight: 600;"
                           bgColor="#F0F0F0">
                </uv-navbar>
				<view style="position: absolute;right: 10vw;top: calc(2px + var(--status-bar-height)) ; z-index: 999;">
					<image src="../../static/scan1.png" style="height: 40px;width: 40px;" @click="newModelScan"></image>
				</view>

                <uni-drawer ref="showSetting" mode="left" :width="360" @change="change($event,'showSetting')">
                    <view class="close">
                        <button style="padding-top: 20rpx;" @click="closeDrawer('showSetting')">
                            <text class="word-btn-white">{{ $t('设置') }}</text>
                        </button>
                        <uni-section title="" subTitle="" type="line">
                            <view class="uni-px-6 uni-pb-6 setting">
								<uv-list>
									<uv-list-item>
										<text style="color: #c5c8c8;margin-right:0.5vw;margin-top:1.22vw;">{{ $t('头像') }}</text>
										<uv-avatar size=80 src="https://via.placeholder.com/400x400.png/2878ff" shape="square"></uv-avatar>
									</uv-list-item>
									<uv-list-item>
										<text style="color: #c5c8c8;margin-right:0.5vw;margin-top:1.22vw;">{{ $t('语音播报') }}</text>
										<uv-switch inactive-color="#c5c8c8" v-model="VoiceStatus" @change="asyncChange('Voice')"  style="margin-right:1vw;margin-top:1vw;"></uv-switch>
									</uv-list-item>
									<uv-list-item>
										<text style="color: #c5c8c8;margin-right:0.5vw;margin-top:1.22vw;">{{ $t('邮件提示') }}</text>
										<uv-switch inactive-color="#c5c8c8" v-model="EmailStatus" @change="asyncChange('Email')"  style="margin-right:1vw;margin-top:1vw;"></uv-switch>
									</uv-list-item>
									<uv-list-item>
										<text style="color: #c5c8c8;margin-right:0.5vw;margin-top:1.22vw;">{{ $t('Teams推送') }}</text>
										<uv-switch inactive-color="#c5c8c8" v-model="TeamsStatus" @change="asyncChange('Teams')"  style="margin-right:1vw;margin-top:1vw;"></uv-switch>
									</uv-list-item>
									<uv-list-item class="settingList">
										<text style="color: #c5c8c8;margin-right:0.5vw;margin-right: 4vw;margin-top:1.22vw;">{{ $t('Teams邮箱') }}</text>
										 <uv-input :placeholder="this.$t('请输入Teams邮箱地址')" border="surround" v-model="teamsAccount"></uv-input>
									</uv-list-item>
									<uv-list-item class="settingList">
										<uv-button type="info" size="normal" style="width: 40%;" :text="this.$t('取消')" @click="closeDrawer('showSetting')"></uv-button>
										<uv-button type="primary" size="normal" style="width: 40%;" :text="this.$t('确定')" @click="saveSetting"></uv-button>
									</uv-list-item>
								</uv-list>
                            </view>
                        </uni-section>
                    </view>
                </uni-drawer>


                <uni-drawer ref="showMenuAdd" mode="left" :width="240" @change="change($event,'showMenuAdd')">
                    <view class="close">
                        <button style="padding-top: 20rpx;" @click="closeDrawer('showMenuAdd')">
                            <text class="word-btn-white">{{ $t('菜单') }}</text>
                        </button>
                        <uni-section :title="this.$t('添加常用菜单')" :subTitle="this.$t('下滑浏览菜单并保存')" type="line">
                            <scroll-view scroll-y="true" style="height: calc(100vh - 250rpx);">
                                <view class="uni-px-5 uni-pb-5">
                                    <view class="text">{{ $t('多选选中：') }}</view>
                                    <uni-data-checkbox wrap multiple v-model="checkbox1"
                                                       :localdata="commonyUsedMenu"></uni-data-checkbox>
                                    <button @click="saveCommonyUsedMenu">{{ $t('保存') }}</button>
                                </view>
                            </scroll-view>
                        </uni-section>
                    </view>
                </uni-drawer>
            </view>

            <view class="banner" style="margin-top: 10vmin;">
                <view class="banner2" style="width: 96vw;">
                    <image src="../../static/home_page/stella1.png" style=""></image>
                </view>
            </view>

            <view>
                <!-- 普通弹窗 -->
                <uni-popup ref="popup" background-color="#fff">
                    <view class="popup-content" :class="{ 'popup-height': type === 'left' || type === 'right' }">
                        <text
                            class="text">{{ $t('当前系统版本') }} V : {{ version }}
                        </text>
                    </view>
                </uni-popup>
            </view>

            <view>
                <!-- 提示信息弹窗 -->
                <uni-popup ref="message" type="message">
                    <uni-popup-message type="info" :message="`
				${this.$t('当前系统版本')}:V ${version}`"></uni-popup-message>
                </uni-popup>
            </view>

            <view class="arrivalNavigation" v-show="ShowHidden" style="z-index: 999">
                <view class="d4"></view>
                <view class="sideNavigation">
                    <navigator @click="dialogToggle" url="#">
                        <li style="list-style: none;color: coral;font-weight:bold;">{{ $t('退出登录') }}</li>
                    </navigator>
                    <navigator @click="toggle('bottom')" url="#">
                        <li style="list-style: none;color: coral;font-weight:bold;">{{ $t('关于系统') }}</li>
                    </navigator>
                </view>
            </view>
            <view class="notice">
                <view class="">
                    <image src="../../static/home_page/xzz.png" mode=""></image>
                </view>
                <view class="" style="display: flex;flex-direction: row;align-items: center;">
                    <text>{{ $t('功能列表') }}</text>
					<uv-notice-bar :text="msg" style="width: 20rem;" bg-color="#f0f0f0"></uv-notice-bar>
                    <!-- <uni-list-item clickable @click="playmp3" thumb="../../static/player.png/" thumb-size="sm"/> -->
                </view>
            </view>
            <view>
                <scroll-view class="scroll-view_H" scroll-x="true" @scroll="scroll">
                    <view class="scroll-view-item_H" v-for="(tab,index) in tabBars" :key="tab.menuDesc" :id="tab.menuNo"
                          :class="navIndex==index ? 'activite' : ''" @click="checkIndex(index)">{{ $t(tab.menuDesc) }}
                    </view>
                </scroll-view>
                <!-- 内容切换 -->
                <template v-for="(tab,index) in tabBars" :key="index">
                    <view class="content" :key="tab.menuDesc" v-if="navIndex==tab.menuNo">
                        <view class="soll">
                            <template v-for="(menu,index1) in tab.menuList" :key="index1">
                                <view @click="togo(menu.menuUrl)" class="soll_view_b">
                                    <image src="../../static/home_page/shqu2.png"></image>
                                    <view class="soll_view_t">{{ $t(menu.menuDesc) }}</view>
									<text class="soll_view_t_text" v-if="menu.menuNo == 49 && topModelSum > 0">{{ $t('開版異常：') }}{{topModelSum}}</text>
									<text class="soll_view_t_text" v-if="menu.menuNo == 50 && topModelSum > 0">{{ $t('開版異常：') }}{{bottomModelSum}}</text>
									<text class="soll_view_t_text" v-if="menu.menuNo == 59 && translationCount > 0" style="color: #FFFFFF; text-shadow: 1.5px 1.5px 1.5px #FF0000, -1.5px -1.5px 1.5px #00FFFF;">{{ $t('已完成待翻译：') }}{{ translationCount }}</text>
                                </view>
                            </template>
                        </view>
                    </view>
                </template>
            </view>


            <!--点击退出提示框-->
            <view>
                <!-- 提示窗示例 -->
                <uni-popup ref="alertDialog" type="dialog">
                    <uni-popup-dialog
                        :cancelText="this.$t('取消')"
                        :confirmText="this.$t('确认')"
                        :title="this.$t('提示')"
                        :content="this.$t('确定退出登录吗?')"
                        @confirm="dialogConfirm"
                    ></uni-popup-dialog>
                </uni-popup>
            </view>


            <view style="position: fixed; bottom: 0;width:96vw;">
                <uv-divider text="@STELLA INTERNATIONAL" lineColor="#2979ff"></uv-divider>
            </view>

        </view>
    </view>
</template>

<script>
import urlPrefix from '@/pages/common/urlPrefix.js'
import wsurlPrefix from '@/pages/common/wsUrlPrefix.js'
import UvAvatar from "../../uni_modules/uv-avatar/components/uv-avatar/uv-avatar.vue";
// var wsUrl = 'ws://***********:8080/pcc/websocket-endpoint';
var wsUrl = 'ws://'+wsurlPrefix+'/websocket-endpoint?username=' + uni.getStorageSync("loginUserName");

let heartCheck = {
  timeout: 60000, // 心跳时间间隔，单位：毫秒
  timeoutObj: null,
  serverTimeoutObj: null,
  reset: function(){
    clearTimeout(this.timeoutObj);
    clearTimeout(this.serverTimeoutObj);
    return this;
  },
  start: function(){
    let self = this;
    this.timeoutObj = setTimeout(function(){
      // 这里发送心跳消息到服务器
      uni.sendSocketMessage({
        data: 'heartbeat'
      });
      self.serverTimeoutObj = setTimeout(function(){
        // 如果超过一定时间仍未收到服务器返回的消息，则重新连接WebSocket
        uni.closeSocket();
        uni.connectSocket({
          url: wsUrl
        });
      }, self.timeout);
    }, this.timeout);
  }
};

export default {
    components: {UvAvatar},
    data() {
        return {
            ShowHidden: false,
            show: false,
            type: 'bottom',
            version: '0.0.1',
            //wh:0,
            userDesc: uni.getStorageSync("loUserDesc"),
            userNo: uni.getStorageSync("loginUserName"),
            navIndex: 0,
            tabBars: [],
            commonyUsedMenu: [],
            checkbox1: [],
			VoiceStatus:false,
			EmailStatus:false,
			TeamsStatus:false,
			teamsAccount:'',
			webSocketTask: null,
			keepAliveFlag:true,
			isReconnecting:false,
			msg:'',
			topModelSum: 0,
			bottomModelSum: 0,
      translationCount: 0
			
        };
    },
    onLoad() {
        let that = this;
		this.queryModelSum();  
    },
    onShow() {
      this.getTranslationCount()
    },
    onBackPress() {
        if (this.showMask) {
            this.showMask = false;
            return true;
        } else {
            this.$refs.alertDialog.open();
            return true
        }
    },
    onResize() {
        uni.getSystemInfo({
            success: function (res) {
                // console.log(res.windowWidth);
                //console.log(res.windowHeight);
                if (res.windowWidth > res.windowHeight) {
                    // console.log('横屏');
                } else {
                    //console.log('竖屏');
                }
            }
        });
    },
	onPullDownRefresh() {
		console.log('refresh');
		this.initmenu();
		setTimeout(function () {
			uni.stopPullDownRefresh();
		}, 1000);
	},
    mounted() {
		this.loginCheck();
        this.initmenu();
    },
    methods: {
        //弹出效果
        toggle(type) {
            // open 方法传入参数 等同在 uni-popup 组件上绑定 type属性
            this.$refs.popup.open(type);

        },
        initmenu() {
            uni.request({
                url: urlPrefix + "/menu/findByPart?menuParentNo=0&userNo=" + this.userNo + "",
                method: "GET"
            }).then(res => {
                this.tabBars = res.data.data;
            }).catch(err => {
                console.log(err)
            });

            uni.request({
                url: urlPrefix + "/menu/findCommonyUsed?userNo=" + this.userNo + "",
                method: "GET"
            }).then(res => {
                // this.commonyUsedMenu = res.data.data;
                // 创建一个新数组，将每个对象转换为value和text对象
                //清空已选中数据
                this.checkbox1.length = 0;
                this.commonyUsedMenu.length = 0;
                const convertedArray = res.data.data.map(item => {
                    if (item.checked) {
                        this.checkbox1.push(item.menuNo);
                    }
                    return {value: item.menuNo, text: this.$t(item.menuDesc)};
                });
                // 将转换后的对象添加到commonlyUsedMenu数组中
                convertedArray.forEach(item => {
                    this.commonyUsedMenu.push(item);
                });
            }).catch(err => {
                console.log(err)
            })
        },
		asyncChange(type){
			if(type == 'Voice'){
				
			}else if(type == 'Email'){
				
			}else if (type == 'Teams'){
				
			}
		},
        checkIndex(index) {
            console.log(index)
            this.navIndex = index;
        },
        scroll: function (e) {
            console.log(e)
            this.old.scrollTop = e.detail.scrollTop
        },
        showDrawer(e) {
            this.$refs[e].open()
        },
        // 关闭窗口
        closeDrawer(e) {
            this.$refs[e].close()
        },
        // 抽屉状态发生变化触发
        change(e, type) {
            console.log((type === 'showMenuAdd' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));
            this[type] = e;
        },
        messageToggle(type) {
            this.$refs.message.open();
        },
        playmp3() {
            const innerAudioContext = uni.createInnerAudioContext();
            innerAudioContext.autoplay = true;
            innerAudioContext.src = '../../static/mp3/pcc.mp3';
            innerAudioContext.onPlay(() => {
                console.log('开始播放');
            });
            innerAudioContext.onError((res) => {
                console.log(res.errMsg);
                console.log(res.errCode);
            });
        },
        //弹窗
        dialogToggle(type) {
            this.$refs.alertDialog.open();
        },
        //退出登录
        dialogConfirm() {
            uni.removeStorageSync("loginPassWord");
            uni.removeStorageSync("loUserDesc");
            uni.removeStorageSync("loUserNo");
            uni.reLaunch({
                url: '../login/login'
            });
        },
        // 公告
        gonggao() {
            uni.navigateTo({
                url: '/pages/home_page/news'
            });
        },
        togo(url) {
            uni.navigateTo({
                url: url
            });
        },
        touchStart(event) {
            // 记录触摸起始点的横坐标
            this.startX = event.touches[0].clientX;
        },
        touchMove(event) {
            // 计算滑动距离
            const currentX = event.touches[0].clientX;
            this.deltaX = currentX - this.startX;
        },
        touchEnd() {
            // 判断滑动方向
            if (this.deltaX > 100) {
                // 向右滑动逻辑   这里只建议写一些性能消耗小的逻辑，比如：this.status = !this.status 因为当他的横坐标大于或小于50时，每隔一个数字都会执行一次，所以...非常消化性能
                this.initmenu();
                this.showDrawer('showMenuAdd');
            } else if (this.deltaX < -100) {
                // 向左滑动逻辑   这里只建议写一些性能消耗小的逻辑，比如：this.status = !this.status 因为当他的横坐标大于或小于50时，每隔一个数字都会执行一次，所以...非常消化性能
                uni.navigateTo({
                	url: '/pages/report/reportMain',
                	"animationType": "slide-in-right",
                	"animationDuration": 500
                });
            }
            this.deltaX = 0;
            // 清除触摸起始点记录，这里也可以写一些比较复杂的逻辑，每滑动一次松后执行。
        },
        saveCommonyUsedMenu() {
            // 创建包含JSON数组的JavaScript对象
            let jsonData = [];

            this.checkbox1.forEach(item => {
                jsonData.push({'userNo': this.userNo, 'menuNo': item});
            });

            if (this.checkbox1.length == 0) {
                jsonData.push({'userNo': this.userNo, 'menuNo': -1});
            }

            // 将JavaScript对象转换为JSON字符串
            let jsonDataString = JSON.stringify(jsonData);

            uni.request({
                url: urlPrefix + "/menu/editCommonyUsed",
                data: jsonDataString,
                method: "POST"
            }).then(res => {
                uni.reLaunch({
                    url: '/pages/menu/menu'
                })
            }).catch(err => {
                console.log(err)
            })
        },
		saveSetting(){
            this.closeDrawer('showSetting');
		},
		loginCheck(){
			//登录校验
			if(uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == ''){
				uni.navigateTo({
					url: `/pages/login/login`,
					animationType: 'pop-in',
					animationDuration: 200
				})
			}
		},
		queryModelSum(){
			// 获取当前日期
			var currentDate = new Date();
			
			// 设置startTime为当天的00:00:00
			var startTime = new Date(currentDate.setHours(0, 0, 0, 0)).getTime();
			
			// 设置endTime为当天的23:59:59
			var endTime = new Date(currentDate.setHours(23, 59, 59, 999)).getTime();
			// 当日面部开版追踪数量
			uni.request({
			    url: urlPrefix + "/devTracking/query",
			    data: {
			        "pageNo": 1,
			        "pageSize": 999,
					"startTime": startTime,
					"endTime": endTime,
					"type": 3
			    },
			    method: "GET"
			}).then(res => {
				this.topModelSum = res.data.data.endRow;
				uni.hideLoading();
			}).catch(err => {
				uni.hideLoading();
			    console.log(err)
			})
			
			// 当日底部开版追踪数量
			uni.request({
			    url: urlPrefix + "/devTracking/query",
			    data: {
			        "pageNo": 1,
			        "pageSize": 999,
					"startTime": startTime,
					"endTime": endTime,
					"type": 4
			    },
			    method: "GET"
			}).then(res => {
				this.bottomModelSum = res.data.data.endRow;
				uni.hideLoading();
			}).catch(err => {
				uni.hideLoading();
			    console.log(err)
			})
		},
		newModelScan(){
			uni.navigateTo({
				url: `/pages/newmodel/newModel`,
				animationType: 'pop-in',
				animationDuration: 200
			})
		},
    getTranslationCount() {
      uni.request({
        url: urlPrefix + '/sop/getTranslationCount',
        method: 'GET'
      }).then(res => {
        if (res.data.code) {
          this.translationCount = res.data.data
        }
      }).catch(err => {
        console.log(err)
      })
    }
    },
	onUnload() {
        let _this =  this
		clearInterval(_this.clock);
		uni.closeSocket({
			success: () => {
				console.info("退出成功")
			},
		})
	},
};
</script>

<style lang="scss" scoped>
#box {
    background-color: #f0f0f0;
    padding-left: 2vw;

}

.popup-content {
    text-align: center;
    align-items: center;
    justify-content: center;
    padding: 50rpx;
    height: 30rpx;
    background-color: #fff;
    font-weight: bold;
}

//从这里开始是弹出框的样式 不需要搜索框的 前面样式都不用加
.arrivalNavigation {
    width: 248rpx;
    position: absolute;
    right: 16rpx;
    z-index: 99;
}

.sideNavigation {
    width: 248rpx;
    background-color: #ffffff;
    color: #000000;
    border-radius: 15rpx;
}

.banner2 {
    border-radius: 1.5vw;
    background-color: #faf5f2;
    //background-image: url(../../static/home_page/stella02.png);
}

.banner image {
    width: 450rpx;
    margin-left: 39%;
    height: 400rpx;
}

.notice {
    display: flex;
    margin-top: -0.5vw;
}

.notice image {
    width: 58rpx;
    height: 58rpx;
    margin-top: 24rpx;
}

.notice text {
    font-size: 2.5vmin;
    font-family: PingFang SC;
    font-weight: 600;
    color: #333333;
    line-height: 110rpx;
    margin-left: 10rpx;
}

.soll {
    display: flex;
    flex-wrap: wrap;
    width: 96vw;
    margin-top: 1.5vw;
}

.soll .soll_view:not(:nth-child(1)) {
    margin-left: 20rpx;
}

.soll_view view:nth-child(1) {
    font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: bold;
    color: #333333;
    line-height: 48rpx;
    margin-top: 20rpx;
}

.soll_view view:nth-child(3) {
    font-size: 32rpx;
    font-family: SourceHanSansCN;
    font-weight: 800;
    color: #02af74;
    line-height: 48rpx;
    margin-top: 10rpx;
}

.soll_view view:nth-child(4) {
    font-size: 24rpx;
    font-family: SourceHanSansCN;
    font-weight: 500;
    color: #02af74;
    line-height: 36rpx;
}

.soll .soll_view_b {
    position: relative;
    width: 31.2%;
    background-color: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(255, 255, 255, 0.2);
    border-radius: 16rpx;
    margin-bottom: 25rpx;
}
.soll > uni-view:nth-child(3n+2){
    margin-left: 3vw;
	margin-right: 3vw;
}


.scroll-view-item_H:nth-child(1)::before {
    content: "";
    display: inline-block;
    width: 20px; /* 图标的宽度 */
    height: 20px; /* 图标的高度 */
    background-image: url('../../static/tabBars/collection.png');
    background-size: cover; /* 图标大小适应 */
    margin-right: 6px; /* 图标与文本之间的间距 */
    vertical-align: middle; /* 调整垂直对齐方式 */
}

.scroll-view-item_H:nth-child(2)::before {
    content: "";
    display: inline-block;
    width: 20px; /* 图标的宽度 */
    height: 20px; /* 图标的高度 */
    background-image: url('../../static/tabBars/plan.png');
    background-size: cover; /* 图标大小适应 */
    margin-right: 6px; /* 图标与文本之间的间距 */
    vertical-align: middle; /* 调整垂直对齐方式 */
}

.scroll-view-item_H:nth-child(3)::before {
    content: "";
    display: inline-block;
    width: 20px; /* 图标的宽度 */
    height: 20px; /* 图标的高度 */
    background-image: url('../../static/tabBars/warehouse.png');
    background-size: cover; /* 图标大小适应 */
    margin-right: 6px; /* 图标与文本之间的间距 */
    vertical-align: middle; /* 调整垂直对齐方式 */
}

.scroll-view-item_H:nth-child(4)::before {
    content: "";
    display: inline-block;
    width: 20px; /* 图标的宽度 */
    height: 20px; /* 图标的高度 */
    background-image: url('../../static/tabBars/costmanage.png');
    background-size: cover; /* 图标大小适应 */
    margin-right: 6px; /* 图标与文本之间的间距 */
    vertical-align: middle; /* 调整垂直对齐方式 */
}

.scroll-view-item_H:nth-child(5)::before {
    content: "";
    display: inline-block;
    width: 20px; /* 图标的宽度 */
    height: 20px; /* 图标的高度 */
    background-image: url('../../static/tabBars/technical-management.png');
    background-size: cover; /* 图标大小适应 */
    margin-right: 6px; /* 图标与文本之间的间距 */
    vertical-align: middle; /* 调整垂直对齐方式 */
}

.scroll-view-item_H:nth-child(6)::before {
    content: "";
    display: inline-block;
    width: 20px; /* 图标的宽度 */
    height: 20px; /* 图标的高度 */
    background-image: url('../../static/tabBars/grow.png');
    background-size: cover; /* 图标大小适应 */
    margin-right: 6px; /* 图标与文本之间的间距 */
    vertical-align: middle; /* 调整垂直对齐方式 */
}

.scroll-view-item_H:nth-child(7)::before {
    content: "";
    display: inline-block;
    width: 20px; /* 图标的宽度 */
    height: 20px; /* 图标的高度 */
    background-image: url('../../static/tabBars/system.png');
    background-size: cover; /* 图标大小适应 */
    margin-right: 6px; /* 图标与文本之间的间距 */
    vertical-align: middle; /* 调整垂直对齐方式 */
}

.scroll-view-item_H:nth-child(1) {
    margin-left: 1.5vw;
}

.soll_view_b:nth-child(2) {

}

.soll_view_b image {
    // position: absolute;
    height: 15vmin;

}

.soll_view_t_text {
    font-size: 3.5vmin;
    font-family: PingFang SC;
    font-weight: bold;
    color: #8c8c8c;
    line-height: 40rpx;
    position: absolute;
    right: 2vmin;
    top: 10vmin;
    z-index: 1;
	display: inline-block;
	padding: 0.25em; /* 添加一些内边距以确保内容不会紧贴边框 */
}

.soll_view_t {
    font-size: 2.5vmin;
    font-family: PingFang SC;
    font-weight: bold;
    color: #333333;
    line-height: 40rpx;
    position: absolute;
    right: 4vmin;
    top: 3vmin;
    z-index: 1;
}

.soll_view_t1 {
    position: absolute;
    right: 20rpx;
    top: 66rpx;
    z-index: 1;
    font-size: 20rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #999999;
}

.list_con {
    width: 96vw;
    height: 210rpx;
    background: #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
    border-radius: 10rpx;
    display: flex;
    position: relative;
}

.list_con > image {
    width: 90rpx;
    height: 144rpx;
    margin-top: 34rpx;
    margin-left: 50rpx;
}

.list_con_text {
    margin-left: 50rpx;
}

.list_con_text image {
    width: 28rpx;
    height: 30rpx;
    position: relative;
    top: 8rpx;
    margin-left: 20rpx;
}

.list_con_text view:nth-child(1) {
    font-size: 32rpx;
    font-family: PingFang SC;
    font-weight: 800;
    color: #333333;
    line-height: 36rpx;
    margin-top: 30rpx;
}

.list_con_text view:nth-child(2) {
    margin-top: 20rpx;
}

.list_con_text view:nth-child(2) > text:nth-child(1) {
    font-size: 24rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #999999;
    line-height: 36rpx;
}

.list_con_text_1 {
    font-size: 32rpx;
    font-family: PingFang SC;
    font-weight: bold;
    color: #c79660;
    line-height: 36rpx;
    margin-left: 6rpx;
    position: relative;
    top: 4rpx;
}

.list_con_text view:nth-child(3) {
    margin-top: 20rpx;
}

.list_con_text view:nth-child(3) > text:nth-child(1) {
    font-size: 24rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #999999;
    line-height: 36rpx;
}

.list_con_text_2 {
    font-size: 30rpx;
    font-family: PingFang SC;
    font-weight: bold;
    color: #333333;
    line-height: 36rpx;
    margin-left: 10rpx;
}

.list_con_right {
    width: 130rpx;
    height: 50rpx;
    background: #1989fa;
    border-radius: 25rpx;
    text-align: center;
    line-height: 50rpx;
    font-size: 28rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #ffffff;
    position: absolute;
    right: 28rpx;
    top: 50%;
    margin-top: -25rpx;
}

.list_con_right image {
    width: 10rpx;
    height: 14rpx;
    position: relative;
    top: -4rpx;
    left: 4rpx;
}

.list_con:not(:nth-child(1)) {
    margin-top: 20rpx;
}

//页签配置
.activite {
    display: inline-block;
    color: #47a3da;
    box-shadow: inset 0 2px #47a3da;
    border-top: 1px solid #47a3da !important;
    border-bottom: 6px solid #f0f0f0 !important;
    border-left: 1px solid #47a3da !important;
    border-right: 1px solid #47a3da !important;
    margin-bottom: -5px;
}

.content {
    /* background: #008000; */
}

.scroll-view_H {
    white-space: nowrap;
    width: 100%;
    color: #000000;
    margin-left: -1.5vw;
}

.scroll-view-item_H {
    display: inline-block;
    width: 10%;
    height: 50rpx;
    line-height: 50rpx;
    text-align: center;
    padding: 10px 0;
    margin-left: 10px;
    border-left: 1px solid #8a8a8a;
    border-right: 1px solid #8a8a8a;
    border-top: 1px solid #8a8a8a;
}

:deep(.uni-scroll-view-content) {
    border-bottom: 1px solid #47a3da;
    padding: 0px 0 0px 0;
}

.uni-list-item {
    display: inline-block;
    background-color: #f0f0f0;
    align-content: center;

}

:deep(.uni-list-item__container) {
    background-color: #f0f0f0;
    padding: 5px 15px;
    padding-top: 7px;
}

#frameTool {
    padding-left: 50px;
    height: 6.5vw;
    background-color: #2d3653;
    color: white;
    display: flex;
    align-items: center;
}

:deep(.setting .uv-list-item__wrapper){
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}

.settingList{
	margin-top: 1.22vw;
}
</style>
