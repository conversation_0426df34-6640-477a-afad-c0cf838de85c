<template>
	<view class="container">
	 <view class="right-top-top">
		<uni-icons @click="back" class="back" type="back" size="36" style="margin-left:1vw;margin-top:10rpx;"></uni-icons>
		<uni-title
		  type="h2"
		  :title="t('已配套待配送')"
		  align="center"
		  style="margin-top:-0.7vw;"
		></uni-title>
	</view>


	  <view style="height: 79%;margin-top:20rpx;">
		<zb-table
			:show-header="true"
			:columns="column"
			:stripe="true"
			:fit="false"
			ref="zbTable"
			show-summary
			@rowClick="rowClick"
			:border="true"
			:cell-style="cellStyle"
			:summary-method="getSummaries"
			@dele="radioClick"
			:data="dataList"></zb-table>
			
			<view class="left-bottom">
				<uni-pagination
				  show-icon="true"
				  :total="pageCount"
				  :current="firstPageNo"
				  :pageSize="firstPageSize"
				  @change="firstChange"
				></uni-pagination>
			</view>
	</view>
	
	<!--单击单行弹出详情-->
	<view class="modelPopup">
	  <uni-popup
	    ref="modelPopup"
	    type="center"
		:mask-click="false"
	  >
	  <view class="modelBox" align="center">
	    <uni-title
	      :title="t('请选择线别配送')"
	      type="h2"
	      align="center"
	    ></uni-title>
		
	
		<view class="uni-padding-wrap" style="margin-top:1.22vw;">
			<view>
				<radio-group>	
				<label class="radio"><radio @click="radioChange('A線DRESS')" value="A線DRESS" :checked="pbLine=='A線DRESS'?true:false"/>{{ t('A线DRESS') }}</label>
				<label class="radio" style="margin-left: 3vw;"><radio @click="radioChange('B線SPORTS')" value="B線SPORTS" :checked="pbLine=='B線SPORTS'?true:false"/>{{ t('B线SPORTS') }}</label>
				</radio-group>
			</view>
		</view>
		
		<view style="display: flex; justify-content: space-evenly;margin-top: 3vh;">
			<button @click="onClean()" type="default" plain="true" style="">{{ t('取消') }}</button>
			<button @click="dele()" type="primary" plain="true" >{{ t('配送') }}</button>
		</view>
	   
		</view>
	  </uni-popup>
	</view>

	<!--单击单行弹出修改界面-->
	<view class="updateSpecificationPopup">
	  <uni-popup
	    ref="updateSpecificationPopup"
	    type="center"
	  >
	    <view class="updateSpecificationBox">
	      <uni-title
	        :title="t('查看详情')"
	        type="h2"
	        align="center"
	      ></uni-title>
	      <view class="updateSpecificationData">
			
			<uni-section :title="t('出货日')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.shp_date }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('型体编号')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.model_no }}
			  </text>
			</uni-section>
				
			<uni-section :title="t('样品单号')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.ord_no }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('样品类型')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.dev_type }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('双数')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.tot_qty }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('鞋面')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.ushelf_no }}
			  </text>
			  <text style="font-size: 1rem; margin-left: 1rem;color:coral">
			     &nbsp;&nbsp;&nbsp;{{  updateSpecificationDetail.u_date}}
			  </text>
			</uni-section>
			
			<uni-section :title="t('大底')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.bshelf_no6 }}
			  </text>
			  <text style="font-size: 1rem; margin-left: 1rem;color:coral">
			     &nbsp;&nbsp;&nbsp;{{  updateSpecificationDetail.b6_date}}
			  </text>
			</uni-section>
			
			<uni-section :title="t('中底')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.bshelf_no2 }}
			  </text>
			  <text style="font-size: 1rem; margin-left: 1rem;color:coral">
			     &nbsp;&nbsp;&nbsp;{{  updateSpecificationDetail.b2_date}}
			  </text>
			</uni-section>
			
			<uni-section :title="t('中底皮')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.bshelf_no1 }}
			  </text>
			  <text style="font-size: 1rem; margin-left: 1rem;color:coral">
			     &nbsp;&nbsp;&nbsp;{{  updateSpecificationDetail.b1_date}}
			  </text>
			</uni-section>
			
			<uni-section :title="t('包粘')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.bshelf_no3}}
			  </text>
			  
			  <text style="font-size: 1rem; margin-left: 1rem;color:coral">
			     &nbsp;&nbsp;&nbsp;{{  updateSpecificationDetail.b3_date}}
			  </text>
			</uni-section> 
					
	      </view>
	    </view>
	  </uni-popup>
	</view>
	
	<view class="uwrap" style="margin-top: 1vh;">
		<text style="margin-right: 1vw;float: right;color: darkgrey;">{{ t('注:红色字-未保存或保存双数小于总数') }}</text>
	</view>
	</view>
</template>

<script setup>
  import { onMounted,ref, reactive } from 'vue'
  import { onPullDownRefresh } from '@dcloudio/uni-app'
  import { useI18n } from 'vue-i18n'
  import urlPrefix from '@/pages/common/urlPrefix.js'
  
  // 国际化
  const { t } = useI18n()
  
	// const urlPrefix = "http://***********:8200";
	//const urlPrefix = "http://**********:8080 http://***********:8200";
	const ypdh=ref()
	const zrbm=ref()
	const ycsm=ref()
	const ycdd=ref()
	const wtsl=ref(0)
	const selectWt=ref()
	const wtsm1=ref([])
	const switchDay=ref(false)
	//第几页
	const firstPageNo=ref(1)
	const firstPageSize=ref(20)
	const pageCount=ref(0)
	
	//表单数据
	const dataList=ref([])
	
	//扫描状态
	const brandCode=ref(0)
	
	//详情弹窗
	const modelData = ref()
	const modelPopup = ref()
	
	const pbLine=ref("A線DRESS")
	//配送取消
	function onClean(){
		modelPopup.value.close();
	}

	const insUs=uni.getStorageSync("loUserNo")
	const insName=uni.getStorageSync("loginUserName")
	const iuser=ref("")
	
	//修改弹窗
	const updateSpecificationPopup = ref()
	const updateSpecificationDetail = reactive({
	  pat_flag: "",
	  model_no: "",
	  shp_date:"",
	  ord_no: "",
	  dev_type: "",
	  tot_qty: "",
	  ushelf_no: "",
	  bshelf_no6: "",
	  bshelf_no2: "",
	  bshelf_no1: "",
	  bshelf_no3: "",
	  b1_date:"",
	  b2_date:"",
	  b3_date:"",
	  b6_date:"",
	  u_date:""
	  
	})
	
	//删除参数
	const deleSpecificationDetail = reactive({
	  ord_no: "",
	  pb_dept: "",
	  pb_addr: "",
	  upd_user: insUs,
	  ins_date: ""
	})
	
	//下拉刷新
	 onPullDownRefresh (()=>{	//通过 onPullDownRefresh  可以监听到下拉刷新的动作
			uni.startPullDownRefresh({
				success(){
					//console.log(urlPrefix)
					getPageCount();
					getData();
					uni.stopPullDownRefresh()	//停止当前页面下拉刷新。
				},fail(){}
			})
	    }) 
	
	//保存data
	const saveData = reactive({
	  "ord_no": "",
	  "pb_qty": "",
	  "pb_desc1":"",
	  "pb_dept": "",
	  "pb_desc": "",
	  "pb_addr": "",
	  "ins_user": insUs
	})
    const stus=ref(0)

	const column=ref([
		{ name: '', type:'operation',label: t('配送'),renders:[
					 {
					   name:'配送',
					   type:'warn',
					   func:'dele'// func 代表子元素点击的事件 父元素接收的事件 父元素 @edit
					 }
		 ]},
          { name: 'shp_date', label: t('出货日'),emptyString:'--',width:93},
		  //{ name: 'pat_flag', label: '配送狀態'},
		  { name: 'ord_no', label: t('样品单号')},
		  { name: 'dev_type', label: t('样品类型'),width:88},
		   { name: 'model_pic', label: t('鞋图'),type:"img",align: "center"},
		  { name: 'model_no', label: t('型体编号'),width:88},
		  { name: 'last_no', label: t('楦头编号'),emptyString:' '},
		  //{ name: 'las_c_flag', label: '楦头状态',emptyString:' '},

		  { name: 'tot_qty', label: t('开单数'),sorter:true,width:65},
          { name: 'pat_qty', label: t('待配送'),emptyString:'/',width:65},
          { name: 'ypat_qty', label: t('已配送'),emptyString:'/',width:65},
          
		  { name: 'ushelf_no', label: t('鞋面'),emptyString:'/',width:60},
		  { name: 'bshelf_no6', label: t('大底'),emptyString:'/',width:60},
		  { name: 'bshelf_no3', label: t('包粘'),emptyString:'/',width:60},
          { name: 'bshelf_no2', label: t('中底'),emptyString:'/',width:60},
		  { name: 'bshelf_no1', label: t('中底皮'),emptyString:'/',width:65},
		  // { name: 'mating_no', label: '编号'}
		  // { name: 'mat_date', label: '配套时间'},
		  // { name: 'pat_date', label: '配送时间'}
		  
        ]);

	const deleCsInfos = reactive({
	  mating_no: "",
	  ord_no : "",
	  pdLine : ""
	})
	
	//表尾合计
	function getSummaries(param){
	  const { columns, data } = param;
	  const sums = [];
	  columns.forEach((column, index) => {
	    if (index === 0) {
	      sums[index] = t('合计');
	      return;
	    }		
	    if(column.name==='tot_qty'||column.name==='pat_qty'||column.name==='ypat_qty'){
	      const values = data.map(item => Number(item[column.name]));
	      if (!values.every(value => isNaN(value))) {
	        sums[index] = values.reduce((prev, curr) => {
	          const value = Number(curr);
	          if (!isNaN(value)) {
	            return prev + curr;
	          } else {
	            return prev;
	          }
	        }, 0);
	        sums[index] += '  ';
	      }
	    }else{
	      sums[index] = ' ';
	    }
	  });
	  return sums;
	}
	
	//判断颜色
	function cellStyle({row, column, rowIndex, columnIndex}){
		
		// if(columnIndex === 4){
		// 	return{
		// 		color:'red'
		// 	}
		// }

		if(row.b1_flag==='N'&&column.name==='bshelf_no1'&&columnIndex === 14){
			return{
				color:'red'
			}
		}
		
		if(row.b2_flag==='N'&&column.name==='bshelf_no2'&&columnIndex === 13){
			return{
				color:'red'
			}
		}
		
		if(row.b3_flag==='N'&&column.name==='bshelf_no3'&&columnIndex === 12){
			return{
				color:'red'
			}
		}
		
		if(row.b6_flag==='N'&&column.name==='bshelf_no6'&&columnIndex === 11){
			return{
				color:'red'
			}
		}
		
		if(row.u_flag==='N'&&column.name==='ushelf_no'&&columnIndex === 10){
			return{
				color:'red'
			}
		}
      }
	
	async function radioClick(e){
		modelPopup.value.open()
		deleCsInfos.mating_no = e.mating_no
		deleCsInfos.ord_no = e.ord_no
		deleCsInfos.pdLine = pbLine.value
	}
	//切换
	function radioChange(line){
		pbLine.value=line
		deleCsInfos.pdLine = line
	}
	//确认配送
	async function dele(){
		//console.log(e.model_pic)
		// uni.showModal({
		// 	title:"提示",
		// 	content:"确定配送资料嘛?",
		// 	success: function (res) {
					uni.request({
						  url: urlPrefix + "/match/updateState",
						  method: "POST",
						  data: deleCsInfos
						}).then(res => {
						  console.log(res.data)
						  if (res.data.code) {
						    uni.showToast({
						    	title: t('配送成功！'),
								duration: 2000
						    });
								  getPageCount();
								  getData();
						  } else {
						    uni.showToast({
						    	title: t('配送失败！'),
						    	icon:"error",
								duration: 2000
						    });
						  }
						}).catch(err => {
						  console.log(err)
						})
						
		// 			}else if (res.cancel) {
		// 				return
		// 			}
			
		// });
		
		modelPopup.value.close();
	
	}
	
	//返回首页
	function back() {
	  uni.navigateBack({
	  	delta: 1,
	  	animationType: 'pop-out',
	  	animationDuration: 200
	  })
	}
			
	async function firstChange(e){
		firstPageNo.value=e.current;
		await uni.request({
			   url: urlPrefix + "/match/getWaitSend",
			   data:{
					"page_no": firstPageNo.value,
					"page_size": firstPageSize.value,
				},
			   method: "POST"
			 }).then(res => {
				//console.log(res.data);
				dataList.value = res.data.data
			 }).catch(err => {
			   console.log(err)
			 })
	}	
	
	//点击详情
	function rowClick(e){
		updateSpecificationDetail.ord_no = e.ord_no
		updateSpecificationDetail.model_no = e.model_no
		updateSpecificationDetail.pat_flag = e.pat_flag
		updateSpecificationDetail.dev_type = e.dev_type
		updateSpecificationDetail.tot_qty = e.tot_qty
		updateSpecificationDetail.shp_date = e.shp_date
		updateSpecificationDetail.ushelf_no = e.ushelf_no
		updateSpecificationDetail.bshelf_no6 = e.bshelf_no6
		updateSpecificationDetail.bshelf_no2 = e.bshelf_no2
		updateSpecificationDetail.bshelf_no1 = e.bshelf_no1
		updateSpecificationDetail.bshelf_no3 = e.bshelf_no3
		updateSpecificationDetail.b1_date = e.b1_date
		updateSpecificationDetail.b2_date = e.b2_date
		updateSpecificationDetail.b3_date = e.b3_date
		updateSpecificationDetail.b6_date = e.b6_date
		updateSpecificationDetail.u_date = e.u_date
		updateSpecificationPopup.value.open()
	}	

	//获取数据
	async function getData(){
		//console.log(iuser.value);
		await uni.request({
			   url: urlPrefix + "/match/getWaitSend",
			   data:{
					"page_no": firstPageNo.value,
					"page_size": firstPageSize.value,
				},
			   method: "POST"
			 }).then(res => {
				 
				dataList.value = res.data.data
				
				for(let i=0;i<dataList.value.length;i++){
					dataList.value[i].model_pic='data:image/png;base64,'+dataList.value[i].model_pic
				}
				
			 }).catch(err => {
			   console.log(err)
			   uni.showToast({
			   	title: t('获取数据失败！'),
				icon:"error"
			   });
			 })
	}
	
	//获取总行数
	async function getPageCount(){
		//console.log(1);
		await uni.request({
			   url: urlPrefix + "/match/getSendCount",
			   method: "POST"
			 }).then(res => {
				//console.log(res.data);
				pageCount.value = res.data.data
				firstPageSize.value = res.data.data
			 }).catch(err => {
			   console.log(err)
			 })
	}
	//预加载
	onMounted(async () => {
	  await getPageCount()
	  await getData()
	  //await search()
	})
	
</script>

<style lang="scss">
	.container2 {
	  margin-top: 2.5vw;
	  display: flex;
	  justify-content: space-between;
		
	}
	.left-btn {
		width: 10vw;
		font-size: 16px;
		height: 4vw;
	  background-color: #409eff;
	  color: #fff;
	  padding: 10rpx 20rpx;
	  border-radius: 5rpx;
	}
	.right-btn {
		width: 10vw;
		font-size: 16px;
		height: 4vw;
	  background-color: #67c23a;
	  color: #fff;
	  padding: 10rpx 20rpx;
	  border-radius: 5rpx;
	}
	page {
	  width: 100%;
	  height: 100%;
	  padding: 2.5% 2% 1.5% 2%;
	  box-sizing: border-box;
	  background-color: white;
	}
	.container {
		width: 100%;
	height: 100%;
	box-sizing: border-box;
	border-radius: 10px;
	box-shadow: 0 0 1px 5px #dddddd;
	}
	
	::v-deep{
	  .uni-card{
	    margin: 8px!important;
	    padding: 0!important;
	    .uni-card__content{
	      padding: 0!important;
	    }
	  }
	}
	.right-top-top{
		display: flex;
	}
.propertyBox{
	margin-top: 2rpx;
	flex-flow: row;
	justify-content: flex-start;
	display: flex;
	position: -webkit-sticky;
	top: var(--window-top);
}
.inpBr{
	width: 15%;
	margin-left:10rpx;
}
	.left-bottom {
	width: 100%;
	height: 10%;
	display: flex;
	justify-content: center;
	align-items: center;

	.uni-pagination__total {
	  margin-right: 0.4375rem !important;
	}

	.uni-pagination__num-tag {
	  min-width: 1.875rem !important;
	  background-color: #F0F0F0 !important;
	}

	.page--active {
	  color: white !important;
	  background-color: deeppink !important;
	}
	}
	
	.modelPopup {
	  .modelBox {
	    width: 30vw;
	    height: 25vh;
	    border-radius: 1vw;
	    background-color: white;
	    
	    .modelData {
	      width: 100%;
	      height: 90%;
	      display: flex;
	      justify-content: center;
	      align-items: flex-start;
	      flex-wrap: wrap;
	      
	      .modelAttribute {
	        width: 30%;
	        margin-left: 3%;
	      }
	    }
	  }
	}
	
	.updateSpecificationPopup {
	  .updateSpecificationBox {
	    width: 75vw;
	    height: 90vh;
	    border-radius: 1vw;
	    background-color: white;
	    
	    .updateSpecificationData {
	      width: 100%;
	      height: 100%;
	      position: relative;
	      display: flex;
	      justify-content: center;
	      align-items: flex-start;
	      flex-wrap: wrap;
	      
	      .updateSpecificationAttribute {
	        width: 35%;
	        margin-left: 15%;
	        
	        .uni-easyinput {
	          width: 70%;
	          margin-left: 1rem;
	        }
	        
	        .uni-stat__select {
	          width: 70%;
	          margin-left: 1rem;
	        }
	        
	        .uni-numbox {
	          margin-left: 1rem;
	        }
	        
	        .uni-file-picker {
	          margin-left: 1rem;
	        }
	      }
	    }
	  }
	}
	
	.submit {
	  display: flex;
	  justify-content: center;
	  align-items: center;
	}

	.back {
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		position: absolute;
		left: 1.5%;
		width: 50px;
		height: 50px;
		top: 5%;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		cursor: pointer;
		z-index: 1;
	}
</style>


