<script setup>
import { ref, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import urlPrefix from '@/pages/common/urlPrefix.js'

// 生产环境
// const urlPrefix = "http://***********:8200"
const user = uni.getStorageSync("loUserNo")

// 开发环境
// const urlPrefix = "http://**********:8080"
// const user = "zqn"

const originalModel = ref('')
const targetModel = ref('')
const inputTargetModel = ref('')

const targetModelListShow = ref(false)
const targetModelList = ref([])
const targetModelOptionList = ref([])

const operationList = ref([
  {
    text: '加工',
    value: '1'
  },
  {
    text: '裁断',
    value: '2'
  },
  {
    text: '准备',
    value: '3'
  },
  {
    text: '针车',
    value: '4'
  },
  {
    text: '半成品',
    value: '5'
  },
  {
    text: '成型',
    value: '6'
  }
])
const selectOperationList = ref([])

const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')

// 返回上一页
function back() {
  uni.navigateBack({
    delta: 1,
    animationType: 'pop-out',
    animationDuration: 200
  })
}

// 获取型体列表
function getModelList() {
  uni.request({
    url: urlPrefix + "/process/getModelList",
    method: "POST"
  }).then(res => {
    targetModelList.value = res.data.data ? res.data.data : [],
    targetModelOptionList.value = targetModelList.value.slice(0, 50)
  }).catch(err => {
    uni.showToast({
      icon: "error",
      title: "请检查网络！"
    })
  })
}

// 复制工序
function copyProcess() {
  if (!targetModel.value || targetModel.value.length === 0) {
    showTip('warn', '请选择目标型体！')
    return
  }
  
  if (!selectOperationList.value || selectOperationList.value.length === 0) {
    showTip('warn', '请选择制程！')
    return
  }
  
  if (originalModel.value === targetModel.value) {
    showTip('warn', '目标型体不能为原始型体！')
    return
  }
  
  uni.request({
    url: urlPrefix + "/process/copyProcess",
    method: "POST",
    data: {
      originalModel: originalModel.value,
      targetModel: targetModel.value,
      operationList: selectOperationList.value,
      user: user
    }
  }).then(res => {
    if (res.data.code) {
      showTip('success', '工序数据复制成功！')
      setTimeout(() => {
        back()
      }, 1000)
    } else {
      showTip('error', '工序数据复制失败！')
    }
  }).catch(err => {
    showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 选择目标型体
function selectTargetModel(param) {
  targetModel.value = param
  inputTargetModel.value = param
}

// 清空目标型体
function clearTargetModel() {
  targetModel.value = ''
  inputTargetModel.value = ''
}

// 提示信息
function showTip(type, message) {
  tipType.value = type
  tipMessage.value = message
  tipPopup.value.open()
}

// 监听目标型体输入框
watch(inputTargetModel, () => {
  targetModelOptionList.value = targetModelList.value.filter(item => item.includes(inputTargetModel.value ? inputTargetModel.value.toUpperCase() : '')).slice(0, 50)
})

onLoad((props) => {
  originalModel.value = props.model
  getModelList()
})
</script>

<template>
  <view class="copy-process">
    <view class="back">
      <uni-icons @click="back()" type="back" size="36" color="#6fa2ce"></uni-icons>
    </view>
    
    <view class="submit">
      <uni-icons @click="copyProcess()" type="checkmarkempty" size="36" color="#45b08c"></uni-icons>
    </view>
    
    <view class="title">
      复制工序
    </view>
    
    <view class="data">
      <uni-section title="原始型体" titleFontSize="20px" type="circle" class="attribute">
        <uni-easyinput :value="originalModel" disabled></uni-easyinput>
      </uni-section>
      
      <uni-section title="目标型体" titleFontSize="20px" type="line" class="attribute" style="margin-left: 0;">
        <view class="targetModel">
          <input
            v-model="inputTargetModel"
            type="text"
            placeholder="请输入目标型体"
            @focus="targetModelListShow = true"
            @blur="targetModelListShow = false"
            class="input"
          />
          
          <view
            v-show="inputTargetModel && inputTargetModel.length > 0"
            @click="clearTargetModel()"
            class="clear"
          >
            <uni-icons type="clear" size="34"></uni-icons>
          </view>
          
          <transition name="list">
            <view v-show="targetModelListShow" class="box">
              <view class="list">
                <view
                  v-for="item in targetModelOptionList"
                  :key="item"
                  @click="selectTargetModel(item)"
                  class="item"
                  :style="{ border: targetModelOptionList.length === 1 ? 'none' : '' }"
                >
                  {{ item }}
                </view>
                
                <view
                  v-show="targetModelOptionList.length === 0"
                  class="item"
                  style="border: none;"
                >
                  暂无该型体
                </view>
              </view>
            </view>
          </transition>
        </view>
      </uni-section>
      
      <uni-section title="制程" titleFontSize="20px" type="line" class="attribute" style="width: 80%;">
        <uni-data-checkbox
          v-model="selectOperationList"
          multiple
          mode="button"
          :localdata="operationList"
        ></uni-data-checkbox>
      </uni-section>
      
      <uni-section title="注意" titleFontSize="20px" type="square" class="attribute" style="width: 80%;">
        <view class="warn">
          复制前请确保目标型体对应的制程中没有工序数据！
        </view>
      </uni-section>
    </view>
  </view>
  
  <view class="tip-popup">
    <uni-popup
      ref="tipPopup"
      type="message"
    >
      <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
    </uni-popup>
  </view>
</template>

<style lang="scss">
page {
  width: 100%;
  height: 100%;
  padding: 2.5% 2% 1.5% 2%;
  box-sizing: border-box;
  background-color: #fdf6e3;
}

.copy-process {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0 0 1px 5px #ddd;
  position: relative;
  overflow: auto;
  
  .back {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 2.5%;
    top: 2.5%;
    border-radius: 50%;
    box-shadow: 0 0 5px gray;
    transition: all 0.05s ease-in-out;
    cursor: pointer;
    z-index: 1;
    
    &:active {
      transform: scale(0.97);
      box-shadow: 0 0 1px gray;
    }
  }
      
  .submit {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 2.5%;
    top: 2.5%;
    border-radius: 50%;
    box-shadow: 0 0 5px gray;
    transition: all 0.05s ease-in-out;
    cursor: pointer;
    z-index: 1;
    
    &:active {
      transform: scale(0.97);
      box-shadow: 0 0 1px gray;
    }
  }
      
  .title {
    margin-bottom: 1%;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    padding: 16px;
  }
      
  .data {
    width: 100%;
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    
    .attribute {
      width: 35%;
      margin-left: 15%;
      margin-bottom: 5%;
      background-color: #fdf6e3;
      
      .uni-easyinput {
        width: 80%;
        margin-left: 16px;
        
        &:deep(.uni-easyinput__content-input) {
          height: 40px;
          color: gray;
          font-size: 20px;
          font-weight: bold;
        }
      }
      
      .uni-data-checklist {
        margin-left: 16px;
        
        &:deep(.checklist-box) {
          margin-top: 0px;
          width: 23%;
        }
        
        &:deep(.checklist-text) {
          padding: 5px;
          font-size: 18px !important;
          font-weight: bold;
        }
      }
      
      &:deep(.square) {
        background-color: red;
      }
      
      .warn {
        margin-left: 16px;
        color: red;
        font-size: 18px !important;
      }
      
      .targetModel {
        width: 80%;
        height: 40px;
        margin-left: 16px;
        position: relative;
        
        .input {
          width: 100%;
          height: 100%;
          padding: 0 10px;
          font-size: 20px;
          font-weight: bold;
          background-color: white;
          border: 1px solid #f0f0f0;
          border-radius: 4px;
          box-sizing: border-box;
          
          &:hover {
            border: 1px solid black;
          }
        }
        
        .clear {
          position: absolute;
          top: 3px;
          right: 3px;
          cursor: pointer;
          z-index: 1;
        }
        
        .box {
          width: 100%;
          height: 270px;
          position: absolute;
          left: 0;
          top: calc(100% + 15px);
          z-index: 1;
          
          .list {
            max-height: 270px;
            text-align: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            background-color: gray;
            border-radius: 5px;
            border-bottom: 1px solid gray;
            overflow: auto;
            
            .item {
              box-sizing: border-box;
              padding: 15px 0;
              border-bottom: 1px solid white;
              cursor: pointer;
              
              &:active {
                background-color: #aaa;
              }
            }
          }
          
          &::before {
            content: "";
            position: absolute;
            left: calc(50% - 10px);
            top: -10px;
            border-top: 0px solid transparent;
            border-left: 10px solid transparent;
            border-bottom: 10px solid gray;
            border-right: 10px solid transparent;
          }
        }
      }
    }
  }
}

.tip-popup {
  &:deep(.fixforpc-width) {
    min-width: 0;
    margin-top: 40px;
    padding: 10px 20px;
  }
  
  &:deep(.uni-popup-message-text) {
    font-size: 18px;
    font-weight: bold;
  }
}

.list-enter-active, .list-leave-active {
  transition: all 0.25s ease-in-out;
}

.list-enter-from, .list-leave-to {
  opacity: 0;
}

.list-enter-to, .list-leave-from {
  opacity: 1;
}
</style>