<!-- 删除弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 提示弹框
const tipPopup = ref()

// 删除弹框
const deleteOverallPopup = ref()

// 选中的工序流程列表
const selectedProcessFlowList = ref([])

// 工序信息
const processInfo = inject('processInfo')
// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getOverallFlow = inject('getOverallFlow')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')

// 显示删除弹框
function showDeleteOverallPopup(param) {
  selectedProcessFlowList.value = param
  deleteOverallPopup.value.open()
}

// 删除工序流程
async function deleteProcessFlow(param) {
  uni.showLoading({
    title: '删除中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/deleteProcessFlow',
    method: 'POST',
    data: {
      processFlowList: param
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getOverallFlow()
      tipPopup.value.showTipPopup('success', '删除成功！')
      deleteOverallPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '删除失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  showDeleteOverallPopup
})
</script>

<template>
  <uni-popup
    ref="deleteOverallPopup"
    type="center"
    :is-mask-click="false"
    class="delete-overall-popup"
  >
    <view class="container">
      <view class="title">
        提示
      </view>
      
      <view class="context">
        你确定要删除选中的工序流程吗？
      </view>
      
      <view class="operate">
        <view @click="deleteOverallPopup.close()" class="cancel button">
          取消
        </view>
        
        <view @click="deleteProcessFlow(selectedProcessFlowList)" class="confirm button">
          确定
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.delete-overall-popup {
  .container {
    padding: 10px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      padding: 5px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      color: red;
      font-size: 22px;
      font-weight: bold;
    }
    
    .context {
      padding: 30px 15px;
      color: black;
      font-size: 20px;
      font-weight: bold;
    }
    
    .operate {
      padding: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .cancel, .confirm {
        width: 145px;
        height: 45px;
        font-size: 20px;
      }
      
      .cancel {
        color: darkred;
      }
      
      .confirm {
        color: lightseagreen;
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
}
</style>