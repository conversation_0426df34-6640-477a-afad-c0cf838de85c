<template>
  <view class="container">
    <!-- 注意这里的绑定，确保使用了正确的插值表达式 -->
    <web-view :src="linkUrl" :scales-page-to-fit="false"></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      linkUrl: '' // 用于存储解码后的链接
    };
  },
  onLoad(options) {
    if (options.url) {
      // 使用 decodeURIComponent 进行解码
      this.linkUrl = decodeURIComponent(options.url);
    }
  }
}
</script>

<style>
.container {
  width: 100%;
  height: 100%;
}
</style>
