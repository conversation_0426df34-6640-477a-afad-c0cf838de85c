<!-- 楦头编号弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 提示弹框
const tipPopup = ref()

// 楦头编号弹框
const lastNosPopup = ref()

// 楦头编号
const lastNos = inject('lastNos')

// 楦头编号列表
const lastNosList = ref([])
// 楦头编号输入框
const lastNosInput = ref('')
// 是否聚焦楦头编号输入框
const focusLastNosInput = ref(false)

// 获取楦头编号列表
async function getLastNosList(param) {
  await uni.request({
    url: urlPrefix + '/sop/getLastNosList',
    method: 'POST',
    data: {
      brand: param
    }
  }).then(res => {
    if (res.data.code) {
      lastNosList.value = res.data.data ? res.data.data : []
    } else {
      lastNosList.value = []
    }
    lastNosList.value.unshift('全部')
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示楦头编号弹框
async function showLastNosPopup(param) {
  lastNosInput.value = ''
  
  if (lastNosList.value.length === 0) {
    uni.showLoading({
      title: '加载中...',
      mask: true
    })
    
    await getLastNosList(param)
    
    uni.hideLoading()
  }
  
  lastNosPopup.value.open()
}

// 选择楦头编号
async function selectLastNos(param) {
  lastNos.value = param
  lastNosPopup.value.close()
}

defineExpose({
  getLastNosList,
  showLastNosPopup
})
</script>

<template>
  <uni-popup
    ref="lastNosPopup"
    type="center"
    class="last-nos-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择楦头编号
      </view>
      
      <view class="last-nos-list">
        <view class="last-nos flex-row-center">
          <input
            v-model="lastNosInput"
            @focus="focusLastNosInput = true"
            @blur="focusLastNosInput = false"
            type="text"
            placeholder="请输入楦头编号"
            class="input"
            :style="{
              boxShadow: focusLastNosInput ? '0 0 5px blue' : '0 0 5px gray'
            }"
          />
          
          <view v-show="lastNosInput" @click="lastNosInput = ''" class="clear button">
            <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
          </view>
        </view>
        
        <view
          v-for="(item, index) in lastNosList.filter(i => i.includes(lastNosInput.toUpperCase())).splice(0, 50)"
          :key="index"
          class="last-nos flex-row-center"
        >
          <view
            @click="selectLastNos(item)"
            class="button"
            :style="{
              color: lastNos === item ? 'red' : 'black'
            }"
          >
            {{ item ? item : '/' }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.last-nos-popup {
  .container {
    width: 430px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .last-nos-list {
      height: 350px;
      overflow: auto;
      transition: all 0.25s ease;
      
      .last-nos {
        width: 100%;
        padding: 10px;
        position: relative;
        
        .button {
          width: 270px;
          min-height: 50px;
          color: darkmagenta;
          font-size: 20px;
          padding: 5px;
        }
        
        .input {
          width: 270px;
          height: 50px;
          padding: 0 10px;
        }
        
        .clear {
          width: 60px;
          min-height: 40px;
          position: absolute;
          top: 15px;
          right: 85px;
        }
        
        &:first-child {
          position: sticky;
          top: 0;
          background-color: #fdf6e3;
          z-index: 1;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>