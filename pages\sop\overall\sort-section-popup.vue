<!-- 排序加工段弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 排序加工段弹框
const sortSectionPopup = ref()

// 工序信息
const processInfo = inject('processInfo')
// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getOverallFlow = inject('getOverallFlow')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')

// 显示排序加工段弹框
function showSortSectionPopup() {
  sortSectionPopup.value.open()
}

// 排序加工段
async function sortSection(param) {
  uni.showLoading({
    title: '排序中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/sortSection',
    method: 'POST',
    data: {
      model: processInfo.value.model,
      operation: processInfo.value.operation,
      rtgCode: processInfo.value.rtgCode,
      startWkGroup: param,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getOverallFlow()
      tipPopup.value.showTipPopup('success', '排序成功！')
      sortSectionPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '排序失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  showSortSectionPopup
})
</script>

<template>
  <uni-popup
    ref="sortSectionPopup"
    type="center"
    class="sort-section-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择排序起始加工段
      </view>
      
      <scroll-view :scroll-y="true" class="sort-section-list">
        <view
          v-for="(item, index) in processFlowList.length"
          :key="index"
          class="sort-section flex-row-center"
        >
          <view
            @click="sortSection(item.toString().padStart(3, '0'))"
            class="button"
          >
            {{ item.toString().padStart(3, '0') }}
          </view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.sort-section-popup {
  .container {
    width: 430px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 70px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .sort-section-list {
      min-height: 70px;
      max-height: 350px;
      overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .sort-section {
        width: 100%;
        padding: 10px;
        
        .button {
          width: 270px;
          height: 50px;
          color: darkgreen;
          font-size: 20px;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>