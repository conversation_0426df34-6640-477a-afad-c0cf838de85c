<!-- 排序加工段弹框 -->
<script setup>
import { ref, inject } from 'vue'
import { operationMap } from '@/pages/sop/common/commonMap'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 传递消息
const emit = defineEmits(['change-flow-index'])

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 排序加工段弹框
const sortSectionPopup = ref()

// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getProcessFlowList = inject('getProcessFlowList')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')
// 流程下标
const flowIndex = inject('flowIndex')
// 是否自动切换流程下标
const isAutoChangeIndex = inject('isAutoChangeIndex')

// 型体
const model = ref('')
// 制程
const operation = ref('')
// 主要代码
const rtgCode = ref('')

// 工序流程数
const flowNumber = ref(0)

// 显示排序加工段弹框
function showSortSectionPopup(param1, param2, param3, param4) {
  model.value = param1
  operation.value = param2
  rtgCode.value = param3
  flowNumber.value = param4
  sortSectionPopup.value.open()
}

// 排序加工段
async function sortSection(param1, param2, param3, param4) {
  uni.showLoading({
    title: '排序中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/sortSection',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3,
      startWkGroup: param4,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getProcessFlowList(param1, param2, param3)
      emit('change-flow-index')
      tipPopup.value.showTipPopup('success', '排序成功！')
      sortSectionPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '排序失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  showSortSectionPopup
})
</script>

<template>
  <uni-popup
    ref="sortSectionPopup"
    type="center"
    class="sort-section-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择排序起始加工段
      </view>
      
      <view class="description flex-row-center">
        {{ operationMap.get(operation) ? operationMap.get(operation) : '未知' }} - {{ model }} - {{ rtgCode }}
      </view>
      
      <scroll-view :scroll-y="true" class="sort-section-list">
        <view
          v-for="(item, index) in flowNumber"
          :key="index"
          class="sort-section flex-row-center"
        >
          <view
            @click="sortSection(model, operation, rtgCode, item.toString().padStart(3, '0'))"
            class="button"
          >
            {{ item.toString().padStart(3, '0') }}
          </view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.sort-section-popup {
  .container {
    width: 430px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .description {
      width: 100%;
      height: 40px;
      font-size: 20px;
      font-weight: bold;
    }
    
    .sort-section-list {
      min-height: 70px;
      max-height: 350px;
      overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .sort-section {
        width: 100%;
        padding: 10px;
        
        .button {
          width: 270px;
          height: 50px;
          color: darkgreen;
          font-size: 20px;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>