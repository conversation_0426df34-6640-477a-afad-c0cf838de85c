<template>
	<view class="container">

		<view class="back">
			<uni-icons @click="back" type="back" size="36"></uni-icons>
		</view>

		<view style="width: 100%; text-align: center; font-size: 24px;">
			<text>FIT樣品進度(面部)</text>
		</view>

		<view class="top" style="margin-top: 15px">
			<view style="margin-left: 20px; display: flex; align-items: center;">
				<text style="margin-right: 5px;">工厂</text>
				<uni-data-select style="margin-left: 5px;width: 80px" :localdata="factoryList" v-model="factory"
					@change="selectFactoryChange"></uni-data-select>
				<!-- <input placeholder="请输入工厂" v-model="factory"
	           style="border-radius: 5px; border: 1px solid #ccc; padding: 5px; margin-right: 10px;width: 10vw;"/>-->
				<text style="margin-right: 5px;margin-left:15px">品牌</text>
				<!--	<input placeholder="请输入品牌" v-model="brandNo"
	       style="border-radius: 5px; border: 1px solid #ccc; padding: 5px; margin-right: 10px; width: 10vw;"/>-->
				<view class="inputDate">
					<uni-data-select style="width: 100px;margin-left: 5px" v-model="brandNo" :localdata="brandList"
						@change="bindBrandChange"></uni-data-select>
				</view>



				<text style="margin-right: 5px;margin-left: 15px">起始日期</text>
				<!--	<uv-datetime-picker ref="datetimePickerSt" v-model="startTime" mode="date" @confirm="confirmSt"></uv-datetime-picker>
                      <view class="inputDate" style="width: 10vw;">
                          <uv-input disabled="true"  @click="openSt" v-model="startTimeText"></uv-input>
                      </view>-->
				<picker mode="date" :value="startTime" @change="bindStartDateChange">
					<view style="padding: 8px;background-color: white;width: 90px" v-if="startTime == ''"
						class="uni-input">请选择</view>
					<view style="padding: 8px;background-color: white;width: 90px" v-if="startTime != ''"
						class="uni-input">{{startTimeText}}</view>
				</picker>




				<text style="margin-right: 5px;margin-left: 10px;">截止日期</text>
				<!--<uv-datetime-picker ref="datetimePickerEnd" v-model="endTime" mode="date" @confirm="confirmEnd"></uv-datetime-picker>
                    <view class="inputDate" style="width: 10vw;">
                        <uv-input disabled="true"  @click="openEnd" v-model="endTimeText"></uv-input>
                    </view>-->
				<picker mode="date" :value="endTime" @change="bindEndDateChange" style="margin-left: 10px">
					<view style="padding: 8px;background-color: white; width: 90px;" v-if="endTime == ''"
						class="uni-input">请选择</view>
					<view style="padding: 8px;background-color: white;width: 90px" v-if="endTime != ''"
						class="uni-input">{{endTimeText}}</view>
				</picker>


				<view class="inpBr" style="width: 8vw;margin-left: 15px">
					<uni-data-select :clear="false" :localdata="wtsm1" v-model="selectWt1"
						@change="selectChange1"></uni-data-select>
				</view>

				<view class="inpBr" style="width: 8vw;">
					<uni-data-select :clear="false" :localdata="wtsm2" v-model="selectWt2"
						@change="selectChange2"></uni-data-select>
				</view>
				<button type="success" @click="queryData()" class="addButton"
					style="background-color: #18bc37; color: white; font-weight: bold; margin-left: 15px;">
					查询
				</button>


			</view>

			// #ifdef H5
			<view style="  display: flex;">

				<strong>
					<button type="success" @click="exportData()" class="addButton"
						style="background-color: #e43d33; color: white; font-weight: bold; margin-left: 10px;">
						导出
					</button>
				</strong>
			</view>

			// #endif

		</view>




		<view class="table-container">
			<table>
				<!-- 表头行 -->
				<tr style="background-color: #fdf6e3;">




					<th style="width: 5vw;" align="center">序號</th>
					<th style="width: 5vw;" align="center">品牌</th>
					<th style="width: 5vw;" align="center">原樣交<br>接日期</th>
					<th style="width: 5vw;" align="center">接單日</th>
					<th style="width: 5vw;" align="center">生產<br>工廠</th>
					<!-- 					<th style="width: 5vw;" align="center">訂單<br>數量</th> -->

					<th style="width: 5vw;" align="center">
						<div style="height: 100%;">
							<div style="height: 50%;  display: flex;  align-items: center; justify-content: center;">
								量產訂單</div>
							<div style="height: 1px; background-color: #ddd; margin: 0 0;"></div>
							<div style="height: 50%;  display: flex; align-items: center; justify-content: center;">样品訂單
							</div>
						</div>
					</th>


					<th style="width: 5vw;" align="center">業務</th>
					<th style="width: 5vw;" align="center">上線日</th>
					<th style="width: 5vw;" align="center">樣品單號</th>
					<th style="width: 5vw;" align="center">鞋圖</th>
					<th style="width: 5vw;" align="center">型體</th>
					<th style="width: 5vw;" align="center">楦頭</th>
					<th style="width: 5vw;" align="center">底料配套</th>


					<th style="width: 5vw;" align="center">面投入</th>
					<th style="width: 5vw;" align="center">收面</th>
					<th style="width: 5vw;" align="center">中底</th>
					<th style="width: 5vw;" align="center">中底皮</th>
					<th style="width: 5vw;" align="center">大底</th>
					<th style="width: 5vw;" align="center">包粘</th>
					<th style="width: 5vw;" align="center">成型投入</th>


					<th style="width: 5vw;" align="center">樣品要<br>求完成<br>日</th>
					<th style="width: 5vw;" align="center">備註</th>

				</tr>
				<tr v-for="(item, index) in dataList" :key="item" class="datalist-tr">



					<td align="center">{{ item.rownum }}</td>
					<td align="center">{{ item.brand_no }}</td>
					<td align="center">{{ item.tran_date }}</td>
					<td align="center">{{ item.get_date }}</td>
					<td align="center">{{ item.factory }}</td>

					<td align="center">
						<div style="height: 100%;">
							<div style="height: 50%;  display: flex; align-items: center; justify-content: center;">
								{{ item.ord_qty }}
							</div>
							<div style="height: 1px; background-color: #ddd; margin: 0 0;"></div>
							<div style="height: 50%;  display: flex; align-items: center; justify-content: center;">
								{{ item.tot_qty }}
							</div>
						</div>
					</td>
					<!-- <td align="center" valign="top" >
						<table  width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
						    <tr>
						      <td>{{ item.ord_qty }}</td>
						    </tr>
						    <tr>
						      <td>{{ item.tot_qty }}</td>
						    </tr>
						  </table>
					</td>
			 -->


					<!-- <td align="center">{{ item.dutyer.replace(/(\d+)/, '<br>$1')}}</td> -->
					<td align="center" v-html="item.dutyer.replace(/(\d+)/, '<br>$1')"></td>

					<td align="center">{{ item.online_date }}</td>
					<td align="center">{{ item.ord_no }}</td>
					<td align="center">
						<image :src="item.pic_base64" @click="previewImage(item.pic_base64)" class="img"
							mode="aspectFit"></image>
					</td>
					<td align="center">{{ item.model_no }}</td>


					<!-- <td align="center" style="min-width: 10vw; max-width: 10vw; word-wrap: break-word; word-break: break-all; white-space: normal;">{{ item.model_name }}</td> -->
					<td align="center">{{ item.last_no }}</td>

					<!-- <td align="center">{{ item.t1_flag.toUpperCase() === 'Y' ? 'ok' : '' }}</td> -->
					<!-- <td align="center">{{ item.t2_date }}</td> -->
					<td align="center">{{ item.t3_flag.toUpperCase() === 'Y' ? 'ok' : '' }}</td>
					<!-- <td align="center">{{ item.y_flag.toUpperCase() === 'Y' ? 'ok' : '' }}</td> -->

					<td align="center">{{item.u1_qty}}</td>
					<td align="center">{{item.t5_qty}}</td>
					<td align="center">{{item.b2_qty}}</td>
					<td align="center">{{item.b1_qty}}</td>
					<td align="center">{{item.b4_qty}}</td>
					<td align="center">{{item.b3_qty}}</td>
					<td align="center">{{item.p1_qty}}</td>




					<td align="center">{{item.shp_date}}</td>
					<!-- <td align="center">{{item.dev_type}}</td> -->
					<td align="center"
						style="min-width: 20vw; max-width: 20vw; word-wrap: break-word; word-break: break-all; white-space: normal;">
						{{item.upp_desc}}
					</td>
					<!-- 					
					<td align="center">{{ item.u_date }}</td>
					<td align="center">{{ item.b_date }}</td>
					<td align="center">{{item.shp_date}}</td>
					<td align="center">{{ item.dev_type }}</td>	
				
					<td align="center">{{ item.upp_desc }}</td>
					<td align="center">{{ item.sol_desc }}</td>
					<td align="center">{{ item.cfm_flag }}</td>
					<td align="center">{{item.cl_flag}}</td> -->


				</tr>
			</table>
		</view>

		<view class="picture-popup">
			<uni-popup ref="picturePopup" type="center">
				<view class="watermark">
					<img :src="preViewPicture" alt="" class="img">
				</view>
			</uni-popup>
		</view>

		<!-- 		<view class="left-bottom">
			<uni-pagination show-icon="true" :total="pageCount" :current="firstPageNo" :pageSize="firstPageSize"
				@change="firstChange"></uni-pagination>
		</view> -->


	</view>

</template>

<script setup>
	import {
		onMounted,
		ref,
		reactive,
		watch
	} from 'vue'
	import {
		onPullDownRefresh
	} from '@dcloudio/uni-app'
	import {
		onShow
	} from '@dcloudio/uni-app';
	import urlPrefix from '@/pages/common/urlPrefix.js'

	//第几页
	const firstPageNo = ref(1)
	const firstPageSize = ref(10)

	// 测试数据
	const wtsm1 = ref([{
			value: "N",
			text: '未确认'
		},
		{
			value: "Y",
			text: '已确认'
		}
		// 更多选项...
	]);


	// 测试数据
	const wtsm2 = ref([{
			value: "N",
			text: '未結案'
		},
		{
			value: "Y",
			text: '已結案'
		}
		// 更多选项...
	]);

	const factoryList = ref([
		/*  {text:'ALL',value:''},*/
		{
			text: 'SR',
			value: 'SR'
		},
		{
			text: 'GX',
			value: 'GX'
		},
		{
			text: 'VN',
			value: 'VN'
		},
		{
			text: 'GN',
			value: 'GN'
		},
		{
			text: 'DD',
			value: 'DD'
		},
		{
			text: 'CS',
			value: 'CS'
		},
		{
			text: 'DS',
			value: 'DS'
		},
		{
			text: 'YN',
			value: 'YN'
		},
		{
			text: 'DK',
			value: 'DK'
		},
		{
			text: 'DE',
			value: 'DE'
		},
		{
			text: 'DR',
			value: 'DR'
		},
		{
			text: 'CV',
			value: 'CV'
		},
		{
			text: 'FS',
			value: 'FS'
		},
		{
			text: 'BG',
			value: 'BG'
		},
		{
			text: 'SL',
			value: 'SL'
		},
		{
			text: 'TP',
			value: 'TP'
		}
	])


	// 当前选中的值
	const selectWt1 = ref('N');

	// 选项改变时的处理函数
	const selectChange1 = (e) => {
		// 更新当前选中的值
		selectWt1.value = e;
		// console.log('当前选择的值为：', selectWt1.value);
	};

	// 选项改变时的处理函数
	const selectFactoryChange = (e) => {
		// 更新当前选中的值
		factory.value = e;
		// console.log('当前选择的值为：', selectWt1.value);
	};

	// 当前选中的值
	const selectWt2 = ref('N');

	// 选项改变时的处理函数
	const selectChange2 = (e) => {

		// 更新当前选中的值
		selectWt2.value = e;
		// console.log('当前选择的值为：', selectWt2.value);
	};



	// 定义两个数据属性
	const factory = ref(''); // 共产的值
	const brandNo = ref(''); // 型体的值



	// // #ifdef H5
	// firstPageSize.value = 12; // 网页版设置为10
	// // #endif

	// // #ifdef APP-PLUS
	// firstPageSize.value = 9; // App版设置为9
	// // #endif


	const pageCount = ref(0)

	//表单数据
	const searchModel = ref([])
	const shoeLastModelList = ref([])
	const dataList = ref([])
	const searchShoeLastModel = ref([])
	const shoeLastModelListShow = ref(false)
	const searchInput = ref()
	const modelList = ref([])

	// //消息提示
	// const tipPopup = ref()
	// const tipType = ref('')
	// const tipMessage = ref('')

	//扫描状态

	//详情弹窗
	const modelData = ref()
	const modelPopup = ref()
	const selectWt = ref()

	//参数
	const model_no = ref('')
	const itemNo = ref('')
	const id = ref('')
	const shoe_last = ref('')
	const brand = ref('AB')
	const brandList = ref([])

	//下拉选择显示
	const modelListShow = ref(false)
	const searchType = ref(true)
	const brandPopup = ref()

	const selectIndex = ref([]);

	const preViewPicture = ref()

	const picturePopup = ref();

	//日期选择开始
	const date = new Date();
	date.setHours(date.getHours() - 24);
	const startTime = ref(date.getTime())
	const datetimePickerSt = ref()
	const startTimeText = ref('')



	function previewImage(url) {
		preViewPicture.value = url;
		picturePopup.value.open();
	}

	function bindStartDateChange(e) {
		startTime.value = new Date(e.detail.value).getTime();
		startTimeText.value = e.detail.value;
	}

	function bindEndDateChange(e) {
		endTime.value = new Date(e.detail.value).getTime();
		endTimeText.value = e.detail.value;
	}


	function openSt() {
		datetimePickerSt.value.open();
	}

	function confirmSt(e) {
		console.log('confirm', e);
		// 创建一个新的日期对象e
		var date = new Date(e.value);

		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
		startTimeText.value = formattedDate;
	}





	//日期选择结束
	const date2 = new Date();
	date2.setHours(date2.getHours() - 24);
	const endTime = ref(date2.getTime())
	const datetimePickerEnd = ref()
	const endTimeText = ref()


	function bindBrandChange(e) {
		console.log(e)
		brandNo.value = e
	}
	// 获取品牌列表
	function getBrands() {
		uni.request({
			url: urlPrefix + "/first/getBrandsPlus",
			method: "POST"
		}).then(res => {
			let arr = []
			for (const item of res.data.data) {
				item.data.forEach(i => {
					arr.push({
						value: i,
						text: i
					})
				})
			}
			brandList.value = arr;
		}).catch(err => {
			console.log(err)
		})
	}



	function openEnd() {
		datetimePickerEnd.value.open();
	}


	function confirmEnd(e) {
		console.log('confirm', e);
		// 创建一个新的日期对象e
		var date = new Date(e.value);

		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
		endTimeText.value = formattedDate;
	}




	function selectItemStep(selectedItem) {
		let isAnyChecked = false;
		dataList.value.forEach(item => {
			if (item.id === selectedItem.id) {
				if (insName.value == item.create_by) {
					item.isChecked = !item.isChecked;
					if (item.isChecked) {
						selectIndex.value = [item.id];
						isAnyChecked = true;
					} else {
						selectIndex.value = [];
					}
				}
			} else {
				item.isChecked = false;
			}
		});

		if (!isAnyChecked) {
			selectIndex.value = [];
		}
	}



	function exportData() {

		// 检查所有参数是否为空
		if (!factory.value && !brandNo.value && !startTimeText.value && !endTimeText.value && !selectWt1.value && !
			selectWt2.value) {
			// console.log('所有参数都为空，不执行导出操作。');
			uni.showToast({
				title: '添加导出参数',
				icon: "error"
			});
			return;
		}


		uni.showLoading({
			title: '下载中',
			mask: true // 设置遮罩层
		});
		// 使用uni.request发送GET请求
		uni.request({
			url: urlPrefix + "/api/matting/exportNew",
			method: 'GET',
			data: {
				pageNo: firstPageNo.value,
				pageSize: firstPageSize.value,
				factory: factory.value,
				brandNo: brandNo.value,
				startTime: startTimeText.value,
				endTime: endTimeText.value,
				cfmFlag: selectWt1.value,
				clFlag: selectWt2.value
			},
			responseType: 'arraybuffer',
			timeout: 600000,
			success: (res) => {
				if (res.statusCode === 200) {

					// 创建Blob对象
					const blob = new Blob([res.data], {
						type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
					});

					// 创建一个下载链接
					const downloadElement = document.createElement('a');
					const href = window.URL.createObjectURL(blob); // 创建下载的链接
					downloadElement.href = href;
					downloadElement.download = 'FIT一览表.xlsx'; // 下载后文件名
					document.body.appendChild(downloadElement);
					downloadElement.click(); // 点击下载
					document.body.removeChild(downloadElement); // 下载完成移除元素
					window.URL.revokeObjectURL(href); // 释放掉blob对象
					uni.hideLoading();
				} else {
					// 处理错误情况
					console.error('导出Excel失败:', res);
					uni.hideLoading();
				}
			},
			fail: (error) => {
				// 处理请求失败
				console.error('请求失败:', error);
				uni.hideLoading();
			}
		});
	}




	// 提示信息
	function showTip(type, message) {
		tipType.value = type
		tipMessage.value = message
		tipPopup.value.open()
	}

	//下拉刷新
	onPullDownRefresh(() => { //通过 onPullDownRefresh  可以监听到下拉刷新的动作
		uni.startPullDownRefresh({
			success() {
				//console.log(urlPrefix)
				getPageCount()
				getData()
				uni.stopPullDownRefresh() //停止当前页面下拉刷新。
			},
			fail() {}
		})
	})



	function initAddPageParam() {
		inputCount.value = [1];
		inputCountNum.value = 1;
	}

	function search(model) {
		model_no.value = model;
		getData(model);
	}

	// 监视输入框中的 model_no，更新搜索提示列表
	watch(model_no, () => {
		searchModel.value = modelList.value.filter(item => item.model_no.includes(model_no.value.toUpperCase()))
			.slice(
				0, 50)
	})





	//返回首页
	function back() {
		let back = getCurrentPages();
		console.log('back.length：' + back.length)
		if (back && back.length > 1) {
			uni.navigateBack({
				delta: 1
			});
		} else {
			history.back();
		}
	}

	async function firstChange(e) {

		uni.showLoading({
			title: '加载中',
			mask: true // 设置遮罩层
		});

		firstPageNo.value = e.current;
		await uni.request({
			url: urlPrefix + "/api/matting/query",
			data: {
				"pageNo": firstPageNo.value,
				"pageSize": firstPageSize.value,
				"factory": factory.value,
				"brandNo": brandNo.value,
				"startTime": startTimeText.value,
				"endTime": endTimeText.value,
				"cfmFlag": selectWt1.value,
				"clFlag": selectWt2.value
			},
			method: "GET"
		}).then(res => {
			//console.log(res.data);
			dataList.value = res.data.data.list
			uni.hideLoading();
		}).catch(err => {
			console.log(err)
			uni.hideLoading();
		})
	}


	//获取数据
	async function queryData() {
		// firstPageNo.value = 1
		// firstPageSize.value = 10

		uni.showLoading({
			title: '加载中',
			mask: true // 设置遮罩层
		});

		if (tableRef.value) {
			tableRef.value.clearSelection();
		}
		await uni.request({
			url: urlPrefix + "/api/matting/query",
			data: {
				// "pageNo": firstPageNo.value,
				// "pageSize": firstPageSize.value,
				"factory": factory.value,
				"brandNo": brandNo.value,
				"startTime": startTimeText.value,
				"endTime": endTimeText.value,
				"cfmFlag": selectWt1.value,
				"clFlag": selectWt2.value
			},
			method: "GET"
		}).then(res => {
			dataList.value = res.data.data;
			// pageCount.value = res.data.data.total;
			// shoe_last.value = res.data.data.total;
			// if (res.data.data.list.length > 0) {
			// 	shoe_last.value = res.data.data.list[0].shoe_last;
			// }
			// itemNo.value = 0;
			uni.hideLoading();
		}).catch(err => {
			console.log(err)
			uni.hideLoading();
			uni.showToast({
				title: '获取数据失败..',
				icon: "error"
			});
		})
	}



	const tableRef = ref();


	//预加载
	onMounted(async () => {

		if (1 == 1) {
			// 创建一个新的日期对象e
			var date = new Date();

			// 获取年、月、日
			var year = date.getFullYear();
			var month = date.getMonth() + 1; // 月份从0开始，需要加1
			var day = date.getDate();

			// 格式化日期为yyyy/MM/DD的样式
			var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day :
				day);

			endTimeText.value = formattedDate;
		}
		if (1 == 1) {
			// 创建一个新的日期对象e
			var date = new Date();

			// 获取当前日期的天数
			var temp = date.getDate();

			// 将日期减去一天
			date.setDate(temp - 1);
			// 获取年、月、日
			var year = date.getFullYear();
			var month = date.getMonth() + 1; // 月份从0开始，需要加1
			var day = date.getDate();

			var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day :
				day);
			startTimeText.value = formattedDate;
		}


		await queryData('')
		getBrands()
	})
</script>

<style lang="scss">
	.img {
		max-width: 50px;
		max-height: 50px;
		/* 	height: 50px;*/
		border-radius: 10px;
		box-shadow: 0 0 5px gray;
		transition: all 0.05s ease-in-out;
		cursor: pointer;

		&:active {
			box-shadow: 0 0 1px gray;
			transform: scale(0.97);
		}
	}

	.back {
		width: 50px;
		height: 50px;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		transition: all 0.05s ease-in-out;
	}

	page {
		width: 100%;
		height: 100%;
		padding: 2.5% 2% 1.5% 2%;
		box-sizing: border-box;
		background-color: #fdf6e3;
	}

	.back {
		width: 50px;
		height: 50px;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		position: absolute;
		left: 1.5%;
		top: 3%;
		cursor: pointer;
		z-index: 1;
	}

	.top {
		display: flex;
		justify-content: space-between;
		/* 使得子元素分布在两端 */
		align-items: center;
		/* 使得子元素垂直居中 */
		padding: 10px;
		/* 添加一些内边距 */
	}



	.addButton {
		/* 由于已经在内联样式中设置了背景颜色和字体颜色，这里可以省略这些属性 */
		// padding: 5px 12px; /* 添加一些内边距 */
		border: none;
		/* 移除边框 */
		border-radius: 5px;
		/* 添加圆角边框 */
		cursor: pointer;
		/* 鼠标悬停时显示手形光标 */
	}

	.container {
		width: 100%;
		height: 100%;
		padding: 1%;
		box-sizing: border-box;
		border-radius: 10px;
		box-shadow: 0 0 1px 5px #dddddd;
		position: relative;
	}

	.right-top-top {
		display: flex;
	}

	.inpBr {
		width: 15%;
		margin-left: 10rpx;
	}

	.left-bottom {
		width: 100%;
		height: 10%;
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
	}

	.uni-pagination__total {
		margin-right: 0.4375rem !important;
	}

	.uni-pagination__num-tag {
		min-width: 1.875rem !important;
		background-color: #F0F0F0 !important;
	}

	.page--active {
		color: white !important;
		background-color: deeppink !important;
	}


	.search {
		width: 100%;
		min-height: 10%;
		margin-bottom: 1%;
		box-sizing: border-box;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		position: relative;

		.searchSelect {
			width: 100%;
			margin-left: 10%;
			margin-right: 0.5%;
			font-size: 24px;
			font-weight: bold;
			padding: 10px;
			border-radius: 10px;
		}

		.search-brand {
			width: 10%;
			margin-left: 0.5%;
			margin-right: 0.5%;
			background: linear-gradient(to right bottom, orangered, pink);
			color: white;
			font-size: 24px;
			font-weight: bold;
			border-radius: 10px;
		}

		.search-type {
			width: 8%;
			margin-left: 0.5%;
			margin-right: 1%;
			background-color: #333;
			color: white;
			font-size: 24px;
			font-weight: bold;
			border-radius: 10px;
		}

		.search-input {
			width: 40%;
			padding: 10px;
			border: 2px solid gray;
			font-size: 24px;
			color: black;
			background-color: white;
			border-radius: 5px;

			&:hover {
				border: 2px solid black;
			}
		}

		.search-input-shoe-last {
			width: 35%;
			padding: 10px;
			border: 2px solid gray;
			font-size: 24px;
			color: black;
			background-color: white;
			border-radius: 5px;

			&:hover {
				border: 2px solid black;
			}
		}

		.search-clear {
			margin-left: -45px;
			margin-top: 2px;
			cursor: pointer;
			z-index: 1;
		}

		.search-clear-shoe-last {
			margin-left: -45px;
			margin-top: 2px;
			cursor: pointer;
			z-index: 1;
		}

		.search-icon-shoe-last {
			width: 5%;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			z-index: 1;
		}

		.search-list {
			width: calc(40% + 20px + 4px);
			height: 270px;
			position: absolute;
			left: 30%;
			top: 120%;
			z-index: 1;

			.search-box {
				max-height: 270px;
				text-align: center;
				border-radius: 5px;
				color: white;
				font-size: 18px;
				font-weight: bold;
				background-color: gray;
				overflow: auto;

				.search-item {
					box-sizing: border-box;
					padding: 15px 0;
					border-bottom: 1px solid white;
					cursor: pointer;

					&:hover {
						background-color: #aaaaaa;
					}
				}
			}

			&::before {
				content: "";
				position: absolute;
				left: calc(50% - 10px);
				top: -10px;
				border-top: 0px solid transparent;
				border-left: 10px solid transparent;
				border-bottom: 10px solid gray;
				border-right: 10px solid transparent;
			}
		}

		.search-list-shoe-last {
			width: calc(35% + 20px + 4px);
			height: 270px;
			position: absolute;
			left: 30%;
			top: 120%;
			z-index: 1;

			.search-box-shoe-last {
				max-height: 270px;
				text-align: center;
				border-radius: 5px;
				color: white;
				font-size: 18px;
				font-weight: bold;
				background-color: gray;
				overflow: auto;

				.search-item-shoe-last {
					box-sizing: border-box;
					padding: 15px 0;
					border-bottom: 1px solid white;
					cursor: pointer;

					&:hover {
						background-color: #aaaaaa;
					}
				}
			}

			&::before {
				content: "";
				position: absolute;
				left: calc(50% - 10px);
				top: -10px;
				border-top: 0px solid transparent;
				border-left: 10px solid transparent;
				border-bottom: 10px solid gray;
				border-right: 10px solid transparent;
			}
		}
	}


	.brand-popup {
		.brand-box {
			width: 80vw;
			height: 80vh;
			border-radius: 1vw;
			background-color: #fdf6e3;
			overflow: auto;

			.brand-part {
				width: 100%;
				margin-bottom: 1%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.brand-initial {
					width: 100%;
					margin-left: 1.5%;
					margin-top: 1%;
					font-size: 32px;
					font-weight: bold;
				}

				.brand {
					width: 18%;
					height: 8vh;
					margin: 1%;
					display: flex;
					justify-content: center;
					align-items: center;
					box-sizing: border-box;
					border-radius: 1vw;
					cursor: pointer;
					font-size: 24px;
					font-weight: bold;
					color: white;
					background: linear-gradient(to right bottom, pink, blue);

					&:hover {
						color: black;
						background: linear-gradient(to right bottom, lightpink, lightblue);
					}
				}
			}
		}
	}

	.dept-popup {
		.dept-box {
			width: 80vw;
			height: 80vh;
			border-radius: 1vw;
			background-color: #fdf6e3;
			overflow: auto;

			.dept-part {
				width: 100%;
				margin-bottom: 1%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.dept-initial {
					width: 100%;
					margin-left: 1.5%;
					margin-top: 1%;
					font-size: 28px;
					font-weight: bold;
				}

				.dept {
					width: 18%;
					height: 8vh;
					margin: 1%;
					display: flex;
					justify-content: center;
					align-items: center;
					box-sizing: border-box;
					border-radius: 1vw;
					cursor: pointer;
					font-size: 16px;
					font-weight: bold;
					color: white;
					background: linear-gradient(to right bottom, pink, blue);

					&:hover {
						color: black;
						background: linear-gradient(to right bottom, lightpink, lightblue);
					}
				}
			}
		}
	}


	.add-part-popup {
		.add-part {
			width: 100vw;
			height: 100vh;
			overflow: auto;
			padding: 2.5% 2% 1.5% 2%;
			box-sizing: border-box;
			background-color: #fdf6e3;

			.add-part-box {
				width: 100%;
				min-height: 100%;
				border-radius: 10px;
				box-shadow: 0 0 1px 5px #dddddd;
				box-sizing: border-box;
				position: relative;

				.back {
					width: 50px;
					height: 50px;
					display: flex;
					justify-content: space-evenly;
					align-items: center;
					position: absolute;
					left: 2.5%;
					top: 4.5%;
					cursor: pointer;
					z-index: 1;
				}

				.submit {
					display: flex;
					justify-content: space-evenly;
					align-items: center;
					position: absolute;
					right: 2.5%;
					top: 4.5%;
					cursor: pointer;
					z-index: 1;
				}

				.title {
					margin-bottom: 1%;
					font-size: 24px;
					font-weight: bold;
					text-align: center;
					padding: 16px;
				}

				.add-part-data {
					width: 100%;
					position: relative;
					display: flex;
					justify-content: flex-start;
					align-items: flex-start;
					flex-wrap: wrap;

					.add-part-attribute {
						width: 28%;
						margin-left: 8%;
						margin-bottom: 4%;
						background-color: #fdf6e3;

						.uni-easyinput {
							width: 80%;
							margin-left: 16px;

							&:deep(.uni-easyinput__content-input) {
								height: 40px;
								font-size: 20px;
								font-weight: bold;
							}
						}

						.uni-stat__select {
							width: 80%;
							margin-left: 16px;

							&:deep(.uni-select) {
								height: 40px;
								font-size: 20px;
								font-weight: bold;
							}

							&:deep(.uni-select__input-placeholder) {
								font-size: 20px;
							}


						}

						.uni-data-checklist {
							margin-left: 16px;

							&:deep(.checklist-box) {
								margin-top: 0px;
							}

							&:deep(.checklist-text) {
								padding: 8px;
								font-size: 20px;
								font-weight: bold;
							}
						}

						.uni-numbox {
							margin-left: 16px;
							height: 40px;

							&:deep(.uni-numbox-btns) {
								width: 40px;
								box-sizing: border-box;

								.uni-numbox--text {
									font-weight: bold;
								}
							}

							&:deep(.uni-numbox__value) {
								width: 60px;
								height: 40px;
								font-size: 20px;
								font-weight: bold;
							}
						}
					}
				}
			}
		}
	}

	#leftImgView {
		width: 23%;
		height: 600px;
		margin-left: 2%;
		margin-top: 5%;
		background-color: #fdf6e3;
	}

	#rightContent {
		margin-top: 3%;
		width: 75%;
		display: flex;
		flex-wrap: wrap;
	}

	.addInput {
		font-size: 20px;
		width: 80%;
		height: 250px;
		margin-left: 8%;
		margin-bottom: 20px;
		background-color: white;
		border: 2px solid #000;
		/* 设置边框颜色和宽度 */
		border-radius: 10px;
		/* 设置边框圆角 */

		&:deep(.uni-easyinput__content-textarea) {
			height: 235px;
			font-size: 20px;
			font-weight: bold;
		}

		&:deep(.uni-easyinput__placeholder-class) {
			font-size: 20px;
		}
	}

	.addInputParent {
		display: flex;
		align-items: center;

		.uni-icons {
			margin-left: 2%;
			margin-right: 2%;
		}
	}

	.table-container {
		height: 75vh;
		width: 100%;
		overflow-x: auto;
		border: 2px solid #ddd;
		background: #fff;
	}

	table {
		font-size: 14px;
		color: #606266;
		border-collapse: collapse;
		// margin: 20px 0;
		font-size: 18px;
		text-align: left;
		width: 100%;
		/* 确保表格宽度大于容器宽度 */
		border-radius: 5px;
		box-sizing: border-box;
		text-align: center;
		white-space: nowrap;
	}

	th,
	td {
		font-size: 14px;
		border: 1px #DDDDDD solid;
		padding: 5px 0px;
		border-bottom: 1px solid #DDDDDD;
		/* 表格底部边框 */
		height: 44px;
		/* 固定行高 */
		box-sizing: border-box;
		/* 包含内边距和边框 */
	}

	th {
		color: #333;
		background-color: #F0F0F0;
		/* 表头文字颜色 */
	}

	tr {
		font-size: 14px;
		height: 44px;
		/* 固定行高 */
	}

	tr:hover {
		background-color: #f1f1f1;
		/* 行悬停效果 */
	}

	uv-button {
		width: 80px;
		/* 固定按钮宽度 */
		box-sizing: border-box;
		/* 包含内边距和边框 */
	}

	th {
		background-color: #f2f2f2;
		/* 表头背景色 */
		position: sticky;
		/* 固定表头 */
		top: 0;
		/* 固定在顶部 */
		z-index: 0;
		/* 确保表头在其他内容之上 */
	}

	.preview-image {
		width: 100%;
		height: 200px;
		object-fit: contain;
	}

	.picture-popup {
		.watermark {
			position: relative;
			transition: all 0.15s ease-in-out;

			&::before {
				content: "STELLA";
				position: absolute;
				top: 2px;
				left: 2px;
				color: black;
				font-size: 18px;
				font-weight: bold;
				text-shadow: 0 0 5px white;
				z-index: 1;
				pointer-events: none;
			}

			&:active {
				transform: scale(1.5);
			}

			.img {
				min-width: 100px;
				min-height: 100px;
				max-width: 100vw;
				max-height: 50vw;
				border-radius: 10px;
				box-shadow: 0 0 10px white;

				&:active {
					box-shadow: 0 0 1px white;
				}
			}
		}
	}
</style>