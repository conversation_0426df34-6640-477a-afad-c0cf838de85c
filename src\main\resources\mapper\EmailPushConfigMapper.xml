<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.email.mapper.EmailPushConfigMapper">

    <resultMap id="ResultMap" type="com.zqn.email.entity.EmailPushConfig">
        <result property="id" column="id" jdbcType="NUMERIC"/>
        <result property="configKey" column="config_key" jdbcType="VARCHAR"/>
        <result property="configValue" column="config_value" jdbcType="VARCHAR"/>
        <result property="configDesc" column="config_desc" jdbcType="VARCHAR"/>
        <result property="configType" column="config_type" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="DATE"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="DATE"/>
    </resultMap>

    <!-- 根据ID查询配置 -->
    <select id="selectById" resultMap="ResultMap">
        SELECT id, config_key, config_value, config_desc, config_type, status, remark,
               create_user, create_date, update_user, update_date
        FROM pcc_email_push_config
        WHERE id = #{id}
    </select>

    <!-- 根据配置键查询配置 -->
    <select id="selectByConfigKey" resultMap="ResultMap">
        SELECT id, config_key, config_value, config_desc, config_type, status, remark,
               create_user, create_date, update_user, update_date
        FROM pcc_email_push_config
        WHERE config_key = #{configKey}
          AND status = 'Y'
    </select>

    <!-- 根据配置类型查询配置列表 -->
    <select id="selectByConfigType" resultMap="ResultMap">
        SELECT id, config_key, config_value, config_desc, config_type, status, remark,
               create_user, create_date, update_user, update_date
        FROM pcc_email_push_config
        WHERE config_type = #{configType}
          AND status = 'Y'
        ORDER BY config_key ASC
    </select>

    <!-- 查询所有启用的配置 -->
    <select id="selectAllEnabled" resultMap="ResultMap">
        SELECT id, config_key, config_value, config_desc, config_type, status, remark,
               create_user, create_date, update_user, update_date
        FROM pcc_email_push_config
        WHERE status = 'Y'
        ORDER BY config_type, config_key ASC
    </select>

    <!-- 根据条件查询配置列表 -->
    <select id="selectByCondition" resultMap="ResultMap">
        SELECT id, config_key, config_value, config_desc, config_type, status, remark,
               create_user, create_date, update_user, update_date
        FROM pcc_email_push_config
        WHERE 1=1
        <if test="configKey != null and configKey != ''">
            AND config_key LIKE '%' || #{configKey} || '%'
        </if>
        <if test="configType != null and configType != ''">
            AND config_type = #{configType}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY config_type, config_key ASC
    </select>

    <!-- 插入配置 -->
    <insert id="insert" parameterType="com.zqn.email.entity.EmailPushConfig">
        INSERT INTO pcc_email_push_config (
            id, config_key, config_value, config_desc, config_type, status, remark,
            create_user, create_date, update_user, update_date
        ) VALUES (
            seq_pcc_email_push_config.nextval, #{configKey}, #{configValue}, #{configDesc}, 
            #{configType}, #{status}, #{remark}, #{createUser}, #{createDate}, 
            #{updateUser}, #{updateDate}
        )
    </insert>

    <!-- 根据ID更新配置 -->
    <update id="updateById" parameterType="com.zqn.email.entity.EmailPushConfig">
        UPDATE pcc_email_push_config
        SET config_key = #{configKey},
            config_value = #{configValue},
            config_desc = #{configDesc},
            config_type = #{configType},
            status = #{status},
            remark = #{remark},
            update_user = #{updateUser},
            update_date = #{updateDate}
        WHERE id = #{id}
    </update>

    <!-- 根据配置键更新配置值 -->
    <update id="updateValueByKey">
        UPDATE pcc_email_push_config
        SET config_value = #{configValue},
            update_user = #{updateUser},
            update_date = SYSDATE
        WHERE config_key = #{configKey}
    </update>

    <!-- 根据ID删除配置 -->
    <delete id="deleteById">
        DELETE FROM pcc_email_push_config WHERE id = #{id}
    </delete>

    <!-- 根据配置键删除配置 -->
    <delete id="deleteByConfigKey">
        DELETE FROM pcc_email_push_config WHERE config_key = #{configKey}
    </delete>

    <!-- 检查配置键是否存在 -->
    <select id="checkConfigKeyExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM pcc_email_push_config
        WHERE config_key = #{configKey}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 批量更新配置 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="configs" item="item" separator=";">
            UPDATE pcc_email_push_config
            SET config_value = #{item.configValue},
                config_desc = #{item.configDesc},
                config_type = #{item.configType},
                status = #{item.status},
                remark = #{item.remark},
                update_user = #{item.updateUser},
                update_date = #{item.updateDate}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <!-- 根据条件统计配置数量 -->
    <select id="countByCondition" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM pcc_email_push_config
        WHERE 1=1
        <if test="configKey != null and configKey != ''">
            AND config_key LIKE '%' || #{configKey} || '%'
        </if>
        <if test="configType != null and configType != ''">
            AND config_type = #{configType}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

</mapper>
