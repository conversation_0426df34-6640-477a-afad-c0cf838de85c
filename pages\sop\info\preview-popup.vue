<!-- 预览弹框 -->
<script setup>
import { ref, inject } from 'vue'
import { operationMap, proSeqMap } from '@/pages/sop/common/commonMap.js'

// 预览弹框
const previewPopup = ref()

// 型体
const model = ref('')
// 制程
const operation = ref('')
// 详细制程
const proSeq = ref('')
// 主要代码
const rtgCode = ref('')
// 项次数量
const flowNumber = ref(0)
// 工序信息
const info = ref()

// 显示预览弹框
function showPreviewPopup(param) {
  model.value = param.model
  operation.value = param.operation
  proSeq.value = param.proSeq
  rtgCode.value = param.rtgCode
  flowNumber.value = param.flowNumber
  info.value = param
  previewPopup.value.open()
}

// 跳转至工序流程预览界面
function toProcessPreview() {
  previewPopup.value.close()
  uni.navigateTo({
    url: `/pages/sop/sop-preview?info=${JSON.stringify(info.value)}`,
    animationType: 'pop-in',
    animationDuration: 300
  })
}

// 跳转至热冷压规格预览界面
function toPressPreview() {
  previewPopup.value.close()
  uni.navigateTo({
    url: `/pages/sop/sop-press?info=${JSON.stringify(info.value)}`,
    animationType: 'pop-in',
    animationDuration: 300
  })
}

defineExpose({
  showPreviewPopup
})
</script>

<template>
  <uni-popup
    ref="previewPopup"
    type="center"
    class="preview-popup"
  >
    <view class="container">
      <view class="title">
        请选择预览类型
      </view>
      
      <view class="description">
        {{ operation === '1' ? proSeqMap.get(proSeq) : operationMap.get(operation) }} - {{ model }} - {{ rtgCode }}
      </view>
      
      <view class="type">
        <view v-show="flowNumber > 0" @click="toProcessPreview()" class="process button">
          工序流程
        </view>
        
        <view @click="toPressPreview()" class="press button">
          热冷压规格
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.preview-popup {
  .container {
    min-width: 300px;
    padding: 0 10px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 22px;
      font-weight: bold;
    }
    
    .description {
      width: 100%;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
    
    .type {
      display: flex;
      justify-content: center;
      align-items: center;
      
      .process, .press {
        padding: 10px 30px;
        margin: 10px 15px 15px 15px;
        font-size: 20px;
      }
      
      .process {
        color: darkred;
      }
      
      .press {
        color: purple;
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
}
</style>