<template>
    <view class="container">
        <view class="back">
            <uni-icons @click="back" type="back" size="36"></uni-icons>
        </view>
		
		<view style="width: 100%; text-align: center; font-size: 24px;">
			<text>前段配套生产进度表</text>
		</view>
		<view class="pltr">
			<uv-button type="error" text="批量投入" @click="batchProdInvestment"></uv-button>
		</view>
		<view class="trjl">
			<uv-button type="primary" text="当日投入记录" @click="queryLog"></uv-button>
		</view>
		<view class="tot_qty_sum">
			<text>订单总量：{{totalOrderQuantity}}</text>
		</view>
		
		<view class="search">
			<text>出货时间</text>
			<picker  mode="date" :value="startTime"   :start="startDate" :end="endDate" @change="bindDateChange">
				<view style="padding: 8px;background-color: white;width: 90px" v-if="startTime === ''" class="uni-input">请选择</view>
				<view style="padding: 8px;background-color: white;width: 90px" v-if="startTime != ''" class="uni-input">{{startTimeText}}</view>
			</picker>
			<picker  mode="date" :value="endTime" @change="bindEndDateChange" style="margin-left: 10px">
				<view style="padding: 8px;background-color: white; width: 90px" v-if="endTime === ''" class="uni-input">请选择</view>
				<view style="padding: 8px;background-color: white;width: 90px" v-if="endTime != ''" class="uni-input">{{endTimeText}}</view>
			</picker>
			<!--<uv-datetime-picker ref="datetimePickerSt" v-model="startTime" mode="datetime" @confirm="confirmSt"></uv-datetime-picker>
			<view class="inputDate">
				<uv-input disabled @click="openSt" v-model="startTimeText"></uv-input>
			</view>-->
			<!--<uv-datetime-picker ref="datetimePickerEnd" v-model="endTime" mode="datetime" @confirm="confirmEnd"></uv-datetime-picker>
			<view class="inputDate">
				<uv-input disabled @click="openEnd" v-model="endTimeText"></uv-input>
			</view>-->
			<text style="margin-left: 20px;">品牌</text>
			<!--<view class="inputDate">
				<uv-input v-model="brand"></uv-input>
			</view>-->
			<view class="inputDate">
				<uni-combox
						:candidates="brandList"
						placeholder="请选择品牌"
						v-model="brand"
						@input="bindBrandChange"
				></uni-combox>
			</view>

			<text style="margin-left: 20px;width: 50px">样品类型</text>
			<view style="width: 25%;">
				<uni-data-select v-model="devType" :localdata="devTypes"
								 :clear="false"
								 emptyTips="請選擇"
								 style="width: 80%;margin-left: 16px;background-color: white;"></uni-data-select>
			</view>
			<text style="margin-left: 20px;width: 50px">裁断配套</text>
			<view style="width: 20%;">
				<uni-data-select v-model="fileType" :localdata="fileTypeList"
								 :clear="false"
								 emptyTips="請選擇"
								 style="width: 80%;margin-left: 16px;background-color: white;"></uni-data-select>
			 </view>
			<text style="margin-left: 20px;width: 50px">裁断完成</text>
			<view style="width: 12%;">
				<uni-data-select v-model="cutComplType" :localdata="cutComplTypeList"
								 :clear="false"
								 emptyTips="請選擇"
								 style="width: 80%;margin-left: 16px;background-color: white;"></uni-data-select>
			</view>
			<uv-button @click="query()" style="margin-left: 25px;" type="primary" text="查询"></uv-button>
		</view>

        <view class="title">
            <table>
                <thead>
                <tr class="title-tr">
					<th class="rowClass  sticky-col sticky-header" style="width: 150px!important;overflow-x: hidden">
						<view>
							<checkbox-group @change="toggleSelectAll">
								<checkbox value="true" :checked="selectAll"/>
							</checkbox-group>
						</view>
					</th>
					<th class="rowClass  sticky-col sticky-header" style="width: 150px!important;overflow-x: hidden" >
						<view  style="display: flex;justify-content: space-between">
							
							<view>样品单号\單據序號</view>
						</view>
					</th>
                    <th class="rowClass  sticky-col sticky-header " style="width: 150px!important;overflow-x: hidden" >投入</th>
                    <th class="rowClass sticky-header ">样品类型</th>
                    <th class="rowClass sticky-header ">鞋图</th>
                    <th class="rowClass2 sticky-header " style="width: 15px;">
						业务/版师
					</th>
                    <th class="rowClass sticky-header ">型体</th>
                    <th class="rowClass sticky-header ">楦头<br>编号</th>
                    <th class="rowClass sticky-header ">派工日</th>
                    <th class="rowClass sticky-header ">
						<view style="display: flex;align-items: center;justify-content: center" @click="sortFun()">
							<view>
								出货日
							</view>
							<view>
								<uni-icons v-if="sortType == 'desc'"  type="down" size="16"></uni-icons>
								<uni-icons v-if="sortType == 'asc'" type="up" size="16"></uni-icons>
							</view>
						</view>
					</th>
                    <th class="rowClass sticky-header ">订单量</th>
                    <th class="rowClass sticky-header ">面板</th>
                    <th class="rowClass sticky-header ">底板</th>
                    <th class="rowClass sticky-header ">面料<br>配套</th>
                    <th class="rowClass sticky-header ">底料<br>配套</th>
                    <th class="rowClass sticky-header ">副料<br>配套</th>
                    <th class="rowClass sticky-header ">裁断发外</th>
                </tr>
                </thead>

                <tbody>
					<template v-for="(item, index) in tableData" :key="item.ord_no">
						<tr class="box-tr">
							<td class="rowClass sticky-col"  rowspan="2">
								<view>
									<checkbox-group @change="selectedItem($event,item)">
										<checkbox value="true" :checked="isSelected(item)"/>
									</checkbox-group>
								</view>
							</td>
							<td class="rowClass sticky-col" rowspan="2">
								<view style="display: flex;justify-content: space-between;align-items: center">
									
									<view style="text-decoration: underline;color: skyblue;"   @click="viewDetail(item.ord_no)">{{ item.ord_no }}</view>
									<text>{{item.ord_seq}}</text>
								</view>
							</td>
							<td class="rowClass sticky-col" rowspan="2">
								<!-- <view>{{ item.ord_seq }}</view> -->
								<view>	<uv-button text="投入" type="error" @click="prodInvestment(item.ord_no,item.item_no)"></uv-button></view>
							</td>
							<td class="rowClass boderBox" rowspan="2">{{ item.dev_type }}</td>
							<td class="rowClass boderBox" rowspan="2"  ><img :src="'data:image/jpg;base64,' + item.model_pic " alt="鞋图"/></td>
							<td class="rowClass2 boderBox" rowspan="2" >
								{{ formattedBRLetters(item.dutyer) }}<br/>
								{{ formattedBRNumbers(item.dutyer) }}
							</td>
							<td class="rowClass boderBox" rowspan="2">{{ item.model_no }}</td>
							<td class="rowClass boderBox" rowspan="2" style="white-space: pre-wrap;">{{ item.last_no }}</td>
							<td class="rowClass boderBox" rowspan="2">{{ item.wo_date }}</td>
							<td class="rowClass boderBox" rowspan="2">{{ item.shp_date }}</td>
							<td class="rowClass boderBox" rowspan="2">{{ item.tot_qty }}</td>
							<td class="rowClass boderBox" rowspan="2">{{ item.upp_flag }}</td>
							<td class="rowClass boderBox" rowspan="2">{{ item.sole_flag }}</td>
							<td class="rowClass boderBox" rowspan="2">{{ item.t1_flag }}</td>
							<td class="rowClass boderBox" rowspan="2">{{ item.t2_flag }}</td>
							<td class="rowClass boderBox" rowspan="2">{{ item.t3_flag }}</td>
							<td class="rowClass boderBox">{{ item.c_f_status }}</td>
						</tr>
						<tr>
							<td class="rowClass boderBox">{{ item.u_f_status }}</td>
						</tr>
					</template>
             </tbody>
            </table>
        </view>
		
		<!--单击单行弹出界面-->
		<view class="updateSpecificationPopup">
		  <uni-popup
		    ref="logPopup"
		    type="center"
			style="height: 80%;">
		  
		    <view class="updateSpecificationBox">
		
		      <uni-title :title="logTitle" type="h2" align="center" ></uni-title>
			  <uni-icons type="closeempty" @click="backDrom" size="32" style="position: absolute;top: 25rpx;right: 1vw;"></uni-icons>
		      <view class="updateSpecificationData">
					<zb-table
						:show-header="true"
						:columns="logcolumn"
						:stripe="true"
						ref="logTable"
						:border="true"
						:data="logTableData"></zb-table>
		      </view>
			</view>
			</uni-popup>
		</view>
		
		<!--单击单行弹出界面-->
		<view class="updateSpecificationPopup">
		  <uni-popup
		    ref="updateSpecificationPopup"
		    type="center"
			style="height: 80%;">
		  
		    <view class="updateSpecificationBox">
		
		      <uni-title title="倉庫掃描明細" type="h2" align="center" ></uni-title>
			  <uni-icons type="closeempty" @click="backDrom" size="32" style="position: absolute;top: 25rpx;right: 1vw;"></uni-icons>
		      <view class="updateSpecificationData">
					<zb-table
						:show-header="true"
						:columns="column"
						:stripe="true"
						ref="zbTable"
						:border="true"
						:cell-style="cellStyle"
						:data="detailTableData"></zb-table>
		      </view>
			  <view style="display: flex; justify-content: center; align-items: center;">
			      <uv-button type="primary" text="已完成" @click="manualClose"></uv-button>
			  </view>
			</view>
			</uni-popup>
		</view>
		
		<!--<view class="left-bottom">
			<uni-pagination show-icon="true" :total="pageCount" :current="firstPageNo" :pageSize="firstPageSize"
				@change="firstChange"></uni-pagination>
		</view>-->
    </view>

</template>

<script setup>
import {
    onMounted,
    ref,
    reactive,
    watch
} from 'vue'
import {
    onPullDownRefresh
} from '@dcloudio/uni-app'
import {
    onShow
} from '@dcloudio/uni-app';
import urlPrefix from '@/pages/common/urlPrefix.js'

//第几页
const firstPageNo = ref(1)
const firstPageSize = ref(999)
const pageCount = ref(0)
//表单数据
const dataList = ref([])

const insUs = uni.getStorageSync("loUserNo")
const insName = uni.getStorageSync("loginUserName")



//日期选择
const endTime = ref(new Date().getTime())
const date = new Date();
date.setHours(date.getHours() - 24);
const startTime = ref(date.getTime())
const datetimePickerSt = ref()
const datetimePickerEnd = ref()
const startTimeText = ref('2024/06/03')
const endTimeText = ref('2024/06/03')

const cfStatus = ref(false);
const brand = ref()
const devType = ref()
const devTypes = ref([])
const fileType = ref()
const fileTypeList = ref([
	{'value':0,'text':'請選擇'},
	{'value':1,'text':'優先'},
	{'value':2,'text':'可配套生產'},
	{'value':3,'text':'只可面部生產'},
	{'value':4,'text':'只可裁斷生產'}
])

const cutComplType = ref()
const cutComplTypeList = ref([
	{'value':0,'text':'請選擇'},
	{'value':1,'text':'已完成'},
	{'value':2,'text':'未完成'}
])

const tableData = ref([]);

const updateSpecificationPopup = ref();
const column=ref([
          { name: 'ord_no', label: '樣品單號',emptyString:'--',width:150},
		  { name: 'bar_date', label: '條碼日期',emptyString:' ',width:150},
		  { name: 'semi_su', label: '製程部位',width:150},
		  { name: 'made_dept', label: '製作組別',emptyString:'/',width:150},
		  { name: 'emp_name', label: '製作人員',emptyString:'/'},
		  { name: 'bar_qty', label: '條碼雙數',emptyString:'0'},
          { name: 'ins_user', label: '建立人',emptyString:'/'}
        ]);

const detailTableData = ref([]);

const logPopup = ref();

const logTitle = ref("投入記錄");

const logcolumn=ref([
          { name: 'ord_no', label: '樣品單號',emptyString:'--',width:150},
		  { name: 'item_no', label: '项次',emptyString:'/'},
		  { name: 'created_date', label: '投入时间',emptyString:'/'}
        ]);

const logTableData = ref([]);

const sortType = ref("asc")

const brandList = ref([])

//关闭弹窗
function backDrom() {
  detailTableData.value=[];
  updateSpecificationPopup.value.close()
  logPopup.value.close()
}	


const selectedItems = ref([]);
const selectAll = ref(false);


// 获取品牌列表
function getBrands() {
    uni.request({
        url: urlPrefix + "/first/getBrandsPlus",
        method: "POST"
    }).then(res => {
        let arr = []
        /*     brandList.value = res.data.data.map(item=>item.data.map(obj=>obj)) ? res.data.data : []*/
        for (const item of  res.data.data) {
            item.data.forEach(i => {
                arr.push({value: i, text: i})
            })
        }
        
		brandList.value = arr.map(item=>{
			return item.value
		});

    }).catch(err => {
        console.log(err)
    })
}


function bindBrandChange(e) {
    brand.value = e
}

// 换行方法
function formattedBRNumbers(text) {
    const numbers = text.match(/\d+/g) || []; // 提取数字
    return  numbers[0];
};

function formattedBRLetters(text){
    const letters = text.match(/[^\d]+/g) || []; // 提取非数字字符
    return letters[0]
};

function toggleSelectAll() {
  selectAll.value = !selectAll.value;
  if (selectAll.value) {
    selectedItems.value = tableData.value.map(item => ({ ord_no: item.ord_no, item_no: item.item_no }));
  } else {
    selectedItems.value = [];
  }
}

function selectedItem(e,item) {
	const checked = e.detail.value[0];
	const itemIndex = selectedItems.value.findIndex(selectedItem => selectedItem.ord_no === item.ord_no && selectedItem.item_no === item.item_no);

	if (checked) {
		if (itemIndex === -1) {
		  selectedItems.value.push({ ord_no: item.ord_no, item_no: item.item_no });
		}
	} else {
		if (itemIndex !== -1) {
		  selectedItems.value.splice(itemIndex, 1);
		}
	}

	// Update the selectAll checkbox state based on selected items
	selectAll.value = selectedItems.value.length === tableData.value.length;
}

function sortFun() {
    if(sortType.value == "desc"){
        sortType.value = "asc"
	}else {
        sortType.value = "desc"
	}
    uni.showLoading({
        title: '加载中'
    });
	query();
}


function isSelected(item) {
  return selectedItems.value.some(selectedItem => selectedItem.ord_no === item.ord_no && selectedItem.item_no === item.item_no);
}

function openSt() {
	datetimePickerSt.value.open();
}

function bindDateChange(e){
   startTime.value = new Date(e.detail.value).getTime();
   startTimeText.value = e.detail.value;
}

function bindEndDateChange(e){
    endTime.value = new Date(e.detail.value).getTime();
    endTimeText.value = e.detail.value;
}

/**
 * 弃用
 * @param e
 */
function confirmSt(e) {
	console.log('confirm', e);
	// 创建一个新的日期对象e
	var date = new Date(e.value);
	
	// 获取年、月、日
	var year = date.getFullYear();
	var month = date.getMonth() + 1; // 月份从0开始，需要加1
	var day = date.getDate();
	
	// 格式化日期为yyyy/MM/DD的样式
	var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
	
	startTimeText.value = formattedDate;
}

//判断颜色
function cellStyle({row, column, rowIndex, columnIndex}){
	if(row.key_flag == "Y" && row.bar_qty == '' && column.name == 'bar_qty'){
		return {
		  'color': 'red'
		};
	}
	// 如果不符合条件，返回空对象
	return {};
}


function openEnd() {
	datetimePickerEnd.value.open();
}

function confirmEnd(e) {
	// 创建一个新的日期对象e
	var date = new Date(e.value);
	
	// 获取年、月、日
	var year = date.getFullYear();
	var month = date.getMonth() + 1; // 月份从0开始，需要加1
	var day = date.getDate();
	
	// 格式化日期为yyyy/MM/DD的样式
	var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
	
	endTimeText.value = formattedDate;
}

const totalOrderQuantity = ref()

function findTouRuLogTotQtyCount(ordersNos){
    console.log("ordersNos",ordersNos)
    uni.request({
        url: urlPrefix + "/qdpt/findTouRuLogTotQtyCount",
        data: {
            orderNos:ordersNos
        },
        method: "POST"
    }).then(res => {
        console.log("投入雙數",res)
		if(res.data){
            logTitle.value  += "（雙數："+ res.data.data+"）";
		}
    }).catch(err => {
        console.log(err)
    })
}



function query(){
	selectedItems.value = [];
	firstPageNo.value = 1;
	uni.request({
	    url: urlPrefix + "/qdpt/query",
	    data: {
	        "pageNo": 1,
	        "pageSize": firstPageSize.value,
			"startTime": startTime.value,
			"endTime": endTime.value,
			"brand": brand.value,
			"devType": devType.value,
			"fileType": fileType.value,
			"cutComplType": cutComplType.value,
			"sortType":sortType.value

	    },
	    method: "GET"
	}).then(res => {
		totalOrderQuantityDeal(res.data.data.list[0]);
	    tableData.value = res.data.data.list
		pageCount.value = res.data.data.total;
        uni.hideLoading();
	}).catch(err => {
	    console.log(err)
        uni.hideLoading();
	})
}

function totalOrderQuantityDeal(res){
	if(res !== undefined){
		totalOrderQuantity.value = res.total_order_quantity;
	}else{
		totalOrderQuantity.value = 0;
	}
}

const currentOrdNo = ref();

//查看明细
function viewDetail(ord_no){
	currentOrdNo.value = ord_no;
	uni.request({
	    url: urlPrefix + "/qdpt/queryDetailTableData",
	    data: {
	        "ordNo": ord_no
	    },
	    method: "GET"
	}).then(res => {
	    detailTableData.value = res.data.data;
	}).catch(err => {
	    console.log(err)
	})
	updateSpecificationPopup.value.open()
}

function manualClose(){
	uni.showModal({
		title: '提示',
		content: '确定要更新这条记录吗？',
		confirmColor: "#ff0000",
		success: function(res) {
			if (res.confirm) {
				console.log('用户点击确定');
				uni.request({
					url: urlPrefix + "/qdpt/manualClose",
					data: {
						"ord_no": currentOrdNo.value
					},
					method: "POST"
				}).then(res => {
					if (res.statusCode != 200) {
						uni.showToast({
							title: res.data.message,
							icon: "error"
						});
					} else{
						updateSpecificationPopup.value.close();
						query();
					}
				}).catch(err => {
					console.log(err);
					uni.showToast({
						title: '操作失败..',
						icon: "error"
					});
				})
			} else if (res.cancel) {
				console.log('用户点击取消');
			}
		}
	});
	
}


//返回首页
function back() {
    let back = getCurrentPages();
    if(back && back.length > 1) {
    	uni.navigateBack({  
    	  delta: 1  
    	});  
    } else {  
    	history.back();  
    }
}

function prodInvestment(ordNo,itemNo){
	var text = {'ord_no':ordNo,'item_no':itemNo};
	uni.showModal({
	    title: '提示',
	    content: ordNo + ': 确定投入吗？',
	    success: function (res) {
	        if (res.confirm) {
				uni.request({
					url: urlPrefix + "/qdpt/update",
					data: text,
					method: "POST"
				}).then(res => {
					if(res.statusCode == 200){
						uni.showToast({
							title: res.data.data,
							icon: "success"
						});
						query();
					} else {
						uni.showToast({
							title: res.data.message,
							icon: "error"
						});
					} 
				}).catch(err => {
					console.log(err)
					uni.showToast({
						title: '保存数据失败..',
						icon: "error"
					});
				})
	        } else if (res.cancel) {
	            console.log('用户点击取消');
	        }
	    }
	});
}


function batchProdInvestment() {
  if (selectedItems.value.length === 0) {
    uni.showToast({
      title: '请选择至少一个项目',
      icon: 'none'
    });
    return;
  }

  const ordNos = selectedItems.value.map(item => item.ord_no).join(',');
  const text = selectedItems.value;
  uni.showModal({
    title: '提示',
    content: `${ordNos}: 确定投入吗？`,
    success: function (res) {
      if (res.confirm) {
		uni.showLoading({
			title: '投入中',
			mask: true // 设置遮罩层
		});
        uni.request({
          url: urlPrefix + "/qdpt/batchUpdate",
          data: text,
          method: "POST"
        }).then(res => {
          if (res.statusCode === 200) {
            uni.showToast({
              title: res.data.data,
              icon: "success"
            });
            query();
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "error"
            });
          } 
		  uni.hideLoading();
        }).catch(err => {
			uni.hideLoading();
          console.log(err);
          uni.showToast({
            title: '保存数据失败..',
            icon: "error"
          });
        });
      } else if (res.cancel) {
        console.log('用户点击取消');
      }
    }
  });
}

async function firstChange(e) {
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
    firstPageNo.value = e.current;
    await uni.request({
        url: urlPrefix + "/qdpt/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
			"startTime": startTime.value,
			"endTime": endTime.value,
			"brand": brand.value,
			"devType": devType.value,
			"fileType": fileType.value,
			"cutComplType": cutComplType.value
        },
        method: "GET"
    }).then(res => {
        //console.log(res.data);
		totalOrderQuantityDeal(res.data.data.list[0]);
        tableData.value = res.data.data.list;
		uni.hideLoading();
    }).catch(err => {
		uni.hideLoading();
        console.log(err)
    })
}

const tableRef = ref();


//获取数据
function queryAllDevType() {
    uni.request({
        url: urlPrefix + "/qdpt/queryAllDevType",
        method: "GET"
    }).then(res => {
		devTypes.value = res.data.data;
    }).catch(err => {
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

function queryLog(){
	uni.request({
	    url: urlPrefix + "/qdpt/queryLog",
	    method: "GET"
	}).then(res => {
		if(res.data != ""){
			logTableData.value = res.data.data;
            logTitle.value = "投入記錄（當日單數："+res.data.data.length+"）"
            let orderNos = logTableData.value.map(item=>item.ord_no);
            findTouRuLogTotQtyCount(orderNos);
		}
	}).catch(err => {
	    console.log(err)
	    uni.showToast({
	        title: '获取数据失败..',
	        icon: "error"
	    });
	})

	logPopup.value.open()
}

//获取数据
async function getData() {
	tableData.value = [];
    if (tableRef.value) {
        tableRef.value.clearSelection();
    }
    await uni.request({
        url: urlPrefix + "/qdpt/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
            "startTime": startTime.value,
            "endTime": endTime.value,
            "brand": brand.value,
            "devType": devType.value
        },
        method: "GET"
    }).then(res => {
		totalOrderQuantityDeal(res.data.data.list[0]);
        dataList.value = res.data.data.list;
        pageCount.value = res.data.data.total;
		tableData.value = res.data.data.list;
    }).catch(err => {
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

//预加载
onMounted(async () => {
	if(1 == 1){
		// 创建一个新的日期对象e
		var date = new Date();
		
		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		
		// 格式化日期为yyyy/MM/DD的样式
		var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
		
		endTimeText.value = formattedDate;
	}
	if(1 == 1){
		// 创建一个新的日期对象e
		var date = new Date();
		
		// 获取当前日期的天数
		var temp = date.getDate();
		
		// 将日期减去一天
		date.setDate(temp - 1);
		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		
		var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
		startTimeText.value = formattedDate;
	}
    await getData()
	queryAllDevType();
    getBrands();
})

//登录校验
function loginCheck(){
	if(uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == ''){
		uni.navigateTo({
			url: `/pages/login/login`,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}
}

onShow(async (props) => {
	loginCheck();
})
</script>

<style lang="scss">
page {
    width: 100%;
    height: 100%;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;
}

.back {
	width: 50px;
	height: 50px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    position: absolute;
    left: 1.5%;
    top: 2%;
    cursor: pointer;
	border-radius: 50%;
	box-shadow: 0 0 5px gray;
    z-index: 1;
}


.search {
	display: flex;
	align-items: center;
	margin-left: 1%;
	margin-top: 2%;
	
	.inputDate{
		width: 15%;
		margin-left: 5px;
	}
	
	.search button{
		width: 20%;
	}
}

.container {
    width: 100%;
    height: 100%;
    padding: 1%;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0 0 1px 5px #dddddd;
    position: relative;
}

.pltr{
	display: flex;
	justify-content: space-evenly;
	align-items: center;
	position: absolute;
	right: 25.5%;
	top: 2%;
	cursor: pointer;
	z-index: 1;
}

.trjl{
	display: flex;
	justify-content: space-evenly;
	align-items: center;
	position: absolute;
	right: 13.5%;
	top: 2%;
	cursor: pointer;
	z-index: 1;
}

.tot_qty_sum{
	display: flex;
	justify-content: space-evenly;
	align-items: center;
	position: absolute;
	right: 1.5%;
	top: 3%;
	cursor: pointer;
	z-index: 1;
}

.right-top-top {
    display: flex;
}

.inpBr {
    width: 15%;
    margin-left: 10rpx;
}

.left-bottom {
    width: 100%;
    height: 10%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.uni-pagination__total {
    margin-right: 0.4375rem !important;
}

.uni-pagination__num-tag {
    min-width: 1.875rem !important;
    background-color: #F0F0F0 !important;
}

.page--active {
    color: white !important;
    background-color: deeppink !important;
}

.title {
    width: 100%;
    overflow-x: auto; /* 添加横向滚动条 */
    height: 78%;
    margin-bottom: 1%;
    margin-top: 1%;
    display: block;
	border: 2px solid #ddd;
	table-layout: fixed; /* 使用固定布局 */
}

.table-container {
	width: 100%;
	overflow-x: auto; /* 使表格容器可横向滚动 */

}

table {
	background-color: #fff;
	table-layout: fixed; /* 使用固定布局 */
    border-collapse: collapse;
    position: relative;
    border-radius: 5px;
    box-sizing: border-box;

}

tbody {
}

.sticky-header {
	position: sticky;
	top: 0;
	background-color: #f2f2f2;
	z-index: 1;
	padding: 2px;
	box-sizing: padding-box; /* 确保 padding 不影响宽度计算 */
}

.sticky-col {
	position: sticky;
	left: 0px;
	padding: 2px;
	background-color: #f2f2f2;
	z-index: 2;
	box-sizing: padding-box; /* 确保 padding 不影响宽度计算 */
}
.sticky-col + .sticky-col {
	left: 45px; /* 第二列距离左侧 100px，避免重叠 */
	z-index: 2;
}
.sticky-col + .sticky-col + .sticky-col {
	left: 196px; /* 第二列距离左侧 100px，避免重叠 */
}
.sticky-header + .sticky-header {
	left: 45px; /* 第二列距离左侧 100px，避免重叠 */
}
.sticky-header + .sticky-header + .sticky-header{
	left: 196px; /* 第二列距离左侧 100px，避免重叠 */
}
.sticky-header.sticky-col {
	z-index: 4; /* Ensure header cells have the highest z-index */
}

thead {
	background-color: #F0F0F0;
}
.rowClass {
	width: 150px;
    height: 2.5rem;
  /*  border: 2px solid #ddd;*/
    padding: 8px;
    text-align: center;
    box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
    /*overflow: visible; !* 允许内容溢出 *!*/
    white-space: nowrap; /* 禁止换行 */
/*	border-bottom: 2px solid #ddd;
	border-right: 2px solid #ddd;*/
}

.rowClass2{
	width: 30px;
    height: 1rem;
/*    border: 2px solid #ddd;*/
    padding: 8px;
    text-align: center;
	box-sizing: border-box;  /* 确保 padding 不影响宽度计算 */
    // white-space: nowrap; /* 禁止换行 */
/*	border-bottom: 2px solid #ddd;
	border-right: 2px solid #ddd;*/
}




tr {
    display: table-row;
}

img {
    width: 50px;
    height: auto;
}

.left-bottom {
	width: 100%;
	height: 10%;
	display: flex;
	justify-content: center;
	align-items: center;
	
	
	.uni-pagination__total {
		margin-right: 0.4375rem !important;
	}
	
	.uni-pagination__num-tag {
		min-width: 1.875rem !important;
		background-color: #F0F0F0 !important;
	}
	
	.page--active {
		color: white !important;
		background-color: deeppink !important;
	}
}

.updateSpecificationPopup {
  .updateSpecificationBox {
	width: 96vw;
	height: 90vh;
	
	border-radius: 1vw;
	background-color: white;
	
	.updateSpecificationData {
	  width: 100%;
	  position: relative;
	  display: flex;
	  justify-content: center;
	  align-items: flex-start;
	  flex-wrap: wrap;
	  height: 85%;
	  
	  .updateSpecificationAttribute {
		width: 35%;
		margin-left: 15%;
		
		.uni-easyinput {
		  width: 70%;
		  margin-left: 1rem;
		}
		
		.uni-stat__select {
		  width: 70%;
		  margin-left: 1rem;
		}
		
		.uni-numbox {
		  margin-left: 1rem;
		}
		
		.uni-file-picker {
		  margin-left: 1rem;
		}
	  }
	}
  }
}


/*
.rowClass::before {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 1px;
	background-color: #ddd;;
}

.rowClass::after {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	width: 1px;
	height: 100%;
	background-color: #ddd;;
}

.rowClass2::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 1px;
	background-color: #ddd;
}

.rowClass2::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	width: 1px;
	height: 100%;
	background-color: #ddd;;
}
*/

.sticky-header::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	width: 1px;
	height: 100%;
	background-color: #ddd;;
}

.sticky-header::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 1px;
	background-color: #ddd;
}
.sticky-col::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	width: 1px;
	height: 100%;
	background-color: #ddd;;
}

.sticky-col::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 1px;
	background-color: #ddd;
}



.boderBox{
	border-right: 1px solid #ddd;border-bottom: 1px solid #ddd;
}


</style>