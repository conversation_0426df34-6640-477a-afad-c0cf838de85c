<template>
  <view class="container">
    <view class="header" style="margin-bottom: 0px;">
      <image src="../../static/stellanewlog.png" style="height: 50px;width: 120px;"></image>
    </view>
    <view class="back">
      <uni-icons @click="back" class="back" type="back" size="36"></uni-icons>
    </view>

    <view class="header">
      <text class="title">翻译</text>
    </view>
    <view class="language-select">
      <picker mode="selector" :range="languageNames" :value="sourceLangIndex" @change="onSourceLangChange">
        <view class="picker">
          <text>源语言: {{ languageNames[sourceLangIndex] }}</text>
        </view>
      </picker>
      <picker mode="selector" :range="languageNames" :value="targetLangIndex" @change="onTargetLangChange">
        <view class="picker">
          <text>目标语言: {{ languageNames[targetLangIndex] }}</text>
        </view>
      </picker>
    </view>
    <view class="content">
      <view class="input-area">
        <textarea maxlength="-1" class="textarea" v-model="inputText" placeholder="请输入要翻译的文本"></textarea>
      </view>
      <view class="result-area">
        <view v-if="translatedText.length" v-for="(translation, index) in translatedText" :key="index" class="translated-text-container">
          <textarea maxlength="-1" class="translated-text" :value="translation.text" readonly></textarea>
        </view>
        <view v-else class="translated-text-container">
          <textarea maxlength="-1" class="translated-text" placeholder="翻译结果将在这里显示" readonly></textarea>
        </view>
      </view>
    </view>
    <button class="translate-button" @click="translateText">翻译</button>
  </view>
</template>

<script>
import urlPrefix from '@/pages/common/urlPrefix.js'
export default {
  data() {
    return {
      inputText: '',
      translatedText: [],
      languages: [
        { name: '中文 (简体)', code: 'zh-Hans' },
        { name: '英文', code: 'en' },
        { name: '越南语', code: 'vi' },
        { name: '印度尼西亚语', code: 'id' },
        { name: '孟加拉语', code: 'bn' },
        { name: '菲律宾语', code: 'fil' }
      ],
      sourceLangIndex: 0,
      targetLangIndex: 1 // 确保目标语言索引为1，即英文
    };
  },
  computed: {
    languageNames() {
      return this.languages.map(lang => lang.name);
    }
  },
  methods: {
    onSourceLangChange(event) {
      this.sourceLangIndex = event.detail.value;
      console.log('源语言索引更新为:', this.sourceLangIndex);
    },
    onTargetLangChange(event) {
      this.targetLangIndex = event.detail.value;
      console.log('目标语言索引更新为:', this.targetLangIndex);
    },
      back() {
        let back = getCurrentPages();
        console.log('back.length：' + back.length)
        if (back && back.length > 1) {
            uni.navigateBack({
                delta: 1
            });
        } else {
            history.back();
        }
    },
    translateText() {
      const sourceLangCode = this.languages[this.sourceLangIndex].code;
      const targetLangCode = this.languages[this.targetLangIndex].code;
      const text = this.inputText;

      uni.request({
        url: urlPrefix + '/translate',
        method: 'POST',
        data: {
          sourceLang: sourceLangCode,
          textToTranslate: text,
          lang: [targetLangCode]
        },
        header: {
          'Content-Type': 'application/json'
        }
      }).then(res => {
        if (res.statusCode === 200 && res.data.code === 1) {
          this.translatedText = res.data.data[0].translations;
        } else {
          console.error('翻译失败:', res.data.msg);
          uni.showToast({
            title: '翻译失败',
            icon: 'error'
          });
        }
      }).catch(err => {
        console.error('请求失败:', err);
        uni.showToast({
          title: '请求失败',
          icon: 'error'
        });
      });
    }
  }
};
</script>


<style>
.container {
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 20px;
}

.title {
  font-size: 24px;
  font-weight: bold;
}

.language-select {
  display: flex;
  justify-content: left;
  margin-bottom: 20px;
}

picker{
	margin-right: 10px;
}

.picker {
  display: inline-block;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  text-align: center;
  margin-right: 10px;
  width: 200px;
}

.picker:last-child {
  margin-right: 0;
}

.content {
  display: flex;
  justify-content: space-between;
  align-items: stretch; /* 使左右两边高度相同 */
}

.input-area,
.result-area {
  width: 50%;
  display: flex;
  flex-direction: column;
}

.textarea {
  width: 100%;
  height: 55vh; /* 显式设置textarea的高度 */
  margin-bottom: 20px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-sizing: border-box; /* 确保padding不影响实际高度 */
}

.translate-button {
	width: calc(100% - 40px); /* 减去左右边距 */
    padding: 10px;
    background-color: #007bff;
    color: white;
    text-align: center;
    border: none;
    border-radius: 5px;
    position: fixed; /* 固定定位 */
    bottom: 50px; /* 距离页面底部50px */
    left: 20px; /* 保持与容器左右边距一致 */
}

.result-area {
  flex: 1; /* 确保result-area与input-area的高度相同 */
}

.translated-text-container {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}

.translated-text {
  width: 100%;
  height: 55vh; /* 显式设置translated-text的高度 */
  padding: 10px;
  font-size: 18px;
  color: #333;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-sizing: border-box; /* 确保padding不影响实际高度 */
}

.back {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  position: absolute;
  left: 3%;
  width: 50px;
  height: 50px;
  top: 7%;
  border-radius: 50%;
  box-shadow: 0 0 5px gray;
  cursor: pointer;
  z-index: 1;
}

</style>