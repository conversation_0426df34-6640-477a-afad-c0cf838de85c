<!-- 添加工序信息弹框 -->
<script setup>
import { ref, computed, watch, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap, proSeqMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 添加工序信息弹框
const addInfoPopup = ref()

// 工序信息列表
const processInfoList = inject('processInfoList')
// 搜索型体
const searchModel = inject('searchModel')
// 是否只显示自己创建的工序列表
const isMine = inject('isMine')
// 楦头编号
const lastNos = inject('lastNos')
// Outsole
const osNo = inject('osNo')
// 获取工序信息列表
const getProcessInfoList = inject('getProcessInfoList')
// 是否选择所有工序信息
const isSelectedAllProcessInfo = inject('isSelectedAllProcessInfo')

// 客户
const brand = ref('')
// 型体编号
const model = ref('')
// 制程
const operation = ref('')
// 详细制程
const proSeq = ref('')
// 生产类型
const infoType = ref('')
// 生产类型列表
const infoTypeList = ref([])
// 材质
const infoMaterial = ref()
// 材质列表
const infoMaterialList = ref([])
// 是否聚焦材质输入框
const focusMaterialInput = ref(false)

// 输入框 SKU
const skuInput = ref('')
// 聚焦 SKU 输入框
const focusSkuInput = ref(false)
// SKU 列表
const skuList = ref([])
// 可选 SKU 列表
const skuOptionList = ref([])
// 已选 SKU 列表
const selectedSkuList = ref([])
// SKU
const infoSku = computed(() => selectedSkuList.value.map(item => item.proNo).join(';'))

// 获取生产类型列表
async function getInfoTypeList(param) {
  await uni.request({
    url: urlPrefix + '/sop/getInfoTypeList',
    method: 'POST',
    data: {
      operation: param
    }
  }).then(res => {
    if (res.data.code) {
      infoTypeList.value = res.data.data ? res.data.data : []
    } else {
      infoTypeList.value = []
      tipPopup.value.showTipPopup('warn', '暂无生产类型列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 获取材质列表
async function getInfoMaterialList(param1, param2) {
  await uni.request({
    url: urlPrefix + '/sop/getInfoMaterialList',
    method: 'POST',
    data: {
      model: param1,
      operation: param2
    }
  }).then(res => {
    if (res.data.code) {
      infoMaterialList.value = res.data.data ? res.data.data : []
      if (infoMaterialList.value.length === 1) {
        infoMaterial.value = infoMaterialList.value[0].material
      }
    } else {
      infoMaterialList.value = []
      tipPopup.value.showTipPopup('warn', '暂无材质列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 获取 SKU 列表
async function getInfoSkuList(param) {
  await uni.request({
    url: urlPrefix + '/sop/getSkuList',
    method: 'POST',
    data: {
      model: param
    }
  }).then(res => {
    if (res.data.code) {
      skuList.value = res.data.data ? res.data.data : []
      skuOptionList.value = skuList.value.slice(0, 50)
    } else {
      skuList.value = []
      skuOptionList.value = []
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示添加工序信息弹框
async function showAddInfoPopup(param1, param2, param3, param4) {
  model.value = param1
  operation.value = param2
  proSeq.value = param3
  brand.value = param4
  infoMaterial.value = ''
  selectedSkuList.value = []
  if (operation.value === '5') {
    infoType.value = operationMap.get(operation.value) + '-' + proSeqMap.get(proSeq.value)
  } else {
    infoType.value = ''
  }
  
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await getInfoTypeList(operation.value)
  await getInfoMaterialList(model.value, operation.value)
  await getInfoSkuList(model.value)
  
  uni.hideLoading()
  
  // if (infoTypeList.value.length === 0 || infoMaterialList.value.length === 0) {
  //   return
  // }
  
  addInfoPopup.value.open()
}

// 选择 SKU
function selectSku(param) {
  if (!selectedSkuList.value.includes(param)) {
    selectedSkuList.value.push(param)
  }
}

// 删除 SKU
function deleteSku(param) {
  selectedSkuList.value = selectedSkuList.value.filter(item => item !== param)
}

// 添加工序信息
async function addProcessInfo(param1, param2, param3, param4, param5, param6, param7) {
  // if (!param4) {
  //   tipPopup.value.showTipPopup('warn', '请选择生产类型！')
  //   return
  // }
  
  // if (!param5) {
  //   tipPopup.value.showTipPopup('warn', '请输入材质！')
  //   return
  // }
  
  // if (operation.value === '1' && !param6) {
  //   tipPopup.value.showTipPopup('warn', '请输入配色！')
  //   return
  // }
  
  uni.showLoading({
    title: '添加中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/addProcessInfo',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      proSeq: param3,
      rtgType: param4 ? param4 : '/',
      material: param5 ? param5 : '/',
      proNos: param6 ? param6 : '/',
      insUser: user,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getProcessInfoList(param2, param7)
      tipPopup.value.showTipPopup('success', '添加成功！')
      addInfoPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '添加失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

let skuInputTimer = null
watch(skuInput, () => {
  if (skuInputTimer) {
    clearTimeout(skuInputTimer)
  }
  skuInputTimer = setTimeout(() => {
    skuOptionList.value = skuList.value.filter(item => (`${item.proNo} | ${item.skuNo} | ${item.colorDesc}`).includes(skuInput.value.toUpperCase())).slice(0, 50)
  }, 300)
})

defineExpose({
  showAddInfoPopup
})
</script>

<template>
  <uni-popup
    ref="addInfoPopup"
    type="center"
    :is-mask-click="false"
    class="add-info-popup"
  >
    <view class="container">
      <view class="top-bar flex-row-between-center">
        <view
          @click="addInfoPopup.close()"
          class="cancel button"
        >
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <view class="title">
          添加工序信息
        </view>
        
        <view
          @click="addProcessInfo(model, operation, proSeq, infoType, infoMaterial, infoSku, brand)"
          class="submit button"
        >
          <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
        </view>
      </view>
      
      <view class="main flex-row-start">
        <view class="attribute flex-row-center-start">
          {{ operationMap.get(operation) ? operationMap.get(operation) : '未知' }} - {{ proSeqMap.get(proSeq) ? proSeqMap.get(proSeq) : '未知' }} - {{ model }}
        </view>
        
        <view class="attribute flex-row-center-start">
          <view class="attribute-name flex-row-center">
            生产类型：
          </view>
          
          <view class="info-type-list">
            <view class="info-type flex-row-center">
              <view class="selected flex-row-center">
                {{ infoType ? infoType : '请选择' }}
              </view>
            </view>
            
            <view
              v-for="(item, index) in infoTypeList"
              :key="index"
              class="info-type flex-row-center"
            >
              <view
                @click="infoType = item"
                class="button"
              >
                {{ item }}
              </view>
            </view>
          </view>
        </view>
        
        <view class="attribute flex-row-center-start">
          <view class="attribute-name flex-row-center">
            材质：
          </view>
          
          <view class="info-material-list">
            <view class="info-material flex-row-center">
              <input
                v-model="infoMaterial"
                @focus="focusMaterialInput = true"
                @blur="focusMaterialInput = false"
                type="text"
                placeholder="请输入材质"
                class="input"
                :style="{
                  boxShadow: focusMaterialInput ? '0 0 5px blue' : '0 0 5px gray'
                }"
              />
              
              <view v-show="infoMaterial" @click="infoMaterial = ''" class="clear button">
                <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
              </view>
            </view>
            
            <view
              v-for="(item, index) in infoMaterialList"
              :key="index"
              v-show="item.material.includes(infoMaterial.toUpperCase())"
              class="info-material flex-row-center"
            >
              <view
                @click="infoMaterial = item.material"
                class="button"
              >
                {{ item.modelVer ? ('版次 ' + item.modelVer + ' | ') : '' }}{{ item.material }}
              </view>
            </view>
          </view>
        </view>
        
        <view v-show="operation === '1'" class="info-sku">
          <view class="sku-title">
            配色：
          </view>
          
          <view class="sku-data">
            <view class="search">
              <input
                v-model="skuInput"
                @focus="focusSkuInput = true"
                @blur="focusSkuInput = false"
                type="text"
                placeholder="请输入成品编号/SKU/配色"
                class="sku-input input"
                :style="{
                  boxShadow: focusSkuInput ? '0 0 5px blue' : '0 0 5px gray'
                }"
              />
              
              <view v-show="skuInput" @click="skuInput = ''" class="sku-clear button">
                <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
              </view>
              
              <!-- #ifdef APP -->
              <uni-transition
                :show="focusSkuInput"
                mode-class="fade"
                class="sku-option"
              >
              <!-- #endif -->
              <!-- #ifdef WEB -->
              <transition name="fade">
              <view v-show="focusSkuInput" class="sku-option">
              <!-- #endif -->
                <view
                  v-for="item in skuOptionList"
                  @click="selectSku(item)"
                  class="sku-option-item"
                >
                  {{ item.proNo }} | {{ item.skuNo }} | {{ item.colorDesc }}
                </view>
                
                <view v-show="skuOptionList.length === 0" class="sku-option-empty">
                  暂无数据
                </view>
              <!-- #ifdef WEB -->
              </view>
              </transition>
              <!-- #endif -->
              <!-- #ifdef APP -->
              </uni-transition>
              <!-- #endif -->
            </view>
            
            <view class="select">
              <view v-for="item in selectedSkuList" class="selected-sku">
                <view class="button">
                  {{ item.proNo }} | {{ item.skuNo }} | {{ item.colorDesc }}
                </view>
                
                <view @click="deleteSku(item)" class="delete-sku">
                  <uni-icons type="closeempty" size="20" color="darkred"></uni-icons>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.add-info-popup {
  .container {
    width: 850px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .top-bar {
      width: 100%;
      height: 70px;
      padding: 0 10px;
      
      .title {
        font-size: 22px;
        font-weight: bold;
      }
      
      .cancel, .submit {
        width: 75px;
        height: 50px;
      }
    }
    
    .main {
      width: 100%;
      padding: 0 10px;
      flex-wrap: wrap;
      
      .attribute {
        width: 50%;
        font-size: 20px;
        font-weight: bold;
        
        .attribute-name {
          height: 70px;
          font-size: 20px;
          font-weight: bold;
        }
        
        &:first-child {
          width: 100%;
          height: 40px;
        }
      }
      
      .info-type-list {
        height: 280px;
        overflow: auto;
        
        // /* #ifdef WEB */
        // &::-webkit-scrollbar {
        //   display: none;
        // }
        // /* #endif */
        
        .info-type {
          padding: 10px;
          
          .selected {
            width: 270px;
            height: 50px;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            background-color: #fdf6e3;
            border-radius: 10px;
            box-shadow: 0 0 5px gray;
          }
          
          .button {
            width: 270px;
            height: 50px;
            color: darkgreen;
            font-size: 20px;
          }
          
          &:first-child {
            position: sticky;
            top: 0;
            background-color: #fdf6e3;
            z-index: 1;
          }
        }
      }
      
      .info-material-list {
        height: 280px;
        overflow: auto;
        
        // /* #ifdef WEB */
        // &::-webkit-scrollbar {
        //   display: none;
        // }
        // /* #endif */
        
        .info-material {
          padding: 10px;
          position: relative;
          
          .button {
            width: 270px;
            min-height: 50px;
            color: darkmagenta;
            font-size: 20px;
            padding: 5px;
          }
          
          .input {
            width: 270px;
            height: 50px;
            padding: 0 10px;
          }
          
          .clear {
            width: 60px;
            min-height: 40px;
            position: absolute;
            top: 15px;
            right: 15px;
          }
          
          &:first-child {
            position: sticky;
            top: 0;
            background-color: #fdf6e3;
            z-index: 1;
          }
        }
      }
      
      .info-sku {
        width: 100%;
        height: 300px;
        padding: 0 20px;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        
        .sku-title {
          width: 60px;
          height: 70px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 20px;
          font-weight: bold;
        }
        
        .sku-data {
          width: calc(100% - 80px);
          height: 100%;
          
          .search {
            width: 100%;
            height: 70px;
            padding: 10px;
            position: relative;
            
            .sku-input {
              width: 100%;
              height: 100%;
              padding: 0 10px;
            }
            
            .sku-clear {
              width: 60px;
              height: 40px;
              margin-top: 5px;
              position: absolute;
              top: 10px;
              right: 15px;
            }
            
            .sku-option {
              width: calc(100% - 20px);
              max-height: 200px;
              position: absolute;
              top: 100%;
              left: 10px;
              border-radius: 10px;
              box-shadow: 0 0 5px gray;
              background-color: #fdf6e3;
              z-index: 2;
              overflow: auto;
              
              .sku-option-item, .sku-option-empty {
                width: 100%;
                min-height: 50px;
                padding: 5px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 20px;
                font-weight: bold;
                text-align: center;
                border-top: 1px solid #ddd;
              }
              
              .sku-option-item {
                transition: all 0.1s linear;
                
                /* #ifdef APP */
                &:active {
                  background-color: #ccc;
                }
                /* #endif */
                
                /* #ifdef WEB */
                cursor: pointer;
                &:hover {
                  background-color: #ccc;
                }
                /* #endif */
              }
              
              .sku-option-item:first-child, .sku-option-empty {
                border: none;
              }
            }
          }
          
          .select {
            width: 100%;
            max-height: 220px;
            padding: 5px;
            overflow: auto;
            
            .selected-sku {
              width: 100%;
              padding: 15px 15px 5px 15px;
              position: relative;
              
              .button {
                width: 100%;
                min-height: 40px;
                padding: 5px;
              }
              
              .delete-sku {
                width: 30px;
                height: 30px;
                position: absolute;
                right: 0;
                top: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                font-weight: bold;
                text-align: center;
                background-color: #fdf6e3;
                border-radius: 50%;
                box-shadow: 0 0 5px gray;
                transition: all 0.1s linear;
                z-index: 1;
                /* #ifdef WEB */
                cursor: pointer;
                /* #endif */
                
                &:active {
                  transform: scale(0.98);
                  box-shadow: 0 0 1px gray;
                  /* #ifdef APP */
                  background-color: #ccc;
                  /* #endif */
                }
                
                /* #ifdef WEB */
                &:hover {
                  background-color: #ccc;
                }
                /* #endif */
              }
            }
          }
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .flex-row-start {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
  }
  
  .flex-row-center-start {
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }
  
  .flex-row-between-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  /* #ifdef WEB */
  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.3s ease;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  
  .fade-enter-to,
  .fade-leave-from {
    opacity: 1;
  }
  /* #endif */
}
</style>