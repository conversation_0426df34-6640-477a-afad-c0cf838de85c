<template>
	<view class="status-bar"></view>
	<view class="top-container">
		<view class="back">
		    <uni-icons @click="back()" type="back" size="36" color="#6fa2ce"></uni-icons>
		</view>
		<view class="title">
		    成型生產配套表
		</view>
	</view>
	
	<view class="select_time">
		<view class="select_time_title">
		    出貨時間:
		</view>
		<uv-datetime-picker ref="datetimePickerSt" v-model="startTime" mode="date" @confirm="confirmSt"></uv-datetime-picker>
			<view class="inputDate">
				<uv-input disabled="true"  @click="openSt" v-model="startTimeText"></uv-input>
			</view>
		<uv-button @click="query()" style="margin-left: 25px;"  type="primary" text="查詢"></uv-button>
	</view>
	
	<view class="container">
				<scroll-view class="table-container2"  :scroll-left="scrollLeft"  scroll-x="true"   ref="scrollHeader" >
		<table >
			<colgroup>
			    <col style="min-width: 100px;width: 100px;" />
			    <col  style="min-width: 100px; width: 100px;" />
			    <col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />		
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />	
																	
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />		
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />	
					
			    <col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />		
				<col style="min-width: 50px;width: 50px;" />
				<col style="min-width: 50px;width: 50px;" />	
				<col style="min-width: 50px;width: 50px;" />	
			  </colgroup>
			<!-- 表头行 -->
		
		<tr style="background-color: #fdf6e3; ">
				
			<th class="sticky-col1" align="center">组别</th>
			<th class="sticky-col2" align="center">配套状况</th>
			<th align="center">1</th>
			<th align="center">2</th>
			<th align="center">3</th>
			<th align="center">4</th>
			<th align="center">5</th>
			<th align="center">6</th>
			<th align="center">7</th>
			<th align="center">8</th>
			<th align="center">9</th>
			<th align="center">10</th>
							
			<th align="center">11</th>
			<th align="center">12</th>
			<th align="center">13</th>
			<th align="center">14</th>
			<th align="center">15</th>
			<th align="center">16</th>
			<th align="center">17</th>
			<th align="center">18</th>
			<th align="center">19</th>
			
			<th align="center">20</th>
			<th align="center">21</th>
			<th align="center">22</th>
			<th align="center">23</th>
			<th align="center">24</th>
			<th align="center">25</th>
			<th align="center">26</th>
			<th align="center">27</th>
			<th align="center">28</th>
			<th align="center">29</th>
			<th align="center">30</th>
			<th align="center">31</th>
		
		</tr>
		</table>
		</scroll-view>
		
		<scroll-view class="table-container"  scroll-x="true"  @scroll="syncScroll"  ref="scrollContainer" >
			<table>
				<colgroup>
				    <col style="min-width: 100px;width: 100px;" />
				    <col  style="min-width: 100px; width: 100px;" />
					
				    <col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />		
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />	
																		
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />		
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />	
						
				    <col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />		
					<col style="min-width: 50px;width: 50px;" />
					<col style="min-width: 50px;width: 50px;" />	
					<col style="min-width: 50px;width: 50px;" />	
				  </colgroup>
				<!-- 表头行 -->
				
				<tr v-for="(item, index) in dataList" :key="item"  class="datalist-tr">
					
					
					<td class="sticky-col1" v-if="shouldDisplayPdLine(index)" :rowspan="getRowspan(index)" align="center">{{ item.pd_line }}</td>


					
					<td class="sticky-col2" align="center">{{ item.phase }}</td>
					
					<td  align="center">{{ item.tday01 }}</td>
					<td  align="center">{{ item.tday02 }}</td>
					<td  align="center">{{ item.tday03 }}</td>
					<td  align="center">{{ item.tday04 }}</td>
					<td  align="center">{{ item.tday05 }}</td>
					<td  align="center">{{ item.tday06 }}</td>
					<td  align="center">{{ item.tday07 }}</td>
					<td  align="center">{{ item.tday08 }}</td>
					<td  align="center">{{ item.tday09 }}</td>
					<td  align="center">{{ item.tday10 }}</td>
					
					<td  align="center">{{ item.tday11 }}</td>
					<td  align="center">{{ item.tday12 }}</td>
					<td  align="center">{{ item.tday13 }}</td>
					<td  align="center">{{ item.tday14 }}</td>
					<td  align="center">{{ item.tday15 }}</td>
					<td  align="center">{{ item.tday16 }}</td>
					<td  align="center">{{ item.tday17 }}</td>
					<td  align="center">{{ item.tday18 }}</td>
					<td  align="center">{{ item.tday19 }}</td>
					<td  align="center">{{ item.tday20 }}</td>

			        <td  align="center">{{ item.tday21 }}</td>
					<td  align="center">{{ item.tday22 }}</td>
					<td  align="center">{{ item.tday23 }}</td>
					<td  align="center">{{ item.tday24 }}</td>
					<td  align="center">{{ item.tday25 }}</td>
					<td  align="center">{{ item.tday26 }}</td>
					<td  align="center">{{ item.tday27 }}</td>
					<td  align="center">{{ item.tday28 }}</td>
					<td  align="center">{{ item.tday29 }}</td>
					<td  align="center">{{ item.tday30 }}</td>
					<td  align="center">{{ item.tday31 }}</td>


				</tr>
			</table>
		</scroll-view>
		

	</view>

</template>

<script setup>
	import {
		onMounted,
		ref,
		reactive,
		watch
	} from 'vue'
	import {
		onPullDownRefresh
	} from '@dcloudio/uni-app'
	import {
		onShow
	} from '@dcloudio/uni-app';
	import urlPrefix from '@/pages/common/urlPrefix.js'


	const dataList = ref([])
    const mergeInfo = ref([]) // 用于跟踪合并状态的数组
	const tableRef = ref();
	
	

	//日期选择
	const date = new Date();
	date.setHours(date.getHours() - 24);
	const startTime = ref(date.getTime())
	const datetimePickerSt = ref()
	const startTimeText = ref('')
	
	function openSt() {
		datetimePickerSt.value.open();
	}
	
	
	function confirmSt(e) {
		console.log('confirm', e);
		// 创建一个新的日期对象e
		var date = new Date(e.value);
		
		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		
		// 格式化日期为yyyy/MM/DD的样式
		// var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
		var formattedDate = year + '/' + (month < 10 ? '0' + month : month);
		console.log("formattedDate:" + formattedDate)
		startTimeText.value = formattedDate;
			console.log("startTimeText.value:" + startTimeText.value)
	}
	
	
	function query(){
		getData();
	}
	
	//获取数据
	async function getData() {
		
		 uni.showLoading({
		        title: '加載中' ,// 加载框显示的文本
				    mask: true  // 设置为true，阻止用户点击
		    });
		
		if (tableRef.value) {
			tableRef.value.clearSelection();
		}
		await uni.request({
			url: urlPrefix + "/api/matting/list?date=" + startTimeText.value,
			// data: {
			// 	"date": startTimeText.value,
			// },
			method: "GET"
		}).then(res => {
			dataList.value = res.data.data;
			mergeInfo.value = calculateMergeInfo(dataList.value)
			 uni.hideLoading();
		}).catch(err => {
			console.log(err)
			uni.showToast({
				title: '获取数据失败..',
				icon: "error"
			});
		})
	}

	//预加载
	onMounted(async () => {
			  const date = new Date();
		      const year = date.getFullYear();
		      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的
		      startTimeText.value = `${year}/${month}`;
		await getData()

	})


// 计算合并信息
function calculateMergeInfo(data) {
  let mergeInfo = []
  let lastPdLine = null
  let count = 0

  data.forEach((item, index) => {
    if (item.pd_line !== lastPdLine) {
      if (lastPdLine !== null) {
        mergeInfo.push({ start: index - count, count: count })
      }
      lastPdLine = item.pd_line
      count = 1
    } else {
      count++
    }
  })

  // 添加最后一个合并信息
  if (lastPdLine !== null) {
    mergeInfo.push({ start: data.length - count, count: count })
  }

  return mergeInfo
}

 // 添加辅助函数到 script 部分
 function shouldDisplayPdLine(rowIndex) {
   const info = mergeInfo.value.find(info => info.start === rowIndex)
   return info !== undefined
 }
 
 function getRowspan(rowIndex) {
   const info = mergeInfo.value.find(info => info.start === rowIndex)
   return info ? info.count : 1
 }

	//返回首页
	function back() {
		let back = getCurrentPages();
		if (back && back.length > 1) {
			uni.navigateBack({
				delta: 1
			});
		} else {
			history.back();
		}
	}
	
// 使用 ref 创建响应式的引用
const scrollContainer = ref(null);
const scrollHeader = ref(null);
const scrollLeft = ref(0); // 初始滚动位置

// 同步两个表格的滚动位置
function syncScroll(event) {
  const left = event.detail.scrollLeft;
  scrollLeft.value = left;

}
</script>


<style lang="scss">
	page {
	  background-color: #FFFFFF; 
	}

	
// .vertical-line {
//   border-left: 1px solid chartreuse; /* 黑色竖线，可以根据需要调整颜色和宽度 */
//   height: 100px; /* 竖线的高度，根据需要调整 */
//   width: 0.1px; /* 宽度为0，只显示边框 */
// }
	
 .top-container {
	 margin-left: 2%;
	 margin-top: 10px;
    display: flex;
    align-items: center; /* 垂直居中对齐 */
    height: 60px; /* 容器高度设为视口高度，以便垂直居中 */

  }
  .title {
	font-size: 22px;
    margin: 0 auto; /* 使标题水平居中 */
    padding: 0; /* 移除默认的内边距 */
  }
	
	.return-button {

	}
	
	.select_time{
		 margin-left: 2.5%;
		  display: flex;
		  align-items: center; 
		  justify-content: flex-start;
	
		  .select_time_title {
		    font-size: 16px;
		    margin-right: 10px;
		  }

	}
	.container{
		width: 95%;
		margin: 0 auto;
		
	}
	
		.table-container {
			background-color: #fff;
			width: 100%;
			position: relative; 
			    overflow-y: auto; 
		}
	
		.table-container2 {
				 // padding-top: var(--status-bar-height);
			background-color: #fff;
			margin-top: 10px;
			width: 100%;
				position: sticky;
				top: var(--status-bar-height);
				z-index: 99;
				
				 ::-webkit-scrollbar {
				    display: none;
				  }

		}

	
	
		table {
			font-size: 14px;
			// color: #606266;
			border-collapse: collapse;
			width: 100%;
			text-align: center;
			white-space: nowrap;
			
		  width: 100%;
		  text-align: center;
		  white-space: nowrap;
		}
		

	
		th,
		td {
			font-size: 12px;
			border: 1px #ccc solid;
			border-bottom: 1px solid #ccc;
			height: 44px;
			box-sizing: border-box;
		}
	
	     
	       th {
	        // position: sticky;
	        // top: 0; 
	        // z-index: 11;
			color: #fff;
	        background-color: #5B9BD5;
	
	      }

		tr {
			font-size: 14px;
			height: 44px;
		}
	
		tr:hover {
			background-color: #f1f1f1;
		}
		
		


	.sticky-col1 {
	  position: sticky;
	  // top: 0; 
	  left: 0;
	  z-index: 55;
	  // background-color: #fff;
	  background-color: #5B9BD5; 
	  color: #fff;
	}
	.sticky-col2 {
	  // top: 0; 
	  position: sticky;
	  left: 100px;
	  z-index: 55;
	  	  // background-color: #fff;
	  background-color: #5B9BD5;
	  color: #fff;
	}

		/* 设置状态栏背景颜色 */
		.status-bar {
		  height: var(--status-bar-height); /* 使用uni-app提供的变量 */
		  width: 100%;
		  background-color: #fff; /* 设置你想要的颜色 */
		  position: fixed;
		  top: 0;
		  left: 0;
		  z-index: 9999;
		}

	.back {
		width: 50px;
		height: 50px;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		left: 5%;
		top: 5%;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		cursor: pointer;
		z-index: 1;
	}
	
</style>