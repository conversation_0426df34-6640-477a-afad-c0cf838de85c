# 邮件推送系统前端开发文档

## 项目概述

本项目为uniapp前端邮件推送管理系统，提供完整的邮件发送、权限管理、日志查询和系统配置功能。

## 功能特性

### 1. 邮件发送功能
- **快速发送**: 支持报价邮件(V1-V5)、预估邮件(Z/ZZ版)、P版邮件、更新通知邮件
- **自定义发送**: 支持自定义模板变量的邮件发送
- **批量发送**: 支持批量邮件发送功能
- **发送结果**: 实时显示发送结果和状态

### 2. 权限管理功能
- **权限查询**: 分页查询用户邮件推送权限
- **权限配置**: 新增、编辑用户权限配置
- **权限类型**: 支持9种邮件类型的权限管理
- **权限状态**: 支持有权限(Y)、无权限(N)、待定(P)三种状态

### 3. 日志查询功能
- **日志列表**: 分页查询邮件发送日志
- **条件筛选**: 支持按邮件类型、发送状态、日期范围等条件筛选
- **统计信息**: 显示发送总量、成功率等统计数据
- **日志详情**: 查看详细的邮件发送信息
- **重试功能**: 支持失败邮件的重试发送

### 4. 系统配置功能
- **SMTP配置**: 完整的SMTP服务器配置管理
- **配置模板**: 提供常用邮件服务商的预设配置
- **连接测试**: 实时测试SMTP连接状态
- **测试邮件**: 发送测试邮件验证配置
- **配置历史**: 记录配置变更历史

### 5. 系统概览功能
- **数据统计**: 显示邮件发送统计信息
- **快速操作**: 提供常用功能的快速入口
- **最近活动**: 显示系统最近的操作记录

## 技术架构

### 前端技术栈
- **框架**: Vue.js + uniapp
- **UI组件**: 自定义组件 + uniapp内置组件
- **网络请求**: 封装的request工具
- **状态管理**: 组件内部状态管理

### 项目结构
```
├── api/
│   └── email.js                    # 邮件API接口
├── components/
│   ├── EmailSender.vue             # 邮件发送组件
│   ├── EmailPermissionManager.vue  # 权限管理组件
│   ├── EmailLogViewer.vue          # 日志查询组件
│   └── EmailConfigManager.vue      # 配置管理组件
├── pages/
│   └── email/
│       └── index.vue               # 邮件主页面
└── utils/
    └── request.js                  # 网络请求工具
```

## API接口说明

### 权限管理API
- `GET /emailPushPermission/query` - 查询权限列表
- `POST /emailPushPermission/add` - 新增权限配置
- `POST /emailPushPermission/update` - 更新权限配置
- `GET /emailPushPermission/hasPermission` - 检查用户权限

### 邮件发送API
- `POST /emailPush/sendQuote` - 发送报价邮件
- `POST /emailPush/sendEstimate` - 发送预估邮件
- `POST /emailPush/sendPVersion` - 发送P版邮件
- `POST /emailPush/sendUpdateNotify` - 发送更新通知邮件
- `POST /emailPush/send` - 自定义邮件发送
- `POST /emailPush/batchSend` - 批量发送邮件

### 日志查询API
- `GET /emailPush/queryLogs` - 查询邮件发送日志
- `GET /emailPush/getLogsByBusinessId` - 根据业务ID查询日志
- `GET /emailPush/getStatistics` - 获取发送统计
- `POST /emailPush/retry` - 重试邮件发送
- `POST /emailPush/batchRetry` - 批量重试失败邮件

### 配置管理API
- `GET /emailPushConfig/getSmtpConfig` - 获取SMTP配置
- `POST /emailPushConfig/updateSmtpConfig` - 更新SMTP配置
- `POST /emailPushConfig/testSmtpConnection` - 测试SMTP连接
- `POST /emailPush/testConfig` - 发送测试邮件

## 使用说明

### 1. 页面访问
在uniapp项目中，通过以下方式访问邮件管理页面：
```javascript
uni.navigateTo({
  url: '/pages/email/index'
});
```

### 2. 组件使用
各个功能组件可以独立使用：
```vue
<template>
  <view>
    <!-- 邮件发送组件 -->
    <EmailSender @send-success="onSendSuccess" />
    
    <!-- 权限管理组件 -->
    <EmailPermissionManager />
    
    <!-- 日志查询组件 -->
    <EmailLogViewer />
    
    <!-- 配置管理组件 -->
    <EmailConfigManager />
  </view>
</template>

<script>
import EmailSender from '@/components/EmailSender.vue';
import EmailPermissionManager from '@/components/EmailPermissionManager.vue';
import EmailLogViewer from '@/components/EmailLogViewer.vue';
import EmailConfigManager from '@/components/EmailConfigManager.vue';

export default {
  components: {
    EmailSender,
    EmailPermissionManager,
    EmailLogViewer,
    EmailConfigManager
  },
  methods: {
    onSendSuccess() {
      console.log('邮件发送成功');
    }
  }
};
</script>
```

### 3. API调用示例
```javascript
import { 
  sendQuoteEmail, 
  getPermissionList, 
  getEmailLogs 
} from '@/api/email.js';

// 发送报价邮件
const sendEmail = async () => {
  try {
    const response = await sendQuoteEmail('V1', 'ORDER001', 'ADMIN');
    if (response.code === 200) {
      uni.showToast({ title: '发送成功', icon: 'success' });
    }
  } catch (error) {
    uni.showToast({ title: '发送失败', icon: 'none' });
  }
};

// 查询权限列表
const loadPermissions = async () => {
  try {
    const response = await getPermissionList({
      pageNo: 1,
      pageSize: 10
    });
    console.log('权限列表:', response.data);
  } catch (error) {
    console.error('查询失败:', error);
  }
};
```

## 配置说明

### 1. 后端API地址配置
在 `pages/common/urlPrefix.js` 中配置后端API地址：
```javascript
const urlPrefix = "http://***************:8691/pcc";
export default urlPrefix;
```

### 2. 邮件类型配置
在 `api/email.js` 中定义了邮件类型枚举：
```javascript
export const EMAIL_TYPES = {
  QUOTE_V1: 'QUOTE_V1',
  QUOTE_V2: 'QUOTE_V2',
  QUOTE_V3: 'QUOTE_V3',
  QUOTE_V4: 'QUOTE_V4',
  QUOTE_V5: 'QUOTE_V5',
  ESTIMATE_Z: 'ESTIMATE_Z',
  ESTIMATE_ZZ: 'ESTIMATE_ZZ',
  P_VERSION: 'P_VERSION',
  UPDATE_NOTIFY: 'UPDATE_NOTIFY'
};
```

## 样式说明

### 1. 响应式设计
所有组件都支持响应式设计，在不同屏幕尺寸下都能正常显示。

### 2. 主题色彩
- 主色调: #007bff (蓝色)
- 成功色: #28a745 (绿色)
- 错误色: #dc3545 (红色)
- 警告色: #ffc107 (黄色)
- 信息色: #17a2b8 (青色)

### 3. 组件样式
每个组件都有独立的scoped样式，避免样式冲突。

## 注意事项

### 1. 权限控制
- 确保用户有相应的邮件发送权限
- 权限配置需要管理员权限

### 2. 错误处理
- 所有API调用都包含错误处理
- 网络错误会显示友好的提示信息

### 3. 性能优化
- 使用分页查询避免大量数据加载
- 组件按需加载，提高页面性能

### 4. 数据验证
- 邮箱地址格式验证
- 必填字段验证
- 数据长度限制

## 扩展开发

### 1. 新增邮件类型
1. 在 `EMAIL_TYPES` 中添加新的邮件类型
2. 在 `EMAIL_TYPE_NAMES` 中添加对应的显示名称
3. 在相关组件中添加对应的处理逻辑

### 2. 新增功能组件
1. 在 `components` 目录下创建新组件
2. 在主页面中引入和使用新组件
3. 添加对应的API接口

### 3. 自定义样式
1. 修改组件的scoped样式
2. 调整主题色彩变量
3. 适配不同的UI设计需求

## 部署说明

### 1. 开发环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 2. 生产环境
```bash
# 构建生产版本
npm run build

# 部署到服务器
# 将dist目录上传到服务器
```

### 3. 配置检查
- 确认后端API地址配置正确
- 确认SMTP服务器配置正确
- 测试邮件发送功能

## 联系方式

如有问题或建议，请联系开发团队。
