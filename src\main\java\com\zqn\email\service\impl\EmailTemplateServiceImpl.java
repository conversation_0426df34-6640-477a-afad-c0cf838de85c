package com.zqn.email.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.email.entity.EmailTemplate;
import com.zqn.email.mapper.EmailTemplateMapper;
import com.zqn.email.service.EmailTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件模板Service实现类
 * @date 2025/01/22 10:00
 */
@Slf4j
@Service
public class EmailTemplateServiceImpl implements EmailTemplateService {

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Override
    @DS("app")
    public R<PageInfo<EmailTemplate>> queryTemplateList(int pageNo, int pageSize, String emailType, 
                                                        String language, String status, String templateName) {
        try {
            PageHelper.startPage(pageNo, pageSize);
            List<EmailTemplate> list = emailTemplateMapper.selectByCondition(emailType, language, status, templateName);
            PageInfo<EmailTemplate> pageInfo = new PageInfo<>(list);
            return R.success(pageInfo);
        } catch (Exception e) {
            log.error("查询邮件模板列表失败", e);
            return R.error("查询邮件模板列表失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    public R<EmailTemplate> getTemplateById(Long id) {
        try {
            if (id == null) {
                return R.error("模板ID不能为空");
            }
            EmailTemplate template = emailTemplateMapper.selectById(id);
            return R.success(template);
        } catch (Exception e) {
            log.error("根据ID查询邮件模板失败，id: {}", id, e);
            return R.error("查询邮件模板失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    public R<EmailTemplate> getTemplateByCode(String templateCode) {
        try {
            if (!StringUtils.hasText(templateCode)) {
                return R.error("模板代码不能为空");
            }
            EmailTemplate template = emailTemplateMapper.selectByTemplateCode(templateCode);
            return R.success(template);
        } catch (Exception e) {
            log.error("根据模板代码查询邮件模板失败，templateCode: {}", templateCode, e);
            return R.error("查询邮件模板失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    public R<EmailTemplate> getDefaultTemplate(String emailType, String language) {
        try {
            if (!StringUtils.hasText(emailType)) {
                return R.error("邮件类型不能为空");
            }
            if (!StringUtils.hasText(language)) {
                language = "zh_CN"; // 默认简体中文
            }
            EmailTemplate template = emailTemplateMapper.selectDefaultByEmailType(emailType, language);
            return R.success(template);
        } catch (Exception e) {
            log.error("获取默认邮件模板失败，emailType: {}, language: {}", emailType, language, e);
            return R.error("获取默认邮件模板失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    public R<List<EmailTemplate>> getTemplatesByEmailType(String emailType) {
        try {
            if (!StringUtils.hasText(emailType)) {
                return R.error("邮件类型不能为空");
            }
            List<EmailTemplate> templates = emailTemplateMapper.selectByEmailType(emailType);
            return R.success(templates);
        } catch (Exception e) {
            log.error("根据邮件类型查询模板失败，emailType: {}", emailType, e);
            return R.error("查询邮件模板失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    public R<List<EmailTemplate>> getAllEnabledTemplates() {
        try {
            List<EmailTemplate> templates = emailTemplateMapper.selectAllEnabled();
            return R.success(templates);
        } catch (Exception e) {
            log.error("查询所有启用的模板失败", e);
            return R.error("查询邮件模板失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    @Transactional(rollbackFor = Exception.class)
    public R<String> addTemplate(EmailTemplate template) {
        try {
            // 参数验证
            if (!StringUtils.hasText(template.getTemplateCode())) {
                return R.error("模板代码不能为空");
            }
            if (!StringUtils.hasText(template.getTemplateName())) {
                return R.error("模板名称不能为空");
            }
            if (!StringUtils.hasText(template.getEmailType())) {
                return R.error("邮件类型不能为空");
            }

            // 检查模板代码是否已存在
            int count = emailTemplateMapper.checkTemplateCodeExists(template.getTemplateCode(), null);
            if (count > 0) {
                return R.error("模板代码已存在");
            }

            // 设置默认值
            setDefaultValues(template);
            template.setCreateDate(new Date());

            int result = emailTemplateMapper.insert(template);
            if (result > 0) {
                return R.success("新增邮件模板成功");
            } else {
                return R.error("新增邮件模板失败");
            }
        } catch (Exception e) {
            log.error("新增邮件模板失败", e);
            return R.error("新增邮件模板失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    @Transactional(rollbackFor = Exception.class)
    public R<String> updateTemplate(EmailTemplate template) {
        try {
            if (template.getId() == null) {
                return R.error("模板ID不能为空");
            }

            // 检查模板代码是否已存在（排除当前记录）
            if (StringUtils.hasText(template.getTemplateCode())) {
                int count = emailTemplateMapper.checkTemplateCodeExists(template.getTemplateCode(), template.getId());
                if (count > 0) {
                    return R.error("模板代码已存在");
                }
            }

            template.setUpdateDate(new Date());
            int result = emailTemplateMapper.updateById(template);
            if (result > 0) {
                return R.success("更新邮件模板成功");
            } else {
                return R.error("更新邮件模板失败，模板不存在");
            }
        } catch (Exception e) {
            log.error("更新邮件模板失败", e);
            return R.error("更新邮件模板失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    @Transactional(rollbackFor = Exception.class)
    public R<String> deleteTemplate(Long id) {
        try {
            if (id == null) {
                return R.error("模板ID不能为空");
            }

            int result = emailTemplateMapper.deleteById(id);
            if (result > 0) {
                return R.success("删除邮件模板成功");
            } else {
                return R.error("删除邮件模板失败，模板不存在");
            }
        } catch (Exception e) {
            log.error("删除邮件模板失败，id: {}", id, e);
            return R.error("删除邮件模板失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    @Transactional(rollbackFor = Exception.class)
    public R<String> setDefaultTemplate(Long id) {
        try {
            if (id == null) {
                return R.error("模板ID不能为空");
            }

            // 获取模板信息
            EmailTemplate template = emailTemplateMapper.selectById(id);
            if (template == null) {
                return R.error("模板不存在");
            }

            // 设置为默认模板
            int result = emailTemplateMapper.setDefaultTemplate(template.getEmailType(), template.getLanguage(), id);
            if (result > 0) {
                return R.success("设置默认模板成功");
            } else {
                return R.error("设置默认模板失败");
            }
        } catch (Exception e) {
            log.error("设置默认模板失败，id: {}", id, e);
            return R.error("设置默认模板失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    public R<Map<String, String>> renderTemplate(String templateCode, Map<String, Object> variables) {
        try {
            if (!StringUtils.hasText(templateCode)) {
                return R.error("模板代码不能为空");
            }

            EmailTemplate template = emailTemplateMapper.selectByTemplateCode(templateCode);
            if (template == null) {
                return R.error("模板不存在");
            }

            Map<String, String> result = renderTemplateContent(template, variables);
            return R.success(result);
        } catch (Exception e) {
            log.error("渲染邮件模板失败，templateCode: {}", templateCode, e);
            return R.error("渲染邮件模板失败：" + e.getMessage());
        }
    }

    @Override
    public R<Map<String, String>> renderTemplateByType(String emailType, String language, Map<String, Object> variables) {
        try {
            R<EmailTemplate> templateResult = getDefaultTemplate(emailType, language);
            if (templateResult.getCode() != 1 || templateResult.getData() == null) {
                return R.error("未找到默认模板");
            }

            Map<String, String> result = renderTemplateContent(templateResult.getData(), variables);
            return R.success(result);
        } catch (Exception e) {
            log.error("根据邮件类型渲染模板失败，emailType: {}, language: {}", emailType, language, e);
            return R.error("渲染邮件模板失败：" + e.getMessage());
        }
    }

    @Override
    public R<String> validateTemplate(String subjectTemplate, String contentTemplate, Map<String, Object> variables) {
        try {
            // 简单的模板验证
            if (!StringUtils.hasText(subjectTemplate)) {
                return R.error("主题模板不能为空");
            }
            if (!StringUtils.hasText(contentTemplate)) {
                return R.error("内容模板不能为空");
            }

            // 尝试渲染模板
            String renderedSubject = renderString(subjectTemplate, variables);
            String renderedContent = renderString(contentTemplate, variables);

            if (StringUtils.hasText(renderedSubject) && StringUtils.hasText(renderedContent)) {
                return R.success("模板验证通过");
            } else {
                return R.error("模板渲染失败");
            }
        } catch (Exception e) {
            log.error("验证模板失败", e);
            return R.error("验证模板失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    @Transactional(rollbackFor = Exception.class)
    public R<String> copyTemplate(Long sourceId, String newCode, String newName, String createUser) {
        try {
            if (sourceId == null) {
                return R.error("源模板ID不能为空");
            }
            if (!StringUtils.hasText(newCode)) {
                return R.error("新模板代码不能为空");
            }
            if (!StringUtils.hasText(newName)) {
                return R.error("新模板名称不能为空");
            }

            // 获取源模板
            EmailTemplate sourceTemplate = emailTemplateMapper.selectById(sourceId);
            if (sourceTemplate == null) {
                return R.error("源模板不存在");
            }

            // 检查新模板代码是否已存在
            int count = emailTemplateMapper.checkTemplateCodeExists(newCode, null);
            if (count > 0) {
                return R.error("新模板代码已存在");
            }

            // 创建新模板
            EmailTemplate newTemplate = new EmailTemplate();
            newTemplate.setTemplateCode(newCode);
            newTemplate.setTemplateName(newName);
            newTemplate.setEmailType(sourceTemplate.getEmailType());
            newTemplate.setSubjectTemplate(sourceTemplate.getSubjectTemplate());
            newTemplate.setContentTemplate(sourceTemplate.getContentTemplate());
            newTemplate.setVariablesDesc(sourceTemplate.getVariablesDesc());
            newTemplate.setLanguage(sourceTemplate.getLanguage());
            newTemplate.setStatus("Y");
            newTemplate.setIsDefault("N");
            newTemplate.setSortOrder(sourceTemplate.getSortOrder());
            newTemplate.setCreateUser(createUser);
            newTemplate.setCreateDate(new Date());

            int result = emailTemplateMapper.insert(newTemplate);
            if (result > 0) {
                return R.success("复制模板成功");
            } else {
                return R.error("复制模板失败");
            }
        } catch (Exception e) {
            log.error("复制模板失败，sourceId: {}", sourceId, e);
            return R.error("复制模板失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    @Transactional(rollbackFor = Exception.class)
    public R<String> importTemplates(List<EmailTemplate> templates) {
        try {
            if (templates == null || templates.isEmpty()) {
                return R.error("模板列表不能为空");
            }

            int successCount = 0;
            for (EmailTemplate template : templates) {
                try {
                    R<String> result = addTemplate(template);
                    if (result.getCode() == 1) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("导入模板失败，templateCode: {}", template.getTemplateCode(), e);
                }
            }

            return R.success("导入模板完成，共处理 " + templates.size() + " 个模板，成功 " + successCount + " 个");
        } catch (Exception e) {
            log.error("导入模板失败", e);
            return R.error("导入模板失败：" + e.getMessage());
        }
    }

    @Override
    @DS("app")
    public R<List<EmailTemplate>> exportTemplates(String emailType, String language) {
        try {
            List<EmailTemplate> templates = emailTemplateMapper.selectByCondition(emailType, language, "Y", null);
            return R.success(templates);
        } catch (Exception e) {
            log.error("导出模板失败", e);
            return R.error("导出模板失败：" + e.getMessage());
        }
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(EmailTemplate template) {
        if (template.getLanguage() == null) template.setLanguage("zh_CN");
        if (template.getStatus() == null) template.setStatus("Y");
        if (template.getIsDefault() == null) template.setIsDefault("N");
        if (template.getSortOrder() == null) template.setSortOrder(0);
    }

    /**
     * 渲染模板内容
     */
    private Map<String, String> renderTemplateContent(EmailTemplate template, Map<String, Object> variables) {
        Map<String, String> result = new HashMap<>();
        
        String subject = renderString(template.getSubjectTemplate(), variables);
        String content = renderString(template.getContentTemplate(), variables);
        
        result.put("subject", subject);
        result.put("content", content);
        return result;
    }

    /**
     * 渲染字符串（简单的变量替换）
     */
    private String renderString(String template, Map<String, Object> variables) {
        if (!StringUtils.hasText(template)) {
            return template;
        }
        
        String result = template;
        if (variables != null) {
            for (Map.Entry<String, Object> entry : variables.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                String value = entry.getValue() != null ? entry.getValue().toString() : "";
                result = result.replace(placeholder, value);
            }
        }
        
        return result;
    }
}
