<template>
  <view class="email-config-manager">
    <!-- SMTP配置区域 -->
    <view class="config-section">
      <view class="section-header">
        <text class="section-title">SMTP服务器配置</text>
        <view class="header-actions">
          <button @click="loadConfig" class="btn btn-refresh">刷新配置</button>
          <button @click="testConnection" class="btn btn-test">测试连接</button>
        </view>
      </view>
      
      <view class="config-form">
        <view class="form-grid">
          <view class="form-group">
            <text class="form-label">SMTP服务器地址 *</text>
            <input 
              v-model="configForm.host" 
              placeholder="例如: smtp.gmail.com" 
              class="form-input"
            />
            <text class="form-hint">邮件服务商的SMTP服务器地址</text>
          </view>
          
          <view class="form-group">
            <text class="form-label">SMTP端口 *</text>
            <input 
              v-model="configForm.port" 
              type="number"
              placeholder="例如: 587" 
              class="form-input"
            />
            <text class="form-hint">常用端口: 25, 465(SSL), 587(STARTTLS)</text>
          </view>
          
          <view class="form-group">
            <text class="form-label">用户名 *</text>
            <input 
              v-model="configForm.username" 
              placeholder="邮箱地址" 
              class="form-input"
            />
            <text class="form-hint">发送邮件的邮箱地址</text>
          </view>
          
          <view class="form-group">
            <text class="form-label">密码 *</text>
            <input 
              v-model="configForm.password" 
              type="password"
              placeholder="邮箱密码或应用专用密码" 
              class="form-input"
            />
            <text class="form-hint">Gmail等需要使用应用专用密码</text>
          </view>
          
          <view class="form-group">
            <text class="form-label">启用认证</text>
            <switch 
              :checked="configForm.auth === 'true'" 
              @change="onAuthChange"
              color="#007bff"
            />
            <text class="form-hint">是否需要用户名密码认证</text>
          </view>
          
          <view class="form-group">
            <text class="form-label">启用STARTTLS</text>
            <switch 
              :checked="configForm.starttls === 'true'" 
              @change="onStarttlsChange"
              color="#007bff"
            />
            <text class="form-hint">推荐启用，提高安全性</text>
          </view>
          
          <view class="form-group">
            <text class="form-label">启用SSL</text>
            <switch 
              :checked="configForm.ssl === 'true'" 
              @change="onSslChange"
              color="#007bff"
            />
            <text class="form-hint">与STARTTLS二选一</text>
          </view>
        </view>
        
        <view class="form-actions">
          <button @click="saveConfig" :disabled="!canSave" class="btn btn-primary">
            保存配置
          </button>
          <button @click="resetConfig" class="btn btn-secondary">
            重置配置
          </button>
        </view>
      </view>
    </view>

    <!-- 预设配置模板 -->
    <view class="templates-section">
      <view class="section-header">
        <text class="section-title">常用邮件服务商配置</text>
        <text class="section-desc">点击应用预设配置，然后填入用户名和密码</text>
      </view>
      
      <view class="templates-grid">
        <view 
          v-for="template in configTemplates" 
          :key="template.name"
          class="template-card"
          @click="applyTemplate(template)"
        >
          <view class="template-icon">
            <text class="icon-text">{{ template.icon }}</text>
          </view>
          <view class="template-info">
            <text class="template-name">{{ template.name }}</text>
            <text class="template-desc">{{ template.desc }}</text>
          </view>
          <view class="template-config">
            <text class="config-item">{{ template.host }}:{{ template.port }}</text>
            <text class="config-item">{{ template.starttls === 'true' ? 'STARTTLS' : 'SSL' }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 测试邮件发送 -->
    <view class="test-section">
      <view class="section-header">
        <text class="section-title">测试邮件发送</text>
        <text class="section-desc">发送测试邮件验证配置是否正确</text>
      </view>
      
      <view class="test-form">
        <view class="form-group">
          <text class="form-label">测试邮箱地址 *</text>
          <input 
            v-model="testEmail" 
            placeholder="请输入接收测试邮件的邮箱地址" 
            class="form-input"
          />
        </view>
        
        <view class="form-actions">
          <button @click="sendTestEmail" :disabled="!testEmail" class="btn btn-test">
            发送测试邮件
          </button>
        </view>
      </view>
    </view>

    <!-- 配置状态 -->
    <view class="status-section">
      <view class="section-header">
        <text class="section-title">配置状态</text>
      </view>
      
      <view class="status-grid">
        <view class="status-card">
          <view class="status-icon success" v-if="connectionStatus === 'success'">✓</view>
          <view class="status-icon error" v-else-if="connectionStatus === 'error'">✗</view>
          <view class="status-icon pending" v-else>?</view>
          <view class="status-info">
            <text class="status-title">连接状态</text>
            <text class="status-desc">{{ getConnectionStatusText() }}</text>
          </view>
        </view>
        
        <view class="status-card">
          <view class="status-icon success" v-if="testStatus === 'success'">✓</view>
          <view class="status-icon error" v-else-if="testStatus === 'error'">✗</view>
          <view class="status-icon pending" v-else>?</view>
          <view class="status-info">
            <text class="status-title">测试状态</text>
            <text class="status-desc">{{ getTestStatusText() }}</text>
          </view>
        </view>
        
        <view class="status-card">
          <view class="status-icon success" v-if="configStatus === 'saved'">✓</view>
          <view class="status-icon pending" v-else>?</view>
          <view class="status-info">
            <text class="status-title">配置状态</text>
            <text class="status-desc">{{ getConfigStatusText() }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 配置历史 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">配置历史</text>
        <text class="section-desc">最近的配置变更记录</text>
      </view>
      
      <view class="history-list">
        <view 
          v-for="(record, index) in configHistory" 
          :key="index"
          class="history-item"
        >
          <view class="history-time">{{ record.time }}</view>
          <view class="history-action">{{ record.action }}</view>
          <view class="history-user">{{ record.user }}</view>
          <view class="history-status" :class="record.success ? 'success' : 'error'">
            {{ record.success ? '成功' : '失败' }}
          </view>
        </view>
      </view>
    </view>

    <!-- 加载提示 -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-content">
        <text class="loading-text">{{ loadingText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { 
  getSmtpConfig,
  updateSmtpConfig,
  testSmtpConnection,
  sendTestEmail
} from '@/api/email.js';

export default {
  name: 'EmailConfigManager',
  data() {
    return {
      // 配置表单
      configForm: {
        host: '',
        port: '',
        username: '',
        password: '',
        auth: 'true',
        starttls: 'true',
        ssl: 'false'
      },
      
      // 测试邮箱
      testEmail: '',
      
      // 状态
      connectionStatus: 'pending', // pending, success, error
      testStatus: 'pending',
      configStatus: 'pending', // pending, saved
      
      // 配置模板
      configTemplates: [
        {
          name: 'Gmail',
          icon: 'G',
          desc: 'Google邮箱服务',
          host: 'smtp.gmail.com',
          port: '587',
          auth: 'true',
          starttls: 'true',
          ssl: 'false'
        },
        {
          name: 'Outlook',
          icon: 'O',
          desc: 'Microsoft邮箱服务',
          host: 'smtp-mail.outlook.com',
          port: '587',
          auth: 'true',
          starttls: 'true',
          ssl: 'false'
        },
        {
          name: '企业邮箱',
          icon: 'E',
          desc: '腾讯企业邮箱',
          host: 'smtp.exmail.qq.com',
          port: '587',
          auth: 'true',
          starttls: 'true',
          ssl: 'false'
        },
        {
          name: 'QQ邮箱',
          icon: 'Q',
          desc: 'QQ邮箱服务',
          host: 'smtp.qq.com',
          port: '587',
          auth: 'true',
          starttls: 'true',
          ssl: 'false'
        },
        {
          name: '163邮箱',
          icon: '1',
          desc: '网易163邮箱',
          host: 'smtp.163.com',
          port: '587',
          auth: 'true',
          starttls: 'true',
          ssl: 'false'
        },
        {
          name: '126邮箱',
          icon: '2',
          desc: '网易126邮箱',
          host: 'smtp.126.com',
          port: '587',
          auth: 'true',
          starttls: 'true',
          ssl: 'false'
        }
      ],
      
      // 配置历史
      configHistory: [],
      
      // 加载状态
      loading: false,
      loadingText: '加载中...'
    };
  },
  
  computed: {
    /**
     * 是否可以保存
     */
    canSave() {
      return this.configForm.host && 
             this.configForm.port && 
             this.configForm.username && 
             this.configForm.password;
    }
  },
  
  mounted() {
    this.loadConfig();
    this.initConfigHistory();
  },
  
  methods: {
    /**
     * 加载配置
     */
    async loadConfig() {
      try {
        this.loading = true;
        this.loadingText = '加载配置中...';
        
        const response = await getSmtpConfig();
        if (response.code === 1 && response.data) {
          this.configForm = {
            host: response.data.host || '',
            port: response.data.port || '',
            username: response.data.username || '',
            password: response.data.password || '',
            auth: response.data.auth || 'true',
            starttls: response.data.starttls || 'true',
            ssl: response.data.ssl || 'false'
          };
          this.configStatus = 'saved';
        }
      } catch (error) {
        console.error('加载配置失败:', error);
        uni.showToast({
          title: '加载配置失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 保存配置
     */
    async saveConfig() {
      if (!this.canSave) {
        uni.showToast({
          title: '请填写必填项',
          icon: 'none'
        });
        return;
      }
      
      try {
        this.loading = true;
        this.loadingText = '保存配置中...';
        
        const response = await updateSmtpConfig(this.configForm);
        if (response.code === 1) {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });
          this.configStatus = 'saved';
          this.addConfigHistory('保存配置', true);
          
          // 保存后自动测试连接
          setTimeout(() => {
            this.testConnection();
          }, 1000);
        } else {
          uni.showToast({
            title: response.message || '保存失败',
            icon: 'none'
          });
          this.addConfigHistory('保存配置', false);
        }
      } catch (error) {
        console.error('保存配置失败:', error);
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
        this.addConfigHistory('保存配置', false);
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 重置配置
     */
    resetConfig() {
      this.configForm = {
        host: '',
        port: '',
        username: '',
        password: '',
        auth: 'true',
        starttls: 'true',
        ssl: 'false'
      };
      this.connectionStatus = 'pending';
      this.testStatus = 'pending';
      this.configStatus = 'pending';
    },
    
    /**
     * 测试连接
     */
    async testConnection() {
      try {
        this.loading = true;
        this.loadingText = '测试连接中...';
        
        const response = await testSmtpConnection();
        if (response.code === 1) {
          this.connectionStatus = 'success';
          uni.showToast({
            title: '连接测试成功',
            icon: 'success'
          });
          this.addConfigHistory('测试连接', true);
        } else {
          this.connectionStatus = 'error';
          uni.showToast({
            title: response.message || '连接测试失败',
            icon: 'none'
          });
          this.addConfigHistory('测试连接', false);
        }
      } catch (error) {
        console.error('测试连接失败:', error);
        this.connectionStatus = 'error';
        uni.showToast({
          title: '连接测试失败',
          icon: 'none'
        });
        this.addConfigHistory('测试连接', false);
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 发送测试邮件
     */
    async sendTestEmail() {
      if (!this.testEmail) {
        uni.showToast({
          title: '请输入测试邮箱地址',
          icon: 'none'
        });
        return;
      }
      
      // 邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(this.testEmail)) {
        uni.showToast({
          title: '请输入正确的邮箱格式',
          icon: 'none'
        });
        return;
      }
      
      try {
        this.loading = true;
        this.loadingText = '发送测试邮件中...';
        
        const response = await sendTestEmail(this.testEmail);
        if (response.code === 1) {
          this.testStatus = 'success';
          uni.showToast({
            title: '测试邮件发送成功',
            icon: 'success'
          });
          this.addConfigHistory('发送测试邮件', true);
        } else {
          this.testStatus = 'error';
          uni.showToast({
            title: response.message || '测试邮件发送失败',
            icon: 'none'
          });
          this.addConfigHistory('发送测试邮件', false);
        }
      } catch (error) {
        console.error('发送测试邮件失败:', error);
        this.testStatus = 'error';
        uni.showToast({
          title: '测试邮件发送失败',
          icon: 'none'
        });
        this.addConfigHistory('发送测试邮件', false);
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 应用配置模板
     */
    applyTemplate(template) {
      this.configForm = {
        ...this.configForm,
        host: template.host,
        port: template.port,
        auth: template.auth,
        starttls: template.starttls,
        ssl: template.ssl
      };
      
      uni.showToast({
        title: `已应用${template.name}配置`,
        icon: 'success'
      });
      
      this.addConfigHistory(`应用${template.name}模板`, true);
    },
    
    /**
     * 认证开关变化
     */
    onAuthChange(event) {
      this.configForm.auth = event.detail.value ? 'true' : 'false';
    },
    
    /**
     * STARTTLS开关变化
     */
    onStarttlsChange(event) {
      this.configForm.starttls = event.detail.value ? 'true' : 'false';
      if (event.detail.value) {
        this.configForm.ssl = 'false';
      }
    },
    
    /**
     * SSL开关变化
     */
    onSslChange(event) {
      this.configForm.ssl = event.detail.value ? 'true' : 'false';
      if (event.detail.value) {
        this.configForm.starttls = 'false';
      }
    },
    
    /**
     * 获取连接状态文本
     */
    getConnectionStatusText() {
      switch (this.connectionStatus) {
        case 'success': return 'SMTP连接正常';
        case 'error': return 'SMTP连接失败';
        default: return '未测试';
      }
    },
    
    /**
     * 获取测试状态文本
     */
    getTestStatusText() {
      switch (this.testStatus) {
        case 'success': return '测试邮件发送成功';
        case 'error': return '测试邮件发送失败';
        default: return '未测试';
      }
    },
    
    /**
     * 获取配置状态文本
     */
    getConfigStatusText() {
      switch (this.configStatus) {
        case 'saved': return '配置已保存';
        default: return '配置未保存';
      }
    },
    
    /**
     * 初始化配置历史
     */
    initConfigHistory() {
      // 这里可以从本地存储或服务器加载历史记录
      this.configHistory = [];
    },
    
    /**
     * 添加配置历史
     */
    addConfigHistory(action, success) {
      const record = {
        time: new Date().toLocaleString(),
        action,
        user: 'ADMIN', // 这里应该从用户信息中获取
        success
      };
      
      this.configHistory.unshift(record);
      
      // 只保留最近10条记录
      if (this.configHistory.length > 10) {
        this.configHistory = this.configHistory.slice(0, 10);
      }
      
      // 可以保存到本地存储
      try {
        uni.setStorageSync('email_config_history', this.configHistory);
      } catch (error) {
        console.error('保存配置历史失败:', error);
      }
    }
  }
};
</script>

<style scoped>
.email-config-manager {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 通用样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #007bff;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.section-desc {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-refresh {
  background-color: #17a2b8;
  color: white;
  padding: 6px 12px;
  font-size: 12px;
}

.btn-test {
  background-color: #28a745;
  color: white;
}

/* 配置区域 */
.config-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.form-input {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-input:focus {
  border-color: #007bff;
  outline: none;
}

.form-hint {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

/* 模板区域 */
.templates-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 15px;
}

.template-card {
  border: 2px solid #eee;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.template-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 8px rgba(0,123,255,0.1);
  transform: translateY(-2px);
}

.template-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.icon-text {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.template-info {
  margin-bottom: 10px;
}

.template-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.template-desc {
  font-size: 12px;
  color: #666;
}

.template-config {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.config-item {
  font-size: 11px;
  color: #999;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
}

/* 测试区域 */
.test-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 状态区域 */
.status-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 6px;
  background: #f9f9f9;
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: white;
}

.status-icon.success {
  background-color: #28a745;
}

.status-icon.error {
  background-color: #dc3545;
}

.status-icon.pending {
  background-color: #6c757d;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.status-desc {
  font-size: 12px;
  color: #666;
}

/* 历史区域 */
.history-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  display: grid;
  grid-template-columns: 140px 1fr 80px 60px;
  gap: 15px;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.history-item:last-child {
  border-bottom: none;
}

.history-time {
  font-size: 12px;
  color: #666;
}

.history-action {
  font-size: 13px;
  color: #333;
}

.history-user {
  font-size: 12px;
  color: #666;
}

.history-status {
  font-size: 12px;
  font-weight: bold;
  text-align: center;
}

.history-status.success {
  color: #28a745;
}

.history-status.error {
  color: #dc3545;
}

/* 加载提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-content {
  background: white;
  padding: 20px 40px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.loading-text {
  font-size: 16px;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .email-config-manager {
    padding: 10px;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .header-actions {
    justify-content: center;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }

  .history-item {
    grid-template-columns: 1fr;
    gap: 5px;
    text-align: center;
  }

  .form-actions {
    flex-direction: column;
  }
}

/* 开关样式调整 */
switch {
  transform: scale(0.8);
}
</style>
