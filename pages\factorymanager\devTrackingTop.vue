<template>
    <view class="container">
        <view class="back">
            <uni-icons @click="back" type="back" size="36"></uni-icons>
        </view>
		
		<view style="width: 100%; text-align: center; font-size: 24px;">
			<text>版師開版追蹤(面)</text>
		</view>
		<view class="tot_qty_sum">
			<text>数量：{{tableData.length}}</text>
		</view>
		
		<view class="search">


			<view class="flex-box" >
				<text>查询日期：</text>
				<!--<uv-datetime-picker ref="datetimePickerSt" v-model="startTime" mode="datetime" @confirm="confirmSt"></uv-datetime-picker>
				<view class="inputDate" style="width: 110px">
					<uv-input disabled="true"  @click="openSt" v-model="startTimeText"></uv-input>
				</view>
				<uv-datetime-picker ref="datetimePickerEnd" v-model="endTime" mode="datetime" @confirm="confirmEnd"></uv-datetime-picker>
				<view class="inputDate" style="width: 110px">
					<uv-input  disabled="true" @click="openEnd" v-model="endTimeText"></uv-input>
				</view>-->
				<picker  mode="date" :value="startTime"   :start="startDate" :end="endDate" @change="bindDateChange">
					<view style="padding: 8px;background-color: white;width: 90px" v-if="startTime === ''" class="uni-input">请选择</view>
					<view style="padding: 8px;background-color: white;width: 90px" v-if="startTime != ''" class="uni-input">{{startTimeText}}</view>
				</picker>
				<picker  mode="date" :value="endTime" @change="bindEndDateChange" style="margin-left: 10px">
					<view style="padding: 8px;background-color: white; width: 90px" v-if="endTime === ''" class="uni-input">请选择</view>
					<view style="padding: 8px;background-color: white;width: 90px" v-if="endTime != ''" class="uni-input">{{endTimeText}}</view>
				</picker>
			</view>

			<uv-button @click="query()" style="margin-left: 25px;" type="primary" text="查询"></uv-button>
		</view>

        <view class="title">
            <table>
                <thead>
                <tr class="boderBox">
                    <th class="rowClass3">客戶</th>
                    <th class="rowClass3">型體</th>
                    <th class="rowClass3">鞋圖</th>
                    <th class="rowClass3">楦頭編號</th>
                    <th class="rowClass3" style="background-color: #ff0000; width: 250px;">A級師傅</th>
                    <th class="rowClass3">版師</th>
					<th class="rowClass3">預計出貨日期</th>
                </tr>
                </thead>
                <tbody>
					<template v-for="(item, index) in tableData" :key="item.id">
						<tr  :class="{ 'highlight-green': item.c7_qty > 0 }">
							<td class="rowClass">{{ item.brand_no }}</td>
							<td class="rowClass" style="white-space: pre-wrap;">{{ item.model_no }}</td>
							<td class="rowClass">
								<img v-if="item.model_pic" :src="'data:image/jpg;base64,' + item.model_pic"  @click="previewImage(item.model_pic)" alt="鞋图"/>
								<span v-else></span> <!-- 如果 model_pic 为 null 或未定义，显示占位文本 -->
							</td>
							<td class="rowClass">{{ item.last_no }}</td>
							<td class="rowClass" style="width: 250px;">
								<view class="brand-fn-container">
									<input 
										v-model="item.brand_fn" 
										class="brand-fn-input"
									/>
									<button 
										@click="saveBrandFn(item)" 
										class="save-btn"
									>
										✓
									</button>
								</view>
							</td>
							<td class="rowClass">{{ item.emp_name }}</td>
							<td class="rowClass">{{ item.shp_date }}</td>
						</tr>
					</template>
                </tbody>
            </table>
        </view>
		
		<!--单击单行弹出界面-->
		<view class="updateSpecificationPopup">
		  <uni-popup
		    ref="updateSpecificationPopup"
		    type="center"
			style="height: 80%;">
		  
		    <view class="updateSpecificationBox">
		
		      <uni-title title="倉庫掃描明細" type="h2" align="center" ></uni-title>
			  <uni-icons type="closeempty" @click="backDrom" size="32" style="position: absolute;top: 25rpx;right: 1vw;"></uni-icons>
		      <view class="updateSpecificationData">
					<zb-table
						:show-header="true"
						:columns="column"
						:stripe="true"
						ref="zbTable"
						:border="true"
						:cell-style="cellStyle"
						:data="detailTableData"></zb-table>
		      </view>
			  <view style="display: flex; justify-content: center; align-items: center;">
			      <uv-button type="primary" text="已完成" @click="manualClose"></uv-button>
			  </view>
			</view>
			</uni-popup>
		</view>
		
		<!--<view class="left-bottom">
			<uni-pagination show-icon="true" :total="pageCount" :current="firstPageNo" :pageSize="firstPageSize"
				@change="firstChange"></uni-pagination>
		</view>-->
    </view>
	
	<view class="tip-popup">
	    <uni-popup ref="tipPopup" type="message">
	        <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
	    </uni-popup>
	</view>
	
	
	<view class="picture-popup">
		<uni-popup ref="picturePopup" type="center">
			<view class="watermark">
				<img :src="'data:image/jpg;base64,' + preViewPicture" alt="" class="img">
			</view>
		</uni-popup>
	</view>

</template>

<script setup>
import {
    onMounted,
    ref,
    reactive,
    watch,computed
} from 'vue'
import {
    onPullDownRefresh
} from '@dcloudio/uni-app'
import {
    onShow
} from '@dcloudio/uni-app';
import urlPrefix from '@/pages/common/urlPrefix.js'

//第几页
const firstPageNo = ref(1)
const firstPageSize = ref(999)
const pageCount = ref(0)

//表单数据
const dataList = ref([])

const insUs = uni.getStorageSync("loUserNo")
const insName = uni.getStorageSync("loginUserName")

//消息提示
const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')

//日期选择
// 当前日期的结束时间（23:59:59）
const end = new Date();
end.setHours(23, 59, 59, 999);
const endTime = ref(end.getTime());

// 当前日期的开始时间（00:00:00）
const start = new Date();
start.setDate(start.getDate()); // 设置为前一天
start.setHours(0, 0, 0, 0);
const startTime = ref(start.getTime());
const datetimePickerSt = ref()
const datetimePickerEnd = ref()
const startTimeText = ref('2024/06/03')
const endTimeText = ref('2024/06/03')


function bindDateChange(e) {
    // 获取选择的日期
    var selectedDate = new Date(e.detail.value);
    
    // 重置时间为当天的0时0分0秒
    selectedDate.setHours(0, 0, 0, 0);
    
    // 获取选择日期0时0分0秒的时间戳
    startTime.value = selectedDate.getTime();

    // 设置文本显示为选择的日期
    startTimeText.value = e.detail.value;
}


function bindEndDateChange(e) {
    // 获取选择的日期
    var selectedDate = new Date(e.detail.value);
    
    // 设置时间为当天的23时59分59秒
    selectedDate.setHours(23, 59, 59, 999);
    
    // 获取选择日期23时59分59秒的时间戳
    endTime.value = selectedDate.getTime();

    // 设置文本显示为选择的日期
    endTimeText.value = e.detail.value;
}



const tableData = ref([]);
const updateSpecificationPopup = ref();
const currentOrdNo = ref();
const detailTableData = ref([]);
const column=ref([
          { name: 'ord_no', label: '樣品單號',emptyString:'--',width:150},
		  { name: 'bar_date', label: '條碼日期',emptyString:' ',width:150},
		  { name: 'semi_su', label: '製程部位',width:150},
		  { name: 'made_dept', label: '製作組別',emptyString:'/',width:150},
		  { name: 'emp_name', label: '製作人員',emptyString:'/'},
		  { name: 'bar_qty', label: '條碼雙數',emptyString:'0'},
          { name: 'ins_user', label: '建立人',emptyString:'/'}
        ]);

const externalStatus = ref();
const externaTypes = ref(
    [
	  {value:3,text:"ALL"},
      { value: 0, text: "未完成" },
      { value: 1, text: "完成" },
	]
)


//判断颜色
function cellStyle({row, column, rowIndex, columnIndex}){
	if(row.key_flag == "Y" && row.bar_qty == '' && column.name == 'bar_qty'){
		return {
		  'color': 'red'
		};
	}
	// 如果不符合条件，返回空对象
	return {};
}

//关闭弹窗
function backDrom() {
  detailTableData.value=[];
  updateSpecificationPopup.value.close()
}	

//查看明细
function viewDetail(ord_no){
	currentOrdNo.value = ord_no;
	uni.request({
	    url: urlPrefix + "/qdpt/queryDetailTableData",
	    data: {
	        "ordNo": ord_no
	    },
	    method: "GET"
	}).then(res => {
	    detailTableData.value = res.data.data;
	}).catch(err => {
	    console.log(err)
	})
	updateSpecificationPopup.value.open()
}


function manualClose(){
	uni.showModal({
		title: '提示',
		content: '确定要更新这条记录吗？',
		confirmColor: "#ff0000",
		success: function(res) {
			if (res.confirm) {
				console.log('用户点击确定');
				uni.request({
					url: urlPrefix + "/qdpt/manualClose",
					data: {
						"ord_no": currentOrdNo.value
					},
					method: "POST"
				}).then(res => {
					if (res.statusCode != 200) {
						uni.showToast({
							title: res.data.message,
							icon: "error"
						});
					} else{
						updateSpecificationPopup.value.close();
						query();
					}
				}).catch(err => {
					console.log(err);
					uni.showToast({
						title: '操作失败..',
						icon: "error"
					});
				})
			} else if (res.cancel) {
				console.log('用户点击取消');
			}
		}
	});
	
}

function openSt() {
	datetimePickerSt.value.open();
}

function confirmSt(e) {
	console.log('confirm', e);
	// 创建一个新的日期对象e
	var date = new Date(e.value);
	
	// 获取年、月、日
	var year = date.getFullYear();
	var month = date.getMonth() + 1; // 月份从0开始，需要加1
	var day = date.getDate();
	
	// 格式化日期为yyyy/MM/DD的样式
	var formattedDate = year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day);
	
	startTimeText.value = formattedDate;
}


function openEnd() {
	datetimePickerEnd.value.open();
}

function query(){
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
	firstPageNo.value = 1;
	uni.request({
	    url: urlPrefix + "/devTracking/query",
	    data: {
	        "pageNo": 1,
	        "pageSize": firstPageSize.value,
			"startTime": startTime.value,
			"endTime": endTime.value,
			"type": 1
	    },
	    method: "GET"
	}).then(res => {
		firstPageNo.value = 1;
	    tableData.value = res.data.data.list
		pageCount.value = res.data.data.total;
		tableData.value = res.data.data.list;
		uni.hideLoading();
	}).catch(err => {
		uni.hideLoading();
	    console.log(err)
	})
}

// 提示信息
function showTip(type, message) {
    tipType.value = type
    tipMessage.value = message
    tipPopup.value.open()
}

//返回首页
function back() {
    let back = getCurrentPages();
    if(back && back.length > 1) {
    	uni.navigateBack({  
    	  delta: 1  
    	});  
    } else {  
    	history.back();  
    }
}

async function firstChange(e) {
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
    firstPageNo.value = e.current;
    await uni.request({
        url: urlPrefix + "/cutProGress/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
			"startTime": startTime.value,
			"endTime": endTime.value,
			"brand": brand.value,
			"devType": devType.value,
			"externalStatus":externalStatus.value
        },
        method: "GET"
    }).then(res => {
        //console.log(res.data);
        tableData.value = res.data.data.list
		totalOrderQuantityDeal(res.data.data.list[0]);
		uni.hideLoading();
    }).catch(err => {
		uni.hideLoading();
        console.log(err)
    })
}

const tableRef = ref();

//获取数据
function queryAllDevType() {
    uni.request({
        url: urlPrefix + "/cutProGress/queryAllDevType",
        method: "GET"
    }).then(res => {
        console.log(res.data.data);
		devTypes.value = res.data.data;
    }).catch(err => {
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

//获取数据
async function getData() {
	tableData.value = [];
    if (tableRef.value) {
        tableRef.value.clearSelection();
    }
	uni.showLoading({
	    title: '加載中',
	    mask: true // 设置遮罩层
	});
    await uni.request({
        url: urlPrefix + "/devTracking/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
            "startTime": startTime.value,
            "endTime": endTime.value,
			"type": 1
        },
        method: "GET"
    }).then(res => {
        dataList.value = res.data.data.list;
        pageCount.value = res.data.data.total;
		tableData.value = res.data.data.list;
		uni.hideLoading();
    }).catch(err => {
		uni.hideLoading();
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

//预加载
onMounted(async () => {
	if(1 == 1){
		// 创建一个新的日期对象e
		var date = new Date();
		
		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		
		// 格式化日期为yyyy/MM/DD的样式
		var formattedDate = year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day);
		
		
		endTimeText.value = formattedDate;
	}
	if(1 == 1){
		// 创建一个新的日期对象e
		var date = new Date();
		
		// 获取当前日期的天数
		var temp = date.getDate();
		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();
		
		var formattedDate = year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day);
		startTimeText.value = formattedDate;
	}
    await getData();
})

const preViewPicture = ref();
const picturePopup = ref();

function previewImage(url) {
	preViewPicture.value = url;
	picturePopup.value.open();
}

//登录校验
function loginCheck(){
	if(uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == ''){
		uni.navigateTo({
			url: `/pages/login/login`,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}
}

onShow(async (props) => {
	loginCheck();
})

// 添加保存A級師傅的方法
function saveBrandFn(item) {
    uni.showLoading({
        title: '保存中',
        mask: true
    });
    
    uni.request({
        url: urlPrefix + "/devTracking/updateBrandFn",
        method: "POST",
        data: {
            id: item.id,
            brandFn: item.brand_fn,
			brand_no: item.brand_no
        }
    }).then(res => {
        if(res.data.code === 1) {
            uni.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000
            });
        } else {
            uni.showToast({
                title: res.data.message || '保存失败',
                icon: 'error',
                duration: 2000
            });
        }
    }).catch(err => {
        console.error(err);
        uni.showToast({
            title: '保存失败',
            icon: 'error',
            duration: 2000
        });
    }).finally(() => {
        uni.hideLoading();
    });
}
</script>

<style lang="scss">
page {
    width: 100%;
    height: 100%;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;
}

.back {
	width: 50px;
	height: 50px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    position: absolute;
    left: 1.5%;
    top: 3%;
	border-radius: 50%;
	box-shadow: 0 0 5px gray;
    cursor: pointer;
    z-index: 1;
}


.search {

	display: flex;
	align-items: center;
	margin-left: 5%;
	margin-top: 2%;
	
	.inputDate{
		width: 110px;
		margin-left: 5px;
	}
	
	.search button{
		width: 20%;
	}
}

.container {
    width: 100%;
    height: 100%;
    padding: 1%;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0 0 1px 5px #dddddd;
    position: relative;
}

.right-top-top {
    display: flex;
}

.inpBr {
    width: 15%;
    margin-left: 10rpx;
}

.left-bottom {
    width: 100%;
    height: 10%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.uni-pagination__total {
    margin-right: 0.4375rem !important;
}

.uni-pagination__num-tag {
    min-width: 1.875rem !important;
    background-color: #F0F0F0 !important;
}

.page--active {
    color: white !important;
    background-color: deeppink !important;
}

.title {
    width: 100%;
    overflow-x: auto; /* 添加横向滚动条 */
    height: 85%;
    margin-bottom: 1%;
    margin-top: 1%;
    display: block;
	table-layout: fixed; /* 使用固定布局 */
	border: 2px solid #ddd;

}

.table-container {
    width: 100%;
    overflow-x: auto; /* 使表格容器可横向滚动 */
}

table {
	background-color: #fff;
	table-layout: fixed; /* 使用固定布局 */
    border-collapse: collapse;
    position: relative;
    border-radius: 5px;
    box-sizing: border-box;
}

tbody {
}

thead {
	background-color: #F0F0F0;
}

.rowClass {
	width: 150px;
    height: 2.5rem;
    border-bottom: 2px solid #ddd;
	border-right: 2px solid #ddd;
    padding: 8px;
    text-align: center;
    box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
    overflow: visible; /* 允许内容溢出 */
    white-space: nowrap; /* 禁止换行 */
}

.rowClass3{
	width: 25vw;
	height: 2.5rem;
	border-right: 2px solid #ddd;
	padding: 8px;
	text-align: center;
	box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
	overflow: visible; /* 允许内容溢出 */
	white-space: nowrap; /* 禁止换行 */
}
.rowClass4{
	width: 30px;
	height: 1rem;
	border-right: 2px solid #ddd;
	padding: 8px;
	text-align: center;
	box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
	// white-space: nowrap; /* 禁止换行 */
}

.rowClass2 {
	width: 30px;
    height: 1rem;
	border-bottom: 2px solid #ddd;
	border-right: 2px solid #ddd;
    padding: 8px;
    text-align: center;
    box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
    // white-space: nowrap; /* 禁止换行 */
}

tr {
    display: table-row;
}

img {
    width: 50px;
    height: auto;
}

.left-bottom {
	width: 100%;
	height: 10%;
	display: flex;
	justify-content: center;
	align-items: center;
	
	
	.uni-pagination__total {
		margin-right: 0.4375rem !important;
	}
	
	.uni-pagination__num-tag {
		min-width: 1.875rem !important;
		background-color: #F0F0F0 !important;
	}
	
	.page--active {
		color: white !important;
		background-color: deeppink !important;
	}
}


.updateSpecificationPopup {
  .updateSpecificationBox {
	width: 96vw;
	height: 90vh;
	
	border-radius: 1vw;
	background-color: white;
	
	.updateSpecificationData {
	  width: 100%;
	  position: relative;
	  display: flex;
	  justify-content: center;
	  align-items: flex-start;
	  flex-wrap: wrap;
	  height: 85%;
	  
	  .updateSpecificationAttribute {
		width: 35%;
		margin-left: 15%;
		
		.uni-easyinput {
		  width: 70%;
		  margin-left: 1rem;
		}
		
		.uni-stat__select {
		  width: 70%;
		  margin-left: 1rem;
		}
		
		.uni-numbox {
		  margin-left: 1rem;
		}
		
		.uni-file-picker {
		  margin-left: 1rem;
		}
	  }
	}
  }
}

.highlight-green {
        background-color: #c3eed3;
}

/* 固定表头 */
thead th {
	position: sticky;
	top: 0; /* 表头始终固定在顶部 */
	background-color: #f2f2f2; /* 表头背景色 */
	z-index: 2; /* 确保表头在最上层 */
}

thead th::after{
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 2px;
	background-color: #ddd;
}

thead th{
	border-bottom: none;
}


.flex-box{
	display: flex;
	align-items: center;

}

.flex-row::after{
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 2px;
	background-color: #ddd;
	}
	
	
	.tot_qty_sum{
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		position: absolute;
		right: 1.5%;
		top: 3%;
		cursor: pointer;
		z-index: 1;
	}
	
	
	
	.picture-popup {
		.watermark {
			position: relative;
			transition: all 0.15s ease-in-out;
	
			&::before {
				content: "STELLA";
				position: absolute;
				top: 2px;
				left: 2px;
				color: black;
				font-size: 18px;
				font-weight: bold;
				text-shadow: 0 0 5px white;
				z-index: 1;
				pointer-events: none;
			}
	
			&:active {
				transform: scale(1.5);
			}
	
			.img {
				min-width: 100px;
				min-height: 100px;
				max-width: 100vw;
				max-height: 50vw;
				border-radius: 10px;
				box-shadow: 0 0 10px white;
	
				&:active {
					box-shadow: 0 0 1px white;
				}
			}
		}
	}

.brand-fn-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  width: 100%;
}

.brand-fn-input {
  width: 160px !important;
  height: 30px;
  padding: 0 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  &:focus {
    outline: none;
    border-color: #409eff;
  }
}

.save-btn {
  min-width: 32px;
  height: 30px;
  padding: 0 8px;
  background: none;
  border: none;
  cursor: pointer;
  color: #67C23A;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    color: #85ce61;
  }
  &:active {
    color: #529b2e;
  }
}

.rowClass3 {
  width: 25vw;
  &[style*="width"] {
    width: 250px !important; // 强制应用宽度
  }
}

.rowClass {
  width: 150px;
  &[style*="width"] {
    width: 250px !important; // 强制应用宽度
  }
}
</style>