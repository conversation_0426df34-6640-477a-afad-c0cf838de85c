<template>
  <view class="container">
    <view class="header">
      <view class="header-left">
        <button class="back-btn" @click="goBack">
          <text class="back-icon">←</text>
          <text class="back-text">返回</text>
        </button>
      </view>
      <view class="header-center">
        <text class="title">鞋面小组排程表</text>
      </view>
      <view class="header-right">
        <text class="date">{{ currentDate }}</text>
      </view>
    </view>
    
    <view class="content">
      <!-- 左侧区域 -->
      <view class="left-panel">
        <!-- 区域选择 -->
        <view class="area-selection">
          <view class="section-title">区域选择</view>
          <view class="area-buttons">
            <button 
              v-for="area in areas" 
              :key="area.code" 
              :class="['area-btn', selectedarea === area.code ? 'active' : '']"
              @click="selectarea(area.code)">
              {{ area.name }}
            </button>
          </view>
        </view>
        
        <!-- 组别列表 - 始终显示 -->
        <view class="group-list">
          <view class="section-title-with-filter">
            <text>组别列表</text>
            <input 
              type="text" 
              v-model="groupFilter" 
              placeholder="搜索组别" 
              class="filter-input"
              @input="filterGroups"
            />
          </view>
          <view class="table-container group-table">
            <view class="group-header-container">
              <view class="table-header group-header">
                <view class="th th-group-name">组长</view>
                <view class="th th-group-dept">组别</view>
                <view class="th th-group-qty">数量</view>
              </view>
            </view>
            <scroll-view scroll-y class="group-scroll-container">
              <view class="group-body">
                <view 
                  v-for="(group, index) in filteredGroupList" 
                  :key="index" 
                  class="table-row group-row"
                  :class="selectedGroup === group.made_dept ? 'active-row' : ''"
                  @click="selectGroup(group.made_dept)">
                  <view class="td td-group-name">{{ group.emp_name }}</view>
                  <view class="td td-group-dept">{{ group.made_dept }}</view>
                  <view class="td td-group-qty">{{ group.qty || 0 }}</view>
                  <view v-if="selectedGroup === group.made_dept" class="selected-indicator"></view>
                </view>
                <view v-if="filteredGroupList.length === 0" class="empty-message">
                  <text>{{ selectedarea ? '无组别数据' : '请选择区域查看组别' }}</text>
                </view>
                <!-- 新增：合计行 -->
                <view class="table-footer group-footer">
                  <view class="td td-group-name"></view> <!-- 空白单元格，对应组长列 -->
                  <view class="td td-group-dept">合计</view>
                  <view class="td td-group-qty">{{ totalGroupQuantity }}</view>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
      
      <!-- 右侧区域 - 始终显示 -->
      <view class="right-panel">
        <!-- 样品单列表 -->
        <view class="order-list">
          <view class="section-title-with-filter">
            <text>样品单列表</text>
            <view class="filter-controls">
              <picker mode="date" :value="selectedDate" @change="onDateChange">
                <view class="picker-text">{{ selectedDate || '选择日期' }}</view>
              </picker>
              <input 
                type="text" 
                v-model="orderFilter" 
                placeholder="搜索样品单" 
                class="filter-input"
                @input="filterOrders"
              />
            </view>
          </view>
          
          <!-- 操作区域 - 移到上方 -->
          <view class="summary-section" v-if="orderList.length > 0">
            <view class="summary-items">
              <view class="summary-item">
                <text class="summary-label">已选样品单数:</text>
                <text class="summary-value">{{ selectedOrderCount }}</text>
              </view>
            </view>
            <view class="button-container">
              <input 
                type="number" 
                v-model="targetPPH" 
                placeholder="输入目标PPH" 
                class="target-pph-input"
              />
              <button class="save-btn" @click="saveTargetPPH">更新目标PPH</button>
            </view>
          </view>
          
          <!-- 工单列表 - 移到下方 -->
          <view class="table-container order-table">
            <div v-if="isLoadingOrders" class="loading-container">
              <text class="loading-text">正在加载样品单数据...</text>
            </div>
            <div v-else class="table-wrapper">
              <table class="order-content-wrapper">
                <thead class="order-header-container">
                  <tr class="order-header">
                    <th class="th th-checkbox"><checkbox :disabled="true" /></th>
                    <th class="th th-status">勾選註記</th>
                    <th class="th th-order">样品单号</th>
                    <th class="th th-model">图片</th>
                    <th class="th th-type">开发类型</th>
                    <th class="th th-date">出货日期</th>
                    <th class="th th-qty">订单数量</th>
                    <th class="th th-bar">已投入待产出数量</th>
                    <th class="th th-pph">pph</th>
                    <th class="th th-rate">达成率</th>
                    <th class="th th-t2">底料状况</th>
                    <th class="th th-prd">日期</th>
                    <th class="th th-dept">制作组别</th>
                    <th class="th th-leader">制作人</th>
                    <th class="th th-desc">问题描述</th>
                  </tr>
                </thead>
                <tbody class="order-body">
                  <tr 
                    v-for="(order, index) in filteredOrderList" 
                    :key="index" 
                    class="order-row">
                    <td class="td th-checkbox">
                      <checkbox 
                        :checked="order.se_flag === 'Y'" 
                        @click="toggleOrderSelection(order)"
                      />
                    </td>
                    <td class="td th-status">{{ order.se_flag === 'Y' ? '已选' : '未选' }}</td>
                    <td class="td th-order">{{ order.ord_no }}</td>
                    <td class="td th-model">
                      <image 
                        v-if="order.model_pic" 
                        :src="'data:image/png;base64,' + order.model_pic"
                        mode="aspectFit" 
                        class="model-pic"
                        @click="previewImage(order.model_pic)"
                      />
                      <text v-else>无图片</text>
                    </td>
                    <td class="td th-type">{{ order.dev_type }}</td>
                    <td class="td th-date">{{ formatDate(order.shp_date) }}</td>
                    <td class="td th-qty">{{ order.tot_qty }}</td>
                    <td class="td th-bar">{{ order.bar_qty }}</td>
                    <td class="td th-pph">{{ order.run_rate }}</td>
                    <td class="td th-rate">{{ order.rate1 }}%</td>
                    <td class="td th-rate">{{ order.t2_flag }}</td>
                    <td class="td th-prd">{{ formatDate(order.prd_date) }}</td>
                    <td class="td th-dept">{{ order.made_dept }}</td>
                    <td class="td th-leader">{{ order.emp_name }}</td>
                    <td class="td th-desc">{{ order.pb_desc }}</td>
                  </tr>
                  <tr v-if="!isLoadingOrders && filteredOrderList.length === 0">
                    <td colspan="14" class="empty-message">
                      <text>{{ selectedGroup ? '无样品单数据' : '请选择组别查看样品单' }}</text>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 添加自定义图片预览组件 -->
    <image-preview 
      v-model:visible="previewVisible" 
      :imageUrl="previewImageUrl"
    />
    
    <!-- 添加高级图片弹出层组件 -->
    <image-popup
      v-model:show="popupVisible"
      :imageUrl="previewImageUrl"
    />
  </view>
</template>

<script>
import { getGroupList, getOrderList, updateOrderSelection, updateGroupTarget } from '@/api/workorder';
import ImagePreview from '@/components/ImagePreview.vue';
import ImagePopup from '@/components/ImagePopup.vue';

export default {
  components: {
    ImagePreview,
    ImagePopup
  },
  data() {
    return {
      currentDate: '',
      selectedDate: '',
      areas: [
        { name: 'A1区域', code: 'A1' },
        { name: 'A2区域', code: 'A2' },
        { name: 'B1区域', code: 'B1' },
        { name: 'B2区域', code: 'B2' },
        { name: 'C1区域', code: 'C1' },
        { name: 'C2区域', code: 'C2' }
      ],
      selectedarea: '',
      groupList: [],
      filteredGroupList: [],
      groupFilter: '',
      selectedGroup: '',
      orderList: [],
      filteredOrderList: [],
      orderFilter: '',
      isLandscape: false, // 添加横屏检测标志
      previewVisible: false,
      popupVisible: false,
      previewImageUrl: '',
      targetPPH: '', // 添加目标PPH输入框的数据绑定
      isLoadingOrders: false // 新增：用于控制加载状态
    }
  },
  computed: {
    selectedOrderCount() {
      return this.orderList.filter(order => order.se_flag === 'Y').length;
    },
    selectedOrderQuantity() {
      return this.orderList
        .filter(order => order.se_flag === 'Y')
        .reduce((sum, order) => sum + Number(order.run_rate || 0), 0);
    },
    // 新增：计算组别列表的总数量
    totalGroupQuantity() {
      return this.filteredGroupList.reduce((sum, group) => sum + Number(group.qty || 0), 0);
    }
  },
  onLoad() {
    this.formatCurrentDate();
    // 设置默认日期为今天
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    this.selectedDate = `${year}-${month}-${day}`;
    
    // 检测初始屏幕方向
    this.checkOrientation();
    
    // 从本地存储获取上次选择的区域
    try {
      const savedArea = uni.getStorageSync('selectedWorkorderArea');
      if (savedArea) {
        this.selectedarea = savedArea;
        this.fetchGroupList(); // 自动加载该区域的组别列表
      }
    } catch (e) {
      console.error('获取保存的区域失败:', e);
    }
  },
  onReady() {
    // 使用uni-app的窗口监听API
    uni.onWindowResize(() => {
      this.checkOrientation();
    });
  },
  onUnload() {
    // 移除uni-app的窗口监听
    uni.offWindowResize();
  },
  watch: {
    // 移除订单列表变化时的列宽调整
    filteredOrderList: {
      handler() {
        // 不再需要调整列宽
      },
      deep: true
    }
  },
  methods: {
    // 处理窗口大小变化
    handleResize() {
      this.checkOrientation();
    },
    
    // 检测屏幕方向
    checkOrientation() {
      // 使用uni-app的API获取系统信息
      uni.getSystemInfo({
        success: (res) => {
          // 通过屏幕宽高比判断是否为横屏
          const isLandscape = res.windowWidth > res.windowHeight;
          
          // 更新方向状态
          this.isLandscape = isLandscape;
          
          // 添加/移除横屏类 - 使用uni-app兼容的方式
          if (isLandscape) {
            // 在H5环境中可以使用document
            // #ifdef H5
            document.body.classList.add('landscape-mode');
            // #endif
          } else {
            // #ifdef H5
            document.body.classList.remove('landscape-mode');
            // #endif
          }
        }
      });
    },
    
    formatCurrentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      this.currentDate = `${year}年${month}月${day}日`;
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    selectarea(areaCode) {
      this.selectedarea = areaCode;
      this.selectedGroup = '';
      this.orderList = [];
      this.filteredOrderList = [];
      this.fetchGroupList();
      
      // 保存用户选择的区域到本地存储
      uni.setStorageSync('selectedWorkorderArea', areaCode);
    },
    async fetchGroupList() {
      try {
        // 实际项目中调用API
        const res = await getGroupList(this.selectedarea, this.selectedDate);
        this.groupList = res.data;
        this.filteredGroupList = [...this.groupList];
      } catch (error) {
        uni.showToast({
          title: '获取组别列表失败',
          icon: 'none'
        });
      }
    },
    filterGroups() {
      if (!this.groupFilter) {
        this.filteredGroupList = [...this.groupList];
        return;
      }
      
      const filter = this.groupFilter.toLowerCase();
      this.filteredGroupList = this.groupList.filter(group => 
        group.made_dept.toLowerCase().includes(filter) || 
        group.emp_name.toLowerCase().includes(filter) ||
        group.area.toLowerCase().includes(filter)
      );
    },
    selectGroup(groupNo) {
      this.selectedGroup = groupNo;
      this.fetchOrderList();
    },
    async fetchOrderList() {
      this.isLoadingOrders = true; // 开始加载
      this.orderList = []; // 清空旧数据，避免闪烁
      this.filteredOrderList = [];
      
        // 同时触发左边的组别查询，确保数量同步
      if (this.selectedarea) {
        await this.fetchGroupList();
      }
      try {
        const res = await getOrderList(this.selectedGroup, this.selectedDate);
        this.orderList = res.data;
        this.filteredOrderList = [...this.orderList];
      } catch (error) {
        uni.showToast({
          title: '获取样品单列表失败',
          icon: 'none'
        });
      } finally {
        this.isLoadingOrders = false; // 结束加载
      }
    },
    filterOrders() {
      if (!this.orderFilter) {
        this.filteredOrderList = [...this.orderList];
        return;
      }
      
      const filter = this.orderFilter.toLowerCase();
      this.filteredOrderList = this.orderList.filter(order => 
        order.ord_no.toLowerCase().includes(filter) || 
        order.made_dept.toLowerCase().includes(filter) ||
        order.emp_name.toLowerCase().includes(filter) ||
        (order.dev_type && order.dev_type.toLowerCase().includes(filter)) ||
        (order.pb_desc && order.pb_desc.toLowerCase().includes(filter))
      );
    },
    onDateChange(e) {
      this.selectedDate = e.detail.value;
      // 如果已选择区域，则重新获取组别列表
      if (this.selectedarea) {
        this.fetchGroupList();
        // 清空已选择的组别，因为日期变化后组别数据可能变化
        this.selectedGroup = '';
        this.orderList = [];
        this.filteredOrderList = [];
      }
      // 如果已选择组别，则重新获取样品单列表
      if (this.selectedGroup) {
        this.fetchOrderList();
      }
    },
    async toggleOrderSelection(order) {
      const newFlag = order.se_flag === 'Y' ? 'N' : 'Y';
      
      try {
        // 实际项目中调用API
        await updateOrderSelection({
          ordNo: order.ord_no,
          seFlag: newFlag,
          ordQty: order.bar_qty
        });
        
        // 更新本地数据
        order.se_flag = newFlag;
        
        uni.showToast({
          title: newFlag === 'Y' ? '已选择样品单' : '已取消选择',
          icon: 'none'
        });
      } catch (error) {
        uni.showToast({
          title: '更新样品单选择状态失败',
          icon: 'none'
        });
      }
    },
    async saveTargetPPH() {
      try {
        // 使用输入框中的目标PPH值，如果为空则默认为0
        const newTargetPPH = this.targetPPH || 0;
        
        // 实际项目中调用API
        await updateGroupTarget({
          deptNo: 'SP0100303',
          madeDept: this.selectedGroup,
          runRate: newTargetPPH,
          date: this.selectedDate
        });
        
        uni.showToast({
          title: '更新PPH成功',
          icon: 'success'
        });
        
        // 更新成功后重新获取工单列表
        this.fetchOrderList();
      } catch (error) {
        uni.showToast({
          title: '更新目标PPH失败',
          icon: 'none'
        });
      }
    },
    previewImage(base64) {
      if (!base64) return;
      // 将base64数据转换为完整图片URL
      this.previewImageUrl = 'data:image/png;base64,' + base64;
      
      // 使用高级弹出层而不是简单预览
      this.popupVisible = true;
      // 如果想使用简单预览，可以取消下面的注释
      // this.previewVisible = true;
      
      // 保留原来的代码作为备用方案
      // uni.previewImage({
      //   urls: [imageUrl]
      // });
    },
    getColumnStyle(column) {
      return {}; // 返回空对象，让CSS处理宽度
    },
    goBack() {
      let back = getCurrentPages();
      if (back && back.length > 1) {
        uni.navigateBack({
          delta: 1
        });
      } else {
        // 在浏览器环境中，如果 uni.navigateBack 不适用，尝试使用 Vue Router
        if (this.$router) {
          this.$router.go(-1);
        } else {
          // 作为备用方案，仍然使用 history.back()
          history.back();
        }
      }
    }
  }
}
</script>

<style>
.container {
  padding: 20px;
  height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f9fafb;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 横屏模式样式 */
.landscape-mode .container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
  padding: 10px 15px;
  background: linear-gradient(135deg, #fdf6e3, #eee8d5);
  color: #657b83;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-left, .header-right {
  flex: 1;
  display: flex;
  align-items: center;
}

.header-left {
  justify-content: flex-start;
  padding-left: 0;
}

.header-center {
  flex: 2;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-right {
  justify-content: flex-end;
}

.back-btn {
  display: flex;
  align-items: center;
  background-color: rgba(101, 123, 131, 0.2);
  border: none;
  color: #657b83;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: 0;
}

.back-btn:hover {
  background-color: rgba(101, 123, 131, 0.3);
}

.back-icon {
  font-size: 18px;
  margin-right: 5px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 1px;
  text-align: center;
}

.date {
  font-size: 16px;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 5px 10px;
  border-radius: 4px;
}

.content {
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: 20px;
}

.left-panel {
  width: 30%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.right-panel {
  width: 70%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #eee8d5;
  color: #657b83;
}

.section-title-with-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #eee8d5;
  color: #657b83;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-input {
  width: 150px;
  height: 36px;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0 12px;
  font-size: 14px;
  background-color: #f5f7fa;
  transition: all 0.3s;
}

.filter-input:focus {
  border-color: #93a1a1;
  box-shadow: 0 0 0 3px rgba(147, 161, 161, 0.1);
  outline: none;
}

.area-selection {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.area-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.area-btn {
  padding: 8px 15px;
  background-color: #fdf6e3;
  border: 1px solid #eee8d5;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  flex: 1 0 calc(33.333% - 10px);
  min-width: 80px;
  text-align: center;
}

.area-btn:hover {
  background-color: #eee8d5;
  transform: translateY(-1px);
}

.area-btn.active {
  background-color: #93a1a1;
  color: white;
  border-color: #93a1a1;
  box-shadow: 0 2px 5px rgba(147, 161, 161, 0.3);
}

.group-list {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.order-list {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.scroll-container {
  flex: 1;
  overflow: hidden;
}

.horizontal-scroll-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  display: block;
  position: relative;
  scrollbar-width: thin;
  scrollbar-color: #c1c9d6 #f5f7fa;
}

.horizontal-scroll-container::-webkit-scrollbar {
  height: 8px;
}

.horizontal-scroll-container::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 4px;
}

.horizontal-scroll-container::-webkit-scrollbar-thumb {
  background-color: #c1c9d6;
  border-radius: 4px;
}

/* 表格通用样式 */
.table-container {
  border: 1px solid #e0e7ff;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.03);
}

.table-header {
  display: table;
  background: linear-gradient(to bottom, #fdf6e3, #eee8d5);
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 1;
  border-bottom: 2px solid #eee8d5;
  table-layout: auto;
  width: 100%;
}

.table-row {
  display: table-row;
  border-top: 1px solid #edf2ff;
  cursor: pointer;
  transition: background-color 0.15s;
}

.table-row:hover {
  background-color: #f5f5eb;
}

.active-row {
  background-color: #eee8d5;
  border-left: 3px solid #93a1a1;
  position: relative;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selected-indicator {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #657b83;
  font-weight: bold;
  font-size: 16px;
}

.group-row {
  position: relative;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

.group-row:hover {
  background-color: #f5f5eb;
  transform: translateX(2px);
}

.group-row.active-row {
  background-color: #eee8d5;
  transform: translateX(3px);
  z-index: 1;
}

.group-row.active-row .td {
  color: #000;
}

.group-row.active-row::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #93a1a1;
}

.th, .td {
  display: table-cell;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
  padding: 12px 15px;
  vertical-align: middle;
}

.th {
  color: #657b83;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.empty-message {
  padding: 30px;
  text-align: center;
  color: #8c9db5;
  font-size: 15px;
  font-style: italic;
  background-color: #f9fafb;
  border-radius: 6px;
  margin: 10px;
}

/* 组别列表样式 */
.group-table {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
}

.group-header-container {
  width: 100%;
}

.group-header {
  display: flex;
  width: 100%;
}

.group-body {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.group-row {
  display: flex;
  width: 100%;
}

.th-group-name, .td-group-name {
  flex: 1;
  min-width: 80px;
  padding: 12px 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.th-group-dept, .td-group-dept {
  flex: 2;
  min-width: 120px;
  padding: 12px 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.th-group-qty, .td-group-qty {
  flex: 0.5;
  min-width: 60px;
  padding: 12px 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.group-scroll-container {
  flex: 1;
  height: calc(100% - 48px);
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: #c1c9d6 #f5f7fa;
}

.group-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.group-scroll-container::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 4px;
}

.group-scroll-container::-webkit-scrollbar-thumb {
  background-color: #c1c9d6;
  border-radius: 4px;
}

/* 样品单列表样式 */
.order-table {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  position: relative;
  height: calc(100vh - 300px);
}

.table-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.order-content-wrapper {
  min-width: 100%;
  display: table;
  border-collapse: separate;
  border-spacing: 0;
  table-layout: auto;
  width: 100%;
}

.order-header-container {
  position: sticky;
  top: 0;
  z-index: 10;
  background: linear-gradient(to bottom, #fdf6e3, #eee8d5);
  display: table-header-group;
}

.order-header {
  display: table-row;
  background: linear-gradient(to bottom, #fdf6e3, #eee8d5);
}

.order-body {
  display: table-row-group;
}

.order-row {
  display: table-row;
  border-bottom: 1px solid #eee8d5;
}

.order-row:nth-child(even) {
  background-color: #faf8f0;
}

/* 列宽设置 - 使用width:1%确保最小宽度，但允许根据内容扩展 */
.th-checkbox, .td.th-checkbox {
  width: 1%;
  white-space: nowrap;
  text-align: center;
}

.th-status, .td.th-status {
  width: 1%;
  white-space: nowrap;
}

.td.th-status {
  font-weight: 500;
}

.th-dept, .td.th-dept,
.th-leader, .td.th-leader {
  width: 1%;
  white-space: nowrap;
}

.th-order, .td.th-order {
  width: 1%;
  white-space: nowrap;
}

.th-model, .td.th-model {
  width: 1%;
  white-space: nowrap;
  text-align: center;
  padding: 12px 5px;
}

.th-type, .td.th-type {
  width: 1%;
  white-space: nowrap;
}

.th-date, .td.th-date,
.th-prd, .td.th-prd {
  width: 1%;
  white-space: nowrap;
  color: #657b83;
}

.th-qty, .td.th-qty,
.th-bar, .td.th-bar,
.th-pph, .td.th-pph,
.th-rate, .td.th-rate {
  width: 1%;
  white-space: nowrap;
  font-weight: 500;
}

.th-desc, .td.th-desc {
  width: auto;
  text-align: left;
}

/* 确保表头和表体列宽一致 */
.order-header-container th,
.order-body td {
  box-sizing: border-box;
}

/* 自定义滚动条样式 */
.table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background-color: #c1c9d6;
  border-radius: 4px;
}

.table-wrapper {
  scrollbar-width: thin;
  scrollbar-color: #c1c9d6 #f5f7fa;
}

.model-pic {
  width: 50px;
  height: 50px;
  object-fit: contain;
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.model-pic:hover {
  transform: scale(1.1);
}

.picker-text {
  padding: 8px 12px;
  background-color: #fdf6e3;
  border-radius: 6px;
  font-size: 14px;
  border: 1px solid #eee8d5;
  color: #657b83;
  cursor: pointer;
  transition: all 0.2s;
}

.picker-text:hover {
  background-color: #eee8d5;
}

/* 底部汇总区域 - 修改为顶部操作区域 */
.summary-section {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  background-color: #eee8d5;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.03);
  width: 100%;
}

.summary-items {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.button-container {
  display: flex;
  align-items: center;
  margin-right: 50rpx;
}

.target-pph-input {
  width: 140px;
  height: 42px;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0 12px;
  margin-right: 10px;
  font-size: 14px;
  background-color: #f5f7fa;
  transition: all 0.3s;
}

.target-pph-input:focus {
  border-color: #93a1a1;
  box-shadow: 0 0 0 3px rgba(147, 161, 161, 0.1);
  outline: none;
}

.summary-item {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 8px 15px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 5px;
}

.summary-label {
  font-weight: bold;
  margin-right: 8px;
  color: #657b83;
  white-space: nowrap;
}

.summary-value {
  font-weight: 500;
  color: #333;
}

.save-btn {
  background: linear-gradient(135deg, #93a1a1, #657b83);
  color: white;
  padding: 8px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 5px rgba(147, 161, 161, 0.3);
  white-space: nowrap;
}

/* 响应式布局 */
@media screen and (max-width: 1200px) and (orientation: portrait) {
  .content {
    flex-direction: column;
  }
  
  .left-panel, .right-panel {
    width: 100%;
  }
  
  .left-panel {
    max-height: 300px;
    margin-bottom: 20px;
  }
  
  .order-scroll-container {
    height: calc(100vh - 500px);
  }
  
  .summary-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .summary-item {
    width: 100%;
  }
  
  .save-btn {
    align-self: flex-start;
    margin-top: 5px;
  }
}

/* 小屏幕设备特定样式 */
@media screen and (max-width: 768px) {
  .container {
    padding: 10px;
  }
  
  .header {
    padding: 8px 10px;
    margin-bottom: 10px;
  }
  
  .title {
    font-size: 18px;
  }
  
  .date {
    font-size: 12px;
    padding: 3px 6px;
  }
  
  .back-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  .back-icon {
    font-size: 14px;
  }
  
  .section-title, .section-title-with-filter {
    font-size: 16px;
    margin-bottom: 10px;
    padding-bottom: 8px;
  }
  
  .filter-input {
    width: 120px;
    height: 30px;
    font-size: 12px;
  }
  
  .area-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .th, .td {
    padding: 8px 10px;
    font-size: 12px;
  }
  
  .group-scroll-container {
    height: calc(100vh - 350px);
  }
  
  .summary-section {
    padding: 10px;
    gap: 10px;
  }
  
  .summary-item {
    padding: 6px 10px;
    font-size: 12px;
  }
  
  .save-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}

/* 横屏模式下的响应式布局 */
@media screen and (orientation: landscape) {
  .content {
    flex-direction: row;
  }
  
  .left-panel {
    width: 30%;
    max-height: none;
    margin-bottom: 0;
    min-width: 250px; /* 确保左侧面板有最小宽度 */
  }
  
  .right-panel {
    width: 70%;
  }
  
  .group-scroll-container {
    height: calc(100% - 48px);
  }
  
  .order-scroll-container {
    height: calc(100vh - 200px);
  }
  
  /* 确保组别列表内容完整显示 */
  .th-group-name, .td-group-name {
    min-width: 60px;
  }
  
  .th-group-dept, .td-group-dept {
    min-width: 100px;
  }
  
  .th-group-qty, .td-group-qty {
    min-width: 40px;
  }
  
  /* 调整表格在横屏模式下的列宽 */
  .th-checkbox, .td.th-checkbox {
    min-width: 50px;
  }
  
  .th-status, .td.th-status {
    min-width: 70px;
  }
  
  .th-dept, .td.th-dept,
  .th-leader, .td.th-leader {
    min-width: 90px;
  }
  
  .order-table {
    height: calc(100vh - 200px);
  }
  
  /* 调整合计行内边距 */
  .th, .td, .group-footer .td {
    padding: 10px 8px;
    font-size: 14px;
  }
}

/* 小屏幕横屏特定样式 */
@media screen and (max-height: 769px) and (orientation: landscape) {
  .container {
    padding: 8px;
  }
  
  .header {
    margin-bottom: 8px;
    padding: 5px 10px;
  }
  
  .title {
    font-size: 16px;
  }
  
  .content {
    gap: 10px;
  }
  
  .left-panel, .right-panel {
    padding: 10px;
  }
  
  .section-title, .section-title-with-filter {
    margin-bottom: 8px;
    padding-bottom: 5px;
    font-size: 14px;
  }
  
  .area-selection {
    margin-bottom: 10px;
  }
  
  .area-btn {
    padding: 5px 10px;
    font-size: 12px;
  }
  
  .th, .td {
    padding: 10px 8px;
    font-size: 14px;
  }
  
  .group-scroll-container, .order-scroll-container {
    height: calc(100vh - 150px);
  }
  
  .summary-section {
    margin-top: 10px;
    padding: 8px;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  
  .summary-item {
    margin-bottom: 0;
    flex: 1 1 auto;
    min-width: 120px;
  }
  
  .save-btn {
    margin-left: auto;
  }
  
  .order-table {
    height: calc(100vh - 150px);
  }
}

/* 平板横屏特定样式 */
@media screen and (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  .container {
    padding: 15px;
  }
  
  .header {
    margin-bottom: 15px;
  }
  
  .title {
    font-size: 22px;
  }
  
  .left-panel {
    width: 35%;
  }
  
  .right-panel {
    width: 65%;
  }
}

/* iPad Pro 特定样式 */
@media screen and (min-width: 1024px) and (max-width: 1366px) and (orientation: landscape) {
  .container {
    padding: 20px;
  }
  
  .left-panel {
    width: 30%;
  }
  
  .right-panel {
    width: 70%;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%; /* 使其充满父容器高度 */
  min-height: 150px; /* 设置最小高度，避免加载时过小 */
  width: 100%;
}

.loading-text {
  font-size: 16px;
  color: #8c9db5;
  font-style: italic;
}

/* 新增：表格页脚样式 */
.table-footer {
  display: flex; /* 使用 flex 布局 */
  background-color: #f0f4f8; /* 页脚背景色 */
  font-weight: bold;
  border-top: 2px solid #eee8d5; /* 顶部边框，与表头分隔 */
  padding: 0; /* 移除内边距，让单元格控制 */
  width: 100%; /* 页脚宽度与表格一致 */
}

/* 新增：组别列表页脚特定样式 */
.group-footer .td {
  display: flex; /* 让内部文本垂直居中 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  padding: 12px 15px; /* 与表头/表体单元格一致 */
  box-sizing: border-box; /* 包含内边距和边框 */
  color: #586e75; /* 合计文本颜色 */
}

/* 覆盖组别列表页脚的特定列样式 */
.group-footer .td-group-name {
  flex: 1; /* 与表头/表体对应列的 flex 保持一致 */
  min-width: 80px;
  justify-content: flex-start; /* 如果需要靠左对齐 */
}
.group-footer .td-group-dept {
  flex: 2;
  min-width: 120px;
  justify-content: flex-end; /* 合计标签靠右 */
  padding-right: 20px; /* 增加右边距 */
}
.group-footer .td-group-qty {
  flex: 0.5;
  min-width: 60px;
  justify-content: center; /* 总数居中 */
}
</style> 