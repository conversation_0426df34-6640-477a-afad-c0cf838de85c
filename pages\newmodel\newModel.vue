<template>
    <view class="container">
        <!-- 表头 -->
        <view class="header">
            <view class="back">
                <uni-icons @click="back" type="back" size="36"></uni-icons>
            </view>
            <view class="title">
                <text>{{title.title}}</text>
            </view>
            <view class="subtitle">
                <text class="left">{{title.series}}</text>
                <text class="center">{{title.chn_sim}}</text>
                <text style="position: absolute;right: 3vw;font-size: 18px;">{{title.ord_seq}}</text>
                <image src="../../static/SL3.png" class="right"></image>
            </view>
            <view class="info">
                <text>{{title.pd_line}}</text>
                <view class="right-info">
                    <text>{{ordNoFrontPart}}</text><text style="font-size: 24px;">{{ordNoLastThree}}</text>
                </view>
            </view>
        </view>
        <view class="table1">
            <!-- 第一个大框  -->
            <view class="table1line1">
                <view class="checkbox-group">
                    <label>裁断：</label>
                    <view class="checkbox-group">
						<checkbox :checked="caiduanCheckboxes[0]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>鞋面
						<checkbox :checked="caiduanCheckboxes[1]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>中底皮
						<checkbox :checked="caiduanCheckboxes[2]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>中底
						<checkbox :checked="caiduanCheckboxes[3]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>大底
						<checkbox :checked="caiduanCheckboxes[4]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>滾條/頭片
						<checkbox :checked="caiduanCheckboxes[5]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>跟皮
						<checkbox :checked="caiduanCheckboxes[6]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>發外
                    </view>
                </view>
                <view class="checkbox-group" style="border-left: 1px solid black;padding-left: 2px;">
                    <label>鞋面：</label>
                    <view class="checkbox-group">
						<checkbox :checked="caiduanCheckboxes[7]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>鞋面
						<checkbox :checked="caiduanCheckboxes[8]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>發外
                    </view>
                </view>
                <view class="checkbox-group" style="border-left: 1px solid black;padding-left: 2px;">
                    <label>半成品：</label>
                    <view class="checkbox-group">
						<checkbox :checked="caiduanCheckboxes[9]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>中底皮
						<checkbox :checked="caiduanCheckboxes[10]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>中底
						<checkbox :checked="caiduanCheckboxes[11]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>包粘
						<checkbox :checked="caiduanCheckboxes[12]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>大底
                    </view>
                </view>
                <view class="checkbox-group" style="border-left: 1px solid black;padding-left: 2px;">
                    <label>成型：</label>
                    <view class="checkbox-group">
						<checkbox :checked="caiduanCheckboxes[13]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>成型
                    </view>
                </view>
            </view>
            <!-- 第二个 -->
            <view class="table1line2">
                <table class="custom-table1" style="width:100%">
                    <tr class="custom-tr1">
                        <th class="custom-th1" colspan="2" style="width: 23%;">型體型號</th>
                        <th class="custom-th1" style="width: 10%;">季節名稱</th>
                        <th class="custom-th1" style="width: 8%;">樣品類型</th>
                        <th class="custom-th1" style="width: 10%;">楦頭編號</th>
                        <th class="custom-th1" style="width: 12%;">VR</th>
                        <td class="custom-td1" rowspan="4" colspan="2" style="width: 37%;">
							<img
								:src="'data:image/jpg;base64,' + title.ord_qr"
								alt=""
								class="qr-image"
							/>
							<img
								:src="'data:image/jpg;base64,' + title.model_pic"
								alt=""
								class="qr-image"
							/>
                        </td>
                    </tr>
                    <tr class="custom-tr1">
                        <td class="custom-td1" colspan="2">{{title.model_desc}}</td>
                        <td class="custom-td1">{{title.season_desc}}</td>
                        <td class="custom-td1">{{title.dev_type}}</td>
                        <td class="custom-td1">{{title.last_no}}</td>
                        <td class="custom-td1" style="display: flex;border: none;padding: 8px 2px;">
							<checkbox :checked="modelCheckboxes[0]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>建模
							<checkbox :checked="modelCheckboxes[1]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>渲染
                        </td>
                    </tr>
                    <tr class="custom-tr1">
                        <th class="custom-th1">SKU編號</th>
                        <th class="custom-th1">顏色描述</th>
                        <th class="custom-th1">鞋組名稱</th>
                        <th class="custom-th1">結構</th>
                        <th class="custom-th1">客户订单</th>
                        <th class="custom-th1">版師</th>
                    </tr>
                    <tr class="custom-tr1">
                        <td class="custom-td1">{{title.sku_no}}</td>
                        <td class="custom-td1">{{title.color_desc}}</td>
                        <td class="custom-td1">{{title.module_desc}}</td>
                        <td class="custom-td1">{{title.constr}}</td>
                        <td class="custom-td1"></td>
                        <td class="custom-td1" style="display: flex;border: none;padding: 8px 2px;">
							<checkbox :checked="banShiCheckboxes[0]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>面版
							<checkbox :checked="banShiCheckboxes[1]" borderColor="#767676" activeBackgroundColor="#000" color="#ffffff" style="transform:scale(0.6)"/>底版
                        </td>
                    </tr>
                </table>
            </view>
            <!-- 第三个框，尺码 -->
            <view class="table1line2" style="margin-top: 5px;">
                <table class="custom-table2" border="1">
                    <thead>
                        <tr class="custom-tr2">
                            <th class="custom-th2" v-for="(header, index) in headers" :key="index">{{ header }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="custom-tr2" v-for="(row, rowIndex) in rows" :key="rowIndex">
                            <td class="custom-td2" v-for="(cell, cellIndex) in row" :key="cellIndex">{{ cell }}</td>
                        </tr>
                        <tr class="custom-tr2">
                            <td class="custom-td2" colspan="24">
                                備注：{{title.remark}}
                            </td>
                            <!-- 合计结果 -->
                            <td class="custom-td2">
								{{sizeData.sizeCount}}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </view>
            <view class="table1line2" style="margin-top: 5px;">
                <table class="custom-table3" border="1">
                    <thead>
                        <tr class="custom-tr3">
                            <th class="custom-th3" style="width:10vw">部位編碼</th>
                            <th class="custom-th3" style="width:10vw">部位描述</th>
                            <th class="custom-th3" style="width:8vw">物料編號</th>
                            <th class="custom-th3" style="width:35vw">材料名稱規格及顔色</th>
                            <th class="custom-th3" style="width:5vw">單位</th>
                            <th class="custom-th3" style="width:5vw">單用量</th>
                            <th class="custom-th3" style="width:5vw">需求量</th>
                            <th class="custom-th3" style="width:10vw">廠商簡稱</th>
                            <th class="custom-th3" style="width:10vw">備注事項</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="custom-tr3" v-for="(row, rowIndex) in table3" :key="rowIndex">
                            <td class="custom-td3">{{row.part_no}}</td>
                            <td class="custom-td3">{{row.part_name}}</td>
                            <td class="custom-td3">{{row.mat_seq}}</td>
                            <td class="custom-td3">{{row.mat_desc}}</td>
                            <td class="custom-td3">{{row.suom}}</td>
                            <td class="custom-td3">{{row.dev_qtya}}</td>
                            <td class="custom-td3">{{row.req_qty}}</td>
                            <td class="custom-td3">{{row.vnd_sim}}</td>
                            <td class="custom-td3">{{row.remark}}</td>
                        </tr>
                    </tbody>
                </table>
            </view>
            <view class="table1line2" style="margin-top: 5px;">
                <table class="custom-table4" border="1">
                    <tr class="custom-tr4">
                        <td class="custom-td4">中底材料</td>
                        <td class="custom-td4" colspan="5">{{title.mb_mater}}</td>
                        <td class="custom-td4">插中材料(上)</td>
                        <td class="custom-td4" colspan="4">{{title.iu_mater}}</td>
                    </tr>
                    <tr>
                        <td class="custom-td4">EVA材料</td>
                        <td class="custom-td4" colspan="5">{{title.eva_mater}}</td>
                        <td class="custom-td4">插中材料(下)</td>
                        <td class="custom-td4" colspan="4">{{title.id_mater}}</td>
                    </tr>
                    <tr>
                        <td class="custom-td4" rowspan="2">飛機做法</td>
                        <td class="custom-td4" rowspan="2">{{title.air_pra}}</td>
                        <td class="custom-td4">削插中長度</td>
                        <td class="custom-td4">{{title.cins_len}}</td>
                        <td class="custom-td4">削插中刀輪</td>
                        <td class="custom-td4">{{title.dw_tic}}</td>
                        <td class="custom-td4">上下插中長度</td>
                        <td class="custom-td4">{{title.ins_len}}</td>
                        <td class="custom-td4">定型模具</td>
                        <td class="custom-td4">{{title.stampo}}</td>
                    </tr>
                    <tr>
                        <td class="custom-td4">馬口鐵材料</td>
                        <td class="custom-td4" colspan="2">{{title.tin_mater}}</td>
                        <td class="custom-td4">鐵芯編號</td>
                        <td class="custom-td4" colspan="2">{{title.core_num}}</td>
                        <td class="custom-td4">鐵心后距</td>
                        <td class="custom-td4">{{title.core_dist}}</td>
                    </tr>
                    <tr>
                        <td class="custom-td4">中底斜度</td>
                        <td class="custom-td4">{{title.fresa}}</td>
                        <td class="custom-td4">跟高</td>
                        <td class="custom-td4">{{title.with_high}}</td>
                        <td class="custom-td4">中底檔案備注</td>
                        <td class="custom-td4" colspan="6">{{title.bot_rmk}}</td>
                    </tr>
                    <tr>
                        <td class="custom-td4">版師</td>
                        <td class="custom-td4">{{title.sole_der}}</td>
                        <td class="custom-td4">中底削邊</td>
                        <td class="custom-td4">{{title.mb_trim}}</td>
                        <td class="custom-td4">中底做法備註</td>
                        <td class="custom-td4" colspan="6">{{title.bot_method}}</td>
                    </tr>
                </table>
            </view>
            <view class="table1line2" style="margin-top: 5px;">
                <table class="custom-table4" border="1">
                    <tr>
                        <th class="custom-th5">面部注意事項</th>
                        <th class="custom-th5">底部注意事項</th>
                    </tr>
                    <tr>
                        <td class="custom-td5">{{title.upp_desc}}</td>
                        <td class="custom-td5">{{title.sol_desc}}</td>
                    </tr>
                </table>
            </view>
        </view>
        <view class="bottom">
            <text>主管核准：</text>
            <text>面部版師：{{title.dev_upper}}</text>
            <text>底部版師：{{title.dev_sole}}</text>
            <text>業務：{{title.dutyer}}</text>
        </view>
    </view>
</template>

<script>
	import { onLoad, onShow } from '@dcloudio/uni-app'
	import urlPrefix from '@/pages/common/urlPrefix.js'
	export default {
		name: 'Header',
		data() {
			return {
				info: '',
				ordNo: '',
				title: '',
				ordNoFrontPart:'',
				ordNoLastThree:'',
				table2: '',
				table3: '',
				titleData: '',
				sizeData: '',
				checkgroupData: [],
				table1Data: '',
				headers: [],
				rows: [],
				caiduanCheckboxes: [],
				modelCheckboxes: [],
				banShiCheckboxes: []
			}
		},
		onShow(){
		/*	plus.screen.lockOrientation('landscape-primary');//锁定横屏*/
		},
		// onLoad 方法
		onLoad(props) {
		    let _that = this
			setTimeout(() => {
       /*         plus.screen.lockOrientation('landscape-primary');//锁定横屏
                var mpaasScanModule = uni.requireNativePlugin("Mpaas-Scan-Module")
                mpaasScanModule.mpaasScan({
                        // 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
                        'scanType':  ['qrCode','barCode'],
                        orientation: "landscape", // 设置扫码界面为横屏
                        // 是否隐藏相册，默认false不隐藏
                        'hideAlbum': false
                    },
                    (res) => {
                        this.ordNo  = res.resp_result;
                        this.getData();
                    }
                );*/
                // 只允许通过相机扫码
                uni.scanCode({
                    onlyFromCamera: true,
                    autoZoom:false,
                    success: function (res) {
                      /*  uni.showToast({
                            icon: 'success',
                            title: res.scanType
                        })
                        uni.showToast({
                            icon: 'success',
                            title:  res.result
                        })*/
                        _that.ordNo  = res.result;
                        _that.getData();
                    }
                });
			}, 500); // 这里的1000表示延迟1秒
			// console.log(props)
		 //    // 解析传入的参数
		 //    this.ordNo = JSON.parse(props.info).ordNo;
				
		    // 处理异步操作
		    // this.getData();
		},
		methods: {
		    // 返回首页
		    back() {
		        let back = getCurrentPages();
		        if (back && back.length > 1) {
		            uni.navigateBack({
		                delta: 1
		            });
		        } else {
		            history.back();
		        }
		    },
		    // 获取数据
		    getData() {
		        let _that = this
				if(this.ordNo != ''){
					return new Promise((resolve, reject) => {
						uni.request({
							url: urlPrefix + "/newmodel/query",
							data: {
								"ordNo": this.ordNo
							},
							method: "GET"
						}).then(res => {
                        /*    uni.showToast({
                                icon: 'success',
                                title: res
                            })*/
                            console.log("scanres",res)
							if (res.statusCode == 200) {
                                _that.title = res.data.data.title;
                                _that.table3 = res.data.data.table3;
                                _that.sizeData = res.data.data.size;
                                _that.headers = res.data.data.size.headers;
                                _that.rows = res.data.data.size.rows;
								//设置裁断类型
                                _that.setCheckboxes();
								//处理样品单号
								const tempOrdNo = res.data.data.title.ord_no;
								// 提取后三位
                                _that.ordNoLastThree = tempOrdNo.slice(-3);
								
								// 提取前面的部分
                                _that.ordNoFrontPart = tempOrdNo.slice(0, -3);

								resolve();
							}
						}).catch(err => {
							console.log(err);
							uni.showToast({
								title: '获取数据失败..',
								icon: "error"
							});
							reject(err);
						});
					});
				}
		    },
		    setCheckboxes() {
		        const pro_sets = this.title.pro_sets;
		        if (pro_sets != null && pro_sets != '') {
		            const resultMap = {
		                'A': 0, 'B': 1, 'C': 2, 'D': 3, 'E': 4, 'F': 5, 'G': 6, 'H': 7,
		                'I': 8, 'J': 9, 'K': 10, 'L': 11, 'M': 12, 'N': 13
		            };
		            for (let char of pro_sets) {
		                const index = resultMap[char];
		                if (index !== undefined) {
		                    this.$set(this.caiduanCheckboxes, index, true);
		                }
		            }
		        }
		
		        const prov_sets = this.title.prov_sets;
		        if (prov_sets != null && prov_sets != '') {
		            const resultMap = {
		                'A': 0, 'B': 1
		            };
		            for (let char of prov_sets) {
		                const index = resultMap[char];
		                if (index !== undefined) {
		                    this.$set(this.modelCheckboxes, index, true);
		                }
		            }
		        }
		
		        const prox_sets = this.title.prox_sets;
		        if (prox_sets != null && prox_sets != '') {
		            const resultMap = {
		                'A': 0, 'B': 1
		            };
		            for (let char of prox_sets) {
		                const index = resultMap[char];
		                if (index !== undefined) {
		                    this.$set(this.banShiCheckboxes, index, true);
		                }
		            }
		        }
		    }
		}
	}
</script>

<style>
	page {
		width: 100%;
		height: 100%;
		padding: 2.5% 1% 1.5% 1%;
		box-sizing: border-box;
		background-color: #fdf6e3;
	}

	.back {
        width: 50px;
        height: 50px;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		position: absolute;
		left: 1%;
        top: 0.5%;
        border-radius: 50%;
        box-shadow: 0 0 5px gray;

		cursor: pointer;
		z-index: 1;
	}

	.container {
		width: 100%;
		height: 100%;
		padding-top: 1%;
		box-sizing: border-box;
		border-radius: 10px;
		box-shadow: 0 0 1px 5px #dddddd;
		position: relative;
		overflow-y: auto;
	}

	.title {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.subtitle {
		display: flex;
		justify-content: space-between;
		align-items: center;
		position: relative;
	}

	.subtitle .center {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
	}

	.subtitle .right {
		width: 2rem;
		height: 2rem;
	}

	.info {
		display: flex;
		justify-content: space-between;
	}

	.right-info {
		display: flex;
		flex-direction: row;
		align-items: flex-end;
	}

	.table1 {
        margin-top:15px;
		flex-wrap: nowrap;
	}

	.table1line1 {
		display: flex;
		border: 1px solid #000;
		font-size: 14px;
	}

	.checkbox-group {
		display: flex;
		flex-direction: row;
		/* 横向排列 */
		align-items: center;
		/* 垂直居中对齐 */
		margin-right: 0.2vw;
		/* 添加一些间距 */
	}

	.checkbox-group label,
	.checkbox-group input {
		/* margin-left: 0.5vw;
		margin-right: 0.2vw; */
	}
	
	.qr-image {
		max-width: 150px;
		max-height: 150px;
		margin-left: 2vw;
	}
	
	.shoe-image {
		width: 100px;
		height: auto;
	}

	/* 第一个表格 */
	.custom-table1 {
		border-collapse: collapse;
	}

	.custom-table1,
	.custom-th1,
	.custom-td1 {
		border: 1px solid black;
	}

	.custom-th1,
	.custom-td1 {
		padding: 8px;
		text-align: left;
		border: 1px solid black;
	}

	/* 第二个表格 */
	.custom-table2 {
		border-collapse: collapse;
	}

	.custom-table2,
	.custom-th2,
	.custom-td2 {
		border: 1px solid black;
	}

	.custom-th2,
	.custom-td2 {
		padding: 8px;
		text-align: left;
		border: 1px solid black;
		width: 5vw;
	}
	
	/* 第三个表格 */
	.custom-table3 {
		border-collapse: collapse;
	}
	
	.custom-table3,
	.custom-th3,
	.custom-td3 {
		border: 1px solid black;
		height:3vh
	}
	
	.custom-th3,
	.custom-td3 {
		padding: 8px;
		text-align: left;
		border: 1px solid black;
	}
	
	
	
	/* 第4个表格 */
	.custom-table4 {
		border-collapse: collapse;
	}
	
	.custom-table4,
	.custom-th4,
	.custom-td4 {
		border: 1px solid black;
	}
	
	.custom-th4,
	.custom-td4 {
		padding: 8px;
		text-align: left;
		border: 1px solid black;
		width: 10vw;
	}
	
	/* 第5个表格 */
	.custom-table5 {
		border-collapse: collapse;
		white-space: nowrap;
	}
	
	.custom-table5,
	.custom-th5,
	.custom-td5 {
		border: 1px solid black;
	}
	
	.custom-th5{
		padding: 8px;
		text-align: left;
		border: 1px solid black;
		width: 50vw;
	}
	
	.custom-td5 {
		padding: 8px;
		text-align: left;
		border: 1px solid black;
		height: 10vh;
		width: 50vw;
	}
	
	.bottom{
		display: flex;
		justify-content: space-between;
	}

</style>