package com.zqn.email.service;

import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.email.entity.EmailPushConfig;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送配置Service接口
 * @date 2025/01/22 10:00
 */
public interface EmailPushConfigService {

    /**
     * 分页查询邮件推送配置列表
     *
     * @param pageNo     页码
     * @param pageSize   页大小
     * @param configKey  配置键（模糊查询）
     * @param configType 配置类型
     * @param status     状态
     * @return 分页结果
     */
    R<PageInfo<EmailPushConfig>> queryConfigList(int pageNo, int pageSize, String configKey, 
                                                 String configType, String status);

    /**
     * 根据ID查询配置
     *
     * @param id 配置ID
     * @return 配置信息
     */
    R<EmailPushConfig> getConfigById(Long id);

    /**
     * 根据配置键查询配置
     *
     * @param configKey 配置键
     * @return 配置信息
     */
    R<EmailPushConfig> getConfigByKey(String configKey);

    /**
     * 根据配置键获取配置值
     *
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);

    /**
     * 根据配置键获取配置值（带默认值）
     *
     * @param configKey    配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValue(String configKey, String defaultValue);

    /**
     * 根据配置类型查询配置列表
     *
     * @param configType 配置类型
     * @return 配置列表
     */
    R<List<EmailPushConfig>> getConfigsByType(String configType);

    /**
     * 查询所有启用的配置
     *
     * @return 配置列表
     */
    R<List<EmailPushConfig>> getAllEnabledConfigs();

    /**
     * 新增配置
     *
     * @param config 配置信息
     * @return 操作结果
     */
    R<String> addConfig(EmailPushConfig config);

    /**
     * 更新配置
     *
     * @param config 配置信息
     * @return 操作结果
     */
    R<String> updateConfig(EmailPushConfig config);

    /**
     * 根据配置键更新配置值
     *
     * @param configKey   配置键
     * @param configValue 配置值
     * @param updateUser  更新人
     * @return 操作结果
     */
    R<String> updateConfigValue(String configKey, String configValue, String updateUser);

    /**
     * 删除配置
     *
     * @param id 配置ID
     * @return 操作结果
     */
    R<String> deleteConfig(Long id);

    /**
     * 批量更新配置
     *
     * @param configs 配置列表
     * @return 操作结果
     */
    R<String> batchUpdateConfigs(List<EmailPushConfig> configs);

    /**
     * 获取SMTP配置
     *
     * @return SMTP配置Map
     */
    Map<String, String> getSmtpConfig();

    /**
     * 更新SMTP配置
     *
     * @param smtpConfig SMTP配置Map
     * @param updateUser 更新人
     * @return 操作结果
     */
    R<String> updateSmtpConfig(Map<String, String> smtpConfig, String updateUser);

    /**
     * 测试SMTP连接
     *
     * @return 测试结果
     */
    R<String> testSmtpConnection();

    /**
     * 重置配置为默认值
     *
     * @param configKey 配置键
     * @return 操作结果
     */
    R<String> resetConfigToDefault(String configKey);

    /**
     * 导入配置
     *
     * @param configs 配置列表
     * @return 操作结果
     */
    R<String> importConfigs(List<EmailPushConfig> configs);

    /**
     * 导出配置
     *
     * @param configType 配置类型（可选）
     * @return 配置列表
     */
    R<List<EmailPushConfig>> exportConfigs(String configType);

    /**
     * 刷新配置缓存
     *
     * @return 操作结果
     */
    R<String> refreshConfigCache();
}
