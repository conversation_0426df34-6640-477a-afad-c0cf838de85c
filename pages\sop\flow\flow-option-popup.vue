<!-- 工序流程选项弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { deptMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 提示弹框
const tipPopup = ref()

// 工序流程选项弹框
const flowOptionPopup = ref()

// 制程
const operation = ref('')

// 工序流程选项类型
const flowOptionType = ref('')
// 工序流程选项类型映射
const flowOptionTypeMap = new Map([
  ['tools', '工具'],
  ['machine', '机器'],
  ['margin', '边距'],
  ['temp', '温度'],
  ['pressure', '压力'],
  ['glue', '胶水'],
  ['carLine', '车线'],
  ['chemical', '化学品'],
  ['needleSpacing', '针距'],
  ['spacing', '间距'],
  ['needle', '车针'],
  ['time', '时间'],
  ['defence', '防护用品']
])
// 工序流程选项类型数字映射
const optionMap = new Map([
  ['tools', 4],
  ['machine', 6],
  ['margin', 13],
  ['temp', 7],
  ['pressure', 8],
  ['glue', 10],
  ['carLine', 11],
  ['chemical', 5],
  ['needleSpacing', 12],
  ['spacing', 15],
  ['needle', 14],
  ['time', 9],
  ['defence', 3]
])

// 工序流程详情
const flowDetail = inject('flowDetail')

// 工序流程选项列表
const flowOptionList = ref([])

// 聚焦工序流程选项输入框
const focusFlowOptionInput = ref(false)
// 工序流程选项搜索框
const flowOptionSearch = ref('')
// 聚焦工序流程选项搜索框
const focusFlowOptionSearch = ref(false)

// 获取工序流程选项列表
async function getFlowOptionList(param1, param2) {
  await uni.request({
    url: urlPrefix + '/sop/getFlowOptionList',
    method: 'POST',
    data: {
      type: optionMap.get(param1),
      dept: deptMap.get(param2)
    }
  }).then(res => {
    if (res.data.code) {
      flowOptionList.value = res.data.data ? res.data.data : []
    } else {
      flowOptionList.value = []
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示工序流程选项弹框
async function showFlowOptionPopup(param1, param2) {
  flowOptionType.value = param1
  operation.value = param2
  
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await getFlowOptionList(param1, param2)
  
  uni.hideLoading()
  
  flowOptionPopup.value.open()
}

// 选择工序流程选项
function selectFlowOption(param) {
  if (flowDetail[flowOptionType.value]) {
    if (flowDetail[flowOptionType.value].includes('\n' + param.toUpperCase() + '\n')) {
      flowDetail[flowOptionType.value] = flowDetail[flowOptionType.value].replace(new RegExp(`\n${param}\n`, 'g'), '\n')
    } else if (flowDetail[flowOptionType.value].startsWith(param + '\n')) {
      flowDetail[flowOptionType.value] = flowDetail[flowOptionType.value].replace(param + '\n', '')
    } else {
      flowDetail[flowOptionType.value] += (param + '\n')
    }
  } else {
    flowDetail[flowOptionType.value] = param + '\n'
  }
}

defineExpose({
  showFlowOptionPopup
})
</script>

<template>
  <uni-popup
    ref="flowOptionPopup"
    type="center"
    class="flow-option-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择{{ flowOptionTypeMap.get(flowOptionType) }}
      </view>
      
      <view class="flow-option-input flex-row-center">
        <textarea
          v-model="flowDetail[flowOptionType]"
          @focus="focusFlowOptionInput = true"
          @blur="focusFlowOptionInput = false"
          :placeholder="'请输入' + flowOptionTypeMap.get(flowOptionType)"
          class="textarea"
          :style="{
            boxShadow: focusFlowOptionInput ? '0 0 5px blue' : '0 0 5px gray'
          }"
        ></textarea>
        
        <view
          v-show="flowDetail[flowOptionType]"
          @click="flowDetail[flowOptionType] = ''"
          class="clear-textarea button"
        >
          <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
        </view>
      </view>
      
      <view class="flow-option-search flex-row-center">
        <input
          v-model="flowOptionSearch"
          @focus="focusFlowOptionSearch = true"
          @blur="focusFlowOptionSearch = false"          
          placeholder="请输入搜索内容"
          type="text"
          class="input"
          :style="{
            boxShadow: focusFlowOptionSearch ? '0 0 5px blue' : '0 0 5px gray'
          }"
        />
        
        <view
          v-show="flowOptionSearch"
          @click="flowOptionSearch = ''"
          class="clear-input button"
        >
          <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
        </view>
      </view>
      
      <view class="flow-option-list">
        <view
          v-for="(item, index) in flowOptionList"
          v-show="item.includes(flowOptionSearch.toUpperCase())"
          class="flow-option-item flex-row-center"
        >
          <view
            @click="selectFlowOption(item)"
            class="button"
            :style="{
              color: flowDetail[flowOptionType] && (flowDetail[flowOptionType].includes('\n' + item.toUpperCase() + '\n') || flowDetail[flowOptionType].startsWith(item.toUpperCase() + '\n')) ? 'darkorchid' : 'darkmagenta'
            }"
          >
            {{ item }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.flow-option-popup {
  .container {
    width: 500px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .flow-option-input {
      width: 100%;
      height: 160px;
      position: relative;
      
      .textarea {
        width: 350px;
        height: 140px;
        padding: 5px;
      }
      
      .clear-textarea {
        width: 70px;
        height: 30px;
        position: absolute;
        right: 80px;
        bottom: 15px;
      }
    }
    
    .flow-option-search {
      width: 100%;
      height: 50px;
      position: relative;
      
      .input {
        width: 350px;
        height: 40px;
        padding: 5px;
      }
      
      .clear-input {
        width: 70px;
        height: 30px;
        position: absolute;
        right: 80px;
        bottom: 10px;
      }
    }
    
    .flow-option-list {
      width: 100%;
      height: 360px;
      overflow: auto;
      
      .flow-option-item {
        width: 100%;
        margin: 15px 0;
        
        .button {
          width: 350px;
          min-height: 40px;
          padding: 5px;
          color: darkmagenta;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .textarea {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-textarea-placeholder), &:deep(.uni-textarea-textarea) {
      width: 100%;
      height: 100%;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>