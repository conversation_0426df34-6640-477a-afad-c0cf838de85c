-- 邮件推送功能初始化数据脚本
-- 根据提供的Excel数据初始化权限配置

-- SOP部门数据
INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2310001100', 'SOP', '<EMAIL>', 'Y', 'Y', 'P', 'Y', '另约各海外工厂相关单位开会了解当前作业模式，再统一签核流程權限，待定', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2411001100', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2407006100', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2009087403', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2009151203', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2406000603', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2406000703', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2407000403', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2501000103', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2503000303', 'SOP', '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'SYSTEM');

-- 化工部门数据
INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009135303', '化工', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2410000303', '化工', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

-- 線外加工部门数据
INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009070003', '線外加工', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2206000503', '線外加工', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2308000303', '線外加工', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

-- 底部部门数据
INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009063903', '底部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009022203', '底部', '', 'Y', 'N', 'N', 'N', 'N', '暂无邮箱地址', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009062003', '底部', '', 'Y', 'N', 'N', 'N', 'N', '暂无邮箱地址', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009064203', '底部', '', 'Y', 'N', 'N', 'N', 'N', '暂无邮箱地址', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009066403', '底部', '', 'Y', 'N', 'N', 'N', 'N', '暂无邮箱地址', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2211000703', '底部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009141003', '底部', '', 'N', 'N', 'N', 'N', 'N', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009140703', '底部', '<EMAIL>', 'N', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009074303', '底部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009073203', '底部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009073403', '底部', '', 'Y', 'N', 'N', 'N', 'N', '暂无邮箱地址', 'SYSTEM');

-- 面部部门数据
INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009012403', '面部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009138003', '面部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES 
(seq_pcc_email_push_permission.nextval, '2009051103', '面部', '<EMAIL>', 'Y', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2009119503', '面部', '', 'Y', 'N', 'N', 'N', 'N', '暂无邮箱地址', 'SYSTEM');

-- IE工程部数据
INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2009035603', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2009035903', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2009035303', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2009036003', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2009044803', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2009035103', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2009035203', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, 'T000819', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2104002403', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, 'G1000778', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, 'G1001310', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '2502000603', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, create_user) VALUES
(seq_pcc_email_push_permission.nextval, '250400083', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'SYSTEM');

-- IE興雄IE工程部 (部门邮箱)
INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES
(seq_pcc_email_push_permission.nextval, 'IE_DEPT_001', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'IE興雄IE工程部门邮箱', 'SYSTEM');

-- IE PCC 1 VN 工程部 (部门邮箱)
INSERT INTO pcc_email_push_permission (id, user_no, dept_name, email, push_estimate_z, push_estimate_zz, push_p_version, update_email_notify, remark, create_user) VALUES
(seq_pcc_email_push_permission.nextval, 'IE_PCC1_VN_001', 'IE工程部', '<EMAIL>', 'N', 'N', 'N', 'Y', 'IE PCC 1 VN 工程部门邮箱', 'SYSTEM');

COMMIT;
