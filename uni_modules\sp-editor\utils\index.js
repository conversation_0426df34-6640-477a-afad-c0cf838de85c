// 标识必须独一无二 - 标识是为了使用insertText插入标识文本后，查找到标识所在delta位置的索引
export const linkFlag = '#-*=*-*=*-*=*@-link超链接标识link-@*=*-*=*-*=*-#'

export function addLink(editorCtx, attr) {
	// 先插入一段文本内容
	editorCtx.insertText({
		text: linkFlag
	})
	// 获取全文delta内容
	editorCtx.getContents({
		success(res) {
			let options = res.delta.ops
			const findex = options.findIndex(item => {
				return item.insert && typeof item.insert !== 'object' && item.insert?.indexOf(linkFlag) !== -1
			})
			// 根据标识查找到插入的位置
			if (findex > -1) {
				const findOption = options[findex]
				const findAttributes = findOption.attributes
				// 将该findOption分成三部分：前内容 要插入的link 后内容
				const [prefix, suffix] = findOption.insert.split(linkFlag);
				const handleOps = []
				// 前内容
				if (prefix) {
					const prefixOps = findAttributes ? {
						insert: prefix,
						attributes: findAttributes
					} : {
						insert: prefix
					}
					handleOps.push(prefixOps)
				}
				// 插入的link
				const linkOps = {
					insert: attr.text,
					attributes: {
						link: attr.href,
						textDecoration: attr.textDecoration || 'none', // 下划线
						color: attr.color || '#007aff'
					}
				}
				handleOps.push(linkOps)
				// 后内容
				if (suffix) {
					const suffixOps = findAttributes ? {
						insert: suffix,
						attributes: findAttributes
					} : {
						insert: suffix
					}
					handleOps.push(suffixOps)
				}
				// 删除原options[findex]并在findex位置插入上述三个ops
				options.splice(findex, 1);
				options.splice(findex, 0, ...handleOps);
				// 最后重新初始化内容，注意该方法会导致光标重置到最开始位置
				editorCtx.setContents({
					delta: {
						ops: options
					}
				})
				// 所以最后建议使富文本光标失焦，让用户手动聚焦光标
				editorCtx.blur()
			}
		}
	})

}

/**
 * 将含有特殊图片形式视频的富文本转换成正常视频的富文本
 * @param {String} html 要进行处理的富文本字符串
 * @returns {String} 返回处理结果
 */
export function handleHtmlWithVideo(html) {
	// console.log("xxxx:" + html)
		// 正则表达式用于匹配img标签中带有alt属性且alt属性值为视频链接的模式
		// const regex = /<img\s+src="[^"]*"\s+alt="([^"]*)"[^>]*>/g
		    const regex = /<img\s+[^>]*alt="([^"]*)"[^>]*>/g

		let result = html.replace(regex, (match, videoUrl) => {
	// 从img标签中提取style属性
			const styleRegex = /style="([^"]*)"/;
			const styleMatch = match.match(styleRegex);
			let style = styleMatch ? styleMatch[1] : '';
	
			// 从img标签中提取width属性
			const widthRegex = /width="(\d+)"/;
			const widthMatch = match.match(widthRegex);
			let width = widthMatch ? widthMatch[1] : '300'; // 默认宽度为180，如果img标签中有width属性则使用该值
	
			// 构建video标签的style属性
			style = style ? `style="${style}"` : '';
	
			// 替换为video标签，并添加controls属性以便用户可以控制播放
			return `<video ${style} width="${width}" controls><source src="${videoUrl}" type="video/mp4"></video>`;
			});
			if(result == '<p><br></p>'){
				result = ''
			}
	// 		console.log("@@@@@@@@@@@" + result)
	// console.log("@@@@@@@@@@@" + adjustVideoAlignment(result))
		return replaceSpacesAndTabsInPTags(adjustVideoAlignment(result))
		    // .replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;') 
		    // .replace(/ /g, '&nbsp;');
}


function replaceSpacesAndTabsInPTags(htmlString) {
  // 匹配<p>标签中的非属性文本
  return htmlString.replace(/<p>([^<]*?)<\/p>/g, function(match, p1) {
    // 替换制表符和普通空格
    return '<p>' + p1.replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;').replace(/ /g, '&nbsp;') + '</p>';
  });
}


function adjustVideoAlignment(htmlFragment) {
    // 正则表达式匹配包含<video>的<p>标签
    const pWithVideoRegex = /<p([^>]*)>([\s\S]*?)<\/p>/g;

    // 替换函数
    return htmlFragment.replace(pWithVideoRegex, function(match, pAttrs, pContent) {
        // 检查<p>标签中是否包含<video>
        if (/<video[\s\S]*?>/i.test(pContent)) {
            // 提取text-align样式
            const textAlignRegex = /text-align:\s*(center|right)/i;
            const textAlignMatch = pAttrs.match(textAlignRegex);

            let newStyle = '';
            if (textAlignMatch) {
                const textAlign = textAlignMatch[1].toLowerCase();
                if (textAlign === 'center') {
                    newStyle = 'style="text-align: center; display: flex; justify-content: center;"';
                } else if (textAlign === 'right') {
                    newStyle = 'style="text-align: right; display: flex; justify-content: flex-end;"';
                }
            }

            // 替换原始的style属性
            const styleRegex = /style="[^"]*"/i;
            const newAttrs = pAttrs.replace(styleRegex, newStyle);

            // 返回替换后的<p>标签
            return `<p${newAttrs}>${pContent}</p>`;
        }

        // 如果没有<video>，则不修改<p>标签
        return match;
    });
}



// function adjustVideoAlignment(htmlFragment) {
//     // 创建一个新的DOM元素来包含传入的HTML片段
//     var tempDiv = document.createElement('div');
//     tempDiv.innerHTML = htmlFragment;

//     // 查找包含<video>的<p>标签
//     var pTags = tempDiv.querySelectorAll('p:has(video)');

//     // 遍历所有找到的<p>标签
//     pTags.forEach(function(p) {
//         // 获取<p>标签的text-align样式
//         var textAlign = p.style.textAlign;

//         // 根据text-align样式修改视频对齐方式
//         if (textAlign === 'center') {
//             p.style.display = 'flex';
//             p.style.justifyContent = 'center';
//         } else if (textAlign === 'right') {
//             p.style.display = 'flex';
//             p.style.justifyContent = 'flex-end';
//         }
//     });

//     // 返回修改后的HTML片段
//     return tempDiv.innerHTML;
// }


 export function revertVideoToImage(html) {
            // 正则表达式用于匹配video标签
            const regex = /<video\s+([^>]*)><source\s+src="([^"]*)"\s+type="video\/mp4"><\/video>/g;

            let result = html.replace(regex, (match, attributes, videoUrl) => {
                // 从video标签中提取width属性
                const widthRegex = /width="(\d+)"/;
                const widthMatch = attributes.match(widthRegex);
                let width = widthMatch ? widthMatch[1] : '300'; // 默认宽度为300，如果video标签中有width属性则使用该值

                // 从video标签中提取style属性
                const styleRegex = /style="([^"]*)"/;
                const styleMatch = attributes.match(styleRegex);
                let style = styleMatch ? styleMatch[1] : '';

                // 构建img标签的style属性
                style = style ? `style="${style}"` : '';

                // 构建img标签，并将video的src属性作为alt属性
                return `<img src="https://img.zcool.cn/community/01055859b8e37fa8012075341db67f.gif" alt="${videoUrl}" width="${width}" ${style}>`;
            });
			// console.log("@@@@@@@@@@@" + removeFlexAlignmentStyles(result))
		return removeFlexAlignmentStyles(result).replace(/&nbsp;/g, ' ');
        }
		

// function removeFlexAlignmentStyles(htmlFragment) {
//     // 创建一个新的DOM元素来包含传入的HTML片段
//     var tempDiv = document.createElement('div');
//     tempDiv.innerHTML = htmlFragment;

//     // 查找所有的<p>标签
//     var pTags = tempDiv.querySelectorAll('p');

//     // 遍历所有找到的<p>标签
//     pTags.forEach(function(p) {
//         // 移除display和justify-content样式
//         p.style.display = '';
//         p.style.justifyContent = '';
//     });

//     // 返回修改后的HTML片段
//     return tempDiv.innerHTML;

// }

function removeFlexAlignmentStyles(htmlFragment) {
    // 正则表达式用于匹配display和justify-content样式属性
    var styleRegex = /(?<=style=")[^"]*?(display:\s*flex;?\s*justify-content:\s*[^\s;]+;?\s*)[^"]*?(?=")/gi;

    // 使用正则表达式替换匹配的样式属性
    var modifiedHtml = htmlFragment.replace(styleRegex, function(match) {
        // 替换display和justify-content样式
        match = match.replace(/display:\s*flex;?\s*/, '');
        if (match.includes('justify-content: flex-end;')) {
            match = match.replace(/justify-content:\s*flex-end;?\s*/, 'text-align: right;');
        } else if (match.includes('justify-content: center;')) {
            match = match.replace(/justify-content:\s*center;?\s*/, 'text-align: center;');
        }
        return match;
    });

    // console.log("@@@@@@@@" + modifiedHtml);
    return modifiedHtml;
}


export function replaceNbspWithSpace(inputString) {
  return inputString.replace(/&nbsp;/g, ' ');
}


// function removeFlexAlignmentStyles(htmlFragment) {
//     // 正则表达式用于匹配display和justify-content样式属性
//     // 它会匹配样式属性名以及随后的任何值，包括引号和分号
//     var styleRegex = /(?<=style=")[^"]*?(display:\s*flex;?\s*|justify-content:\s*[^\s;]+;?\s*)[^"]*?(?=")/gi;

//     // 使用正则表达式替换匹配的样式属性
//     var modifiedHtml = htmlFragment.replace(styleRegex, function(match) {
//         // 移除display和justify-content样式
//         return match.replace(/display:\s*flex;?\s*|justify-content:\s*[^\s;]+;?\s*/gi, '');
//     });

// 	console.log("@@@@@@@@" + modifiedHtml)
//     return modifiedHtml;
// }



export     function alignMediaInHtml(htmlFragment) {
            // 创建一个临时的div元素来容纳HTML片段
            const tempDiv = document.createElement('div');
            // 将HTML片段设置到tempDiv中
            tempDiv.innerHTML = htmlFragment;


            // 获取所有的img和video元素
            const imgElements = tempDiv.querySelectorAll('img');

            // 遍历每个媒体元素
            imgElements.forEach(element => {
                // 获取包含媒体元素的p标签
                let parentP = element.closest('p');

                // 如果找到了p标签
                if (parentP) {
                    // 检查class属性中是否包含fr-fir或fr-fil
                    if (element.classList.contains('fr-fir')) {
                        // 如果包含fr-fir，设置p标签的style为右对齐
                        parentP.style.textAlign = 'right';
                    } else if (element.classList.contains('fr-fil')) {
                        // 如果包含fr-fil，设置p标签的style为左对齐
                        parentP.style.textAlign = 'left';
                    }
                }
            });
         // 获取所有包含特定类名的元素
            const mediaElements3 = tempDiv.querySelectorAll('.fr-video.fr-dvb.fr-draggable');

            // 遍历每个包含特定类名的元素
            mediaElements3.forEach(element => {
            // 获取包含媒体元素的最接近的p标签
            let parentP = element.closest('p');

            // 如果找到了p标签
            if (parentP) {
                // 设置p标签的style为居中对齐
                parentP.style.textAlign = 'center';
            }
            });

            // 获取所有包含fr-fvr类的元素
            const mediaElements = tempDiv.querySelectorAll('.fr-fvr');

            // 遍历每个包含fr-fvr类的元素
            mediaElements.forEach(element => {
                // 获取包含媒体元素的p标签
                let parentP = element.closest('p');

                // 如果找到了p标签
                if (parentP) {
                    // 设置p标签的style为右对齐
                    parentP.style.textAlign = 'right';
                }
            });


            // 获取所有包含fr-fvr类的元素
            const mediaElements2 = tempDiv.querySelectorAll('.fr-fvl');

            // 遍历每个包含fr-fvr类的元素
            mediaElements2.forEach(element => {
                // 获取包含媒体元素的p标签
                let parentP = element.closest('p');

                // 如果找到了p标签
                if (parentP) {
                    // 设置p标签的style为右对齐
                    parentP.style.textAlign = 'left';
                }
            });
			
	   // 获取表格元素并设置边框、边距和宽度
	   const tableElements = tempDiv.querySelectorAll('table');
	   tableElements.forEach(table => {
	     // 设置表格边框样式
	     table.style.borderCollapse = 'collapse'; // 确保边框不会重叠
	     table.style.border = '1px solid #ddd'; // 使用浅灰色边框
	     table.style.width = '95%'; // 设置表格宽度为90%
	     table.style.margin = '0 auto'; // 设置左右自动边距以实现居中
	 
	     // 设置单元格边框样式
	     const tdElements = table.querySelectorAll('td');
	     tdElements.forEach(td => {
	       td.style.border = '1px solid #ddd'; // 使用浅灰色边框
	       td.style.padding = '8px'; // 添加一些内边距，使内容不紧贴边框
	     });
	   });


            // 返回修改后的HTML片段
            return tempDiv.innerHTML;
        }



// export  function alignMediaInHtmlWithRegex(htmlFragment) {
// 	console.log("old@@@@@@@@@@@" + htmlFragment)


// 	 // 使用正则表达式替换img元素的父级p标签的样式
// 	    htmlFragment = htmlFragment.replace(/<p>(.*?)<img class="([^"]*?)(fr-fir|fr-fil)([^"]*?)"(.*?)>/g, function(match, p1, class1, alignClass, class2, attrs) {
// 	        let textAlign = alignClass === 'fr-fir' ? 'right' : 'left';
// 	        return `<p style="text-align: ${textAlign};">${p1}<img class="${class1}${alignClass}${class2}"${attrs}>`;
// 	    });
	
// 	    // 使用正则表达式替换包含特定类名的video元素的父级p标签的样式
// 	    htmlFragment = htmlFragment.replace(/<p>(.*?)<video class="([^"]*?)(fr-video fr-dvb fr-draggable)([^"]*?)"(.*?)>/g, function(match, p1, class1, videoClass, class2, attrs) {
// 	        return `<p style="text-align: center;">${p1}<video class="${class1}${videoClass}${class2}"${attrs}>`;
// 	    });
	
// 	    // 使用正则表达式替换包含fr-fvr类的元素的父级p标签的样式
// 	    htmlFragment = htmlFragment.replace(/<p>(.*?)<(\w+ class="[^"]*?fr-fvr[^"]*?)"(.*?)>/g, function(match, p1, element, attrs) {
// 	        return `<p style="text-align: right;">${p1}<${element}${attrs}>`;
// 	    });
	
// 	    // 使用正则表达式替换包含fr-fvl类的元素的父级p标签的样式
// 	    htmlFragment = htmlFragment.replace(/<p>(.*?)<(\w+ class="[^"]*?fr-fvl[^"]*?)"(.*?)>/g, function(match, p1, element, attrs) {
// 	        return `<p style="text-align: left;">${p1}<${element}${attrs}>`;
// 	    });
	
// 	    // 使用正则表达式替换表格元素的样式
// 	    htmlFragment = htmlFragment.replace(/<table(.*?)>/g, '<table style="border-collapse: collapse; border: 1px solid #ddd; width: 95%; margin: 0 auto;">');
// 	    htmlFragment = htmlFragment.replace(/<td(.*?)>/g, '<td style="border: 1px solid #ddd; padding: 8px;">');




// 	console.log("new@@@@@@@@@@@" + htmlFragment)
//   return htmlFragment;
// }



// export function alignMediaInHtml(htmlFragment) {
//     // 在uniapp中，可以使用rich-text组件来处理HTML内容
//     // 创建一个临时变量来存储处理后的nodes数组
//     let nodes = [];

//     // 使用正则表达式匹配图片和视频标签
//     const imgRegex = /<img[^>]*>/g;
//     const videoRegex = /<video[^>]*class="([^"]*fr-video[^"]*)"[^>]*>/g;

//     // 分割HTML片段，以便于单独处理媒体元素
//     let splitHtml = htmlFragment.split(imgRegex).concat(htmlFragment.split(videoRegex));

//     // 遍历分割后的HTML片段
//     splitHtml.forEach(fragment => {
//         if (imgRegex.test(fragment) || videoRegex.test(fragment)) {
//             // 如果是媒体元素，创建rich-text节点
//             let node = {
//                 name: fragment.startsWith('<img') ? 'img' : 'video',
//                 attrs: {
//                     class: fragment.match(/class="([^"]*)"/)[1] || '',
//                     src: fragment.match(/src="([^"]*)"/)[1] || '',
//                     // 添加其他需要的属性
//                 }
//             };

//             // 根据类名设置对齐方式
//             if (node.attrs.class.includes('fr-fir')) {
//                 node.attrs.style = 'text-align:right;';
//             } else if (node.attrs.class.includes('fr-fil')) {
//                 node.attrs.style = 'text-align:left;';
//             } else if (node.attrs.class.includes('fr-video')) {
//                 node.attrs.style = 'text-align:center;';
//             } else if (node.attrs.class.includes('fr-fvr')) {
//                 node.attrs.style = 'text-align:right;';
//             } else if (node.attrs.class.includes('fr-fvl')) {
//                 node.attrs.style = 'text-align:left;';
//             }

//             nodes.push(node);
//         } else {
//             // 如果不是媒体元素，直接添加到nodes数组
//             nodes.push({
//                 type: 'text',
//                 text: fragment
//             });
//         }
//     });

//     // 返回处理后的nodes数组，可以用于rich-text组件
//     return nodes;
// }


export  function alignMediaInHtmlWithRegex(htmlFragment) {
	
	
            // 使用正则表达式和字符串操作来处理HTML
            const imgRegex = /<img([^>]*)>/g;

            const tableRegex = /<table[^>]*>([\s\S]*?)<\/table>/g;
			
			
//             // 定义正则表达式来匹配包含特定class的span标签，且该span标签直接位于p标签内
			            const pWithSpanRegex = /<p[^>]*>(?:\s*<span [^>]*class="[^"]*\bfr-fvr\b[^"]*"[^>]*>.*?<\/span>\s*<\/p>)/g;
			
			            // 替换包含特定class的span标签的段落
			            htmlFragment = htmlFragment.replace(pWithSpanRegex, (match) => {
			                // 设置样式为右对齐
			                const newP = match.replace('<p', `<p style="text-align: right; display: flex; justify-content: flex-end;"`);
			                return newP;
			            });
			
			            
			
			//             // 定义正则表达式来匹配包含特定class的span标签，且该span标签直接位于p标签内
			            const pWithSpanRegex2 = /<p[^>]*>(?:\s*<span [^>]*class="[^"]*\bfr-fvl\b[^"]*"[^>]*>.*?<\/span>\s*<\/p>)/g;
			
			                // 替换包含特定class的span标签的段落
			                htmlFragment = htmlFragment.replace(pWithSpanRegex2, (match) => {
			                    // 设置样式为右对齐
			                    const newP = match.replace('<p', `<p style="text-align: left; display: flex; justify-content: flex-start;"`);
			                    return newP;
			                });
			

            //             // 定义正则表达式来匹配包含特定class的span标签，且该span标签直接位于p标签内
            const pWithSpanRegex1 = /<p[^>]*>(?:\s*<span [^>]*class="(?:[^"]*\s)?fr-video\sfr-dvb\sfr-draggable(?:\s[^"]*)?"[^>]*>.*?<\/span>\s*<\/p>)/g;


            // 替换包含特定class的span标签的段落
            htmlFragment = htmlFragment.replace(pWithSpanRegex1, (match) => {
                // 设置样式为右对齐
                const newP = match.replace('<p', `<p style="text-align: center; display: flex; justify-content: center;"`);
                return newP;
            });

			
			
			
			
			

            // 替换图片标签
            htmlFragment = htmlFragment.replace(imgRegex, (match, attributes) => {
                if (/(class="[^"]*\bfr-fir\b[^"]*")/.test(attributes)) {
                    return `<p style="text-align:right;">${match}</p>`;
                } else if (/(class="[^"]*\bfr-fil\b[^"]*")/.test(attributes)) {
                    return `<p style="text-align:left;">${match}</p>`;
                } else {
                    return `<p style="text-align:center;">${match}</p>`;
                }
                return match;
            });

 

            // 替换表格标签
            htmlFragment = htmlFragment.replace(tableRegex, (match, tableContent) => {
                const styledTable = `<table style="border-collapse:collapse;border:1px solid #ddd;width:95%;margin:0 auto;">${tableContent}</table>`;
                return styledTable.replace(/<td [^>]*>/g, (tdMatch) => {
                    return `<td style="border:1px solid #ddd;padding:8px;">`;
                });
            });


var updatedHtmlString = htmlFragment.replace(
    'style="text-align: center; font-size: 14px; margin-top: 30px; opacity: 0.65; font-family: sans-serif;"',
    'style="text-align: center; font-size: 14px; margin-top: 30px; opacity: 0.65; font-family: sans-serif; display: none;"'
);
			console.log(updatedHtmlString)
            // 返回处理后的HTML片段
            return updatedHtmlString;
        }
		
