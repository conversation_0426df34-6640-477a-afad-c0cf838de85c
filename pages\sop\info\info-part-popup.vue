<!-- 工序信息部位弹框 -->
<script setup>
import { ref } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap, proSeqMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 工序信息部位弹框
const infoPartPopup = ref()

// 工序信息
const processInfo = ref()
// 部位列表
const infoPartList = ref([])
// 部位输入框
const partInput = ref('')
// 是否聚焦部位输入框
const focusPartInput = ref(false)

// 获取部位列表
async function getInfoPartList() {
  await uni.request({
    url: urlPrefix + '/sop/getPartList',
    method: 'GET'
  }).then(res => {
    if (res.data.code) {
      infoPartList.value = res.data.data ? res.data.data : []
    } else {
      infoPartList.value = []
      // tipPopup.value.showTipPopup('warn', '暂无部位列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示工序信息部位弹框
async function showInfoPartPopup(param) {
  if (infoPartList.value.length === 0) {
    uni.showLoading({
      title: '加载中...',
      mask: true
    })
    
    await getInfoPartList()
    
    uni.hideLoading()
    
    // if (infoPartList.value.length === 0) {
    //   return
    // }
  }
  
  processInfo.value = param
  partInput.value = ''
  
  infoPartPopup.value.open()
}

// 选择部位
async function selectInfoPart(param1, param2, param3, param4) {
  // if (!param4) {
  //   tipPopup.value.showTipPopup('warn', '请输入部位！')
  //   return
  // }
  
  if (processInfo.value.part && param4 === processInfo.value.part) {
    infoPartPopup.value.close()
    return
  }
  
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateInfoPart',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3,
      partName: param4 ? param4 : '/',
      updUser: user
    }
  }).then(res => {
    if (res.data.code) {
      processInfo.value.partName = param4
      tipPopup.value.showTipPopup('success', '修改成功！')
      infoPartPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  getInfoPartList,
  showInfoPartPopup
})
</script>

<template>
  <uni-popup
    ref="infoPartPopup"
    type="center"
    class="info-part-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择部位
      </view>
      
      <view class="description flex-row-center">
        {{ operationMap.get(processInfo.operation) ? operationMap.get(processInfo.operation) : '未知' }} - {{ proSeqMap.get(processInfo.proSeq) ? proSeqMap.get(processInfo.proSeq) : '未知' }} - {{ processInfo.model }} - {{ processInfo.rtgCode }}
      </view>
      
      <view class="info-part-list">
        <view class="info-part flex-row-center">
          <input
            v-model="partInput"
            @focus="focusPartInput = true"
            @blur="focusPartInput = false"
            type="text"
            placeholder="请输入部位"
            class="input"
            :style="{
              boxShadow: focusPartInput ? '0 0 5px blue' : '0 0 5px gray'
            }"
          />
          
          <view v-show="partInput" @click="partInput = ''" class="clear button">
            <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
          </view>
          
          <view
            @click="selectInfoPart(processInfo.model, processInfo.operation, processInfo.rtgCode, partInput)"
            class="save button"
          >
            <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
          </view>
        </view>
        
        <view
          v-for="(item, index) in infoPartList.filter(i => i.includes(partInput.toUpperCase())).splice(0, 50)"
          :key="index"
          class="info-part flex-row-center"
        >
          <view
            @click="selectInfoPart(processInfo.model, processInfo.operation, processInfo.rtgCode, item)"
            class="button"
            :style="{
              color: item === processInfo.partName ? 'goldenrod' : 'darkgoldenrod'
            }"
          >
            {{ item }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.info-part-popup {
  .container {
    width: 430px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .description {
      width: 100%;
      height: 40px;
      font-size: 20px;
      font-weight: bold;
    }
    
    .info-part-list {
      height: 350px;
      overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .info-part {
        width: 100%;
        padding: 10px;
        position: relative;
        
        .button {
          width: 270px;
          min-height: 50px;
          color: darkmagenta;
          font-size: 20px;
          padding: 5px;
        }
        
        .input {
          width: 270px;
          height: 50px;
          padding: 0 10px;
        }
        
        .clear {
          width: 60px;
          min-height: 40px;
          position: absolute;
          top: 15px;
          right: 85px;
        }
        
        .save {
          width: 50px;
          height: 50px;
          position: absolute;
          top: 10px;
          right: 15px;
        }
        
        &:first-child {
          position: sticky;
          top: 0;
          background-color: #fdf6e3;
          z-index: 1;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>