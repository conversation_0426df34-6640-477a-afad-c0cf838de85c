<template>
    <view class="container">
        <view class="back">
            <uni-icons @click="back" type="back" size="36"></uni-icons>
        </view>

        <view style="width: 100%; text-align: center; font-size: 24px;">
            <text>鞋面配套待投入進度表</text>
        </view>
		<view class="tot_qty_sum">
			<text>订单总量：{{tot_qty_sum}}</text>
		</view>

        <view class="search">
        	<!-- 第一行 -->
        	<view class="row">
        		<text style="width: 40px;">出货时间</text>
                <picker  mode="date" :value="startTime"   @change="bindStartDateChange">
                    <view style="padding: 8px;background-color: white;width: 90px" v-if="startTime === ''" class="uni-input">请选择</view>
                    <view style="padding: 8px;background-color: white;width: 90px" v-if="startTime != ''" class="uni-input">{{startTimeText}}</view>
                </picker>
                <picker  mode="date" :value="endTime" @change="bindEndDateChange" style="margin-left: 10px">
                    <view style="padding: 8px;background-color: white; width: 90px" v-if="endTime === ''" class="uni-input">请选择</view>
                    <view style="padding: 8px;background-color: white;width: 90px" v-if="endTime != ''" class="uni-input">{{endTimeText}}</view>
                </picker>
        		<text style="margin-left: 25px;width: 35px;">品牌</text>
                <view class="inputDate">
                    <uni-combox
							:candidates="brandList"
							placeholder="请选择品牌"
							v-model="brand"
							@input="bindBrandChange"
					></uni-combox>
                </view>
        		<!--<view class="inputDate">
        			<uv-input v-model="brand"></uv-input>
        		</view>-->
        		<text style="margin-left: 25px;width: 47px;">样品单号 </text>
        		<!--<view class="inputDate">
        			<uv-input v-model="searchOrdNo"></uv-input>
        		</view>-->
                <view class="inputDate">
                  <!--  <uni-data-select
                            v-model="searchOrdNo"
                            :localdata="searchOrdNoList"
                            @change="bindOrdNoListChange"
                    ></uni-data-select>-->
                    <uni-combox
                            :candidates="searchOrdNoList"
                            placeholder="请选择单号"
                            v-model="searchOrdNo"
                            @input="bindOrdNoListChange"
                    ></uni-combox>
                </view>


                <text style="margin-left: 10px;width: 47px;">製鞋組別 </text>
                <view class="inputDate">
                    <uni-data-select
                            v-model="groNo"
                            :localdata="groNoGroup"
                            @change="bindGroNoChange"
                    ></uni-data-select>
                </view>


                <text style="margin-left: 10px;width: 50px;">生产线</text>
				<view class="inputDate">
				<!--	<uv-input v-model="pdLine"></uv-input>-->
                    <uni-data-select
                            v-model="pdLine"
                            :localdata="lineList"
                            @change="bindLineListChange"
                    ></uni-data-select>
				</view>
        	</view>
        
        	<!-- 第二行 -->
        	<view class="row">
        		<text style="width: 47px;">底料状况</text>
        		<view class="inputDate">
        			<uni-data-select v-model="t2Flag" :localdata="t2FlagList" :clear="false" emptyTips="請選擇"
        				style="width: 80%;margin-left: 16px;background-color: white;"></uni-data-select>
        		</view>
        		<text style="margin-left: 25px;width: 50px;">半成品日期</text>
        		<view class="inputDate">
        			<!--<picker mode="date" :value="bDate" @change="bindDateChange">
        				<view style="padding: 8px;background-color: white;" v-if="bDate === ''" class="uni-input">请选择</view>
        				<view style="padding: 8px;background-color: white;" v-if="bDate != ''" class="uni-input">{{bDate}}</view>
        			</picker>-->
                    <zxz-uni-data-select v-model="bDate" multiple :localdata="bDateTypes" collapseTags="true" :clear="false" ></zxz-uni-data-select>
                   <!-- <uni-data-select v-model="bDate" :localdata="bDateTypes" :clear="false" emptyTips="請選擇"
                                     style="width: 80%;margin-left: 16px;background-color: white;"></uni-data-select>-->
        		</view>
        		<text style="margin-left: 25px;width: 47px;">样品类型</text>
        		<view style="width: 20%;">
        			<uni-data-select v-model="devType" :localdata="devTypes" :clear="false" emptyTips="請選擇"
        				style="width: 80%;margin-left: 16px;background-color: white;"></uni-data-select>
        		</view>
        		<uv-button @click="query()" style="margin-left: 25px;" type="primary" text="查询"></uv-button>
        	</view>
        </view>

        <view class="title">
            <table>
                <thead class="sticky-header">
                    <tr>
                        <th class="rowClass">样品单号</th>
                        <th class="rowClass">样品类型</th>
                        <th class="rowClass">鞋图</th>
                        <th class="rowClass">製鞋組別</th>
                        <th class="rowClass" style="width: 50px!important;">生產綫</th>
                        <th class="rowClass2" style="width: 15px;white-space: nowrap;">业务/版师</th>
                        <th class="rowClass">型体</th>
                        <th class="rowClass">楦头<br>数量</th>
                        <th class="rowClass">派工日</th>
                        <th class="rowClass">出货日</th>
                        <th class="rowClass">订单量</th>
                        <th class="rowClass">底料狀況</th>
                        <th class="rowClass">半成品</th>
                        <th class="rowClass">副料<br>配套</th>
                        <th class="rowClass">发外操作</th>
                        <th class="rowClass">鞋面投入</th>
                    </tr>
                </thead>
                <tbody>
                    <template v-for="(item, index) in tableData" :key="item.id">
                        <tr>
                            <td style="text-decoration: underline;color: skyblue;" class="rowClass" rowspan="2" @click="viewMkData(item.ord_no)">{{ item.ord_no }}</td>
                            <td class="rowClass" rowspan="2">{{ item.dev_type }}</td>
                            <td class="rowClass" rowspan="2"><img :src="'data:image/jpg;base64,' + item.model_pic " alt="鞋图"/></td>
                            <td class="rowClass2" rowspan="2" style="white-space: nowrap;">{{ item.made_dept }}</td>
                            <td class="rowClass" rowspan="2"  style="width: 50px!important;">{{item.pd_line.substr(0,2)}} <br/> {{ item.pd_line.substr(2) }}</td>
                            <td class="rowClass" rowspan="2" style="white-space: nowrap;">{{ item.dutyer }}/<br>{{ item.upper_der }}</td>
                            <td class="rowClass" rowspan="2">{{ item.model_no }}</td>
                            <td class="rowClass" rowspan="2">{{ item.sum_app_qty }}</td>
                            <td class="rowClass" rowspan="2">{{ item.wo_date }}</td>
                            <td class="rowClass" rowspan="2">{{ item.shp_date }}</td>
                            <td class="rowClass" rowspan="2">{{ item.tot_qty }}</td>
                            <td class="rowClass" rowspan="2">{{ item.t2_flag }}</td>
                            <td class="rowClass" rowspan="2">{{ item.b_date }}</td>
                            <td class="rowClass" rowspan="2">{{ item.t3_date }}</td>
                            <td class="rowClass" rowspan="2">
                                <uv-button v-if="item.in_qty == 0" text="发外投入" type="error" @click="fwtr(item.ord_no,item.tot_qty,item.made_dept)"></uv-button>
                                <uv-button v-if="item.in_qty > 0" text="取消投入" type="success" @click="cancel(item.ord_no,item.tot_qty,item.made_dept,1)"></uv-button>
                                <uv-button v-if="item.out_qty == 0" style="margin-top: 5px;" text="发外产出" type="error" @click="fwcc(item.ord_no,item.tot_qty,item.made_dept)"></uv-button>
                                <uv-button v-if="item.out_qty > 0" style="margin-top: 5px;" text="取消产出" type="success" @click="cancel(item.ord_no,item.tot_qty,item.made_dept,2)"></uv-button>
                            </td>
                            <td class="rowClass" rowspan="2">
                                <uv-button text="投入" type="error" @click="viewMadeDept(item.ord_no,item.tot_qty,item.made_dept)"></uv-button>
                            </td>
                        </tr>
                        <tr>
                        </tr>
                    </template>
                </tbody>
            </table>
        </view>

        <!--单击单行弹出界面-->
        <view class="updateSpecificationPopup">
            <uni-popup
                ref="updateSpecificationPopup"
                type="center"
                style="height: 80%;">

                <view class="updateSpecificationBox">

                    <uni-title title="設定組別" type="h2" align="center" ></uni-title>
                    <uni-icons type="closeempty" @click="backDrom" size="32" style="position: absolute;top: 25rpx;right: 1vw;"></uni-icons>
                    <view class="updateSpecificationData">
						<uni-easyinput style="margin-left: 5px;" :styles="{ color: '#2979FF', borderColor: '#2979FF' }" type="text" class="uni-input"
									   @input="getMadeDeptByGrpNo"  placeholder="輸入組別編號"/>
                        <zb-table
                            @edit="editMadeDept"
                            :show-header="true"
                            :columns="column"
                            :stripe="true"
                            ref="zbTable"
                            :border="true"
                            :cell-style="cellStyle"
                            :data="detailTableData"></zb-table>
                    </view>
                </view>
            </uni-popup>
        </view>

        <!--单击单行弹出界面-->
        <view class="updateSpecificationPopup">
            <uni-popup
                ref="mkPopup"
                type="center"
                style="height: 80%;">

                <view class="updateSpecificationBox">

                    <uni-title title="鞋面投入扫描" type="h2" align="center" ></uni-title>
                    <uni-icons type="closeempty" @click="backDrom" size="32" style="position: absolute;top: 25rpx;right: 1vw;"></uni-icons>
                    <view class="updateSpecificationData">
                        <zb-table
                            :show-header="true"
                            :columns="mkcolumn"
                            :stripe="true"
                            ref="zbTable"
                            :border="true"
                            :data="mkData"></zb-table>
                    </view>
                </view>
            </uni-popup>
        </view>

        <view class="left-bottom">
            <uni-pagination show-icon="true" :total="pageCount" :current="firstPageNo" :pageSize="firstPageSize"
                            @change="firstChange"></uni-pagination>
        </view>
    </view>
	<view class="tip-popup">
	    <uni-popup ref="tipPopup" type="message">
	        <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
	    </uni-popup>
	</view>

</template>

<script setup>
import {
    onMounted,
    ref,
    reactive,
    watch
} from 'vue'
import {
    onPullDownRefresh
} from '@dcloudio/uni-app'
import {
    onShow
} from '@dcloudio/uni-app';
import urlPrefix from '@/pages/common/urlPrefix.js'

//第几页
const firstPageNo = ref(1)
const firstPageSize = ref(500)
const pageCount = ref(0)

//表单数据
const dataList = ref([])

const insUs = uni.getStorageSync("loUserNo")
const insName = uni.getStorageSync("loginUserName")

//消息提示
const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')

//日期选择
const endTime = ref(new Date().getTime())
const date = new Date();
date.setHours(date.getHours() - 24);
const startTime = ref(date.getTime())
const datetimePickerSt = ref()
const datetimePickerEnd = ref()
const startTimeText = ref('2024/06/03')
const endTimeText = ref('2024/06/03')

const cfStatus = ref(false);
const brand = ref()
const searchOrdNo = ref()

const pdLine = ref()
const t2Flag = ref()
const t2FlagList = ref([
    {'value':'','text':'請選擇'},
    {'value':'OK','text':'OK'}
])

const bDate = ref('');
const devType = ref()
const devTypes = ref([])
const fileType = ref()
const fileTypeList = ref([
    {'value':0,'text':'請選擇'},
    {'value':1,'text':'優先'},
    {'value':2,'text':'可配套生產'},
    {'value':3,'text':'只可面部生產'},
    {'value':4,'text':'只可裁斷生產'}
])

const cutComplType = ref()
const cutComplTypeList = ref([
    {'value':0,'text':'請選擇'},
    {'value':1,'text':'已完成'},
    {'value':2,'text':'未完成'}
])

const tableData = ref([]);

const bDateTypes = ref([])

const updateSpecificationPopup = ref();
const column=ref([
    { name: '', type:'operation',label: '设定',renders:[
            {
                name:'设定',
                type:'warn',
                func:'edit'// func 代表子元素点击的事件 父元素接收的事件 父元素 @edit
            }
        ]},
    { name: 'made_dept', label: '製作組別',width:150},
    {
        name: 'emp_name',
        label: '名稱',
        width: 150
    },
    { name: 'grp_no', label: '組別編號',emptyString:'/',width:150}




]);

const mkPopup = ref();
const mkcolumn=ref([
    { name: 'ord_no', label: '樣品單號',emptyString:'--',width:150},
    { name: 'bar_date', label: '條碼日期',emptyString:' ',width:150},
    { name: 'semi_no', label: '製程部門',width:150},
    { name: 'semi_su', label: '製程部位',emptyString:'/',width:150},
    { name: 'made_dept', label: '製作組別',emptyString:'/',width:150},
    { name: 'type', label: '投入產出',emptyString:'/',width:150},
    { name: 'bar_qty', label: '條碼雙數',emptyString:'/',width:150},
    { name: 'ins_user', label: '建立人',emptyString:'/',width:150},
    { name: 'ins_date', label: '建立日期',emptyString:'/',width:150}
]);

const detailTableData = ref([]);
const mkData = ref([]);
const brandList =ref([])

const searchOrdNoList = ref([])

const madeDept = ref()

const groNoGroup = ref([])
const groNo = ref()


const lineList = ref([
 /*   {text:"ALL",value:''},*/
    {text:"B線SPORTS",value:'B線SPORTS'},
    {text:"C線CASUAL",value:'C線CASUAL'},
    {text:"A線DRESS",value:'A線DRESS'},
])


function bindGroNoChange(e) {
    groNo.value = e
}

function bindLineListChange(e) {
    pdLine.value = e
}

function bindOrdNoListChange(e) {
  /*  searchOrdNo.value =e*/
    searchOrdNo.value = searchOrdNoList.value[e.detail.value]


}
function bindBrandChange(e) {
    brand.value = e

    searchOrdNoList.value = []
    if(e === "所有品牌"){
        for (const item of dataList.value) {
            /*let obj = {
                value: item.ord_no,
                text: item.ord_no
            }*/
            searchOrdNoList.value.push(item.ord_no)
        }
    }else {
        for (const item of dataList.value) {
            if (item.model_no.indexOf(e) > -1) {
                /* let obj = {
                     value: item.ord_no,
                     text: item.ord_no
                 }*/
                searchOrdNoList.value.push(item.ord_no)
            }
        }
    }

    searchOrdNoList.value.sort((a, b) => {
        if (a < b) {
            return -1;
        }
        if (a > b) {
            return 1;
        }
        return 0;
    });

  /*  searchOrdNoList.value = []
    if(e === "所有品牌"){
        for (const item of dataList.value) {
            let obj = {
                value: item.ord_no,
                text: item.ord_no
            }
            searchOrdNoList.value.push(obj)
        }
    }else {
        for (const item of dataList.value) {
            if (item.model_no.indexOf(e) > -1) {
                let obj = {
                    value: item.ord_no,
                    text: item.ord_no
                }
                searchOrdNoList.value.push(obj)
            }
        }
    }
    searchOrdNoList.value.sort((a, b) => {
        if (a.text < b.text) {
            return -1;
        }
        if (a.text > b.text) {
            return 1;
        }
        return 0;
    });*/
}
// 获取品牌列表
function getBrands() {
    uni.request({
        url: urlPrefix + "/first/getBrandsPlus",
        method: "POST"
    }).then(res => {
        let arr = []
        for (const item of  res.data.data) {
            item.data.forEach(i=>{
                arr.push({value:i,text:i})
            })
        }
        
		brandList.value = arr.map(item=>{
			return item.value
		});
    }).catch(err => {
        console.log(err)
    })
}



//关闭弹窗
function backDrom() {
    detailTableData.value=[];
    mkData.value=[];
    updateSpecificationPopup.value.close()
    mkPopup.value.close()
}

function openSt() {
    datetimePickerSt.value.open();
}

function confirmSt(e) {
    console.log('confirm', e);
    // 创建一个新的日期对象e
    var date = new Date(e.value);

    // 获取年、月、日
    var year = date.getFullYear();
    var month = date.getMonth() + 1; // 月份从0开始，需要加1
    var day = date.getDate();

    // 格式化日期为yyyy/MM/DD的样式
    var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);

    startTimeText.value = formattedDate;
}

//判断颜色
function cellStyle({row, column, rowIndex, columnIndex}){
    if(row.key_flag == "Y"){
        return {
            'background-color': 'red',
            'color': 'white'
        };
    }
    // 如果不符合条件，返回空对象
    return {};
}


function openEnd() {
    datetimePickerEnd.value.open();
}

function confirmEnd(e) {
    // 创建一个新的日期对象e
    var date = new Date(e.value);

    // 获取年、月、日
    var year = date.getFullYear();
    var month = date.getMonth() + 1; // 月份从0开始，需要加1
    var day = date.getDate();

    // 格式化日期为yyyy/MM/DD的样式
    var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);

    endTimeText.value = formattedDate;
}

function bindStartDateChange(e){
    startTime.value = new Date(e.detail.value).getTime();
    startTimeText.value = e.detail.value;
}

function bindEndDateChange(e){
    endTime.value = new Date(e.detail.value).getTime();
    endTimeText.value = e.detail.value;
}
	
function bindDateChange(e){
	bDate.value = e.detail.value
}

// 提示信息
function showTip(type, message) {
    tipType.value = type
    tipMessage.value = message
    tipPopup.value.open()
}


const totalOrderQuantity = ref()
const tot_qty_sum = ref()

function query(){
    uni.showLoading({
        title: '加載中',
        mask: true // 设置遮罩层
    });
    firstPageNo.value = 1;
    uni.request({
        url: urlPrefix + "/mbpt/query",
        data: {
            "pageNo": 1,
            "pageSize": firstPageSize.value,
            "startTime": startTime.value,
            "endTime": endTime.value,
            "brand": brand.value,
            "devType": devType.value,
            "fileType": fileType.value,
            "cutComplType": cutComplType.value,
            "ordNo": searchOrdNo.value,
			"pdLine": pdLine.value,
			"t2Flag": t2Flag.value,
			"bDate": bDate.value,
            "madeDept":madeDept.value,
            "groNo":groNo.value
        },
        method: "GET"
    }).then(res => {
		if(res.data.data.list.length > 0){
			tot_qty_sum.value = res.data.data.list[0].tot_qty_sum;
		}else{
			tot_qty_sum.value = 0;
		}
        tableData.value = res.data.data.list
        pageCount.value = res.data.data.total;
        uni.hideLoading();
    }).catch(err => {
        uni.hideLoading();
        console.log(err)
    })
}
const filterUserNo = ref()
const filterGrpNo = ref()

function getMadeDept(user_no){
	filterUserNo.value = user_no;
    uni.request({
        url: urlPrefix + "/mbpt/queryDetailTableData",
        data: {
            "userNo": user_no,
			"grpNo": filterGrpNo.value
        },
        method: "GET"
    }).then(res => {
        detailTableData.value = res.data.data;

        groNoGroup.value = detailTableData.value.map(item => {
            return {
                text: item.grp_no,
                value: item.grp_no
            }
        })

        groNoGroup.value.sort((a, b) => {
            if (a.text < b.text) {
                return -1;
            }
            if (a.text > b.text) {
                return 1;
            }
            return 0;
        });
    }).catch(err => {
        console.log(err)
    })
}

function getMadeDeptByGrpNo(grp_no){
	filterGrpNo.value = grp_no;
    uni.request({
        url: urlPrefix + "/mbpt/queryDetailTableData",
        data: {
            "userNo": filterUserNo.value,
			"grpNo": filterGrpNo.value
        },
        method: "GET"
    }).then(res => {
        detailTableData.value = res.data.data;
    }).catch(err => {
        console.log(err)
    })
}

//查看明细
function viewMkData(ord_no){
    uni.request({
        url: urlPrefix + "/mbpt/queryMkSorderbarDtoByOrdNo",
        data: {
            "ordNo": ord_no
        },
        method: "GET"
    }).then(res => {
        mkData.value = res.data.data;
        uni.hideLoading();
    }).catch(err => {
        uni.hideLoading();
        console.log(err)
    })
    mkPopup.value.open()
}

const currentOrdNo = ref();
const bar_qty = ref();
const currentDept = ref();
//发外投入
function fwtr(ord_no,tot_qty,dept){
	uni.showModal({
	    title: '提示',
	    content: '确定投入吗？',
	    success: function (res) {
	        if (res.confirm) {
				currentOrdNo.value = ord_no;
				bar_qty.value = tot_qty;
				currentDept.value = dept;
				uni.request({
				   url: urlPrefix + "/mbpt/fwtr",
				   data: {
					   "ord_no": ord_no,
					   "made_dept": dept,
					   "bar_qty": tot_qty
				   },
				   method: "POST"
				}).then(res => {
				   uni.hideLoading();
				   showTip('success', res.data.data);
				   query();
				}).catch(err => {
				   uni.hideLoading();
				   console.log(err)
				})
	        } else if (res.cancel) {
	            console.log('用户点击取消');
	        }
	    }
	});
}
//发外投入
function fwcc(ord_no,tot_qty,dept){
	uni.showModal({
	    title: '提示',
	    content: '确定投入吗？',
	    success: function (res) {
	        if (res.confirm) {
				currentOrdNo.value = ord_no;
				bar_qty.value = tot_qty;
				currentDept.value = dept;
				uni.request({
				   url: urlPrefix + "/mbpt/fwcc",
				   data: {
					   "ord_no": ord_no,
					   "made_dept": dept,
					   "bar_qty": tot_qty
				   },
				   method: "POST"
				}).then(res => {
				   uni.hideLoading();
				   showTip('success', res.data.data);
				   query();
				}).catch(err => {
				   uni.hideLoading();
				   console.log(err)
				})
	        } else if (res.cancel) {
	            console.log('用户点击取消');
	        }
	    }
	});
}

//取消投入、产出
function cancel(ord_no,tot_qty,dept,semi_su){
	uni.showModal({
	    title: '提示',
	    content: '确定取消吗？',
	    success: function (res) {
	        if (res.confirm) {
				currentOrdNo.value = ord_no;
				bar_qty.value = tot_qty;
				currentDept.value = dept;
				uni.request({
				   url: urlPrefix + "/mbpt/cancel",
				   data: {
					   "ord_no": ord_no,
					   "made_dept": dept,
					   "bar_qty": tot_qty,
					   "semi_no": "Y",
					   "semi_su": semi_su
				   },
				   method: "POST"
				}).then(res => {
				   uni.hideLoading();
				   showTip('success', res.data.data);
				   query();
				}).catch(err => {
				   uni.hideLoading();
				   console.log(err)
				})
	        } else if (res.cancel) {
	            console.log('用户点击取消');
	        }
	    }
	});
}
//查看明细
function viewMadeDept(ord_no,tot_qty,dept){
    currentOrdNo.value = ord_no;
    bar_qty.value = tot_qty;
    currentDept.value = dept;
    console.log(ord_no)
    getMadeDept('');
    updateSpecificationPopup.value.open()
}

function editMadeDept(row){
    uni.showModal({
        title: '提示',
        content: '确定投入吗？',
        success: function (res) {
            if (res.confirm) {
                console.log('用户点击确定');
                //根据是否已有组别判断是新增还是修改
                uni.showLoading({
                    title: '保存中',
                    mask: true // 设置遮罩层
                });
                if(currentDept.value == '' || currentDept.value == null){
                    uni.request({
                        url: urlPrefix + "/mbpt/addMadeDept",
                        data: {
                            "ord_no": currentOrdNo.value,
                            "made_dept": row.made_dept,
                            "bar_qty": bar_qty.value,
                            "ins_user": row.emp_no
                        },
                        method: "POST"
                    }).then(res => {
                        uni.hideLoading();
                        updateSpecificationPopup.value.close();
                        query();
                    }).catch(err => {
                        uni.hideLoading();
                        console.log(err)
                    })
                }else{
                    uni.request({
                        url: urlPrefix + "/mbpt/editMadeDept",
                        data: {
                            "ord_no": currentOrdNo.value,
                            "made_dept": row.made_dept,
                            "bar_qty": bar_qty.value,
                            "ins_user": row.emp_no
                        },
                        method: "POST"
                    }).then(res => {
                        uni.hideLoading();
                        updateSpecificationPopup.value.close();
                        query();
                    }).catch(err => {
                        uni.hideLoading();
                        console.log(err)
                    })
                }
            } else if (res.cancel) {
                console.log('用户点击取消');
            }
        }
    });


}

//返回首页
function back() {
    let back = getCurrentPages();
    if(back && back.length > 1) {
        uni.navigateBack({
            delta: 1
        });
    } else {
        history.back();
    }
}

async function firstChange(e) {
    uni.showLoading({
        title: '加載中',
        mask: true // 设置遮罩层
    });
    firstPageNo.value = e.current;
    await uni.request({
        url: urlPrefix + "/mbpt/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
            "startTime": startTime.value,
            "endTime": endTime.value,
            "brand": brand.value,
            "devType": devType.value,
            "fileType": fileType.value,
            "cutComplType": cutComplType.value,
            "pdLine": pdLine.value,
            "t2Flag": t2Flag.value,
            "bDate": bDate.value,
            "madeDept":madeDept.value,
            "groNo":groNo.value
        },
        method: "GET"
    }).then(res => {
		if(res.data.data.list.length > 0){
			tot_qty_sum.value = res.data.data.list[0].tot_qty_sum;
		}else{
			tot_qty_sum.value = 0;
		}
        //console.log(res.data);
        tableData.value = res.data.data.list
        uni.hideLoading();
    }).catch(err => {
        uni.hideLoading();
        console.log(err)
    })
}

const tableRef = ref();


//获取数据
function queryAllDevType() {
    uni.request({
        url: urlPrefix + "/mbpt/queryAllDevType",
        method: "GET"
    }).then(res => {
        devTypes.value = res.data.data;
    }).catch(err => {
        console.log(err)
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

//获取数据
async function getData() {
    uni.showLoading({
        title: '加載中',
        mask: true // 设置遮罩层
    });
    tableData.value = [];
    if (tableRef.value) {
        tableRef.value.clearSelection();
    }
    await uni.request({
        url: urlPrefix + "/mbpt/query",
        data: {
            "pageNo": firstPageNo.value,
            "pageSize": firstPageSize.value,
            "startTime": startTime.value,
            "endTime": endTime.value,
            "brand": brand.value,
            "devType": devType.value,
            "pdLine": pdLine.value,
            "t2Flag": t2Flag.value,
            "bDate": bDate.value,
            "madeDept":madeDept.value,
            "groNo":groNo.value
        },
        method: "GET"
    }).then(res => {
		if(res.data.data.list.length > 0){
			tot_qty_sum.value = res.data.data.list[0].tot_qty_sum;
		}else{
			tot_qty_sum.value = 0;
		}
        dataList.value = res.data.data.list;
        pageCount.value = res.data.data.total;
        tableData.value = res.data.data.list;
      /*  searchOrdNoList.value = dataList.value.map(item=>{
            console.log(item.ord_no)
            return {
                value:item.ord_no
                ,text:item.ord_no
            }
        });
        searchOrdNoList.value.sort((a, b) => {
            if (a.text < b.text) {
                return -1;
            }
            if (a.text > b.text) {
                return 1;
            }
            return 0;
        });
*/

        searchOrdNoList.value = dataList.value.map(item=>{
            /*  return {
                  value:item.ord_no
                  ,text:item.ord_no
              }*/
            return item.ord_no
        });
        searchOrdNoList.value.sort((a, b) => {
            if (a < b) {
                return -1;
            }
            if (a > b) {
                return 1;
            }
            return 0;
        });

        let datas = new Set(dataList.value.map((item) => item.b_date));
        bDateTypes.value = []
        for (const date of datas) {
            if(date){
                bDateTypes.value.push({
                    text:date,
                    value:date
                })
            }
        }
        console.log("bDateTypes",bDateTypes.value)

        uni.hideLoading();
    }).catch(err => {
        console.log(err)
        uni.hideLoading();
        uni.showToast({
            title: '获取数据失败..',
            icon: "error"
        });
    })
}

//预加载
onMounted(async () => {
    if(1 == 1){
        // 创建一个新的日期对象e
        var date = new Date();

        // 获取年、月、日
        var year = date.getFullYear();
        var month = date.getMonth() + 1; // 月份从0开始，需要加1
        var day = date.getDate();

        // 格式化日期为yyyy/MM/DD的样式
        var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);

        endTimeText.value = formattedDate;
    }
    if(1 == 1){
        // 创建一个新的日期对象e
        var date = new Date();

        // 获取当前日期的天数
        var temp = date.getDate();

        // 将日期减去一天
        date.setDate(temp - 1);
        // 获取年、月、日
        var year = date.getFullYear();
        var month = date.getMonth() + 1; // 月份从0开始，需要加1
        var day = date.getDate();

        var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
        startTimeText.value = formattedDate;
    }
    await getData()
    queryAllDevType();
    getBrands();
    getMadeDept()
})

//登录校验
function loginCheck(){
    if(uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == ''){
        uni.navigateTo({
            url: `/pages/login/login`,
            animationType: 'pop-in',
            animationDuration: 200
        })
    }
}

onShow(async (props) => {
    loginCheck();
})
</script>

<style lang="scss">
page {
    width: 100%;
    height: 100%;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;
}

.back {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    position: absolute;
    left: 1.5%;
    width: 50px;
    height: 50px;
    top: 2%;
    border-radius: 50%;
    box-shadow: 0 0 5px gray;
    transition: all 0.05s ease-in-out;
    cursor: pointer;
    z-index: 1;
}

.tot_qty_sum{
	display: flex;
	justify-content: space-evenly;
	align-items: center;
	position: absolute;
	right: 1.5%;
	top: 3%;
	cursor: pointer;
	z-index: 1;
}


.search {
	display: flex;
	flex-direction: column;
	margin-left: 1%;
	margin-top: 2%;



	.row {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		/* 每行之间的间距 */
	}

	.inputDate {
		width: 15%;
		margin-left: 5px;
	}

	.search button {
		width: 20%;
	}
}

.container {
    width: 100%;
    height: 95%;
    padding: 1%;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0 0 1px 5px #dddddd;
    position: relative;
}

.right-top-top {
    display: flex;
}

.inpBr {
    width: 15%;
    margin-left: 10rpx;
}

.left-bottom {
    width: 100%;
    height: 10%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.uni-pagination__total {
    margin-right: 0.4375rem !important;
}

.uni-pagination__num-tag {
    min-width: 1.875rem !important;
    background-color: #F0F0F0 !important;
}

.page--active {
    color: white !important;
    background-color: deeppink !important;
}

.title {
    width: 100%;
    overflow-x: auto; /* 添加横向滚动条 */
    height: 65%;
    margin-bottom: 1%;
    margin-top: 1%;
    display: block;
    table-layout: fixed; /* 使用固定布局 */
    border: 2px solid #ddd;

}

.table-container {
    width: 100%;
    overflow-x: auto; /* 使表格容器可横向滚动 */
}

table {
    background-color: #fff;
    table-layout: fixed; /* 使用固定布局 */
    border-collapse: collapse;
    position: relative;
    border-radius: 5px;
    box-sizing: border-box;
}

tbody {
}

thead {
    background-color: #F0F0F0;
}


	
.sticky-header th {
	position: -webkit-sticky; /* For Safari */
	position: sticky;
	top: 0;
	background-color: #F0F0F0; /* 设置表头背景颜色 */
	/*border: 1px solid #ddd; !* 设置边框 *!*/
	padding: 8px; /* 设置内边距 */
	z-index: 1;
	box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.4);
}

th:before{
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background-color: #ddd;
}

th::after{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #ddd;
}

.rowClass {
    width: 150px;
    height: 2.5rem;
  /*  border: 1px solid #ddd;*/
    padding: 8px;
    text-align: center;
    box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
    overflow: visible; /* 允许内容溢出 */
    white-space: nowrap; /* 禁止换行 */
}

.rowClass2 {
    width: 30px;
    height: 1rem;
   /* border: 1px solid #ddd;*/
    padding: 8px;
    text-align: center;
    box-sizing: border-box; /* 确保 padding 不影响宽度计算 */
    // white-space: nowrap; /* 禁止换行 */
}

tr {
    display: table-row;
}

td{
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}



img {
    width: 50px;
    height: auto;
}

.left-bottom {
    width: 100%;
    height: 10%;
    display: flex;
    justify-content: center;
    align-items: center;


    .uni-pagination__total {
        margin-right: 0.4375rem !important;
    }

    .uni-pagination__num-tag {
        min-width: 1.875rem !important;
        background-color: #F0F0F0 !important;
    }

    .page--active {
        color: white !important;
        background-color: deeppink !important;
    }
}

.updateSpecificationPopup {
    .updateSpecificationBox {
        width: 96vw;
        height: 90vh;

        border-radius: 1vw;
        background-color: white;

        .updateSpecificationData {
            width: 100%;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-wrap: wrap;
            height: 85%;

            .updateSpecificationAttribute {
                width: 35%;
                margin-left: 15%;

                .uni-easyinput {
                    width: 70%;
                    margin-left: 1rem;
                }

                .uni-stat__select {
                    width: 70%;
                    margin-left: 1rem;
                }

                .uni-numbox {
                    margin-left: 1rem;
                }

                .uni-file-picker {
                    margin-left: 1rem;
                }
            }
        }
    }
}
</style>