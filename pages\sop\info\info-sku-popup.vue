<!-- 工序信息 SKU 弹框 -->
<script setup>
import { ref, watch, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap, proSeqMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 工序信息 SKU 弹框
const infoSkuPopup = ref()

// 工序信息
const processInfo = ref()

// 输入框 SKU
const skuInput = ref('')
// 聚焦 SKU 输入框
const focusSkuInput = ref(false)
// SKU 列表
const skuList = ref([])
// 可选 SKU 列表
const skuOptionList = ref([])
// 已选 SKU 列表
const selectedSkuList = ref([])

// 工序信息列表
const processInfoList = inject('processInfoList')
// 搜索型体
const searchModel = inject('searchModel')
// 是否只显示自己创建的工序列表
const isMine = inject('isMine')
// 楦头编号
const lastNos = inject('lastNos')
// Outsole
const osNo = inject('osNo')
// 获取工序信息列表
const getProcessInfoList = inject('getProcessInfoList')
// 是否选择所有工序信息
const isSelectedAllProcessInfo = inject('isSelectedAllProcessInfo')

// 获取 SKU 列表
async function getInfoSkuList(param) {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/getSkuList',
    method: 'POST',
    data: {
      model: param
    }
  }).then(res => {
    if (res.data.code) {
      skuList.value = res.data.data ? res.data.data : []
      skuOptionList.value = skuList.value.slice(0, 50)
    } else {
      skuList.value = []
      skuOptionList.value = []
      // tipPopup.value.showTipPopup('warn', '该型体暂无 SKU 列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 显示工序信息 SKU 弹框
async function showInfoSkuPopup(param) {
  processInfo.value = param
  
  skuInput.value = ''
  selectedSkuList.value = processInfo.value.skuList ? processInfo.value.skuList : []
  
  await getInfoSkuList(processInfo.value.model)
  
  infoSkuPopup.value.open()
}

// 选择 SKU
function selectSku(param) {
  if (!selectedSkuList.value.includes(param)) {
    selectedSkuList.value.push(param)
  }
}

// 删除 SKU
function deleteSku(param) {
  selectedSkuList.value = selectedSkuList.value.filter(item => item !== param)
}

// 修改工序信息 SKU
async function updateInfoSku() {
  // if (selectedSkuList.value.length === 0) {
  //   tipPopup.value.showTipPopup('warn', '请选择配色！')
  //   return
  // }
  
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateInfoSku',
    method: 'POST',
    data: {
      model: processInfo.value.model,
      operation: processInfo.value.operation,
      rtgCode: processInfo.value.rtgCode,
      proNos: selectedSkuList.value.map(item => item.proNo).join(';'),
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getProcessInfoList(processInfo.value.operation, processInfo.value.brand)
      tipPopup.value.showTipPopup('success', '修改成功！')
      infoSkuPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

let skuInputTimer = null
watch(skuInput, () => {
  if (skuInputTimer) {
    clearTimeout(skuInputTimer)
  }
  skuInputTimer = setTimeout(() => {
    skuOptionList.value = skuList.value.filter(item => (`${item.proNo} | ${item.skuNo} | ${item.colorDesc}`).includes(skuInput.value.toUpperCase())).slice(0, 50)
  }, 300)
})

defineExpose({
  getInfoSkuList,
  showInfoSkuPopup
})
</script>

<template>
  <uni-popup
    ref="infoSkuPopup"
    type="center"
    :is-mask-click="false"
    class="info-sku-popup"
  >
    <view class="container">
      <view class="top-bar flex-row-between-center">
        <view
          @click="infoSkuPopup.close()"
          class="cancel button"
        >
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <view class="title">
          请选择配色
        </view>
        
        <view
          @click="updateInfoSku()"
          class="submit button"
        >
          <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
        </view>
      </view>
      
      <view class="description flex-row-center">
        {{ operationMap.get(processInfo.operation) ? operationMap.get(processInfo.operation) : '未知' }} - {{ proSeqMap.get(processInfo.proSeq) ? proSeqMap.get(processInfo.proSeq) : '未知' }} - {{ processInfo.model }} - {{ processInfo.rtgCode }}
      </view>
      
      <view class="search">
        <input
          v-model="skuInput"
          @focus="focusSkuInput = true"
          @blur="focusSkuInput = false"
          type="text"
          placeholder="请输入成品编号/SKU/配色"
          class="sku-input input"
          :style="{
            boxShadow: focusSkuInput ? '0 0 5px blue' : '0 0 5px gray'
          }"
        />
        
        <view v-show="skuInput" @click="skuInput = ''" class="sku-clear button">
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <!-- #ifdef APP -->
        <uni-transition
          :show="focusSkuInput"
          mode-class="fade"
          class="sku-option"
        >
        <!-- #endif -->
        <!-- #ifdef WEB -->
        <transition name="fade">
        <view v-show="focusSkuInput" class="sku-option">
        <!-- #endif -->
          <view
            v-for="item in skuOptionList"
            @click="selectSku(item)"
            class="sku-option-item"
          >
            {{ item.proNo }} | {{ item.skuNo }} | {{ item.colorDesc }}
          </view>
          
          <view v-show="skuOptionList.length === 0" class="sku-option-empty">
            暂无数据
          </view>
        <!-- #ifdef WEB -->
        </view>
        </transition>
        <!-- #endif -->
        <!-- #ifdef APP -->
        </uni-transition>
        <!-- #endif -->
      </view>
      
      <view class="select flex-row-start-center">
        <view v-for="item in selectedSkuList" class="selected-sku">
          <view class="button">
            {{ item.proNo }} | {{ item.skuNo }} | {{ item.colorDesc }}
          </view>
          
          <view @click="deleteSku(item)" class="delete-sku">
            <uni-icons type="closeempty" size="20" color="darkred"></uni-icons>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.info-sku-popup {
  .container {
    width: 700px;
    height: 600px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .top-bar {
      width: 100%;
      height: 70px;
      padding: 0 10px;
      
      .title {
        font-size: 22px;
        font-weight: bold;
      }
      
      .cancel, .submit {
        width: 75px;
        height: 50px;
      }
    }
    
    .description {
      width: 100%;
      height: 40px;
      font-size: 20px;
      font-weight: bold;
    }
    
    .search {
      width: 100%;
      height: 70px;
      padding: 10px 20px;
      position: relative;
      
      .sku-input {
        width: 100%;
        height: 100%;
        padding: 0 10px;
      }
      
      .sku-clear {
        width: 60px;
        height: 40px;
        margin-top: 5px;
        position: absolute;
        top: 10px;
        right: 25px;
      }
      
      .sku-option {
        width: calc(100% - 40px);
        max-height: 250px;
        position: absolute;
        top: 100%;
        left: 20px;
        border-radius: 10px;
        box-shadow: 0 0 5px gray;
        background-color: #fdf6e3;
        z-index: 2;
        overflow: auto;
        
        .sku-option-item, .sku-option-empty {
          width: 100%;
          min-height: 50px;
          padding: 5px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 20px;
          font-weight: bold;
          text-align: center;
          border-top: 1px solid #ddd;
        }
        
        .sku-option-item {
          transition: all 0.1s linear;
          
          /* #ifdef APP */
          &:active {
            background-color: #ccc;
          }
          /* #endif */
          
          /* #ifdef WEB */
          cursor: pointer;
          &:hover {
            background-color: #ccc;
          }
          /* #endif */
        }
        
        .sku-option-item:first-child, .sku-option-empty {
          border: none;
        }
      }
    }
    
    .select {
      width: 100%;
      max-height: 420px;
      padding: 5px;
      overflow: auto;
      
      .selected-sku {
        width: 100%;
        margin: 15px 15px 5px 15px;
        position: relative;
        
        .button {
          width: 100%;
          min-height: 40px;
          padding: 5px;
        }
        
        .delete-sku {
          width: 30px;
          height: 30px;
          position: absolute;
          right: -15px;
          top: -15px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: bold;
          text-align: center;
          background-color: #fdf6e3;
          border-radius: 50%;
          box-shadow: 0 0 5px gray;
          transition: all 0.1s linear;
          z-index: 1;
          /* #ifdef WEB */
          cursor: pointer;
          /* #endif */
          
          &:active {
            transform: scale(0.98);
            box-shadow: 0 0 1px gray;
            /* #ifdef APP */
            background-color: #ccc;
            /* #endif */
          }
          
          /* #ifdef WEB */
          &:hover {
            background-color: #ccc;
          }
          /* #endif */
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
  
  .flex-row-start-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
  }
  
  .flex-row-between-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }
  
  /* #ifdef WEB */
  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.3s ease;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  
  .fade-enter-to,
  .fade-leave-from {
    opacity: 1;
  }
  /* #endif */
}
</style>