<script setup>
import { ref, onMounted, watch, reactive } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import urlPrefix from '@/pages/common/urlPrefix.js'

// 生产环境
// const urlPrefix = "http://***********:8200"
const user = uni.getStorageSync("loUserNo")

// 开发环境
// const urlPrefix = "http://**********:8080"
// const user = "zqn"

const brand = ref('AB')
const brandList = ref([])
const brandPopup = ref()
const scrollId = ref('A')
const brandLoading = ref(false)

const model = ref('')
const inputModel = ref('')
const modelList = ref([])
const modelOptionList = ref([])
const modelListShow = ref(false)
const modelPicture = ref('')

const picturePopup = ref()

const operation = ref('')
const operationMap = new Map([
  ["加工", "1"],
  ["裁断", "2"],
  ["准备", "3"],
  ["针车", "4"],
  ["半成品", "5"],
  ["成型", "6"]
])

const processInfoList = ref([])
const processStepList = ref([])
const infoLoading = ref(false)
const stepLoading = ref(false)
const selectProcess = ref('')
const checkAllInfo = ref(false)
const checkAllStep = ref(false)

const deleteInfoPopup = ref()
const deleteProcessInfo = reactive({
  model_no: "",
  operation: "",
  rtg_code: ""
})
const batchDeleteInfoPopup = ref()
const batchDeleteProcessInfo = ref([])

const isEdit = ref(false)
const deleteStepPopup = ref()
const deleteProcessStep = reactive({
  model_no: "",
  operation: "",
  rtg_code: "",
  seq_no: ""
})
const batchDeleteStepPopup = ref()
const batchDeleteProcessStep = ref([])

const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')

const foreverTipPopup = ref()

// 监听输入的型体
watch(inputModel, () => {
  modelOptionList.value = modelList.value.filter(item => item.includes(inputModel.value.toUpperCase())).slice(0, 50)
})

// 返回上一页
function back() {
  uni.navigateBack({
    delta: 1,
    animationType: 'pop-out',
    animationDuration: 200
  })
}

// 显示品牌弹框
function showBrandPopup() {
  brandPopup.value.open()
  scrollId.value = 'A'
  if (brandList.value.length === 0) {
    getBrand()
  }
}

// 获取品牌
async function getBrand() {
  brandLoading.value = true
  await uni.request({
    url: urlPrefix + "/process/getBrand",
    method: "POST",
    timeout: 5000
  }).then(res => {
    brandList.value = res.data.data ? res.data.data : []
  }).catch(err => {
    brandPopup.value.close()
    uni.showToast({
      icon: "error",
      title: "请检查网络！"
    })
  })
  brandLoading.value = false
}

// 选择品牌
function selectBrand(param) {
  if (brand.value === param && modelList.value.length > 0) {
    brandPopup.value.close()
    return
  }
  brand.value = param
  model.value = ''
  inputModel.value = ''
  modelPicture.value = ''
  operation.value = ''
  processInfoList.value = []
  processStepList.value = []
  selectProcess.value = ''
  checkAllInfo.value = false
  checkAllStep.value = false
  isEdit.value = false
  getModel()
  brandPopup.value.close()
}

// 获取型体
function getModel() {
  uni.request({
    url: urlPrefix + "/process/getModel",
    method: "POST",
    timeout: 5000,
    data: {
      brand_no: brand.value
    }
  }).then(res => {
    modelList.value = res.data.data ? res.data.data : []
    modelOptionList.value = modelList.value.filter(item => item.includes(inputModel.value.toUpperCase())).slice(0, 50)
  }).catch(err => {
    uni.showToast({
      icon: "error",
      title: "请检查网络！"
    })
  })
}

// 选择型体
function selectModel(param) {
  if (model.value === param) {
    inputModel.value = param
    return
  }
  model.value = param
  inputModel.value = param
  operation.value = ''
  processInfoList.value = []
  processStepList.value = []
  selectProcess.value = ''
  checkAllInfo.value = false
  checkAllStep.value = false
  isEdit.value = false
  getPicture()
  selectOperation('半成品')
}

// 获取鞋图
function getPicture() {
  uni.request({
    url: urlPrefix + "/process/getPicture",
    method: "POST",
    data: {
      model_no: model.value
    }
  }).then(res => {
    modelPicture.value = res.data.data.model_pic ? res.data.data.model_pic : ""
  }).catch(err => {
    uni.showToast({
      icon: "error",
      title: "请检查网络！"
    })
  })
}

// 选择制程
async function selectOperation(param) {
  if (model.value.length === 0) {
    showTip('warn', '请选择型体！')
    return
  }
  if (operation.value === param) {
    return
  }
  operation.value = param
  processStepList.value = []
  selectProcess.value = ''
  checkAllInfo.value = false
  checkAllStep.value = false
  isEdit.value = false
  await getProcessInfo()
  if (processInfoList.value.length > 0) {
    getProcessStep(processInfoList.value[0].rtg_code)
  }
}

// 获取工序信息
async function getProcessInfo() {
  infoLoading.value = true
  checkAllInfo.value = false
  await uni.request({
    url: urlPrefix + "/process/getProcessInfo",
    method: "POST",
    data: {
      model_no: model.value,
      operation: operationMap.get(operation.value)
    }
  }).then(res => {
    processInfoList.value = res.data.data ? res.data.data : []
    batchDeleteProcessInfo.value = []
  }).catch(err => {
    uni.showToast({
      icon: "error",
      title: "请检查网络！"
    })
  })
  infoLoading.value = false
}

// 获取工序步骤
async function getProcessStep(param) {
  selectProcess.value = param
  checkAllStep.value = false
  isEdit.value = false
  
  stepLoading.value = true
  await uni.request({
    url: urlPrefix + "/process/getProcessStep",
    method: "POST",
    data: {
      model_no: model.value,
      operation: operationMap.get(operation.value),
      rtg_code: param
    }
  }).then(res => {
    processStepList.value = res.data.data ? res.data.data : []
    batchDeleteProcessStep.value = []
  }).catch(err => {
    uni.showToast({
      icon: "error",
      title: "请检查网络！"
    })
  })
  stepLoading.value = false
}

// 选择单个工序信息
function selectItemInfo(param) {
  for (let item of processInfoList.value) {
    if (item.rtg_code === param) {
      item.isChecked = !item.isChecked
      break
    }
  }
  
  let checkNumber = 0
  for (let item of processInfoList.value) {
    if (item.isChecked) {
      checkNumber++
    }
  }
  if (checkNumber === processInfoList.value.length) {
    checkAllInfo.value = true
  } else {
    checkAllInfo.value = false
  }
}

// 选择全部工序信息
function selectAllInfo() {
  checkAllInfo.value = !checkAllInfo.value
  for (let item of processInfoList.value) {
    item.isChecked = checkAllInfo.value ? true : false
  }
}

// 选择单个工序步骤
function selectItemStep(param) {
  for (let item of processStepList.value) {
    if (item.seq_no === param) {
      item.isChecked = !item.isChecked
      break
    }
  }
  
  let checkNumber = 0
  for (let item of processStepList.value) {
    if (item.isChecked) {
      checkNumber++
    }
  }
  if (checkNumber === processStepList.value.length) {
    checkAllStep.value = true
  } else {
    checkAllStep.value = false
  }
}

// 选择全部工序步骤
function selectAllStep() {
  checkAllStep.value = !checkAllStep.value
  for (let item of processStepList.value) {
    item.isChecked = checkAllStep.value ? true : false
  }
}

// 跳转至添加工序信息界面
function addInfo() {
  if (model.value.length === 0) {
    showTip('warn', '请选择型体！')
    return
  }
  
  if (operation.value.length === 0) {
    showTip('warn', '请选择制程！')
    return
  }
  
  let info = {
    model_no: model.value,
    operation: operation.value
  }
  
  uni.navigateTo({
    url: `/pages/process/add-info?info=${JSON.stringify(info)}`,
    animationType: 'pop-in',
    animationDuration: 200
  })
}

// 跳转至修改工序信息界面
function updateInfo(param) {
  uni.navigateTo({
    url: `/pages/process/update-info?info=${JSON.stringify(param)}`,
    animationType: 'pop-in',
    animationDuration: 200
  })
}

// 显示删除工序信息弹框
function showDeleteInfo(param) {
  deleteProcessInfo.model_no = param.model_no
  deleteProcessInfo.operation = param.operation
  deleteProcessInfo.rtg_code = param.rtg_code
  deleteInfoPopup.value.open()
}

// 删除工序信息
function deleteInfo() {
  uni.request({
    url: urlPrefix + "/process/deleteProcessInfo",
    method: "POST",
    data: deleteProcessInfo
  }).then(async (res) => {
    if (res.data.code) {
      showTip('success', '工序信息删除成功！')
    } else {
      showTip('error', '工序信息删除失败！')
    }
    closeDeleteInfo()
    await getProcessInfo()
    if (processInfoList.value.length === 0) {
      selectProcess.value = ''
    }
    getProcessStep(selectProcess.value)
  }).catch(err => {
    uni.showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 关闭删除工序信息弹框
function closeDeleteInfo() {
  deleteProcessInfo.model_no = ''
  deleteProcessInfo.operation = ''
  deleteProcessInfo.rtg_code = ''
  deleteInfoPopup.value.close()
}

// 显示批量删除工序信息弹框
function showBatchDeleteInfo(param) {
  if (model.value.length === 0) {
    showTip('warn', '请选择型体！')
    return
  }
  
  if (operation.value.length === 0) {
    showTip('warn', '请选择制程！')
    return
  }
  
  let checkNumber = 0
  for (let processInfo of processInfoList.value) {
    processInfo.operation = operationMap.get(operation.value)
    if (processInfo.isChecked) {
      checkNumber++
      batchDeleteProcessInfo.value.push(processInfo)
    }
  }
  if (checkNumber === 0) {
    showTip('warn', '请勾选工序信息！')
    return
  }
  batchDeleteInfoPopup.value.open()
}

// 批量删除工序信息
function batchDeleteInfo() {
  uni.request({
    url: urlPrefix + "/process/batchDeleteProcessInfo",
    method: "POST",
    data: {
      processInfoList: batchDeleteProcessInfo.value
    }
  }).then(async (res) => {
    if (res.data.code) {
      showTip('success', '工序信息批量删除成功！')
    } else {
      showTip('error', '工序信息批量删除失败！')
    }
    closeBatchDeleteInfo()
    await getProcessInfo()
    if (processInfoList.value.length === 0) {
      selectProcess.value = ''
    }
    getProcessStep(selectProcess.value)
    checkAllInfo.value = false
  }).catch(err => {
    uni.showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 关闭批量删除工序信息弹框
function closeBatchDeleteInfo() {
  batchDeleteInfoPopup.value.close()
}

// 跳转至添加工序步骤界面
function addStep() {
  if (model.value.length === 0) {
    showTip('warn', '请选择型体！')
    return
  }
  
  if (operation.value.length === 0) {
    showTip('warn', '请选择制程！')
    return
  }
  
  if (selectProcess.value.length === 0) {
    showTip('warn', '请选择工序！')
    return
  }
  
  let step = {
    model_no: model.value,
    operation: operation.value,
    rtg_code: selectProcess.value
  }
  
  uni.navigateTo({
    url: `/pages/process/add-step?step=${JSON.stringify(step)}`,
    animationType: 'pop-in',
    animationDuration: 200
  })
}

// 跳转至修改工序步骤界面
// function updateStep(param) {
//   uni.navigateTo({
//     url: `/pages/process/update-step?step=${JSON.stringify(param)}`,
//     animationType: 'pop-in',
//     animationDuration: 200
//   })
// }

// 修改工序步骤
function updateStep(param) {
  if (!param.skey || param.skey.length === 0) {
    showTip('warn', '请输入排序号！')
    return
  }
  
  if (!param.wk_group || param.wk_group.length === 0) {
    showTip('warn', '请输入加工段！')
    return
  }
  
  if (!param.seq_name || param.seq_name.length === 0) {
    showTip('warn', '请输入工序名称！')
    return
  }
  
  uni.request({
    url: urlPrefix + "/process/updateProcessStep",
    method: "POST",
    data: param
  }).then(res => {
    if (res.data.code) {
      showTip('success', '工序步骤修改成功！')
    } else {
      showTip('error', '工序步骤修改失败！')
    }
    getProcessStep(selectProcess.value)
  }).catch(err => {
    showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 显示删除工序步骤弹框
function showDeleteStep(param) {
  deleteProcessStep.model_no = param.model_no
  deleteProcessStep.operation = param.operation
  deleteProcessStep.rtg_code = param.rtg_code
  deleteProcessStep.seq_no = param.seq_no
  deleteStepPopup.value.open()
}

// 删除工序步骤
function deleteStep() {
  uni.request({
    url: urlPrefix + "/process/deleteProcessStep",
    method: "POST",
    data: deleteProcessStep
  }).then(res => {
    if (res.data.code) {
      showTip('success', '工序步骤删除成功！')
    } else {
      showTip('error', '工序步骤删除失败！')
    }
    closeDeleteStep()
    getProcessStep(selectProcess.value)
  }).catch(err => {
    uni.showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 关闭删除工序步骤弹框
function closeDeleteStep() {
  deleteProcessStep.model_no = ''
  deleteProcessStep.operation = ''
  deleteProcessStep.rtg_code = ''
  deleteProcessStep.seq_no = ''
  deleteStepPopup.value.close()
}

// 显示批量删除工序步骤弹框
function showBatchDeleteStep(param) {
  if (model.value.length === 0) {
    showTip('warn', '请选择型体！')
    return
  }
  
  if (operation.value.length === 0) {
    showTip('warn', '请选择制程！')
    return
  }
  
  if (selectProcess.value.length === 0) {
    showTip('warn', '请选择工序！')
    return
  }
  
  let checkNumber = 0
  for (let processStep of processStepList.value) {
    processStep.operation = operationMap.get(operation.value)
    if (processStep.isChecked) {
      checkNumber++
      batchDeleteProcessStep.value.push(processStep)
    }
  }
  if (checkNumber === 0) {
    showTip('warn', '请勾选工序步骤！')
    return
  }
  batchDeleteStepPopup.value.open()
}

// 批量删除工序步骤
function batchDeleteStep() {
  uni.request({
    url: urlPrefix + "/process/batchDeleteProcessStep",
    method: "POST",
    data: {
      processStepList: batchDeleteProcessStep.value
    }
  }).then(res => {
    if (res.data.code) {
      showTip('success', '工序步骤批量删除成功！')
    } else {
      showTip('error', '工序步骤批量删除失败！')
    }
    closeBatchDeleteStep()
    getProcessStep(selectProcess.value)
    checkAllStep.value = false
  }).catch(err => {
    uni.showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 跳转至模板导入界面
function templateImport() {
  if (model.value.length === 0) {
    showTip('warn', '请选择型体！')
    return
  }
  
  if (operation.value.length === 0) {
    showTip('warn', '请选择制程！')
    return
  }
  
  if (selectProcess.value.length === 0) {
    showTip('warn', '请选择工序！')
    return
  }
  
  if (processStepList.value.length > 0) {
    showTip('warn', '请先清空工序步骤！')
    return
  }
  
  let template = {
    model_no: model.value,
    operation: operation.value,
    rtg_code: selectProcess.value
  }
  
  uni.navigateTo({
    url: `/pages/process/template-import?template=${JSON.stringify(template)}`,
    animationType: 'pop-in',
    animationDuration: 200
  })
}

// 关闭批量删除工序信息弹框
function closeBatchDeleteStep() {
  batchDeleteStepPopup.value.close()
}

// 显示编辑工序步骤
function showEditProcessStep() {
  if (model.value.length === 0) {
    showTip('warn', '请选择型体！')
    return
  }
  
  if (operation.value.length === 0) {
    showTip('warn', '请选择制程！')
    return
  }
  
  if (selectProcess.value.length === 0) {
    showTip('warn', '请选择工序！')
    return
  }
  
  isEdit.value = true
}

// 取消编辑工序步骤
function cancelEditProcessStep() {
  isEdit.value = false
  getProcessStep(selectProcess.value)
}

// 编辑工序步骤
function editProcessStep() {
  let editProcessStepList = []
  for (let item of processStepList.value) {
    if (!item.skey || item.skey.length === 0) {
      showTip('warn', '排序号不能为空！')
      return
    }
    
    if (!item.wk_group || item.wk_group.length === 0) {
      showTip('warn', '加工段不能为空！')
      return
    }
    
    if (!item.seq_name || item.seq_name.length === 0) {
      showTip('warn', '工序名称不能为空！')
      return
    }
    if (item.edit) {
      item.upd_user = user
      editProcessStepList.push(item)
    }
  }
  uni.request({
    url: urlPrefix + "/process/editProcessStep",
    method: "POST",
    data: {
      processStepList: editProcessStepList
    }
  }).then(res => {
    if (res.data.code) {
      showTip('success', '工序步骤编辑成功！')
    } else {
      showTip('error', '工序步骤编辑失败！')
    }
    isEdit.value = false
    getProcessStep(selectProcess.value)
  }).catch(err => {
    uni.showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 跳转至复制工序界面
function copyProcess() {
  if (model.value.length === 0) {
    showTip('warn', '请选择型体！')
    return
  }
  
  uni.navigateTo({
    url: `/pages/process/copy-process?model=${model.value}`,
    animationType: 'pop-in',
    animationDuration: 200
  })
}

// 提示信息
function showTip(type, message) {
  tipType.value = type
  tipMessage.value = message
  tipPopup.value.open()
}

onMounted(() => {
  // getBrand()
  // getModel()
  foreverTipPopup.value.open()
})

// onShow(async () => {
//   if (operation.value) {
//     await getProcessInfo()
//     getProcessStep(selectProcess.value)
//   }
// })
</script>

<template>
  <scroll-view :scroll-y="true" class="container">
    <view class="back">
      <uni-icons @click="back()" type="back" size="36" color="#6fa2ce"></uni-icons>
    </view>
    
    <view class="search">
      <view class="brand" @click="showBrandPopup()">
        {{ brand }}
      </view>
      
      <input
        v-model="inputModel"
        type="text"
        placeholder="请输入型体编号"
        @focus="modelListShow = true"
        @blur="modelListShow = false"
        class="input"
      >
      
      <view
        v-show="inputModel.length > 0"
        @click="inputModel = ''"
        class="clear"
      >
        <uni-icons type="clear" size="34"></uni-icons>
      </view>
      
      <transition name="list">
        <view v-show="modelListShow" class="box">
          <view class="list">
            <view
              v-for="item in modelOptionList"
              :key="item"
              @click="selectModel(item)"
              class="item"
              :style="{ border: modelOptionList.length === 1 ? 'none' : '' }"
            >
              {{ item }}
            </view>
            
            <view
              v-show="modelOptionList.length === 0"
              class="item"
              style="border: none;"
            >
              暂无该型体数据
            </view>
          </view>
        </view>
      </transition>
    </view>
    
    <view v-show="modelPicture.length > 0" class="picture">
      <view class="watermark">
        <img
          :src="'data:image/jpg;base64,' + modelPicture"
          @click="picturePopup.open()"
          alt=""
          class="img"
        />
      </view>
    </view>
    
    <view class="copy" @click="copyProcess()">
      <uni-icons type="link" size="36" color="slateblue"></uni-icons>
    </view>
    
    <view class="operation">
      <view
        @click="selectOperation('加工')"
        class="item"
        :style="{
          color: operation === '加工' ? 'white' : 'black',
          backgroundColor: operation === '加工' ? '#2979ff' : 'white'
        }"
      >
        加工
      </view>
      
      <view
        @click="selectOperation('裁断')"
        class="item"
        :style="{
          color: operation === '裁断' ? 'white' : 'black',
          backgroundColor: operation === '裁断' ? '#2979ff' : 'white'
        }"
      >
        裁断
      </view>
      
      <view
        @click="selectOperation('准备')"
        class="item"
        :style="{
          color: operation === '准备' ? 'white' : 'black',
          backgroundColor: operation === '准备' ? '#2979ff' : 'white'
        }"
      >
        准备
      </view>
      
      <view
        @click="selectOperation('针车')"
        class="item"
        :style="{
          color: operation === '针车' ? 'white' : 'black',
          backgroundColor: operation === '针车' ? '#2979ff' : 'white'
        }"
      >
        针车
      </view>
      
      <view
        @click="selectOperation('半成品')"
        class="item"
        :style="{
          color: operation === '半成品' ? 'white' : 'black',
          backgroundColor: operation === '半成品' ? '#2979ff' : 'white'
        }"
      >
        半成品
      </view>
      
      <view
        @click="selectOperation('成型')"
        class="item"
        :style="{
          color: operation === '成型' ? 'white' : 'black',
          backgroundColor: operation === '成型' ? '#2979ff' : 'white'
        }"
      >
        成型
      </view>
      
      <view
        @click="addInfo()"
        class="item"
        style="color: white; backgroundColor: #18bc37;"
      >
        添加
      </view>
      
      <view
        @click="showBatchDeleteInfo()"
        class="item"
        style="color: white; backgroundColor: #e43d33;"
      >
        批量删除
      </view>
    </view>
    
    <view class="info">
      <table>
        <thead>
          <tr>
            <th style="width: 5%; z-index: 1;">
              <checkbox
                @click="selectAllInfo()"
                :checked="checkAllInfo"
                color="white"
                activeBackgroundColor="violet"
                borderColor="black"
                activeBorderColor="black"
              ></checkbox>
            </th>
            <th style="width: 20%;">型体编号</th>
            <th style="width: 10%;">主要代码</th>
            <th style="width: 15%;">生产类型</th>
            <th style="width: 30%;">材质</th>
            <th style="width: 20%;">操作</th>
          </tr>
        </thead>
        
        <tbody v-show="processInfoList.length > 0">
          <tr
            v-for="item in processInfoList"
            :key="item.rtg_code"
            @click="getProcessStep(item.rtg_code)"
            style="transition: all 0.25s ease-in-out;"
            :style="{ backgroundColor: selectProcess === item.rtg_code ? '#fdf699' : '#fdf6e3' }"
          >
            <td @click.stop="">
              <checkbox
                @click="selectItemInfo(item.rtg_code)"
                :checked="item.isChecked"
                color="white"
                activeBackgroundColor="violet"
                borderColor="black"
                activeBorderColor="black"
              ></checkbox>
            </td>
            <td>{{ item.model_no }}</td>
            <td>{{ item.rtg_code }}</td>
            <td>{{ item.rtg_type }}</td>
            <td>{{ item.material }}</td>
            <td @click.stop="">
              <view class="button">
                <view @click="updateInfo(item)" class="update">
                  修改
                </view>
                
                <view @click="showDeleteInfo(item)" class="delete">
                  删除
                </view>
              </view>
            </td>
          </tr>
        </tbody>
        
        <tbody v-show="processInfoList.length === 0">
          <tr>
            <td colspan="6">暂无数据</td>
          </tr>
        </tbody>
      </table>
      
      <view v-show="infoLoading" class="loading">
        <view class="box">
          <uni-icons type="pyq" size="36" class="icon" color="#8f00ff"></uni-icons>
        </view>
      </view>
    </view>
    
    <view class="button">
      <view @click="addStep()" class="add">
        手动添加
      </view>
      
      <view @click="templateImport()" class="template">
        模板导入
      </view>
      
      <view @click="showBatchDeleteStep()" class="delete">
        批量删除
      </view>
      
      <view class="icon">
        <view v-show="!isEdit" @click="showEditProcessStep()" class="edit">
          <uni-icons type="compose" size="36" color="#f3a73f"></uni-icons>
        </view>
        
        <view v-show="isEdit" @click="cancelEditProcessStep()" class="cancel">
          <uni-icons type="closeempty" size="36" color="#e43d33"></uni-icons>
        </view>
        
        <view v-show="isEdit" @click="editProcessStep()" class="confirm">
          <uni-icons type="checkmarkempty" size="36" color="#45b08c"></uni-icons>
        </view>
      </view>
    </view>
    
    <view class="detail">
      <table>
        <thead>
          <tr>
            <th v-show="!isEdit" style="width: 5%;">
              <checkbox
                @click="selectAllStep()"
                :checked="checkAllStep"
                color="white"
                activeBackgroundColor="violet"
                borderColor="black"
                activeBorderColor="black"
              ></checkbox>
            </th>
            <th style="width: 10%;">工序编号</th>
            <th style="width: 10%;">排序号</th>
            <th style="width: 10%;">加工段</th>
            <th style="width: 25%;">工序名称</th>
            <th style="width: 20%;">备注</th>
            <th v-show="!isEdit" style="width: 20%;">操作</th>
          </tr>
        </thead>
        
        <tbody v-show="processStepList.length > 0">
          <tr v-for="(item, index) in processStepList" :key="index">
            <td v-show="!isEdit">
              <checkbox
                @click="selectItemStep(item.seq_no)"
                :checked="item.isChecked"
                color="white"
                activeBackgroundColor="violet"
                borderColor="black"
                activeBorderColor="black"
              ></checkbox>
            </td>
            
            <td>
              {{ item.seq_no }}
            </td>
            
            <td>
              <view v-show="!isEdit && !item.isUpdate">
                {{ item.skey }}
              </view>
              
              <view v-show="isEdit || item.isUpdate" class="editStep">
                <input v-model="item.skey" @input="item.edit = true" type="digit" class="input" />
              </view>
            </td>
            
            <td>
              <view v-show="!isEdit && !item.isUpdate">
                {{ item.wk_group }}
              </view>
              
              <view v-show="isEdit || item.isUpdate" class="editStep">
                <input v-model="item.wk_group" @input="item.edit = true" type="number" class="input" />
              </view>
            </td>
            
            <td>
              <view v-show="!isEdit && !item.isUpdate">
                {{ item.seq_name }}
              </view>
              
              <view v-show="isEdit || item.isUpdate" class="editStep">
                <textarea
                  v-model="item.seq_name"
                  @input="item.edit = true"
                  placeholder="请输入工序名称"
                  class="textarea"
                ></textarea>
              </view>
            </td>
            
            <td>
              <view v-show="!isEdit && !item.isUpdate">
                {{ item.remark }}
              </view>
              
              <view v-show="isEdit || item.isUpdate" class="editStep">
                <textarea
                  v-model="item.remark"
                  @input="item.edit = true"
                  placeholder="请输入备注"
                  class="textarea"
                ></textarea>
              </view>
            </td>
            
            <td v-show="!isEdit">
              <view class="button">
                <view v-show="!item.isUpdate" @click="item.isUpdate = true" class="update">
                  修改
                </view>
                
                <view v-show="!item.isUpdate" @click="showDeleteStep(item)" class="delete">
                  删除
                </view>
                
                <view v-show="item.isUpdate" @click="updateStep(item)" class="save">
                  保存
                </view>
                
                <view v-show="item.isUpdate" @click="getProcessStep(selectProcess)" class="cancel">
                  取消
                </view>
              </view>
            </td>
          </tr>
        </tbody>
        
        <tbody v-show="processStepList.length === 0">
          <tr>
            <td colspan="7">暂无数据</td>
          </tr>
        </tbody>
      </table>
      
      <view v-show="stepLoading" class="loading">
        <view class="box">
          <uni-icons type="pyq" size="36" class="icon" color="#8f00ff"></uni-icons>
        </view>
      </view>
    </view>
  </scroll-view>
  
  <view class="brand-popup">
    <uni-popup
      ref="brandPopup"
      type="center"
    >
      <view
        v-show="brandList.length > 0"
        class="navigate"
      >
        <view
          v-show="item.data.length > 0"
          v-for="item in brandList"
          @click="scrollId = item.initial"
          class="initial"
          :style="{
            color: item.initial === scrollId ? 'white' : 'black',
            backgroundColor: item.initial === scrollId ? '#b6b7a4' : '#e5e5fa'
          }"
        >
          {{ item.initial }}
        </view>
      </view>
      
      <scroll-view
        :scroll-y="true"
        :scroll-into-view="scrollId"
        :scroll-with-animation="true"
        class="box"
      >
        <view
          v-show="item.data.length > 0"
          v-for="(item, index) in brandList"
          :key="index"
          class="part"
        >
          <view class="initial" :id="item.initial">
            {{ item.initial }}
          </view>
          
          <view
            v-for="brand in item.data"
            :key="brand"
            @click="selectBrand(brand)"
            class="brand"
          >
            {{ brand }}
          </view>
        </view>
        
        <view v-show="brandLoading" class="loading">
          <view class="box">
            <uni-icons type="pyq" size="36" class="icon" color="#8f00ff"></uni-icons>
          </view>
        </view>
      </scroll-view>
    </uni-popup>
  </view>
  
  <view class="picture-popup">
    <uni-popup
      ref="picturePopup"
      type="center"
    >
      <view class="watermark">
        <img
          :src="'data:image/jpg;base64,' + modelPicture"
          alt=""
          class="img"
        >
      </view>
    </uni-popup>
  </view>
  
  <view class="delete-info-popup">
    <uni-popup ref="deleteInfoPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        mode="base"
        title="提示"
        content="你确定要删除该工序信息吗？"
        confirmText="确定"
        cancelText="取消"
        @confirm="deleteInfo()"
        @close="closeDeleteInfo()"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
  
  <view class="batch-delete-info-popup">
    <uni-popup ref="batchDeleteInfoPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        mode="base"
        title="提示"
        content="你确定要批量删除选中的工序信息吗？"
        confirmText="确定"
        cancelText="取消"
        @confirm="batchDeleteInfo()"
        @close="closeBatchDeleteInfo()"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
  
  <view class="delete-step-popup">
    <uni-popup ref="deleteStepPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        mode="base"
        title="提示"
        content="你确定要删除该工序步骤吗？"
        confirmText="确定"
        cancelText="取消"
        @confirm="deleteStep()"
        @close="closeDeleteStep()"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
  
  <view class="batch-delete-step-popup">
    <uni-popup ref="batchDeleteStepPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        mode="base"
        title="提示"
        content="你确定要批量删除选中的工序步骤吗？"
        confirmText="确定"
        cancelText="取消"
        @confirm="batchDeleteStep()"
        @close="closeBatchDeleteStep()"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
  
  <view class="tip-popup">
    <uni-popup
      ref="tipPopup"
      type="message"
    >
      <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
    </uni-popup>
  </view>
  
  <view class="forever-tip-popup">
    <uni-popup
      ref="foreverTipPopup"
      type="message"
    >
      <uni-popup-message type="warn" message="该功能已禁用！请使用 SOP 预估版！" :duration="0"></uni-popup-message>
    </uni-popup>
  </view>
</template>

<style lang="scss">
page {
  width: 100%;
  height: 100%;
  padding: 2.5% 2% 1.5% 2%;
  box-sizing: border-box;
  background-color: #fdf6e3;
}

.container {
  width: 100%;
  height: 100%;
  padding: 1%;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0 0 1px 5px #ddd;
  position: relative;
  pointer-events: none;
  
  .back, .copy {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 1%;
    border-radius: 50%;
    box-shadow: 0 0 5px gray;
    transition: all 0.05s ease-in-out;
    cursor: pointer;
    z-index: 1;
    
    &:active {
      transform: scale(0.97);
      box-shadow: 0 0 1px gray;
    }
  }
  
  .back {
    left: 1.5%;
    pointer-events: auto;
  }
  
  .copy {
    right: 1.5%;
  }
  
  .search {
    width: 100%;
    height: 10%;
    margin-bottom: 1%;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    
    .brand {
      width: 10%;
      height: 80%;
      margin-left: 15%;
      margin-right: 5%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: white;
      font-size: 24px;
      font-weight: bold;
      background: linear-gradient(to right bottom, lightpink, violet);
      border-radius: 10px;
      box-shadow: 0 0 5px gray;
      transition: all 0.05s ease-in-out;
      cursor: pointer;
      
      &:active {
        color: black;
        background: linear-gradient(to right bottom, whitesmoke, lightpink);
        box-shadow: 0 0 1px gray;
        transform: scale(0.97);
      }
    }
    
    .input {
      width: 40%;
      padding: 10px;
      border: 2px solid gray;
      font-size: 24px;
      color: black;
      background-color: white;
      border-radius: 5px;
      
      &:hover {
        border: 2px solid black;
      }
    }
    
    .clear {
      margin-left: -45px;
      margin-top: 2px;
      cursor: pointer;
      z-index: 1;
    }
    
    .box {
      width: calc(40% + 20px + 4px);
      height: 270px;
      position: absolute;
      left: 30%;
      top: 120%;
      z-index: 1;
      
      .list {
        max-height: 270px;
        text-align: center;
        border-radius: 5px;
        color: white;
        font-size: 18px;
        font-weight: bold;
        background-color: gray;
        overflow: auto;
        
        .item {
          box-sizing: border-box;
          padding: 15px 0;
          border-bottom: 1px solid white;
          cursor: pointer;
          
          &:active {
            background-color: #aaa;
          }
        }
      }
      
      &::before {
        content: "";
        position: absolute;
        left: calc(50% - 10px);
        top: -10px;
        border-top: 0px solid transparent;
        border-left: 10px solid transparent;
        border-bottom: 10px solid gray;
        border-right: 10px solid transparent;
      }
    }
  }
  
  .picture {
    height: 9%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 5px;
    left: calc(15% + 10% + 5% + 40% + 20px + 4px + 5%);
    cursor: pointer;
    
    .watermark {
      position: relative;
      
      &::before {
        content: "STELLA";
        position: absolute;
        top: 2px;
        left: 2px;
        color: white;
        font-size: 12px;
        font-weight: bold;
        text-shadow: 0 0 5px black;
        z-index: 1;
        pointer-events: none;
      }
      
      .img {
        max-width: 140px;
        max-height: 50px;
        border-radius: 10px;
        box-shadow: 0 0 5px gray;
        transition: all 0.05s ease-in-out;
        cursor: pointer;
        
        &:active {
          box-shadow: 0 0 1px gray;
          transform: scale(0.97);
        }
      }
    }
  }
  
  .operation {
    width: 100%;
    height: 6%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    
    .item {
      width: 110px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 0 5px gray;
      transition: all 0.05s ease-in-out;
      cursor: pointer;
      
      &:active {
        transform: scale(0.97);
        box-shadow: 0 0 1px gray;
      }
    }
  }
  
  .info, .detail {
    width: 100%;
    height: 40%;
    margin-bottom: 1%;
    position: relative;
    overflow: auto;
    
    table {
      width: 100%;
      font-size: 18px;
      text-align: center;
      
      thead, tbody {
        width: 100%;
        
        tr {
          width: 100%;
          
          th {
            padding: 12px 10px;
            border-bottom: 1px solid #ebeef5;
          }
          
          td {
            padding: 8px 10px;
            border-bottom: 1px solid #ebeef5;
          }
        }
      }
      
      thead tr th {
        position: sticky;
        top: 0;
        background-color: #fdf6e3;
      }
    }
    
    .button {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      
      .update, .delete, .save, .cancel {
        width: 70px;
        height: 40px;
        margin: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 18px;
        font-weight: bold;
        color: white;
        border-radius: 5px;
        box-shadow: 0 0 5px gray;
        transition: all 0.05s ease-in-out;
        cursor: pointer;
        
        &:active {
          transform: scale(0.97);
          box-shadow: 0 0 1px gray;
        }
      }
      
      .update {
        background-color: #f3a73f;
      }
      
      .save {
        background-color: #18bc37;
      }
      
      .delete, .cancel {
        background-color: #e43d33;
      }
    }
  }
  
  .button {
    width: 100%;
    height: 7%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    
    .add, .template, .delete {
      width: 100px;
      height: 40px;
      margin: 0 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: white;
      border-radius: 5px;
      box-shadow: 0 0 5px gray;
      transition: all 0.05s ease-in-out;
      cursor: pointer;
      
      &:active {
        transform: scale(0.97);
        box-shadow: 0 0 1px gray;
      }
    }
    
    .add {
      background-color: #18bc37;
    }
    
    .template {
      background-color: #f3a73f;
    }
    
    .delete {
      background-color: #e43d33;
    }
    
    .icon {
      position: absolute;
      top: 0;
      right: 5%;
      display: flex;
      justify-content: center;
      align-items: center;
      
      .edit, .cancel, .confirm {
        width: 50px;
        height: 50px;
        margin: 0 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        box-shadow: 0 0 5px gray;
        transition: all 0.05s ease-in-out;
        cursor: pointer;
        z-index: 2;
        
        &:active {
          transform: scale(0.97);
          box-shadow: 0 0 1px gray;
        }
      }
    }
  }
  
  .detail {
    height: auto;
    margin-bottom: 0;
    overflow: visible;
    
    table thead tr th {
      z-index: 1;
    }
    
    .editStep {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      
      .input, .textarea {
        width: 100%;
        height: 100%;
        padding: 5px;
        background-color: white;
        border: 1px solid black;
        border-radius: 5px;
        
        &:deep(.uni-input-input) {
          font-size: 18px;
        }
        
        &:deep(.uni-textarea-wrapper) {
          height: 70px;
          text-align: left;
          font-size: 18px;
        }
      }
    }
  }
}

.brand-popup {
  .navigate {
    width: 80vw;
    height: 5vh;
    margin-bottom: 1vh;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    background-color: #e5e5fa;
    border-radius: 2.5vh;
    box-shadow: 0 0 10px white;
    
    .initial {
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 22px;
      font-weight: bold;
      border-radius: 50%;
      transition: all 0.25s ease-in-out;
    }
  }
  
  .box {
    width: 80vw;
    height: 80vh;
    border-radius: 1vw;
    background-color: #fdf6e3;
    box-shadow: 0 0 10px white;
    
    .part {
      width: 100%;
      margin-bottom: 1%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
      
      .initial {
        width: 100%;
        margin-left: 1.5%;
        margin-top: 1%;
        font-size: 32px;
        font-weight: bold;
      }
      
      .brand {
        width: 18%;
        height: 8vh;
        margin: 1%;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        font-size: 24px;
        font-weight: bold;
        color: white;
        background: linear-gradient(to right bottom, pink, blue);
        border-radius: 1vw;
        box-shadow: 0 0 5px gray;
        transition: all 0.05s ease-in-out;
        cursor: pointer;
        
        &:active {
          color: black;
          background: linear-gradient(to right bottom, lightpink, lightblue);
          box-shadow: 0 0 1px gray;
          transform: scale(0.97);
        }
      }
    }
  }
}

.picture-popup {
  .watermark {
    position: relative;
    transition: all 0.15s ease-in-out;
    
    &::before {
      content: "STELLA";
      position: absolute;
      top: 2px;
      left: 2px;
      color: black;
      font-size: 28px;
      font-weight: bold;
      text-shadow: 0 0 5px white;
      z-index: 1;
      pointer-events: none;
    }
    
    &:active {
      transform: scale(1.5);
    }
    
    .img {
      min-width: 100px;
      min-height: 100px;
      max-width: 700px;
      max-height: 400px;
      border-radius: 10px;
      box-shadow: 0 0 10px white;
      
      &:active {
        box-shadow: 0 0 1px white;
      }
    }
  }
}

.delete-info-popup, .batch-delete-info-popup, .delete-step-popup, .batch-delete-step-popup {
  &:deep(.uni-popup-dialog) {
    width: 370px;
  }
  
  &:deep(.uni-dialog-title-text) {
    color: red;
    font-size: 24px;
    font-weight: bold;
  }
  
  &:deep(.uni-dialog-content) {
    padding: 30px;
  }
  
  &:deep(.uni-dialog-content-text) {
    color: black;
    font-size: 18px;
    font-weight: bold;
  }
  
  &:deep(.uni-dialog-button-text) {
    color: gray;
    font-size: 18px;
    font-weight: bold;
  }
  
  &:deep(.uni-button-color) {
    color: #007aff;
  }
}

.tip-popup, .forever-tip-popup {
  pointer-events: none;
  
  &:deep(.fixforpc-width) {
    min-width: 0;
    margin-top: 40px;
    padding: 10px 20px;
  }
  
  &:deep(.uni-popup-message-text) {
    font-size: 18px;
    font-weight: bold;
  }
}

.loading {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  
  .box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    
    .icon {
      animation: 1.5s linear infinite rotate;
      
      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
    }
  }
}

.list-enter-active, .list-leave-active {
  transition: all 0.25s ease-in-out;
}

.list-enter-from, .list-leave-to {
  opacity: 0;
}

.list-enter-to, .list-leave-from {
  opacity: 1;
}
</style>