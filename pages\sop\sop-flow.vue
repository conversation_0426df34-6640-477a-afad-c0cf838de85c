<!-- 工序流程 -->
<script setup>
import { ref, reactive, onMounted, watch, provide } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap, proSeqMap, deptMap, optionListMap } from '@/pages/sop/common/commonMap.js'
import FlowPopup from '@/pages/sop/flow/flow-popup.vue'
import FlowActionPopup from '@/pages/sop/flow/flow-action-popup.vue'
import FlowPartPopup from '@/pages/sop/flow/flow-part-popup.vue'
import FlowOptionPopup from '@/pages/sop/flow/flow-option-popup.vue'
import FlowOptionPopup1 from '@/pages/sop/flow/flow-option-popup1.vue'
import SaveChangePopup from '@/pages/sop/flow/save-change-popup.vue'
import PicturePopup from '@/pages/sop/common/picture-popup.vue'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 工序流程弹框
const flowPopup = ref()
// 工序流程动作弹框
const flowActionPopup = ref()
// 工序流程部位弹框
const flowPartPopup = ref()
// 工序流程选项弹框
const flowOptionPopup = ref()
// 工序流程选项弹框 - 加工
const flowOptionPopup1 = ref()

// 保存工序流程更改弹框
const saveChangePopup = ref()

// 删除工序流程弹框
const deleteFlowPopup = ref()

// 提示弹框
const tipPopup = ref()
// 图片弹框
const picturePopup = ref()

// 接收参数
const props = defineProps({
  info: {
    type: String,
    default: '{}'
  }
})

// 工序信息
const processInfo = ref(JSON.parse(props.info))

// 型体图片
const modelPicture = ref('')

// 工序编号
const seqNo = ref('')
// 序号
const skey = ref(0)
// 加工段
const wkGroup = ref('')
// 流程下标
const flowIndex = ref(-1)
// 是否自动切换流程下标
const isAutoChangeIndex = ref(false)

// 原始工序流程
let originalFlowDetail = {}
// 工序流程详情
const flowDetail = reactive({
  model: processInfo.value.model,  // 型体
  operation: processInfo.value.operation,  // 制程
  rtgCode: processInfo.value.rtgCode,  // 主要代码
  seqNo: '',  // 工序编号
  actions: '',  // 动作
  imgTit1: '',  // 图片备注
  imgList: [],  // 图片列表
  standardId: -1,  // 操作标准 ID
  standard: '',  // 操作标准
  checkPointId: -1,  // 自检点 ID
  checkPoint: '',  // 自检点
  tools: '',  // 工具
  machine: '',  // 机器
  margin: '',  // 边距
  temp: '',  // 温度
  pressure: '',  // 压力
  glue: '',  // 胶水
  carLine: '',  // 车线
  chemical: '',  // 化学品
  needleSpacing: '',  // 针距
  spacing: '',  // 间距
  needle: '',  // 车针
  time: '',  // 时间
  defence: '',  // 防护用品
  insUser: user,  // 添加者
  insDate: '',  // 添加时间
  updUser: user,  // 修改者
  updDate: '',  // 修改时间
  processOption1: '',
  processOption2: '',
  processOption3: '',
  processOption4: '',
  processOption5: '',
  processOption6: '',
  processOption7: '',
  processOption8: '',
  processOption9: '',
  processOption10: '',
  partName: ''  // 部位
})

// 聚焦动作输入框
const focusActionInput = ref(false)
// 聚焦图片备注输入框
const focusPictureInput = ref(false)
// 聚焦操作标准输入框
const focusStandardInput = ref(false)
// 聚焦自检点输入框
const focusCheckPointInput = ref(false)

// 图片映射
let pictureMap = ref(new Map())

// 返回
function back() {
  uni.navigateBack()
}

// 返回之前判断是否需要保存
function beforeBack() {
  if (seqNo.value && isFlowDetailChange()) {
    saveChangePopup.value.showSaveChangePopup(true)
  } else {
    back()
  }
}

// 获取鞋图
async function getModelPicture(param) {
  modelPicture.value = ''
  await uni.request({
    url: urlPrefix + '/sop/getModelPicture',
    method: 'POST',
    data: {
      model: param
    }
  }).then(res => {
    if (res.data.code) {
      modelPicture.value = res.data.data ? ('data:image/jpg;base64,' + res.data.data.modelPicture) : ''
    } else {
      tipPopup.value.showTipPopup('warn', '暂无鞋图数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 上一个工序流程
function lastProcessFlow() {
  if (flowIndex.value === 0) {
    return
  }
  flowIndex.value--
}

// 下一个工序流程
function nextProcessFlow() {
  if (flowIndex.value === flowPopup.value.processFlowList.length - 1) {
    return
  }
  flowIndex.value++
}

// 跳转至动作管理页面
function toSopAction() {
  uni.navigateTo({
    url: `/pages/sop/sop-action`,
    animationType: 'pop-in',
    animationDuration: 300
  })
}

// 获取工序流程详情
async function getFlowDetail(param1, param2, param3, param4) {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/getFlowDetail',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3,
      seqNo: param4
    }
  }).then(res => {
    if (res.data.code) {
      let data = res.data.data
      flowDetail.seqNo = data.seqNo
      flowDetail.actions = data.actions
      flowDetail.imgTit1 = data.imgTit1
      flowDetail.imgList = data.imgList
      flowDetail.standardId = data.standardId
      flowDetail.standard = data.standard
      flowDetail.checkPointId = data.checkPointId
      flowDetail.checkPoint = data.checkPoint
      flowDetail.tools = data.tools
      flowDetail.machine = data.machine
      flowDetail.margin = data.margin
      flowDetail.temp = data.temp
      flowDetail.pressure = data.pressure
      flowDetail.glue = data.glue
      flowDetail.carLine = data.carLine
      flowDetail.chemical = data.chemical
      flowDetail.needleSpacing = data.needleSpacing
      flowDetail.spacing = data.spacing
      flowDetail.needle = data.needle
      flowDetail.time = data.time
      flowDetail.defence = data.defence
      flowDetail.version = data.version
      flowDetail.processOption1 = data.processOption1
      flowDetail.processOption2 = data.processOption2
      flowDetail.processOption3 = data.processOption3
      flowDetail.processOption4 = data.processOption4
      flowDetail.processOption5 = data.processOption5
      flowDetail.processOption6 = data.processOption6
      flowDetail.processOption7 = data.processOption7
      flowDetail.processOption8 = data.processOption8
      flowDetail.processOption9 = data.processOption9
      flowDetail.processOption10 = data.processOption10
      flowDetail.partName = data.partName
      
      // 图片映射
      pictureMap.value.clear()
      for (let p of flowDetail.imgList) {
        let url = urlPrefix + p.imgUrl
        urlToBase64(url).then((res) => {
          pictureMap.value.set(p.id, res)
        }).catch(err => {
          tipPopup.value.showTipPopup('warn', '图片缓存失败！')
        })
      }
      
      originalFlowDetail = {
        ...flowDetail,
        imgList: [...data.imgList]
      }
      
      isAutoChangeIndex.value = false
    } else {
      tipPopup.value.showTipPopup('warn', '暂无工序流程详情数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 图片 url 转 base64 字符串
function urlToBase64(param) {
  return new Promise((resolve, reject) => {
    uni.request({
      url: param,
      responseType: 'arraybuffer'
    }).then(res => {
      let base64 = uni.arrayBufferToBase64(res.data)
      resolve('data:' + res.header['content-type'] + ';base64,' + base64)
    }).catch(err => {
      reject(err)
    })
  })
}

// 选择图片
function selectPicture() {
  uni.chooseImage({
    count: 9,
    sizeType: ['original', 'compressed'],
    extension: ['jpg', 'png'],
    sourceType: ['camera ', 'album'],
    success: async (res) => {
      for (let i = 0; i < res.tempFiles.length; i++) {
        // #ifdef WEB
        let file = res.tempFiles[i]
        let imgUrl = await webReadFileAsDataURL(file)
        // #endif
        
        // #ifdef APP
        let path = res.tempFiles[i].path
        let imgUrl = await appReadFileAsDataURL(path)
        // #endif
        
        flowDetail.imgList.push({
          id: -1,
          imgUrl: imgUrl
        })
      }
    }
  })
}

// web 以 url 方式读取文件
function webReadFileAsDataURL(file) {
  return new Promise((resolve, reject) => {
    let reader = new FileReader()
    reader.onload = () => {
      resolve(reader.result)
    }
    reader.onerror = (err) => {
      reject(err)
    }
    reader.readAsDataURL(file)
  })
}

// app 以 url 方式读取文件
function appReadFileAsDataURL(path) {
  return new Promise((resolve, reject) => {
    plus.io.resolveLocalFileSystemURL(path, (entry) => {
      entry.file((file) => {
        let reader = new plus.io.FileReader()
        reader.onload = () => {
          resolve(reader.result)
        }
        reader.onerror = (err) => {
          reject(err)
        }
        reader.readAsDataURL(file)
      })
    })
  })
}

// 清除图片
function clearPicture(param) {
  flowDetail.imgList.splice(param, 1)
}

// 保存工序流程详情
async function saveFlowDetail(param1, param2, param3) {
  if (!param1.seqNo) {
    tipPopup.value.showTipPopup('warn', '请选择工序流程！')
    return
  }
  
  if (!param1.actions) {
    tipPopup.value.showTipPopup('warn', '动作不能为空！')
    return
  }
  
  uni.showLoading({
    title: '保存中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/saveFlowDetail',
    method: 'POST',
    data: flowDetail
  }).then(res => {
    if (res.data.code) {
      tipPopup.value.showTipPopup('success', '保存成功！')
      if (!param3 && (flowIndex.value < flowPopup.value.processFlowList.length - 1)) {
        isAutoChangeIndex.value = true
        flowIndex.value++
      } else if (!param3 && (flowIndex.value === flowPopup.value.processFlowList.length - 1) && user === processInfo.value.insUser) {
        isAutoChangeIndex.value = true
        insertProcessFlow(param1.model, param1.operation, param1.rtgCode)
      }
    } else {
      tipPopup.value.showTipPopup('error', '保存失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
  
  if (param2) {
    getFlowDetail(param1.model, param1.operation, param1.rtgCode, seqNo.value)
  }
}

// 插入工序流程
async function insertProcessFlow(param1, param2, param3, param4) {
  uni.showLoading({
    title: '添加中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/insertProcessFlow',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3,
      skey: param4 ? param4 : flowPopup.value.processFlowList.length,
      insUser: user,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await flowPopup.value.getProcessFlowList(param1, param2, param3)
      flowIndex.value = param4 ? Math.min(param4, flowPopup.value.processFlowList.length - 1) : (flowPopup.value.processFlowList.length - 1)
      tipPopup.value.showTipPopup('success', '添加成功！')
    } else {
      tipPopup.value.showTipPopup('error', '添加失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 显示删除工序流程弹框
function showDeleteFlowPopup() {
  if (!seqNo.value) {
    tipPopup.value.showTipPopup('warn', '请选择工序流程！')
    return
  }
  
  deleteFlowPopup.value.open()
}

// 删除工序流程
async function deleteProcessFlow() {
  let processFlow = [{
    model: processInfo.value.model,
    operation: processInfo.value.operation,
    rtgCode: processInfo.value.rtgCode,
    seqNo: seqNo.value
  }]
  
  uni.showLoading({
    title: '删除中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/deleteProcessFlow',
    method: 'POST',
    data: {
      processFlowList: processFlow
    }
  }).then(async (res) => {
    if (res.data.code) {
      await flowPopup.value.getProcessFlowList(processInfo.value.model, processInfo.value.operation, processInfo.value.rtgCode)
      changeFlowIndex()
      deleteFlowPopup.value.close()
      tipPopup.value.showTipPopup('success', '删除成功！')
    } else {
      tipPopup.value.showTipPopup('error', '删除失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 判断工序流程详情是否改变
function isFlowDetailChange() {
  const keys = Object.keys(flowDetail)
  for (let key of keys) {
    if (key === 'imgList') {
      let imgList1 = flowDetail.imgList
      let imgList2 = originalFlowDetail.imgList
      
      if (imgList1.length !== imgList2.length) {
        return true
      }
      for (let i = 0; i < imgList1.length; i++) {
        if (imgList1[i].id !== imgList2[i].id || imgList1[i].imgUrl !== imgList2[i].imgUrl) {
          return true
        }
      }
    } else {
      if (flowDetail[key] !== originalFlowDetail[key]) {
        return true
      }
    }
  }
  return false
}

// 保存工序流程更改
async function saveChange(param1, param2) {
  await saveFlowDetail(param1, false, true)
  if (param2) {
    setTimeout(() => {
      back()
    }, 2000)
  } else {
    changeFlowIndex()
  }
}

// 不保存工序流程更改
function notSaveChange(param) {
  if (param) {
    back()
  } else {
    changeFlowIndex()
  }
}

// 下标修改触发
function changeFlowIndex() {
  if (flowIndex.value === -1) {
    flowDetail.seqNo = ''
    flowDetail.actions = ''
    flowDetail.imgTit1 = ''
    flowDetail.imgList = []
    flowDetail.standardId = -1
    flowDetail.standard = ''
    flowDetail.checkPointId = -1
    flowDetail.checkPoint = ''
    flowDetail.tools = ''
    flowDetail.machine = ''
    flowDetail.margin = ''
    flowDetail.temp = ''
    flowDetail.pressure = ''
    flowDetail.glue = ''
    flowDetail.carLine = ''
    flowDetail.chemical = ''
    flowDetail.needleSpacing = ''
    flowDetail.spacing = ''
    flowDetail.needle = ''
    flowDetail.time = ''
    flowDetail.defence = ''
    flowDetail.processOption1 = ''
    flowDetail.processOption2 = ''
    flowDetail.processOption3 = ''
    flowDetail.processOption4 = ''
    flowDetail.processOption5 = ''
    flowDetail.processOption6 = ''
    flowDetail.processOption7 = ''
    flowDetail.processOption8 = ''
    flowDetail.processOption9 = ''
    flowDetail.processOption10 = ''
    flowDetail.partName = ''
    
    seqNo.value = ''
    skey.value = 0
    wkGroup.value = ''
  } else {
    seqNo.value = flowPopup.value.processFlowList[flowIndex.value].seqNo
    skey.value = flowPopup.value.processFlowList[flowIndex.value].skey
    wkGroup.value = flowPopup.value.processFlowList[flowIndex.value].wkGroup
    
    getFlowDetail(processInfo.value.model, processInfo.value.operation, processInfo.value.rtgCode, seqNo.value)
  }
}

watch(flowIndex, () => {
  if (seqNo.value && isFlowDetailChange() && !isAutoChangeIndex.value && flowIndex.value > 0) {
    saveChangePopup.value.showSaveChangePopup()
  } else {
    changeFlowIndex()
  }
})

onMounted(() => {
  getModelPicture(processInfo.value.model)
  if (processInfo.value.operation === '1') {
    flowPartPopup.value.getFlowPartList()
  }
  setTimeout(() => {
    flowPopup.value.showFlowPopup(processInfo.value.model, processInfo.value.operation, processInfo.value.rtgCode)
  }, 600)
})

provide('flowIndex', flowIndex)
provide('isAutoChangeIndex', isAutoChangeIndex)
provide('flowDetail', flowDetail)
provide('processInfo', processInfo)
</script>

<template>
  <view class="sop-flow">
    <view class="top-bar flex-row-start-center">
      <view @click="beforeBack()" class="back button">
        <uni-icons type="back" size="30" color="steelblue"></uni-icons>
      </view>
      
      <view class="model-picture" :style="{ visibility: modelPicture.replace(/ /g, '+').length > 0 ? 'visible' : 'hidden' }">
        <!-- #ifdef APP -->
        <uni-transition
          :show="modelPicture.replace(/ /g, '+').length > 0"
          mode-class="fade"
          class="watermark"
        >
        <!-- #endif -->
        <!-- #ifdef WEB -->
        <transition name="fade">
        <view
          v-show="modelPicture.replace(/ /g, '+').length > 0"
          class="watermark"
        >
        <!-- #endif -->
          <img
            @click="picturePopup.showPicturePopup(modelPicture.replace(/ /g, '+'))"
            :src="modelPicture.replace(/ /g, '+')"
            alt=""
            class="button"
          />
        <!-- #ifdef WEB -->
        </view>
        </transition>
        <!-- #endif -->
        <!-- #ifdef APP -->
        </uni-transition>
        <!-- #endif -->
      </view>
      
      <view
        @click="lastProcessFlow()"
        class="last button"
        :style="{
          visibility: seqNo && flowIndex > 0 ? 'visible' : 'hidden'
        }"
      >
        <uni-icons type="arrow-left" size="30" color="brown"></uni-icons>
      </view>
      
      <view class="title flex-row-center">
        流程详情
      </view>
      
      <view
        @click="nextProcessFlow()"
        class="next button"
        :style="{
          visibility: seqNo && flowIndex < flowPopup.processFlowList.length - 1 ? 'visible' : 'hidden'
        }"
      >
        <uni-icons type="arrow-right" size="30" color="brown"></uni-icons>
      </view>
      
      <view
        @click="flowPopup.showFlowPopup(processInfo.model, processInfo.operation, processInfo.rtgCode, skey)"
        class="list button"
      >
        <uni-icons type="list" size="30" color="chocolate"></uni-icons>
      </view>
      
      <view
        @click="saveFlowDetail(flowDetail, true)"
        class="save button"
      >
        <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
      </view>
      
      <view
        @click="insertProcessFlow(processInfo.model, processInfo.operation, processInfo.rtgCode, skey)"
        class="add button"
        :style="{
          visibility: user === processInfo.insUser ? 'visible' : 'hidden'
        }"
      >
        <uni-icons type="plusempty" size="30" color="green"></uni-icons>
      </view>
      
      <view
        @click="showDeleteFlowPopup()"
        class="delete button"
        :style="{
          visibility: user === processInfo.insUser ? 'visible' : 'hidden'
        }"
      >
        <uni-icons type="trash" size="30" color="red"></uni-icons>
      </view>
    </view>
    
    <view class="process-info flex-row-evenly-center">
      <view class="operation process-info-item flex-row-start-center">
        制程：{{ operationMap.get(processInfo.operation) ? operationMap.get(processInfo.operation) : '未知' }} - {{ proSeqMap.get(processInfo.proSeq) ? proSeqMap.get(processInfo.proSeq) : '未知' }}
      </view>
      
      <view class="brand process-info-item flex-row-start-center">
        品牌：{{ processInfo.brand }}
      </view>
      
      <view class="model process-info-item flex-row-start-center">
        型体：{{ processInfo.model }}
      </view>
      
      <view class="model-desc process-info-item flex-row-start-center">
        型体描述：{{ processInfo.modelDesc ? processInfo.modelDesc : '/' }}
      </view>
      
      <view class="code process-info-item flex-row-start-center">
        主要代码：{{ processInfo.rtgCode }}
      </view>
      
      <view class="type process-info-item flex-row-start-center">
        生产类型：{{ processInfo.rtgType }}
      </view>
      
      <view class="material process-info-item flex-row-start-center">
        材质：{{ processInfo.material ? processInfo.material : '/' }}
      </view>
      
      <view class="last-nos process-info-item flex-row-start-center">
        楦头编号：{{ processInfo.lastNos ? processInfo.lastNos : '/' }}
      </view>
      
      <!-- <view class="id process-info-item flex-row-start-center">
        工序编号：{{ seqNo ? seqNo : '/' }}
      </view> -->
      
      <view class="sequence process-info-item flex-row-start-center">
        序号：{{ skey ? skey : '/' }}
      </view>
      
      <view class="section process-info-item flex-row-start-center">
        加工段：{{ wkGroup ? wkGroup : '/' }}
      </view>
      
      <view class="version process-info-item flex-row-start-center">
        版本：{{ flowDetail.version ? ('v' + flowDetail.version) : '/' }}
      </view>
      
      <view class="outsole process-info-item flex-row-start-center">
        Outsole：{{ processInfo.osNo ? processInfo.osNo : '/' }}
      </view>
    </view>
    
    <view
      v-show="seqNo"
      class="flow-detail flex-row-between-center"
      :style="{
        filter: seqNo ? 'none' : 'blur(5px)',
        pointerEvents: seqNo ? 'auto' : 'none'
      }"
    >
      <view class="action">
        <view class="action-title flex-row-between-start">
          <text>动作</text>
          
          <view class="action-operate">
            <!-- <view
              @click="toSopAction()"
              class="sop-action button"
            >
              <uni-icons type="plusempty" size="30" color="green"></uni-icons>
            </view> -->
            
            <view
              @click="flowActionPopup.showFlowActionPopup(processInfo.proSeq)"
              class="action-search button"
              :style="{
                visibility: user === processInfo.insUser ? 'visible' : 'hidden'
              }"
            >
              <uni-icons type="search" size="30" color="orangered"></uni-icons>
            </view>
          </view>
        </view>
        
        <textarea
          v-model="flowDetail.actions"
          :disabled="user !== processInfo.insUser"
          @focus="focusActionInput = true"
          @blur="focusActionInput = false"
          :maxlength="-1"
          placeholder="请输入动作"
          class="action-content textarea"
          :style="{
            boxShadow: focusActionInput ? '0 0 5px blue' : '0 0 5px gray'
          }"
        ></textarea>
        
        <view
          v-show="flowDetail.actions && user === processInfo.insUser"
          @click="flowDetail.actions = ''"
          class="action-clear-content button"
        >
          <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
        </view>
      </view>
      
      <view class="option">
        <view class="option-title">
          选项
        </view>
        
        <view
          v-show="deptMap.get(processInfo.operation) === 1 || deptMap.get(processInfo.operation) === 2"
          class="option-list flex-row-start"
        >
          <view v-show="deptMap.get(processInfo.operation) === 1" class="chemical option-item">
            <text>化学品：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('chemical', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.chemical ? flowDetail.chemical : '请选择' }}
            </view>
          </view>
          
          <view v-show="deptMap.get(processInfo.operation) === 1" class="defence option-item">
            <text>防护用品：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('defence', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.defence ? flowDetail.defence : '请选择' }}
            </view>
          </view>
          
          <view class="machine option-item">
            <text>机器：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('machine', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.machine ? flowDetail.machine : '请选择' }}
            </view>
          </view>
          
          <view v-show="deptMap.get(processInfo.operation) === 1" class="temp option-item">
            <text>温度：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('temp', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.temp ? flowDetail.temp : '请选择' }}
            </view>
          </view>
          
          <view v-show="deptMap.get(processInfo.operation) === 1" class="time option-item">
            <text>时间：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('time', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.time ? flowDetail.time : '请选择' }}
            </view>
          </view>
          
          <view v-show="deptMap.get(processInfo.operation) === 1" class="pressure option-item">
            <text>压力：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('pressure', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.pressure ? flowDetail.pressure : '请选择' }}
            </view>
          </view>
          
          <view class="tools option-item">
            <text>工具：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('tools', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.tools ? flowDetail.tools : '请选择' }}
            </view>
          </view>
          
          <view v-show="deptMap.get(processInfo.operation) === 2" class="glue option-item">
            <text>胶水：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('glue', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.glue ? flowDetail.glue : '请选择' }}
            </view>
          </view>
          
          <view v-show="deptMap.get(processInfo.operation) === 2" class="carLine option-item">
            <text>车线：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('carLine', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.carLine ? flowDetail.carLine : '请选择' }}
            </view>
          </view>
          
          <view v-show="deptMap.get(processInfo.operation) === 2" class="margin option-item">
            <text>边距：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('margin', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.margin ? flowDetail.margin : '请选择' }}
            </view>
          </view>
          
          <view v-show="deptMap.get(processInfo.operation) === 2" class="needleSpacing option-item">
            <text>针距：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('needleSpacing', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.needleSpacing ? flowDetail.needleSpacing : '请选择' }}
            </view>
          </view>
          
          <view v-show="deptMap.get(processInfo.operation) === 2" class="spacing option-item">
            <text>间距：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('spacing', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.spacing ? flowDetail.spacing : '请选择' }}
            </view>
          </view>
          
          <view class="needle option-item">
            <text>车针：</text>
            
            <view
              @click="flowOptionPopup.showFlowOptionPopup('needle', processInfo.operation)"
              class="button"
            >
              {{ flowDetail.needle ? flowDetail.needle : '请选择' }}
            </view>
          </view>
        </view>
        
        <view v-show="deptMap.get(processInfo.operation) === 3" class="option-list flex-row-start">
          <view class="option-item">
            <text>部位：</text>
            
            <view
              @click="flowPartPopup.showFlowPartPopup()"
              class="button"
            >
              {{ flowDetail.partName ? flowDetail.partName : '请选择' }}
            </view>
          </view>
          
          <view
            v-for="(item, index) in optionListMap.get(processInfo.proSeq)"
            :key="index"
            class="option-item"
          >
            <text>{{ item }}：</text>
            
            <view
              @click="flowOptionPopup1.showFlowOptionPopup(index, processInfo.proSeq)"
              class="button"
            >
              {{ flowDetail['processOption' + (index + 1)] ? flowDetail['processOption' + (index + 1)] : '请选择' }}
            </view>
          </view>
        </view>
      </view>
      
      <view class="picture">
        <view class="picture-title">
          图片
        </view>
        
        <view class="picture-detail">
          <textarea
            v-model="flowDetail.imgTit1"
            @focus="focusPictureInput = true"
            @blur="focusPictureInput = false"
            :maxlength="-1"
            placeholder="请输入图片备注"
            class="picture-remark textarea"
            :style="{
              boxShadow: focusPictureInput ? '0 0 5px blue' : '0 0 5px gray'
            }"
          ></textarea>
          
          <view
            v-show="flowDetail.imgTit1"
            @click="flowDetail.imgTit1 = ''"
            class="picture-clear-remark button"
          >
            <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
          </view>
          
          <view class="picture-list flex-row-start">
            <view v-for="(item, index) in flowDetail.imgList" class="preview-picture watermark">
              <img
                @click="picturePopup.showPicturePopup(item.id > 0 ? (pictureMap.get(item.id) ? pictureMap.get(item.id) : (urlPrefix + item.imgUrl)) : item.imgUrl)"
                :src="item.id > 0 ? (pictureMap.get(item.id) ? pictureMap.get(item.id) : (urlPrefix + item.imgUrl)) : item.imgUrl"
                alt=""
                class="button"
              />
              
              <view @click="clearPicture(index)" class="clear-picture">
                <uni-icons type="closeempty" size="20" color="darkred"></uni-icons>
              </view>
            </view>
            
            <view @click="selectPicture()" class="add-picture button">
              <uni-icons type="plusempty" size="50" color="green"></uni-icons>
            </view>
          </view>
        </view>
      </view>
      
      <view class="detail">
        <view class="standard">
          <view class="standard-title">
            操作标准
          </view>
          
          <textarea
            v-model="flowDetail.standard"
            @focus="focusStandardInput = true"
            @blur="focusStandardInput = false"
            :maxlength="-1"
            placeholder="请输入操作标准"
            class="standard-content textarea"
            :style="{
              boxShadow: focusStandardInput ? '0 0 5px blue' : '0 0 5px gray'
            }"
          ></textarea>
          
          <view
            v-show="flowDetail.standard"
            @click="flowDetail.standard = ''"
            class="standard-clear-content button"
          >
            <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
          </view>
        </view>
        
        <view class="check-point">
          <view class="check-point-title">
            自检点
          </view>
          
          <textarea
            v-model="flowDetail.checkPoint"
            @focus="focusCheckPointInput = true"
            @blur="focusCheckPointInput = false"
            :maxlength="-1"
            placeholder="请输入自检点"
            class="check-point-content textarea"
            :style="{
              boxShadow: focusCheckPointInput ? '0 0 5px blue' : '0 0 5px gray'
            }"
          ></textarea>
          
          <view
            v-show="flowDetail.checkPoint"
            @click="flowDetail.checkPoint = ''"
            class="check-point-clear-content button"
          >
            <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <uni-popup
    ref="deleteFlowPopup"
    type="center"
    :is-mask-click="false"
    class="delete-flow-popup"
  >
    <view class="container">
      <view class="title">
        提示
      </view>
      
      <view class="context">
        你确定要删除该工序流程吗？
      </view>
      
      <view class="operate">
        <view @click="deleteFlowPopup.close()" class="cancel button">
          取消
        </view>
        
        <view @click="deleteProcessFlow()" class="confirm button">
          确定
        </view>
      </view>
    </view>
  </uni-popup>
  
  <flow-popup ref="flowPopup" @change-flow-index="changeFlowIndex" />
  <flow-action-popup ref="flowActionPopup" />
  <flow-part-popup ref="flowPartPopup" />
  <flow-option-popup ref="flowOptionPopup" />
  <flow-option-popup1 ref="flowOptionPopup1" />
  <save-change-popup ref="saveChangePopup" @save-change="saveChange" @not-save-change="notSaveChange" />
  <picture-popup ref="picturePopup" />
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss">
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

page {
  width: 100%;
  height: 100%;
  padding: 2.5% 2% 1.5% 2%;
  background-color: #fdf6e3;
}

.sop-flow {
  width: 100%;
  height: 100%;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 0 1px 5px #ddd;
  
  .top-bar {
    width: 100%;
    height: 50px;
    margin-bottom: 20px;
    
    .back, .model-picture {
      height: 50px;
      margin-right: 9.5%;
    }
    
    .back {
      width: 6%;
    }
    
    .model-picture {
      width: 9%;
    }
    
    .model-picture img {
      min-width: 50px;
      max-width: 100%;
      height: 50px;
    }
    
    .title {
      width: 20%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .last, .next, .list, .save, .add, .delete {
      width: 6%;
      height: 50px;
    }
    
    .list {
      margin-left: 4%;
      margin-right: 2%;
    }
    
    .save, .add {
      margin-right: 2%;
    }
  }
  
  .process-info {
    width: 100%;
    height: 120px;
    padding: 0px 10px;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    overflow: auto;
    
    .process-info-item {
      width: calc(100% / 4);
      height: 40px;
    }
  }
  
  .flow-detail {
    width: 100%;
    height: calc(100% - 210px);
    align-content: space-between;
    
    .action, .option, .picture, .detail {
      height: calc(50% - 10px);
      padding: 10px;
      border-radius: 10px;
      box-shadow: 0 0 5px gray;
      overflow: auto;
    }
    
    .action, .picture {
      width: calc(30% - 10px);
    }
    
    .option, .detail {
      width: calc(70% - 10px);
    }
    
    .action {
      position: relative;
      
      .action-title {
        width: 100%;
        height: 40px;
        
        text {
          font-size: 20px;
          font-weight: bold;
        }
        
        .action-operate {
          display: flex;
          
          .sop-action, .action-search {
            padding: 0 20px;
          }
          
          .sop-action {
            margin-right: 20px;
          }
        }
      }
      
      .action-content {
        width: 100%;
        height: calc(100% - 40px);
        padding: 5px;
      }
      
      .action-clear-content {
        width: 70px;
        height: 30px;
        position: absolute;
        right: 15px;
        bottom: 15px;
      }
    }
    
    .option {
      .option-title {
        width: 100%;
        height: 30px;
        font-size: 20px;
        font-weight: bold;
      }
      
      .option-list {
        width: 100%;
        height: calc(100% - 30px);
        align-items: stretch;
        overflow: auto;
        
        .option-item {
          width: calc(100% / 3);
          min-height: 50px;
          padding: 5px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          
          text {
            white-space: nowrap;
          }
          
          .button {
            padding: 5px;
            flex-grow: 1;
            word-break: break-all;
            text-align: start;
          }
        }
      }
    }
    
    .picture {
      padding: 0;
      
      .picture-title {
        width: 100%;
        height: 40px;
        padding: 10px 10px 0 10px;
        font-size: 20px;
        font-weight: bold;
      }
      
      .picture-detail {
        width: 100%;
        height: calc(100% - 40px);
        padding: 10px;
        overflow-x: hidden;
        overflow-y: auto;
        position: relative;
        
        .picture-remark {
          width: 100%;
          height: 60px;
          padding: 5px;
        }
        
        .picture-clear-remark {
          width: 70px;
          height: 30px;
          position: absolute;
          right: 15px;
          top: 35px;
        }
        
        .picture-list {
          width: 100%;
          padding: 5px;
          
          .preview-picture {
            width: calc((100% - 45px) / 4);
            margin-top: 15px;
            margin-right: 15px;
            position: relative;
            
            img {
              width: 100%;
              height: 70px;
              overflow: hidden;
            }
            
            .clear-picture {
              width: 30px;
              height: 30px;
              position: absolute;
              right: -15px;
              top: -15px;
              display: flex;
              justify-content: center;
              align-items: center;
              font-weight: bold;
              text-align: center;
              background-color: #fdf6e3;
              border-radius: 50%;
              box-shadow: 0 0 5px gray;
              transition: all 0.1s linear;
              z-index: 1;
              /* #ifdef WEB */
              cursor: pointer;
              /* #endif */
              
              &:active {
                transform: scale(0.98);
                box-shadow: 0 0 1px gray;
                /* #ifdef APP */
                background-color: #ccc;
                /* #endif */
              }
              
              /* #ifdef WEB */
              &:hover {
                background-color: #ccc;
              }
              /* #endif */
            }
          }
          
          .preview-picture:nth-child(4n) {
            margin-right: 0px;
          }
          
          .add-picture {
            width: calc((100% - 45px) / 4);
            height: 70px;
            margin-top: 15px;
          }
        }
      }
    }
    
    .detail {
      padding: 0;
      display: flex;
      
      .standard, .check-point {
        width: 50%;
        height: 100%;
        padding: 10px;
        position: relative;
        
        .standard-title, .check-point-title {
          width: 100%;
          height: 40px;
          font-size: 20px;
          font-weight: bold;
        }
        
        .standard-content, .check-point-content {
          width: 100%;
          height: calc(100% - 40px);
          padding: 5px;
        }
        
        .standard-clear-content, .check-point-clear-content {
          width: 70px;
          height: 30px;
          position: absolute;
          right: 15px;
          bottom: 15px;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .watermark {
    position: relative;
    word-break: break-all;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    
    &:before {
      content: 'STELLA';
      position: absolute;
      top: 2px;
      left: 2px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      text-shadow: 0 0 2px black;
      z-index: 1;
      pointer-events: none;
    }
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
    }
  }
  
  .textarea {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-textarea-placeholder), &:deep(.uni-textarea-textarea) {
      width: 100%;
      height: 100%;
      font-size: 18px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
  
  .flex-row-start {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
  }
  
  .flex-row-start-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
  }
  
  .flex-row-evenly-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    align-items: center;
  }
  
  .flex-row-between-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }
  
  .flex-row-between-start {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
  }
  
  .flex-column-start {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
  }
  
  /* #ifdef WEB */
  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.3s ease;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  
  .fade-enter-to,
  .fade-leave-from {
    opacity: 1;
  }
  /* #endif */
}

.delete-flow-popup {
  .container {
    padding: 10px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      padding: 5px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      color: red;
      font-size: 22px;
      font-weight: bold;
    }
    
    .context {
      padding: 30px;
      color: black;
      font-size: 20px;
      font-weight: bold;
    }
    
    .operate {
      padding: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .cancel, .confirm {
        width: 145px;
        height: 45px;
        font-size: 20px;
      }
      
      .cancel {
        color: darkred;
      }
      
      .confirm {
        color: lightseagreen;
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
}
</style>