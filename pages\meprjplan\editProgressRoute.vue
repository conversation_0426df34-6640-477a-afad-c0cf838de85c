<script setup>
import {ref, watch, reactive, defineProps, onMounted, getCurrentInstance} from 'vue'
import {pathToBase64,base64ToPath} from '@/pages/common/image-tools.js'
import {onLoad, onReady } from '@dcloudio/uni-app'
import urlPrefix from '@/pages/common/urlPrefix.js'


const currentInstance = getCurrentInstance()
const data = ref('') 
const url = ref(null);
const path = ref(null);

onLoad((option) => {
	const id = option.id
	
		  // #ifdef H5
				url.value = `/pcc/hybrid/html/rich.html?id=` +id +"&urlPrefix=" + urlPrefix  +"&user=" +  uni.getStorageSync("loginUserName") 
		  // #endif
		  
		  // #ifdef APP-PLUS
				url.value = `/hybrid/html/rich.html?id=` + id +"&urlPrefix=" + urlPrefix  +"&user=" +  uni.getStorageSync("loginUserName") 
		  // #endif
	
	
   	// url.value = `/pcc/hybrid/html/image_styles.html`
});

  
  onReady(() => {
	// console.log("---------onReady--------------")
    window.addEventListener('message', handleMessageWeb);
  });
  
const handleMessageWeb = (event) => {
	// console.log("---------handleMessageWeb--------------")
  // console.log('Received message:', event.data);
  if(event.data.key == 'success'){
				uni.navigateTo({
							url: `/pages/meprjplan/progressRouteList`,
							animationType: 'pop-in',
							animationDuration: 200
						})
  }
};
  

const handleMessage = e => {
		// console.log("-------------------handleMessage-------------------------------")
	    const data = e.detail.data;
	    if (data.length > 0) {
	        const firstElement = data[0];
	        if (firstElement && firstElement.action) {
	            const action = firstElement.action;
	            if (action === 'cancel') {
					
					uni.navigateBack({
					  delta: 1
					});
					
	            }else if (action === 'save') {
					const data = firstElement.data;
					// console.log('--------------save---------------------' + data);
						uni.navigateTo({
									url: `/pages/meprjplan/progressRouteList`,
									animationType: 'pop-in',
									animationDuration: 200
								})
	            }else if (action === 'back') {
							// console.log("-------------------back-------------------------------")
					let back = getCurrentPages();
					if (back && back.length > 1) {
						uni.navigateBack({
							delta: 1
						});
					} else {
						history.back();
					}
					
				} else {
	                console.log('Unknown action:', action);
	            }
	        } else {
	            console.log('No action found in the message data');
	        }
	    } else {
	        console.log('Message data is empty');
	    }
}
		

function closeCurrentPageWithResult(path) {
	  const data = {
	    path: path
	  };
    uni.setStorage({
      key: 'customData',
      data: data,
      success: () => {
        uni.navigateBack({
          delta: 1
        });
      }
    });
}

</script>

<template>
 <view class="container">
    <web-view :src="url" @message="handleMessage"></web-view>

  </view>
</template>

<style lang="scss">
	
</style>