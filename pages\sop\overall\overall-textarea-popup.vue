<!-- 图片备注、操作标准、自检点 弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()
// 文本弹框
const overallTextareaPopup = ref()

// 文本输入框
const overallTextareaInput = ref('')
// 聚焦文本输入框
const focusOverallTextareaInput = ref(false)
// 文本类型
const overallTextareaType = ref('')
// 文本类型映射
const overallTextareaTypeMap = new Map([
  ['imgTit1', '图片备注'],
  ['standard', '操作标准'],
  ['checkPoint', '自检点']
])

// 工序流程详情
const flowDetail = ref({})

// 工序信息
const processInfo = inject('processInfo')
// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getOverallFlow = inject('getOverallFlow')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')

// 显示文本弹框
async function showOverallTextareaPopup(param1, param2) {
  overallTextareaType.value = param1
  flowDetail.value = {
    ...param2,
    imgList: []
  }
  overallTextareaInput.value = param2[param1]
  overallTextareaPopup.value.open()
}

// 修改整体流程
async function updateOverallFlow() {
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateOverallFlow',
    method: 'POST',
    data: {
      ...flowDetail.value,
      [overallTextareaType.value]: overallTextareaInput.value,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getOverallFlow()
      tipPopup.value.showTipPopup('success', '修改成功！')
      overallTextareaPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  showOverallTextareaPopup
})
</script>

<template>
  <uni-popup
    ref="overallTextareaPopup"
    type="center"
    :is-mask-click="false"
    class="overall-textarea-popup"
  >
    <view class="container">
      <view class="top-bar">
        <view
          @click="overallTextareaPopup.close()"
          class="cancel button"
        >
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <view class="title">
          请输入{{ overallTextareaTypeMap.get(overallTextareaType) }} - {{ flowDetail.skey }}
        </view>
        
        <view
          @click="updateOverallFlow()"
          class="submit button"
        >
          <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
        </view>
      </view>
      
      <view v-show="flowDetail.actions" class="flow-action flex-row-center">
        <view class="flow-action-description">
          {{ flowDetail.actions }}
        </view>
      </view>
      
      <view class="overall-textarea-input flex-row-center">
        <textarea
          v-model="overallTextareaInput"
          @focus="focusOverallTextareaInput = true"
          @blur="focusOverallTextareaInput = false"
          :placeholder="'请输入' + overallTextareaTypeMap.get(overallTextareaType)"
          class="textarea"
          :style="{
            boxShadow: focusOverallTextareaInput ? '0 0 5px blue' : '0 0 5px gray'
          }"
        ></textarea>
        
        <view
          v-show="overallTextareaInput"
          @click="overallTextareaInput = ''"
          class="clear-textarea button"
        >
          <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.overall-textarea-popup {
  .container {
    width: 500px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .top-bar {
      width: 100%;
      height: 70px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title {
        font-size: 22px;
        font-weight: bold;
      }
      
      .cancel, .submit {
        width: 75px;
        height: 50px;
      }
    }
    
    .flow-action {
      width: 100%;
      padding: 10px;
      
      .flow-action-description {
        width: 400px;
        font-size: 20px;
        font-weight: bold;
        text-align: center;
      }
    }
    
    .overall-textarea-input {
      width: 100%;
      height: 260px;
      position: relative;
      
      .textarea {
        width: 350px;
        height: 240px;
        padding: 5px;
      }
      
      .clear-textarea {
        width: 70px;
        height: 40px;
        position: absolute;
        right: 80px;
        bottom: 15px;
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .textarea {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-textarea-placeholder), &:deep(.uni-textarea-textarea) {
      width: 100%;
      height: 100%;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>