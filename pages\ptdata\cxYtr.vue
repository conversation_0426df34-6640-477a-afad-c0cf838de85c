<template>
	<view class="container">
	 <view class="right-top-top">
		<uni-icons class="back" @click="back" type="back" size="36" style="margin-left:1vw;margin-top:10rpx;"></uni-icons>
		<view class="uni-padding-wrap" style="margin-top:1.22vw;margin-left: 1.5vw;">
			<view>
				<radio-group>	
				<label class="radio"><radio @click="radioChange2(6)" value="6" :checked="cxStatus === 6"/>{{ t('成型全部') }}</label>
				<label class="radio"><radio @click="radioChange2(4)" value="4" :checked="cxStatus === 4"/>{{ t('成型A') }}</label>
				<label class="radio"><radio @click="radioChange2(5)" value="5" :checked="cxStatus === 5"/>{{ t('成型B') }}</label>
				</radio-group>
			</view>
		</view>
		<uni-title
		  type="h2"
		  :title="t('成型已投入未产出查询')"
		  align="center"
		  style="margin-top:-0.7vw;margin-left: -4vw;"
		></uni-title>
		<view class="uni-padding-wrap" style="margin-top:1.22vw;margin-right: 1.5vw;">
			<view>
				<radio-group>	
				<label class="radio"><radio @click="radioChange(1)" value="1" :checked="qhStatus === 1" />{{ t('数据全部') }}</label>
				<label class="radio"><radio @click="radioChange(2)" value="2" :checked="qhStatus === 2"/>{{ t('异常') }}</label>
				<label class="radio"><radio @click="radioChange(3)" value="3" :checked="qhStatus === 3"/>{{ t('正常') }}</label>
				</radio-group>
			</view>
		</view>
		<!-- <text style="color: #c5c8c8;margin-right:0.5vw;margin-top:1.22vw;">{{Qstatus==1?"按架位":"全部数"}}</text><uv-switch inactive-color="#c5c8c8" :value="Qstatus==1?true:false" @change="asyncChange"  style="margin-right:1vw;margin-top:1vw;"></uv-switch> -->
	</view>


	<view class="cTr" style="height: 86%;margin-top:20rpx;">
		<zb-table
			:show-header="true"
			:columns="column"
			:stripe="true"
			:isShowLoadMore="true"
			ref="zbTable"
			:highlight="true"
			show-summary
			:currentChange="currentChange"
			:pullUpLoading="pullUpLoadingAction"
			:summary-method="getSummaries"
			@sort-change="sortChange"
			:border="true"
			:cell-style="cellStyle"
			:data="dataList"></zb-table>
	</view>
	
	<view class="uwrap" style="margin-top: 1vw;">
		<text style="margin-left: 1vw;color: lightcoral;font-weight: 600;">{{ t('总笔数：') }} {{countSum}}</text>
		<text style="margin-right: 1vw;float: right;color: darkgrey;">{{ t('注:红色-时数大于8，黄色-时数大于5小于8，共为异常时数') }}</text>
	</view>
	

	</view>
</template>

<script setup>
  import { onMounted,ref, reactive, getCurrentInstance } from 'vue'
  import { onPullDownRefresh } from '@dcloudio/uni-app'
  import { useI18n } from 'vue-i18n'
  import urlPrefix from '@/pages/common/urlPrefix.js'
  
  // 国际化
  const { t } = useI18n()
  
	// const urlPrefix = "http://***********:8200";
	//const urlPrefix = "http://***********:8080  ***********:8200";

	// 获取当前组件实例
	const { ctx } = getCurrentInstance();
	
	const selectWt=ref()
	const wtsm1=ref([])
	const switchDay=ref(false)
	//第几页
	const firstPageNo=ref(1)
	const firstPageSize=ref(13)
	const pageCount=ref(0)
	
	//表单数据
	const dataList=ref([])
	const dataList2=ref([])
	
	//扫描状态
	const brandCode=ref(0)
	
	//详情弹窗
	const modelData = ref()
	
	//状态
	const Qstatus = ref(0)
	
	//切换状态
	const qhStatus = ref(1)
	const cxStatus = ref(6)
	
	//总笔数
	const countSum = ref(1)
	
	const insUs=uni.getStorageSync("loUserNo")
	const insName=uni.getStorageSync("loginUserName")
	const iuser=ref("")
	
	//修改弹窗
	const updateSpecificationPopup = ref()
	const updateSpecificationDetail = reactive({
	  pat_flag: "",
	  model_no: "",
	  shp_date:"",
	  ord_no: "",
	  dev_type: "",
	  tot_qty: "",
	  ushelf_no: "",
	  bshelf_no6: "",
	  bshelf_no2: "",
	  bshelf_no1: "",
	  bshelf_no3: ""
	})
	
	//删除参数
	const deleSpecificationDetail = reactive({
	  ord_no: "",
	  pb_dept: "",
	  pb_addr: "",
	  upd_user: insUs,
	  ins_date: ""
	})
	
	//点击成型
	async function radioChange2(v){
			cxStatus.value=v
			dataList.value=[]
			await getData()
			await getData2()
	}
	
	//点击异常
	async function radioChange(v){
			qhStatus.value=v
			dataList.value=[]
			await getData()
			await getData2()
	}
	
	//下拉刷新
	 onPullDownRefresh (()=>{	//通过 onPullDownRefresh  可以监听到下拉刷新的动作
			uni.startPullDownRefresh({
				success(){
					dataList.value=[]
					getData();
					getData2()
					uni.stopPullDownRefresh()	//停止当前页面下拉刷新。
				},fail(){}
			})
	    }) 
    const stus=ref(0)

	const column=ref([
		  { name: 'shp_date', label: t('出货日'),sorter:true,align: "center",width:'125'},
		  { name: 'ord_no', label: t('样品单号'),width:'185'},
		  { name: 'dev_type', label: t('样品类型'),width:'152',emptyString:'/'},
		  { name: 'model_pic', label: t('鞋图'),type:"img",align: "center",width:'90'},
		   { name: 'pd_line', label: t('生产线'),sorter:true,emptyString:'/',width:'125'},
		  { name: 'model_no', label: t('型体编号'),sorter:true,emptyString:' ',width:'150'},
		  { name: 'last_no', label: t('楦头编号'),emptyString:'/',width:'120'},
		  { name: 'tot_qty', label: t('开单数'),emptyString:' '},
		  { name: 'p1_qty', label: t('已投入'),emptyString:' '},
		  { name: 'p2_qty', label: t('已产出'),emptyString:' '},
		  { name: 'bar_date', label: t('投入时间'),sorter:true,emptyString:'/',width:'150'},
		  { name: 'bar_hour', label: t('投入时数'),sorter:true,emptyString:'/',width:'100'},
		  { name: 'pb_desc', label: t('异常原因'),emptyString:'/',width:'100'}
		 
		]);	

	const deleCsInfos = reactive({
	  mating_no: ""
	})
	
	//判断颜色
	function cellStyle({row, column, rowIndex, columnIndex}){
		//console.log(JSON.stringify(row)+"**"+column+"**"+rowIndex+"**"+columnIndex);
		if(row.bar_hour>5&&row.bar_hour<=8){
			return{
				color:'#FFE597'
			}
		}
		if(row.bar_hour>8){
			return{
				color:'red'
			}
		}
    }
	
	function sortChange(v,t){
		//alert(JSON.stringify(v))
	}
	
	//返回首页
	function back() {
	  uni.navigateBack({
	  	delta: 1,
	  	animationType: 'pop-out',
	  	animationDuration: 200
	  })
	}	
	
	//关闭弹窗
	function backDrom() {
	  dataList2.value=[];
	  updateSpecificationPopup.value.close()
	}	
	
	//选中当前行
	function currentChange(row,index){
	  
	}
	
	//下拉刷新
	const flagLoad=ref(true)
	function pullUpLoadingAction(done){
	   if(!flagLoad.value){
		done('ok')
	    return
	   }
	   
	   setTimeout(()=>{
			firstPageNo.value++
			done()
			getData(firstPageNo.value,firstPageSize.value)
		},300)
	}
	
	//点击切换按架位
	async function asyncChange(e) {
		Qstatus.value=e?1:0
		getData();
	}
	
	//表尾合计
	function getSummaries(param){
	  const { columns, data } = param;
	  const sums = [];
	  columns.forEach((column, index) => {
	    if (index === 0) {
	      sums[index] = t('合计');
	      return;
	    }		
	    if(column.name==='tot_qty'||column.name==='p1_qty'||column.name==='p2_qty'){
	      const values = data.map(item => Number(item[column.name]));
	      if (!values.every(value => isNaN(value))) {
	        sums[index] = values.reduce((prev, curr) => {
	          const value = Number(curr);
	          if (!isNaN(value)) {
	            return prev + curr;
	          } else {
	            return prev;
	          }
	        }, 0);
	        sums[index] += '  ';
	      }
	    }else{
	      sums[index] = ' ';
	    }
	  });
	  return sums;
	}
	
	
	//点击详情
	function rowClick(e){
		updateSpecificationDetail.ord_no = e.ord_no
		uni.request({
			   url: urlPrefix + "/match/getNoMatchDetail",
			   data:{
					"brand_no": e.brand_no,
					"status": Qstatus.value
				},
			   method: "POST"
			 }).then(res => {
				dataList2.value = res.data.data
				for(let i=0;i<dataList2.value.length;i++){
					dataList2.value[i].model_pic='data:image/png;base64,'+dataList2.value[i].model_pic
				}
				
			 }).catch(err => {
			   //console.log(err)
			   uni.showToast({
			   	title: t('获取数据失败！'),
				icon:"error"
			   });
			 })
		updateSpecificationPopup.value.open()
	}	

	//获取数据
	async function getData(pageNo,pageSize){
		//console.log(pageNo);
		await uni.request({
			   url: urlPrefix + "/match/formingQuery",
			   data:{
					"page_no": pageNo,
					"page_size": pageSize,
					"status": qhStatus.value,
					"status2": cxStatus.value,
			   	},
			   method: "POST"
			 }).then(res => {
				const dataCon=res.data.data
				for(let i=0;i<dataCon.length;i++){
					dataCon[i].model_pic='data:image/png;base64,'+dataCon[i].model_pic
					dataList.value.push(dataCon[i]);
				}
				flagLoad.value=dataCon.length<firstPageSize.value?false:true;
			 }).catch(err => {
			   //console.log(err)
			   uni.showToast({
			   	title: t('获取数据失败！'),
				icon:"error"
			   });
			 })
	}
	
	//获取数据2
	async function getData2(){
		await uni.request({
			   url: urlPrefix + "/match/getformingCount",
			   data:{
					"status": qhStatus.value,
					"status2": cxStatus.value,
			   	},
			   method: "POST"
			 }).then(res => {
				countSum.value=res.data.data[0]
			 }).catch(err => {
			 })
	}

	//预加载
	onMounted(async () => {
	  await getData()
	  await getData2()
	  //await search()
	})
	
</script>

<style lang="scss">
	page {
	  width: 100%;
	  height: 100%;
	  padding: 2.5% 2% 1.5% 2%;
	  box-sizing: border-box;
	  background-color: white;
	}
	
	.radio{
		margin-left: 1.5vw;
		font-size: 15px;
		font-weight:480;
	}
	
	.item-tr{
	  height: 40px;
	  font-size: 17px;
	  font-weight:480;
	}
	
	// .cTr .item-tr{
	// 	background-color: seagreen;
	// }
	
	.container {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		border-radius: 10px;
		box-shadow: 0 0 1px 5px #dddddd;
		
	}
	
	::v-deep{
	  .uni-card{
	    margin: 8px!important;
	    padding: 0!important;
	    .uni-card__content{
	      padding: 0!important;
	    }
	  }
	}
	.right-top-top{
		display: flex;
	}
.propertyBox{
	margin-top: 2rpx;
	flex-flow: row;
	justify-content: flex-start;
	display: flex;
	position: -webkit-sticky;
	top: var(--window-top);
}
.inpBr{
	width: 15%;
	margin-left:10rpx;
}
	.left-bottom {
	width: 100%;
	height: 10%;
	display: flex;
	justify-content: center;
	align-items: center;

	.uni-pagination__total {
	  margin-right: 0.4375rem !important;
	}

	.uni-pagination__num-tag {
	  min-width: 1.875rem !important;
	  background-color: #F0F0F0 !important;
	}

	.page--active {
	  color: white !important;
	  background-color: deeppink !important;
	}
	}
	
	.updateSpecificationPopup {
	  .updateSpecificationBox {
	    width: 96vw;
	    height: 90vh;
		
	    border-radius: 1vw;
	    background-color: white;
	    
	    .updateSpecificationData {
	      width: 100%;
	      position: relative;
	      display: flex;
	      justify-content: center;
	      align-items: flex-start;
	      flex-wrap: wrap;
		  height: 89%;
	      
	      .updateSpecificationAttribute {
	        width: 35%;
	        margin-left: 15%;
	        
	        .uni-easyinput {
	          width: 70%;
	          margin-left: 1rem;
	        }
	        
	        .uni-stat__select {
	          width: 70%;
	          margin-left: 1rem;
	        }
	        
	        .uni-numbox {
	          margin-left: 1rem;
	        }
	        
	        .uni-file-picker {
	          margin-left: 1rem;
	        }
	      }
	    }
	  }
	}
	
	.submit {
	  display: flex;
	  justify-content: center;
	  align-items: center;
	}

	.back {
		width:50px;
		height: 50px;
		display: flex;
		justify-content:center;
		align-items: center;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		cursor: pointer;
		z-index: 1;
	}

</style>


