<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=1.0" />
  <link type="text/css" rel="stylesheet" href="css/froala_editor.pkgd.min.css">
  <!-- <link type="text/css" rel="stylesheet" href="https://cdn.jsdelivr.net/npm/froala-editor@latest/css/froala_editor.pkgd.min.css"> -->
  <link rel="stylesheet" href="css/all.min.css">
 <link rel="stylesheet" href="css/sweetalert2.min.css">
 
  <style>
	  body{
		background-color: #FDF6E3;
	  }
	   .container {
			margin: auto;
			width: 81vw;
		}
	

    div#editor {
		margin: auto;
		text-align: left;
    }
	
	.fr-second-toolbar{
		display: none;
	}

	.saveBtn {
	  margin: 20px auto; /* 水平居中，并添加上下边距 */
	  display: block; /* 使按钮宽度充满父元素宽度 */
	  width: 150px; /* 可根据需要调整按钮宽度 */
	  padding: 10px; /* 添加内边距，使按钮内容与边框之间有间隔 */
	  background-color: #3498db; /* 设置按钮的背景颜色 */
	  color: white; /* 设置按钮文字颜色 */
	  border: none; /* 移除边框 */
	  border-radius: 5px; /* 设置圆角，数值越大圆角越明显 */
	  cursor: pointer; /* 鼠标悬停时显示指针 */
	  text-align: center; /* 文字居中 */
	  transition: background-color 0.3s, box-shadow 0.3s; /* 平滑过渡效果 */
	}
	
	.saveBtn:hover {
	  background-color: #2980b9; /* 鼠标悬停时的背景颜色 */
	  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); /* 鼠标悬停时添加阴影效果 */
	}
	

    .textarea {
	  font-size: 16px;
	  padding: 10px;
      width: 100%  !important; /* 使文本框宽度为 100% */
      box-sizing: border-box; /* 确保填充和边框包含在宽度内 */
      resize: vertical; /* 如果需要，可以调整文本框的垂直尺寸 */
    }
	
	 .label {
	    text-align: left;
		
		flex-shrink: 0; /* 防止标签收缩 */
	  }
	  .input {
		      flex-grow: 1; /* 输入框宽度充满剩余空间 */
	    height: 30px;
		width: 90%;
	  }

	  
	  .top{
		  display: flex;
		      align-items: center; /* 垂直居中 */
		      justify-content: center; /* 水平居中 */
	  }
	  
	.radio-group {
	  margin-top: 20px;
	  display: flex;
	  flex-wrap: wrap; /* 允许换行 */
	  gap: 5px; /* 设置元素之间的间隔 */
	  align-items: center; /* 垂直居中对齐所有子元素 */
	}

		/* 设置label和input的样式使其水平排列并垂直居中 */
		.label, .input-radio {
		  display: inline-block;
		  vertical-align: middle; /* 垂直居中对齐 */
		  margin-right: 10px; /* 添加一些右边距 */
		}
		.input-radio {
		  margin-right: 5px; /* 添加一些右边距 */
		  margin-bottom: 2px;
		}	  
	  
	  .editer_label{
		  display: block;
		  margin-top: 20px;
	  }
	  .edit{
		  margin-top: 15px;
	  }
	    .center-title {
			font-size: 20px;
			display: block;
	      text-align: center; /* 文本居中 */
	      margin-top: 20px; /* 顶部外边距 */
	    }
		
	
	
 .top-container {
	 margin-top: 20px;
    display: flex;
    align-items: center; /* 垂直居中对齐 */
    height: 50px; /* 容器高度设为视口高度，以便垂直居中 */
  }
  .center-title {
    margin: 0 auto; /* 使标题水平居中 */
    padding: 0; /* 移除默认的内边距 */
    flex: 1; /* 允许标题扩展以填充剩余空间 */
  }
	.return-button {
	  height: 25px; /* 示例高度，根据图片大小调整 */
	  width: 25px; /* 示例宽度，根据图片大小调整 */
	  background-image: url('icon/back.png'); /* 设置按钮的背景图片 */
	  background-size: cover; /* 背景图片覆盖整个按钮 */
	  background-repeat: no-repeat; /* 防止背景图片重复 */
	  background-position: center; /* 背景图片居中 */
	  border: none; /* 移除按钮边框 */
	  background-color: transparent; /* 设置背景颜色为透明 */
	}
	
	
	.fr-quick-insert {
	  display: none !important;
	}
	
	  div[style*="z-index:9999"] {
	    display: none !important;
	  }
	  
	  .fr-wrapper > div[style*='z-index: 9999'] {
	          position: absolute;
	          top: -10000px;
	          opacity: 0;
	          display: none;
	        }
  </style>
</head>

<body>
	
  <div class="top-container">
    <button style="margin-left: 20px;" class="return-button" onclick="goBack()"></button>
    <span class="center-title">成长轨迹</span>
  </div>
  <div class="container">
	  

	<div class="top" style="margin-top: 50px;">
		<label class="label">主题名称：</label>
		<input type="text" id="theme" placeholder="请输入主题" class="input" />
	
	</div>
	
	<div style="margin-top: 20px;">
		<label class="label">提案人：</label>
		<input type="text" id="proposer" placeholder="请输入提案人" style="height: 30px; width: 15%; margin-left: 11px;" />
	
	</div>
	

<div class="radio-group">
	  <label class="label">选择厂别：</label>
	  <div id="factoryOptions"></div>
<!-- 
	  <input type="radio" id="option1" name="factoryradiogroup" value="越南" class="input-radio">
	  <label for="option1" class="label">越南</label>
	  <input type="radio" id="option2" name="factoryradiogroup" value="菲律宾" class="input-radio">
	  <label for="option2" class="label">菲律宾</label>
	  <input type="radio" id="option3" name="factoryradiogroup" value="孟加拉" class="input-radio">
	  <label for="option3" class="label">孟加拉</label>
	  <input type="radio" id="option4" name="factoryradiogroup" value="印尼" class="input-radio">
	  <label for="option4" class="label">印尼</label> -->
</div>



<div class="radio-group">
  <label class="label">选择部门：</label>
  <input type="radio" id="option11" name="deptradiogroup" value="倉庫" class="input-radio">
  <label for="option11" class="label">倉庫</label>
  <input type="radio" id="option22" name="deptradiogroup" value="成型" class="input-radio">
  <label for="option22" class="label">成型</label>
  <input type="radio" id="option33" name="deptradiogroup" value="生控" class="input-radio">
  <label for="option33" class="label">生控</label>
  <input type="radio" id="option44" name="deptradiogroup" value="面部" class="input-radio">
  <label for="option44" class="label">面部</label>
  <input type="radio" id="option55" name="deptradiogroup" value="底部" class="input-radio">
  <label for="option55" class="label">底部</label>
</div>

			

		
<label class="editer_label">STEP1  問題的明確化 Problem Statement</label>
<!--  <div id="editor"> -->
    <div id='edit1' class="edit" >

    </div>
	
	<label class="editer_label">STEP1  改善目的 Purpose</label>
	<!--  <div id="editor"> -->
	    <div id='edit11' class="edit" >
	
	    </div>
	

<!-- 	<label class="editer_label" style="margin-bottom: 15px;">改善目的 Purpose</label>

	<textarea id="purpose" class="textarea" rows="6"></textarea> -->

<label class="editer_label">STEP2  現狀把握  Current States</label>
    <div id='edit2' class="edit" >

    </div>
<label class="editer_label">STEP3  目標設定  Target Setting</label>
    <div id='edit3' class="edit" >

    </div>
<label class="editer_label">STEP4   要因分析  Cause analysis</label>
    <div id='edit4' class="edit" >

    </div>
	<label class="editer_label">STEP5   對策立案   Contemeasures</label>
	<div id='edit5' class="edit">
	
	</div>
		<label class="editer_label">STEP6   實施計劃   Action Plan</label>
	<div id='edit6' class="edit"  >
	
	</div>
			<label class="editer_label">STEP7  结果確認  Results Evaluation</label>
	<div id='edit7' class="edit">
	
	</div>
			<label class="editer_label">STEP8  標準化  Standardization</label>
	<div id='edit8' class="edit"  >
	
	</div>
<!-- <button class="saveBtn">获取内容</button> -->
   <button onclick="getContent()" class="saveBtn">保存</button>

  </div>
  
  <div>
  
  
    <script src="js/sweetalert2.all.min.js"></script>
  <script type="text/javascript" src="js/froala_editor.pkgd.min.js"></script>
  <script type="text/javascript" src="js/languages/zh_cn.js"></script>
  <script src="js/main2.js"></script>

  <script  >
	  

	    function goBack() {
			
				webUni.postMessage({
				  data: {
					action: "back"
				  },
				});

	      window.history.back();
		  
		  
	    }
	  







// 国家数组
var countries = ['東莞PCC', '越南PCC', '湖南雙峰', '湖南洞口', '越南太平','越南安佬','越南廣寧'];

// 动态生成单选按钮
function generateRadioButtons() {
      var container = document.getElementById('factoryOptions');
      countries.forEach(function(country, index) {
          var radioButton = document.createElement('input');
          radioButton.type = 'radio';
          radioButton.id = 'option' + (index + 1);
          radioButton.name = 'factoryradiogroup';
          radioButton.value = country;
          radioButton.className = 'input-radio';
          
          var label = document.createElement('label');
          label.htmlFor = radioButton.id;
          label.className = 'label';
          label.textContent = country;
          
          // 创建一个包裹元素来保持水平排列和垂直居中
          var wrapper = document.createElement('span');
          wrapper.className = 'radio-wrapper'; // 添加一个类来设置样式
          
          wrapper.appendChild(radioButton);
          wrapper.appendChild(label);
          
          container.appendChild(wrapper);
      });
	
	// 获取所有单选按钮
	var factoryRadios = document.getElementsByName('factoryradiogroup');
	// 为每个单选按钮添加事件监听器
	for(var i = 0; i < factoryRadios.length; i++) {
	  factoryRadios[i].addEventListener('change', function() {
	    // 检查哪个单选按钮被选中
	    for(var i = 0; i < factoryRadios.length; i++) {
	      if(factoryRadios[i].checked) {
	        // 显示选中的值
	        console.log('Selected value:', factoryRadios[i].value);
					  factory = factoryRadios[i].value;
	        break;
	      }
	    }
	  });
	}
	
	if(window.id  && window.id !== 'undefined'){
		  var radios = document.getElementsByClassName('input-radio');
		  for (var i = 0; i < radios.length; i++) {
		    radios[i].disabled = true;
		  }
	}
	
}

// 在页面加载完成后生成单选按钮
window.onload = generateRadioButtons;
	  
	  
	function fetchData(id) {
      fetch(window.urlPrefix + '/pccprogressroute/getById?id=' + id)
        .then(response => {
          if (!response.ok) {
            throw new Error('网络响应错误');
          }
          return response.json();
        })
        .then(data => {
          // console.log("data:" + JSON.stringify(data));
		  // var jsonData = JSON.parse(data);
			editor1.html.set(data.data.problem_statement)
			editor11.html.set(data.data.purpose)
			editor2.html.set(data.data.current_states)
			editor3.html.set(data.data.target_setting)
			editor4.html.set(data.data.cause_analysis)
			editor5.html.set(data.data.contemeasures)
			editor6.html.set(data.data.action_plan)
			editor7.html.set(data.data.results_evaluation)
			editor8.html.set(data.data.standardization)
			
		 document.getElementById('theme').value = data.data.theme;
		 document.getElementById('proposer').value = data.data.proposer;
			
		  // document.getElementById('purpose').value = data.data.purpose;
		  
		  
		        // 获取所有单选按钮
		        const factoryRadios = document.getElementsByName('factoryradiogroup');
		  
		        // 遍历单选按钮，根据返回的数据设置选中的单选按钮
		        for (const radio of factoryRadios) {
		          if (radio.value === data.data.factory) {
		            radio.checked = true;
					factory = data.data.factory;
		            break;
		          }
		        }
				
				// 获取所有单选按钮
				const deptRadios = document.getElementsByName('deptradiogroup');
						  
				// 遍历单选按钮，根据返回的数据设置选中的单选按钮
				for (const radio of deptRadios) {
				  if (radio.value === data.data.dept) {
				    radio.checked = true;
						dept = data.data.dept;
				    break;
				  }
				}
			
			
        })
        .catch(error => {
          console.error('请求失败:', error);
        });
    };


	function saveData(data) {
		data.create_by = window.user
     // 调用保存数据的API
         fetch(window.urlPrefix + '/pccprogressroute/create', {
           method: 'POST', 
           headers: {
             'Content-Type': 'application/json', 
           },
           body: JSON.stringify(data) 
         })
       .then(function(response) {
         // 检查响应状态
         console.log('Response status:', response.status);
         
         // 尝试解析响应体为文本
         return response.text().then(function(text) {
           return {
             status: response.status,
             statusText: response.statusText,
             body: text
           };
         });
       })
       .then(function(data) {
         // 处理返回的数据
         if (data.status >= 200 && data.status < 300) {
           // 如果是成功的响应，尝试将响应体解析为JSON
           try {
             // data.body = JSON.parse(data.body);
			 console.log('保存数据成功');
			 
			 
			   window.parent.postMessage({
			 	key: "success"
			   }, '*'); 
			 	  
			 	webUni.postMessage({
			 	  data: {
			 		action: "save",
			 		data: "success",
			 	  },
			 	});
			 
           } catch (e) {
             // 如果解析失败，则保持为文本
           }

         }else if(data.status == 400) {

			   var jsonData = JSON.parse(data.body);
				 var msg = jsonData.msg;
				 console.log('Message:', msg);
				 // toastr.error(msg);
				     // 显示一个错误消息
				     Swal.fire({
				         title: '错误！',
				         text: msg,
				         icon: 'error',
				         confirmButtonText: '知道了'
				     })

         }
       })
       .catch(function(error) {
         // 处理网络错误或其他问题
         console.error('There has been a problem with your fetch operation:', error);
       });
    };
	
	
	function updateData(data) {


		data.update_by = window.user;
		
		
	     fetch(window.urlPrefix + '/pccprogressroute/update', {
	       method: 'POST', 
	       headers: {
	         'Content-Type': 'application/json', 
	       },
	       body: JSON.stringify(data) 
	     })
	   .then(function(response) {
	     // 检查响应状态
	     console.log('Response status:', response.status);
	     
	     // 尝试解析响应体为文本
	     return response.text().then(function(text) {
	       return {
	         status: response.status,
	         statusText: response.statusText,
	         body: text
	       };
	     });
	   })
	   .then(function(data) {
	     // 处理返回的数据
	     if (data.status >= 200 && data.status < 300) {
	       // 如果是成功的响应，尝试将响应体解析为JSON
	       try {
	         // data.body = JSON.parse(data.body);
			 console.log('保存数据成功');
			 
			 window.parent.postMessage({
			 			 	key: "success"
			 }, '*'); 
			 			 	  
			 			 	webUni.postMessage({
			 			 	  data: {
			 			 		action: "save",
			 			 		data: "success",
			 			 	  },
			 			 	});
			 
			 
			 
			 
			 
	       } catch (e) {
	         // 如果解析失败，则保持为文本
	       }
	
	     }else if(data.status == 400) {
	
			   var jsonData = JSON.parse(data.body);
				 var msg = jsonData.msg;
				 console.log('Message:', msg);
	     }
	   })
	   .catch(function(error) {
	     // 处理网络错误或其他问题
	     console.error('There has been a problem with your fetch operation:', error);
	   });
	};

		  

  
  
  
  
    document.addEventListener('DOMContentLoaded', () => {
		
		const urlParams = new URLSearchParams(window.location.search);
		const id = urlParams.get('id');
		// const user = urlParams.get('user');
		// console.log("user：" + user)
		window.user = urlParams.get('user');
		if(id !== 'undefined'){
			window.id = id;
		}
		window.urlPrefix = urlParams.get('urlPrefix');
		// console.log("接受到id:" + window.id);
		// console.log("接受到id:" + urlPrefix);
		
		  // // 获取所有单选按钮
		  // var factoryRadios = document.getElementsByName('factoryradiogroup');
		  // // 为每个单选按钮添加事件监听器
		  // for(var i = 0; i < factoryRadios.length; i++) {
		  //   factoryRadios[i].addEventListener('change', function() {
		  //     // 检查哪个单选按钮被选中
		  //     for(var i = 0; i < factoryRadios.length; i++) {
		  //       if(factoryRadios[i].checked) {
		  //         // 显示选中的值
		  //         console.log('Selected value:', factoryRadios[i].value);
				//   factory = factoryRadios[i].value;
		  //         break;
		  //       }
		  //     }
		  //   });
		  // }
		  
		  // 获取所有单选按钮
		  var deptRadios = document.getElementsByName('deptradiogroup');
		  // 为每个单选按钮添加事件监听器
		  for(var i = 0; i < deptRadios.length; i++) {
		    deptRadios[i].addEventListener('change', function() {
		      // 检查哪个单选按钮被选中
		      for(var i = 0; i < deptRadios.length; i++) {
		        if(deptRadios[i].checked) {
		          // 显示选中的值
		          console.log('Selected value:', deptRadios[i].value);
		  				  dept = deptRadios[i].value;
		          break;
		        }
		      }
		    });
		  }
		  
		  
		  if(window.id  && window.id !== 'undefined'){
		  	  var radios = document.getElementsByClassName('input-radio');
		  	  for (var i = 0; i < radios.length; i++) {
		  	    radios[i].disabled = true;
		  	  }
		  }
	
	

	  editor1 = new FroalaEditor("#edit1", {
	    language: 'zh_cn',
	    height: 500,
	    toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor', , 'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	    quickInsertButtons: [],
	    imageUploadURL: window.urlPrefix + "/api/files/upload2",
	    videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	    colorPicker: true,
	    imageUpload: true,
		imageInsertButtons: ['imageUpload'], 
		videoInsertButtons: ['videoUpload'], 

		// imageResize: true,
		// imageResizeWithPercent: true,
		// imageDefaultWidth: '20%',

	  });
	  
	  
	  editor11 = new FroalaEditor("#edit11", {
	  language: 'zh_cn',
	    height: 500,
	    toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor',  'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	    quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	    imageUploadURL: window.urlPrefix + "/api/files/upload2",
	    videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	    colorPicker: true,
	    imageUpload: true,
	  		imageInsertButtons: ['imageUpload'], 
	  		videoInsertButtons: ['videoUpload'], 

	  
	  });
	  

  
	   editor2 = new FroalaEditor("#edit2", {
	   language: 'zh_cn',
	    height: 500,
	    toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor',  'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	    quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	    imageUploadURL: window.urlPrefix + "/api/files/upload2",
	    videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	    colorPicker: true,
	    imageUpload: true,
		imageInsertButtons: ['imageUpload'], 
		videoInsertButtons: ['videoUpload'], 

	  });
	  
	  
	   editor3 = new FroalaEditor("#edit3", {
	   language: 'zh_cn',
	    height: 500,
	    toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor',  'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	    quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	    imageUploadURL: window.urlPrefix + "/api/files/upload2",
	    videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	    colorPicker: true,
	    imageUpload: true,
		imageInsertButtons: ['imageUpload'], 
		videoInsertButtons: ['videoUpload'], 
	
	  });
	  
	  
	   editor4 = new FroalaEditor("#edit4", {
	 language: 'zh_cn',
	    height: 500,
	    toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor',  'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	    quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	    imageUploadURL: window.urlPrefix + "/api/files/upload2",
	    videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	    colorPicker: true,
	    imageUpload: true,
		imageInsertButtons: ['imageUpload'], 
		videoInsertButtons: ['videoUpload'], 

	  });
	  
	   editor5 = new FroalaEditor("#edit5", {
	    language: 'zh_cn',
	    height: 500,
	    toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor',  'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	    quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	    imageUploadURL: window.urlPrefix + "/api/files/upload2",
	    videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	    colorPicker: true,
	    imageUpload: true,
		imageInsertButtons: ['imageUpload'], 
		videoInsertButtons: ['videoUpload'], 

	  });
	  
	   editor6 = new FroalaEditor("#edit6", {
		language: 'zh_cn',
	    height: 500,
	    toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor',  'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	    quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	    imageUploadURL: window.urlPrefix + "/api/files/upload2",
	    videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	    colorPicker: true,
	    imageUpload: true,
		imageInsertButtons: ['imageUpload'], 
		videoInsertButtons: ['videoUpload'], 

	  });
	  
	   editor7 = new FroalaEditor("#edit7", {
	    language: 'zh_cn',
	    height: 500,
	    toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor',  'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	    quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	    imageUploadURL: window.urlPrefix + "/api/files/upload2",
	    videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	    colorPicker: true,
	    imageUpload: true,
		imageInsertButtons: ['imageUpload'], 
		videoInsertButtons: ['videoUpload'], 

	  });
	  
	   editor8 = new FroalaEditor("#edit8", {
	    language: 'zh_cn',
	    height: 500,
	    toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor',  'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	    quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	    imageUploadURL: window.urlPrefix + "/api/files/upload2",
	    videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	    colorPicker: true,
	    imageUpload: true,
		imageInsertButtons: ['imageUpload'], 
		videoInsertButtons: ['videoUpload'], 

	  });
	  
	  if(window.id  && window.id !== 'undefined'){
	  		  fetchData(id);
	  }
	  
    });
	
	
	

	
 




	  
	  // 获取内容的函数
	  function getContent() {
		  var theme = document.getElementById('theme').value;
		  var proposer = document.getElementById('proposer').value;
		  
	if (typeof dept == 'undefined') {
	   Swal.fire({
	       title: '错误！',
	       text: '部门不能为空',
	       icon: 'error',
	       confirmButtonText: '知道了'
	   })
	}
			  var content1 = editor1.html.get(true)
			  var content11 = editor11.html.get(true)
			  var content2 = editor2.html.get(true)
			  var content3 = editor3.html.get(true)
			  var content4 = editor4.html.get(true)
			  var content5 = editor5.html.get(true)
			  var content6 = editor6.html.get(true)
			  var content7 = editor7.html.get(true)
			  var content8 = editor8.html.get(true)
			  
			   var data = {
					  "problem_statement": content1,
						"current_states": content2,
						"target_setting": content3,
						"cause_analysis":content4,
						"contemeasures":content5,
						"action_plan": content6,
						"results_evaluation": content7,
						"standardization": content8,
						
						
						  "theme": theme,
						  "factory": factory,
						  "dept": dept,
						  "proposer":proposer,
						  "purpose": content11
						  
					
			      };
				  if (typeof window.id !== 'undefined' && window.id !== null && window.id !== 'undefined') {
					  data.id = window.id;
					  updateData(data);  
				  }else{
						saveData(data); 
				  }
				
	  }
	  




  </script>
</body>

</html>
  <script src="js/uni.webview.1.5.2.js"></script>