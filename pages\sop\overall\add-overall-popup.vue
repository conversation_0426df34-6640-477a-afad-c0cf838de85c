<!-- 添加工序流程弹框 -->
<script setup>
import { ref, watch, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 添加工序流程弹框
const addOverallPopup = ref()

// 标签
const tag = ref('全部')
// 标签列表
const tagList = ref(['全部'])
// 动作列表
const overallActionList = ref([])
// 显示动作列表
const showOverallActionList = ref([])
// 动作输入框
const overallActionInput = ref('')
// 是否聚焦动作输入框
const focusOverallActionInput = ref(false)
// 选择动作列表
const selectedActionList = ref([])

// 工序信息
const processInfo = inject('processInfo')
// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getOverallFlow = inject('getOverallFlow')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')

// 获取动作列表
async function getOverallActionList(param) {
  await uni.request({
    url: urlPrefix + '/sop/getFlowActionList',
    method: 'POST',
    data: {
      proSeq: param
    }
  }).then(res => {
    if (res.data.code) {
      overallActionList.value = res.data.data ? res.data.data : []
      showOverallActionList.value = res.data.data ? res.data.data : []
      for (let item of overallActionList.value) {
        if (!tagList.value.includes(item.tag)) {
          tagList.value.push(item.tag)
        }
      }
    } else {
      overallActionList.value = []
      showOverallActionList.value = []
      tipPopup.value.showTipPopup('warn', '暂无动作列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示添加工序流程弹框
async function showAddOverallPopup() {
  tag.value = '全部'
  tagList.value = ['全部']
  selectedActionList.value = []
  
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await getOverallActionList(processInfo.value.proSeq)
  
  uni.hideLoading()
  
  // if (overallActionList.value.length === 0) {
  //   return
  // }
  
  addOverallPopup.value.open()
}

// 添加工序流程
async function addOverallFlow() {
  if (selectedActionList.value.length === 0) {
    tipPopup.value.showTipPopup('warn', '请选择要添加的动作！')
    return
  }
  
  uni.showLoading({
    title: '添加中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/addOverallFlow',
    method: 'POST',
    data: {
      model: processInfo.value.model,
      operation: processInfo.value.operation,
      rtgCode: processInfo.value.rtgCode,
      actionList: JSON.stringify(selectedActionList.value),
      user: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getOverallFlow()
      tipPopup.value.showTipPopup('success', '添加成功！')
      addOverallPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '添加失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 传输动作
function transferAction() {
  if (!overallActionInput.value) {
    tipPopup.value.showTipPopup('warn', '请输入动作！')
    return
  }
  
  selectedActionList.value.push({
    id: -1,
    actionCn: overallActionInput.value
  })
}

// 选择动作
function selectAction(param) {
  // for (let item of selectedActionList.value) {
  //   if (param.id === item.id) {
  //     return
  //   }
  // }
  selectedActionList.value.push(param)
}

watch(tag, () => {
  if (tag.value === '全部') {
    showOverallActionList.value = overallActionList.value
  } else {
    showOverallActionList.value = overallActionList.value.filter(item => item.tag === tag.value)
  }
})

defineExpose({
  showAddOverallPopup
})
</script>

<template>
  <uni-popup
    ref="addOverallPopup"
    type="center"
    :is-mask-click="false"
    class="add-overall-popup"
  >
    <view class="container">
      <view class="top-bar">
        <view
          @click="addOverallPopup.close()"
          class="cancel button"
        >
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <view class="title">
          添加工序流程
        </view>
        
        <view
          @click="addOverallFlow()"
          class="submit button"
        >
          <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
        </view>
      </view>
      
      <view class="main">
        <view class="select">
          <view class="flow-action-input flex-row-center">
            <textarea
              v-model="overallActionInput"
              @focus="focusOverallActionInput = true"
              @blur="focusOverallActionInput = false"
              placeholder="请输入动作"
              class="textarea"
              :style="{
                boxShadow: focusOverallActionInput ? '0 0 5px blue' : '0 0 5px gray'
              }"
            ></textarea>
            
            <view
              v-show="overallActionInput"
              @click="overallActionInput = ''"
              class="clear-textarea button"
            >
              <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
            </view>
          </view>
          
          <view class="flow-action-tag-list">
            <view
              v-for="item in tagList"
              class="flow-action-tag"
            >
              <view
                @click="tag = item"
                class="button"
                :style="{
                  backgroundColor: tag === item ? '#ccc' : 'transparent'
                }"
              >
                {{ item ? item : '其它' }}
              </view>
            </view>
          </view>
          
          <view class="flow-action-list">
            <view
              v-for="(item, index) in showOverallActionList"
              :key="index"
              v-show="item.actionCn && item.actionCn.includes(overallActionInput.toUpperCase())"
              class="flow-action flex-row-center"
            >
              <view
                @click="selectAction(item)"
                class="button"
              >
                {{ item.tag ? (item.tag + ' - ' + item.actionCn) : item.actionCn }}
              </view>
            </view>
          </view>
        </view>
        
        <view class="transfer">
          <view
            @click="transferAction()"
            class="button"
          >
            <uni-icons type="arrow-right" size="30" color="deepskyblue"></uni-icons>
          </view>
        </view>
        
        <view class="preview">
          <view class="preview-title">
            预览
          </view>
          
          <view class="preview-list">
            <view
              v-for="(item, index) in selectedActionList"
              :key="index"
              class="preview-item"
            >
              <view class="index">
                {{ index + 1 + (processFlowList.length > 0 ? processFlowList[processFlowList.length - 1].skey : 0) }}
              </view>
              
              <view class="item">
                {{ ((item.id > 0) && item.tag ? (item.tag + ' - ') : '') + item.actionCn }}
              </view>
              
              <view @click="selectedActionList.splice(index, 1)" class="clear">
                <uni-icons type="closeempty" size="20" color="darkred"></uni-icons>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.add-overall-popup {
  .container {
    width: 1000px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .top-bar {
      width: 100%;
      height: 70px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title {
        font-size: 22px;
        font-weight: bold;
      }
      
      .cancel, .submit {
        width: 75px;
        height: 50px;
      }
    }
    
    .main {
      width: 100%;
      display: flex;
      
      .select {
        width: 50%;
        
        .flow-action-input {
          width: 100%;
          height: 160px;
          position: relative;
          
          .textarea {
            width: 350px;
            height: 140px;
            padding: 5px;
          }
          
          .clear-textarea {
            width: 70px;
            height: 40px;
            position: absolute;
            right: 80px;
            bottom: 15px;
          }
        }
        
        .flow-action-tag-list {
          width: calc(100% - 20px);
          margin: 0 10px;
          display: flex;
          overflow: auto;
          
          .flow-action-tag {
            width: 140px;
            padding: 10px;
            flex-shrink: 0;
            
            .button {
              height: 40px;
            }
          }
        }
        
        .flow-action-list {
          height: 280px;
          overflow: auto;
          
          // /* #ifdef WEB */
          // &::-webkit-scrollbar {
          //   display: none;
          // }
          // /* #endif */
          
          .flow-action {
            width: 100%;
            padding: 10px;
            position: relative;
            
            .button {
              width: 350px;
              min-height: 50px;
              color: darkmagenta;
              font-size: 20px;
              padding: 5px;
            }
          }
        }
      }
    }
    
    .transfer {
      width: 10%;
      padding: 10px;
      
      .button {
        width: 100%;
        height: 50px;
      }
    }
    
    .preview {
      width: 40%;
      padding: 10px;
      
      .preview-title {
        width: 100%;
        height: 50px;
        margin-bottom: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        font-weight: bold;
        border-radius: 10px;
        box-shadow: 0 0 5px gray;
      }
      
      .preview-list {
        width: 100%;
        height: 410px;
        border-radius: 10px;
        box-shadow: 0 0 5px gray;
        overflow: auto;
        
        .preview-item {
          width: calc(100% - 20px);
          margin: 10px;
          padding: 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          position: relative;
          
          .index {
            width: 50px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            border-radius: 50%;
            box-shadow: 0 0 5px gray;
          }
          
          .item {
            width: calc(100% - 60px);
            min-height: 50px;
            padding: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
            border-radius: 10px;
            box-shadow: 0 0 5px gray;
          }
          
          .clear {
            width: 30px;
            height: 30px;
            position: absolute;
            right: -5px;
            top: -5px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            text-align: center;
            background-color: #fdf6e3;
            border-radius: 50%;
            box-shadow: 0 0 5px gray;
            transition: all 0.1s linear;
            z-index: 1;
            /* #ifdef WEB */
            cursor: pointer;
            /* #endif */
            
            &:active {
              transform: scale(0.98);
              box-shadow: 0 0 1px gray;
              /* #ifdef APP */
              background-color: #ccc;
              /* #endif */
            }
            
            /* #ifdef WEB */
            &:hover {
              background-color: #ccc;
            }
            /* #endif */
          }
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .textarea {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-textarea-placeholder), &:deep(.uni-textarea-textarea) {
      width: 100%;
      height: 100%;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>