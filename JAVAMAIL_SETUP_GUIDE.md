# JavaMail 邮件发送配置指南

## 概述

本文档说明如何配置和使用邮件推送系统的JavaMail功能。系统已集成Spring Boot Mail Starter，支持多种邮件服务提供商。

## 依赖配置

已在 `pom.xml` 中添加了以下依赖：

```xml
<!-- Spring Boot Mail Starter -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-mail</artifactId>
</dependency>
```

## SMTP 配置

### 1. 数据库配置方式（推荐）

系统支持从数据库动态读取SMTP配置，执行以下SQL初始化配置：

```sql
-- 执行初始化配置脚本
@sql/init_email_config.sql
```

### 2. 主要配置项

| 配置键 | 说明 | 示例值 |
|--------|------|--------|
| SMTP_HOST | SMTP服务器地址 | smtp.gmail.com |
| SMTP_PORT | SMTP端口 | 587 |
| SMTP_USERNAME | 邮箱用户名 | <EMAIL> |
| SMTP_PASSWORD | 邮箱密码或应用专用密码 | your-app-password |
| SMTP_AUTH | 是否启用认证 | true |
| SMTP_STARTTLS | 是否启用STARTTLS | true |
| SMTP_SSL | 是否启用SSL | false |

### 3. 常见邮件服务商配置

#### Gmail 配置
```
SMTP_HOST: smtp.gmail.com
SMTP_PORT: 587
SMTP_AUTH: true
SMTP_STARTTLS: true
SMTP_SSL: false
```

#### Outlook/Hotmail 配置
```
SMTP_HOST: smtp-mail.outlook.com
SMTP_PORT: 587
SMTP_AUTH: true
SMTP_STARTTLS: true
SMTP_SSL: false
```

#### 企业邮箱配置
```
SMTP_HOST: smtp.exmail.qq.com
SMTP_PORT: 587
SMTP_AUTH: true
SMTP_STARTTLS: true
SMTP_SSL: false
```

## 配置管理

### 1. 通过API配置

```bash
# 获取SMTP配置
GET /emailPushConfig/getSmtpConfig

# 更新SMTP配置
POST /emailPushConfig/updateSmtpConfig
Content-Type: application/json

{
    "host": "smtp.gmail.com",
    "port": "587",
    "username": "<EMAIL>",
    "password": "your-app-password",
    "auth": "true",
    "starttls": "true",
    "ssl": "false"
}
```

### 2. 测试SMTP连接

```bash
# 测试SMTP连接
POST /emailPushConfig/testSmtpConnection

# 发送测试邮件
POST /emailPush/testConfig?testEmail=<EMAIL>
```

## 邮件发送功能

### 1. 发送报价邮件

```bash
POST /emailPush/sendQuote?quoteVersion=V1&businessId=ORDER001&sendUser=ADMIN
```

### 2. 发送预估邮件

```bash
POST /emailPush/sendEstimate?estimateVersion=Z&businessId=ORDER001&sendUser=ADMIN
```

### 3. 自定义邮件发送

```bash
POST /emailPush/send
Content-Type: application/json

{
    "emailType": "QUOTE_V1",
    "businessId": "ORDER001",
    "templateVariables": {
        "businessId": "ORDER001",
        "userName": "张三",
        "deptName": "SOP",
        "currentDate": "2025-01-22"
    },
    "sendUser": "ADMIN"
}
```

## 安全配置

### 1. Gmail 应用专用密码

如果使用Gmail，需要：
1. 启用两步验证
2. 生成应用专用密码
3. 使用应用专用密码而不是账户密码

### 2. 企业邮箱配置

对于企业邮箱：
1. 确认SMTP服务已启用
2. 获取正确的SMTP服务器地址和端口
3. 使用正确的认证方式

## 故障排除

### 1. 常见错误

#### 认证失败
```
javax.mail.AuthenticationFailedException
```
**解决方案**：
- 检查用户名和密码是否正确
- 对于Gmail，确保使用应用专用密码
- 确认邮箱服务商是否支持SMTP

#### 连接超时
```
javax.mail.MessagingException: Could not connect to SMTP host
```
**解决方案**：
- 检查SMTP服务器地址和端口
- 确认网络连接正常
- 检查防火墙设置

#### SSL/TLS错误
```
javax.net.ssl.SSLException
```
**解决方案**：
- 检查SSL/STARTTLS配置
- 尝试不同的端口（25, 465, 587）
- 确认邮件服务商的SSL要求

### 2. 调试方法

#### 启用邮件调试
在 `EmailConfig.java` 中设置：
```java
props.put("mail.debug", "true");
```

#### 查看日志
```bash
# 查看邮件发送日志
GET /emailPush/queryLogs?emailType=QUOTE_V1&sendStatus=FAILED

# 查看系统日志
tail -f logs/application.log | grep "EmailSend"
```

## 性能优化

### 1. 连接池配置

JavaMailSender 默认为每次发送创建新连接，对于大量邮件发送，可以考虑：
- 使用连接池
- 批量发送
- 异步发送

### 2. 批量发送

```bash
# 批量发送邮件
POST /emailPush/batchSend
Content-Type: application/json

[
    {
        "emailType": "QUOTE_V1",
        "businessId": "ORDER001",
        "sendUser": "ADMIN"
    },
    {
        "emailType": "QUOTE_V2", 
        "businessId": "ORDER002",
        "sendUser": "ADMIN"
    }
]
```

## 监控和维护

### 1. 邮件发送统计

```bash
# 获取发送统计
GET /emailPush/getStatistics?emailType=QUOTE_V1&startDate=2025-01-01&endDate=2025-01-31
```

### 2. 自动重试

系统已配置定时任务，每30分钟自动重试失败的邮件：
- 最大重试次数：3次
- 重试间隔：30分钟
- 自动清理：30天前的日志

### 3. 手动重试

```bash
# 重试单个邮件
POST /emailPush/retry?logId=123

# 批量重试失败邮件
POST /emailPush/batchRetry
```

## 最佳实践

1. **配置管理**：使用数据库配置而不是硬编码
2. **安全性**：使用应用专用密码，不要在代码中暴露密码
3. **监控**：定期检查邮件发送状态和失败率
4. **测试**：在生产环境部署前充分测试邮件发送功能
5. **备份**：定期备份邮件配置和模板
6. **日志**：保留适当的邮件发送日志用于故障排除

## 扩展功能

1. **附件支持**：可扩展支持邮件附件
2. **模板引擎**：集成Thymeleaf等模板引擎
3. **消息队列**：使用RabbitMQ等实现异步邮件发送
4. **多租户**：支持多个邮件服务商配置
