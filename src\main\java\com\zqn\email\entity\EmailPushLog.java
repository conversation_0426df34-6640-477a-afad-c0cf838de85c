package com.zqn.email.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件推送日志实体类
 * @date 2025/01/22 10:00
 */
@Data
public class EmailPushLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 批次ID（同一批发送的邮件使用相同批次ID）
     */
    private String batchId;

    /**
     * 邮件类型 (QUOTE_V1, QUOTE_V2, QUOTE_V3, QUOTE_V4, QUOTE_V5, ESTIMATE_Z, ESTIMATE_ZZ, P_VERSION, UPDATE_NOTIFY)
     */
    private String emailType;

    /**
     * 邮件主题
     */
    private String subject;

    /**
     * 邮件内容
     */
    private String content;

    /**
     * 发件人邮箱
     */
    private String fromEmail;

    /**
     * 收件人邮箱
     */
    private String toEmail;

    /**
     * 抄送邮箱（多个用逗号分隔）
     */
    private String ccEmail;

    /**
     * 密送邮箱（多个用逗号分隔）
     */
    private String bccEmail;

    /**
     * 收件人工号
     */
    private String userNo;

    /**
     * 收件人部门
     */
    private String deptName;

    /**
     * 业务ID（如订单号、报价单号等）
     */
    private String businessId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 发送状态 (PENDING-待发送, SENDING-发送中, SUCCESS-成功, FAILED-失败, RETRY-重试中)
     */
    private String sendStatus;

    /**
     * 发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetry;

    /**
     * 下次重试时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nextRetryTime;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 附件信息（JSON格式存储）
     */
    private String attachmentInfo;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}
