<!-- 工序信息材质弹框 -->
<script setup>
import { ref } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap, proSeqMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 工序信息材质弹框
const infoMaterialPopup = ref()

// 工序信息
const processInfo = ref()
// 材质列表
const infoMaterialList = ref([])
// 材质输入框
const materialInput = ref('')
// 是否聚焦材质输入框
const focusMaterialInput = ref(false)

// 获取材质列表
async function getInfoMaterialList(param1, param2) {
  await uni.request({
    url: urlPrefix + '/sop/getInfoMaterialList',
    method: 'POST',
    data: {
      model: param1,
      operation: param2
    }
  }).then(res => {
    if (res.data.code) {
      infoMaterialList.value = res.data.data ? res.data.data : []
    } else {
      infoMaterialList.value = []
      tipPopup.value.showTipPopup('warn', '暂无材质列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示工序信息材质弹框
async function showInfoMaterialPopup(param) {
  if (infoMaterialList.value.length === 0) {
    uni.showLoading({
      title: '加载中...',
      mask: true
    })
    
    await getInfoMaterialList(param.model, param.operation)
    
    uni.hideLoading()
    
    // if (infoMaterialList.value.length === 0) {
    //   return
    // }
  }
  
  processInfo.value = param
  materialInput.value = ''
  
  infoMaterialPopup.value.open()
}

// 选择材质
async function selectInfoMaterial(param1, param2, param3, param4) {
  // if (!param4) {
  //   tipPopup.value.showTipPopup('warn', '请输入材质！')
  //   return
  // }
  
  if (param4 === processInfo.value.material) {
    infoMaterialPopup.value.close()
    return
  }
  
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateInfoMaterial',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3,
      material: param4 ? param4 : '/',
      updUser: user
    }
  }).then(res => {
    if (res.data.code) {
      processInfo.value.material = param4
      tipPopup.value.showTipPopup('success', '修改成功！')
      infoMaterialPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  getInfoMaterialList,
  showInfoMaterialPopup
})
</script>

<template>
  <uni-popup
    ref="infoMaterialPopup"
    type="center"
    class="info-material-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择材质
      </view>
      
      <view class="description flex-row-center">
        {{ operationMap.get(processInfo.operation) ? operationMap.get(processInfo.operation) : '未知' }} - {{ proSeqMap.get(processInfo.proSeq) ? proSeqMap.get(processInfo.proSeq) : '未知' }} - {{ processInfo.model }} - {{ processInfo.rtgCode }}
      </view>
      
      <view class="info-material-list">
        <view class="info-material flex-row-center">
          <input
            v-model="materialInput"
            @focus="focusMaterialInput = true"
            @blur="focusMaterialInput = false"
            type="text"
            placeholder="请输入材质"
            class="input"
            :style="{
              boxShadow: focusMaterialInput ? '0 0 5px blue' : '0 0 5px gray'
            }"
          />
          
          <view v-show="materialInput" @click="materialInput = ''" class="clear button">
            <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
          </view>
          
          <view
            @click="selectInfoMaterial(processInfo.model, processInfo.operation, processInfo.rtgCode, materialInput)"
            class="save button"
          >
            <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
          </view>
        </view>
        
        <view
          v-for="(item, index) in infoMaterialList"
          :key="index"
          v-show="item.material.includes(materialInput.toUpperCase())"
          class="info-material flex-row-center"
        >
          <view
            @click="selectInfoMaterial(processInfo.model, processInfo.operation, processInfo.rtgCode, item.material)"
            class="button"
            :style="{
              color: item.material === processInfo.material ? 'darkorchid' : 'darkmagenta'
            }"
          >
            {{ item.modelVer ? ('版次 ' + item.modelVer + ' | ') : '' }}{{ item.material }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.info-material-popup {
  .container {
    width: 430px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .description {
      width: 100%;
      height: 40px;
      font-size: 20px;
      font-weight: bold;
    }
    
    .info-material-list {
      height: 350px;
      overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .info-material {
        width: 100%;
        padding: 10px;
        position: relative;
        
        .button {
          width: 270px;
          min-height: 50px;
          color: darkmagenta;
          font-size: 20px;
          padding: 5px;
        }
        
        .input {
          width: 270px;
          height: 50px;
          padding: 0 10px;
        }
        
        .clear {
          width: 60px;
          min-height: 40px;
          position: absolute;
          top: 15px;
          right: 85px;
        }
        
        .save {
          width: 50px;
          height: 50px;
          position: absolute;
          top: 10px;
          right: 15px;
        }
        
        &:first-child {
          position: sticky;
          top: 0;
          background-color: #fdf6e3;
          z-index: 1;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>