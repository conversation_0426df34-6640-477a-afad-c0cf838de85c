package com.zqn.email.service;

import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.email.entity.EmailPermissionQuery;
import com.zqn.email.entity.EmailPushPermission;

import java.util.List;

/**
 * <AUTHOR> Yang
 * @version 1.0
 * @description: 邮件推送权限配置Service接口
 * @date 2025/01/22 10:00
 */
public interface EmailPushPermissionService {

    /**
     * 分页查询邮件推送权限列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    R<PageInfo<EmailPushPermission>> queryPermissionList(EmailPermissionQuery query);

    /**
     * 根据工号查询邮件推送权限
     *
     * @param userNo 工号
     * @return 权限信息
     */
    R<EmailPushPermission> getPermissionByUserNo(String userNo);

    /**
     * 根据邮件类型查询有权限的用户列表
     *
     * @param emailType 邮件类型
     * @return 权限列表
     */
    R<List<EmailPushPermission>> getPermissionsByEmailType(String emailType);

    /**
     * 根据邮件类型和部门查询有权限的用户列表
     *
     * @param emailType 邮件类型
     * @param deptName  部门名称
     * @return 权限列表
     */
    R<List<EmailPushPermission>> getPermissionsByEmailTypeAndDept(String emailType, String deptName);

    /**
     * 新增邮件推送权限
     *
     * @param permission 权限信息
     * @return 操作结果
     */
    R<String> addPermission(EmailPushPermission permission);

    /**
     * 更新邮件推送权限
     *
     * @param permission 权限信息
     * @return 操作结果
     */
    R<String> updatePermission(EmailPushPermission permission);

    /**
     * 删除邮件推送权限
     *
     * @param userNo 工号
     * @return 操作结果
     */
    R<String> deletePermission(String userNo);

    /**
     * 批量新增邮件推送权限
     *
     * @param permissions 权限列表
     * @return 操作结果
     */
    R<String> batchAddPermissions(List<EmailPushPermission> permissions);

    /**
     * 批量更新邮件推送权限
     *
     * @param permissions 权限列表
     * @return 操作结果
     */
    R<String> batchUpdatePermissions(List<EmailPushPermission> permissions);

    /**
     * 根据用户工号列表查询权限信息
     *
     * @param userNos 工号列表
     * @return 权限列表
     */
    R<List<EmailPushPermission>> getPermissionsByUserNos(List<String> userNos);

    /**
     * 查询所有部门列表
     *
     * @return 部门列表
     */
    R<List<String>> getAllDepts();

    /**
     * 根据邮件类型获取有效的收件人列表
     *
     * @param emailType 邮件类型
     * @return 收件人列表（包含邮箱地址的用户）
     */
    List<EmailPushPermission> getValidRecipients(String emailType);

    /**
     * 检查用户是否有指定邮件类型的权限
     *
     * @param userNo    工号
     * @param emailType 邮件类型
     * @return 是否有权限
     */
    boolean hasPermission(String userNo, String emailType);

    /**
     * 同步用户权限（从sy_user表同步用户信息）
     *
     * @return 操作结果
     */
    R<String> syncUserPermissions();
}
