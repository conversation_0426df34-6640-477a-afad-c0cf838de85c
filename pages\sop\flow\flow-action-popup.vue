<!-- 工序流程动作弹框 -->
<script setup>
import { ref, watch, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 动作弹框
const flowActionPopup = ref()

// 工序流程详情
const flowDetail = inject('flowDetail')

// 标签
const tag = ref('全部')
// 标签列表
const tagList = ref(['全部'])
// 动作列表
const flowActionList = ref([])
// 显示动作列表
const showFlowActionList = ref([])
// 动作输入框
const flowActionInput = ref('')
// 是否聚焦动作输入框
const focusFlowActionInput = ref(false)

// 获取动作列表
async function getFlowActionList(param) {
  await uni.request({
    url: urlPrefix + '/sop/getFlowActionList',
    method: 'POST',
    data: {
      proSeq: param
    }
  }).then(res => {
    if (res.data.code) {
      flowActionList.value = res.data.data ? res.data.data : []
      showFlowActionList.value = res.data.data ? res.data.data : []
      for (let item of flowActionList.value) {
        if (!tagList.value.includes(item.tag)) {
          tagList.value.push(item.tag)
        }
      }
    } else {
      flowActionList.value = []
      showFlowActionList.value = []
      tipPopup.value.showTipPopup('warn', '暂无动作列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示动作弹框
async function showFlowActionPopup(param) {
  tag.value = '全部'
  tagList.value = ['全部']
  flowActionInput.value = ''
  
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await getFlowActionList(param)
  
  uni.hideLoading()
  
  if (flowActionList.value.length === 0) {
    return
  }
  
  flowActionPopup.value.open()
}

// 选择动作
async function selectFlowAction(param) {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/selectFlowAction',
    method: 'POST',
    data: {
      id: param.id
    }
  }).then(res => {
    flowDetail.actions = param.actionCn
    if (res.data.code) {
      let data = res.data.data
      flowDetail.standard = data.standard
      flowDetail.checkPoint = data.checkPoint
      flowDetail.tools = data.tools
      flowDetail.machine = data.machine
      flowDetail.margin = data.margin
      flowDetail.temp = data.temp
      flowDetail.pressure = data.pressure
      flowDetail.glue = data.glue
      flowDetail.carLine = data.carLine
      flowDetail.chemical = data.chemical
      flowDetail.needleSpacing = data.needleSpacing
      flowDetail.spacing = data.spacing
      flowDetail.needle = data.needle
      flowDetail.time = data.time
      flowDetail.defence = data.defence
      flowDetail.processOption1 = data.processOption1
      flowDetail.processOption2 = data.processOption2
      flowDetail.processOption3 = data.processOption3
      flowDetail.processOption4 = data.processOption4
      flowDetail.processOption5 = data.processOption5
      flowDetail.processOption6 = data.processOption6
      flowDetail.processOption7 = data.processOption7
      flowDetail.processOption8 = data.processOption8
      flowDetail.processOption9 = data.processOption9
      flowDetail.processOption10 = data.processOption10
    } else {
      
    }
    flowActionPopup.value.close()
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

watch(tag, () => {
  if (tag.value === '全部') {
    showFlowActionList.value = flowActionList.value
  } else {
    showFlowActionList.value = flowActionList.value.filter(item => item.tag === tag.value)
  }
})

defineExpose({
  showFlowActionPopup
})
</script>

<template>
  <uni-popup
    ref="flowActionPopup"
    type="center"
    class="flow-action-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择动作
      </view>
      
      <view class="flow-action-tag-list">
        <view
          v-for="item in tagList"
          @click="tag = item"
          class="flow-action-tag button"
          :style="{
            backgroundColor: tag === item ? '#ccc' : 'transparent'
          }"
        >
          {{ item ? item : '其它' }}
        </view>
      </view>
      
      <view class="flow-action-list">
        <view class="flow-action flex-row-center">
          <input
            v-model="flowActionInput"
            @focus="focusFlowActionInput = true"
            @blur="focusFlowActionInput = false"
            type="text"
            placeholder="请输入动作"
            class="input"
            :style="{
              boxShadow: focusFlowActionInput ? '0 0 5px blue' : '0 0 5px gray'
            }"
          />
          
          <view v-show="flowActionInput" @click="flowActionInput = ''" class="clear button">
            <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
          </view>
        </view>
        
        <view
          v-for="(item, index) in showFlowActionList"
          :key="index"
          v-show="item.actionCn && item.actionCn.includes(flowActionInput.toUpperCase())"
          class="flow-action flex-row-center"
        >
          <view
            @click="selectFlowAction(item)"
            class="button"
          >
            {{ item.tag ? (item.tag + ' - ' + item.actionCn) : item.actionCn }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.flow-action-popup {
  .container {
    width: 500px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .flow-action-tag-list {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      
      .flow-action-tag {
        width: calc((100% - 80px) / 4);
        height: 40px;
        margin: 10px;
      }
    }
    
    .flow-action-list {
      height: 490px;
      overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .flow-action {
        width: 100%;
        padding: 10px;
        position: relative;
        
        .button {
          width: 350px;
          min-height: 50px;
          color: darkmagenta;
          font-size: 20px;
          padding: 5px;
        }
        
        .input {
          width: 350px;
          height: 50px;
          padding: 0 10px;
        }
        
        .clear {
          width: 60px;
          min-height: 40px;
          position: absolute;
          top: 15px;
          right: 80px;
        }
        
        &:first-child {
          position: sticky;
          top: 0;
          background-color: #fdf6e3;
          z-index: 1;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>