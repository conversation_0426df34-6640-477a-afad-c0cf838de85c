<template>
  <view class="image-preview-container" v-if="visible" @click="closePreview">
    <view class="image-preview-wrapper" @click.stop>
      <image 
        :src="imageUrl" 
        :style="{ width: width, height: height }" 
        mode="aspectFit" 
        class="preview-image"
      />
      <view class="close-btn" @click="closePreview">×</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ImagePreview',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    imageUrl: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '80%'
    },
    height: {
      type: String,
      default: '80%'
    }
  },
  methods: {
    closePreview() {
      this.$emit('update:visible', false);
    }
  }
}
</script>

<style>
.image-preview-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.image-preview-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 80%;
  height: 80%;
  max-width: 80%;
  max-height: 80%;
  aspect-ratio: 1/1;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.close-btn {
  position: absolute;
  top: -15px;
  right: -15px;
  width: 30px;
  height: 30px;
  background-color: #1976d2;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
  .image-preview-wrapper {
    width: 90%;
    height: 90%;
    max-width: 90%;
    max-height: 90%;
    padding: 15px;
  }
  
  .close-btn {
    width: 26px;
    height: 26px;
    font-size: 16px;
    top: -10px;
    right: -10px;
  }
}

/* 横屏模式 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .image-preview-wrapper {
    width: 70%;
    height: 70%;
    max-width: 70%;
    max-height: 70%;
  }
}
</style> 