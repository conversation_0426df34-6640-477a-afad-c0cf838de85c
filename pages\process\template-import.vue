<script setup>
import { ref, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import urlPrefix from '@/pages/common/urlPrefix.js'

// 生产环境
// const urlPrefix = "http://***********:8200"
const user = uni.getStorageSync("loUserNo")

// 开发环境
// const urlPrefix = "http://**********:8080"
// const user = "zqn"

const template = ref()

const templateList = ref([])
const templateOptionList = ref([])
const templateListShow = ref(false)

const operationMap = new Map([
  ["加工", "1"],
  ["裁断", "2"],
  ["准备", "3"],
  ["针车", "4"],
  ["半成品", "5"],
  ["成型", "6"]
])

const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')

// 返回上一页
function back() {
  template.value = ''
  uni.navigateBack({
    delta: 1,
    animationType: 'pop-out',
    animationDuration: 200
  })
}

// 获取模板
function getTemplate() {
  uni.request({
    url: urlPrefix + "/process/getTemplate",
    method: "POST",
    data: {
      operation: operationMap.get(template.value.operation)
    }
  }).then(res => {
    templateList.value = res.data.data ? res.data.data : []
    templateOptionList.value = templateList.value.filter(item => (item.pro_seq + ' | ' + item.type_desc + ' | ' + item.material).includes(template.value.template ? template.value.template.toUpperCase() : '')).slice(0, 50)
  }).catch(err => {
    showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 清空模板
function clearTemplate() {
  template.value.doc_no = ''
  template.value.template = ''
}

// 选择模板
function selectTemplate(param) {
  template.value.doc_no = param.doc_no
  template.value.template = param.pro_seq + (param.type_desc ? ' | ' : '') + param.type_desc + (param.material ? ' | ' : '') + param.material
}

// 模板导入
function templateImport() {
  if (!template.value.doc_no || template.value.doc_no.length === 0) {
    showTip('warn', '请选择模板！')
    return
  }
  
  uni.request({
    url: urlPrefix + "/process/templateImport",
    method: "POST",
    data: {
      ...template.value,
      operation: operationMap.get(template.value.operation)
    }
  }).then(res => {
    if (res.data.code) {
      showTip('success', '模板导入成功！')
      setTimeout(() => {
        back()
      }, 1000)
    } else {
      showTip('error', '模板导入失败！')
    }
  }).catch(err => {
    showToast({
      icon: 'error',
      title: '请检查网络！'
    })
  })
}

// 提示信息
function showTip(type, message) {
  tipType.value = type
  tipMessage.value = message
  tipPopup.value.open()
}

watch(template, () => {
  templateOptionList.value = templateList.value.filter(item => (item.pro_seq + ' | ' + item.type_desc + ' | ' + item.material).includes(template.value.template ? template.value.template.toUpperCase() : '')).slice(0, 50)
}, { deep: true })

onLoad((props) => {
  template.value = JSON.parse(props.template)
  template.value.doc_no = ''
  template.value.template = ''
  template.value.ins_user = user
  template.value.upd_user = user
  getTemplate()
})
</script>

<template>
  <view class="template-import">
    <view class="back">
      <uni-icons @click="back()" type="back" size="36" color="#6fa2ce"></uni-icons>
    </view>
    
    <view class="submit">
      <uni-icons @click="templateImport()" type="checkmarkempty" size="36" color="#45b08c"></uni-icons>
    </view>
    
    <view class="title">
      模板导入工序步骤
    </view>
    
    <view class="data">
      <uni-section title="型体编号" titleFontSize="20px" type="circle" class="attribute">
        <uni-easyinput :value="template.model_no" disabled></uni-easyinput>
      </uni-section>
      
      <uni-section title="制程" titleFontSize="20px" type="circle" class="attribute" style="margin-left: 0;">
        <uni-easyinput :value="template.operation" disabled></uni-easyinput>
      </uni-section>
      
      <uni-section title="主要代码" titleFontSize="20px" type="circle" class="attribute">
        <uni-easyinput :value="template.rtg_code" disabled></uni-easyinput>
      </uni-section>
      
      <uni-section title="模板编号" titleFontSize="20px" type="circle" class="attribute" style="margin-left: 0;">
        <uni-easyinput :value="template.doc_no" disabled></uni-easyinput>
      </uni-section>
      
      <uni-section title="模板" titleFontSize="20px" type="line" class="attribute" style="margin-left: 5%; width: 90%;">
        <view class="template">
          <input
            v-model="template.template"
            type="text"
            @focus="templateListShow = true"
            @blur="templateListShow = false"
            class="input"
          />
          
          <view
            v-show="template && template.template && template.template.length > 0"
            @click="clearTemplate()"
            class="clear"
          >
            <uni-icons type="clear" size="34"></uni-icons>
          </view>
          
          <transition name="list">
            <view v-show="templateListShow" class="box">
              <view class="list">
                <view
                  v-for="item in templateOptionList"
                  :key="item"
                  @click="selectTemplate(item)"
                  class="item"
                  :style="{ border: templateOptionList.length === 1 ? 'none' : '' }"
                >
                  {{ item.pro_seq + (item.type_desc ? ' | ' : '') + item.type_desc + (item.material ? ' | ' : '') + item.material }}
                </view>
                
                <view
                  v-show="templateOptionList.length === 0"
                  class="item"
                  style="border: none;"
                >
                  暂无该模板数据
                </view>
              </view>
            </view>
          </transition>
        </view>
      </uni-section>
    </view>
  </view>
  
  <view class="tip-popup">
    <uni-popup
      ref="tipPopup"
      type="message"
    >
      <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
    </uni-popup>
  </view>
</template>

<style lang="scss">
page {
  width: 100%;
  height: 100%;
  padding: 2.5% 2% 1.5% 2%;
  box-sizing: border-box;
  background-color: #fdf6e3;
}

.template-import {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-radius: 10px;
  box-shadow: 0 0 1px 5px #ddd;
  position: relative;
  overflow: auto;
  
  .back {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 2.5%;
    top: 2.5%;
    border-radius: 50%;
    box-shadow: 0 0 5px gray;
    transition: all 0.05s ease-in-out;
    cursor: pointer;
    z-index: 1;
    
    &:active {
      transform: scale(0.97);
      box-shadow: 0 0 1px gray;
    }
  }
      
  .submit {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 2.5%;
    top: 2.5%;
    border-radius: 50%;
    box-shadow: 0 0 5px gray;
    transition: all 0.05s ease-in-out;
    cursor: pointer;
    z-index: 1;
    
    &:active {
      transform: scale(0.97);
      box-shadow: 0 0 1px gray;
    }
  }
      
  .title {
    margin-bottom: 1%;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    padding: 16px;
  }
      
  .data {
    width: 100%;
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    
    .attribute {
      width: 35%;
      margin-left: 15%;
      margin-bottom: 5%;
      background-color: #fdf6e3;
      
      .uni-easyinput {
        width: 80%;
        margin-left: 16px;
        
        &:deep(.uni-easyinput__content-input) {
          height: 40px;
          color: gray;
          font-size: 20px;
          font-weight: bold;
        }
      }
      
      .template {
        width: 100%;
        height: 40px;
        margin-left: 16px;
        position: relative;
        
        .input {
          width: 100%;
          height: 100%;
          padding: 0 10px;
          font-size: 20px;
          font-weight: bold;
          background-color: white;
          border: 1px solid #f0f0f0;
          border-radius: 4px;
          box-sizing: border-box;
          
          &:hover {
            border: 1px solid black;
          }
        }
        
        .clear {
          position: absolute;
          top: 3px;
          right: 3px;
          cursor: pointer;
          z-index: 1;
        }
        
        .box {
          width: 100%;
          height: 270px;
          position: absolute;
          left: 0;
          top: -285px;
          z-index: 1;
          transform: rotate(180deg);
          
          .list {
            max-height: 270px;
            text-align: center;
            border-radius: 5px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            background-color: gray;
            overflow: auto;
            transform: rotate(180deg);
            
            .item {
              box-sizing: border-box;
              padding: 15px 0;
              border-top: 1px solid white;
              cursor: pointer;
              
              &:active {
                background-color: #aaa;
              }
            }
          }
          
          &::before {
            content: "";
            position: absolute;
            left: calc(50% - 10px);
            top: -10px;
            border-top: 0px solid transparent;
            border-left: 10px solid transparent;
            border-bottom: 10px solid gray;
            border-right: 10px solid transparent;
          }
        }
      }
    }
  }
}

.tip-popup {
  &:deep(.fixforpc-width) {
    min-width: 0;
    margin-top: 40px;
    padding: 10px 20px;
  }
  
  &:deep(.uni-popup-message-text) {
    font-size: 18px;
    font-weight: bold;
  }
}

.list-enter-active, .list-leave-active {
  transition: all 0.25s ease-in-out;
}

.list-enter-from, .list-leave-to {
  opacity: 0;
}

.list-enter-to, .list-leave-from {
  opacity: 1;
}
</style>