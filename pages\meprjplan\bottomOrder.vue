<template>
	<view class="status-bar"></view>
	<view class="top-container">
		<view class="back">
			<uni-icons @click="back()" type="back" size="30" color="#6fa2ce"></uni-icons>
		</view>
		<view class="title">
			皮底訂單生產狀況表
		</view>
	</view>

	<view class="select_time">
		<view class="select_time_title">
			下單日期:
		</view>


		<picker mode="date" :value="startTime" @change="bindStartDateChange">
			<view style="padding: 8px;background-color: #F5F7FA;width: 90px" v-if="startTime == ''" class="uni-input">
				请选择
			</view>
			<view style="padding: 8px;background-color: #F5F7FA;width: 90px" v-if="startTime != ''" class="uni-input">
				{{startTimeText}}
			</view>
		</picker>


		<text style="margin-right: 5px;margin-left: 10px;">-</text>

		<picker mode="date" :value="endTime" @change="bindEndDateChange" style="margin-left: 10px">
			<view style="padding: 8px;background-color: #F5F7FA; width: 90px;" v-if="endTime == ''" class="uni-input">
				请选择
			</view>
			<view style="padding: 8px;background-color: #F5F7FA;width: 90px" v-if="endTime != ''" class="uni-input">
				{{endTimeText}}
			</view>
		</picker>

		<!-- <uv-datetime-picker ref="datetimePickerSt" v-model="startTime" mode="date"
			@confirm="confirmSt"></uv-datetime-picker>
		<view class="inputDate">
			<uv-input disabled="true" @click="openSt" v-model="startTimeText"></uv-input>
		</view> -->
		<uv-button @click="query()" style="margin-left: 25px;" type="primary" text="查詢"></uv-button>




		// #ifdef H5
		<view style="  display: flex;">
			<uv-button @click="exportData()" style="margin-left: 25px;" type="primary" text="导出"></uv-button>
		</view>

		// #endif
	</view>

	<view class="container">


		<!-- 原有的表格内容 -->
		<scroll-view class="table-scroll" scroll-x="true" scroll-y="true" :scroll-left="scrollLeft"
			:show-scrollbar="false" :enhanced="true" :show-scrollbar-y="false" :show-scrollbar-x="false"
			@scroll="syncMainScroll" ref="scrollContainer">
			<table>
				<colgroup>
					<col style="min-width: 90px; width: 90px;" />
					<col style="min-width: 60px; width: 60px;" />
					<col style="min-width: 90px; width: 90px;" />
					<col style="min-width: 130px; width: 130px;" />
					<col style="min-width: 90px; width: 90px;" />
					<col style="min-width: 60px; width: 60px;" />
					<col style="min-width: 60px; width: 60px;" />
					<col style="min-width: 200px; width: 200px;" />
					<col style="min-width: 100px; width: 100px;" />

					<col style="min-width: 150px; width: 150px;" />
					<col style="min-width: 60px; width: 60px;" />
					<col style="min-width: 60px; width: 60px;" />
					<col style="min-width: 60px; width: 60px;" />
					<col style="min-width: 60px; width: 60px;" />
					<col style="min-width: 60px; width: 60px;" />
					<col style="min-width: 60px; width: 60px;" />
					<col style="min-width: 60px; width: 60px;" />
					<col style="min-width: 60px; width: 60px;" />
					<col style="min-width: 60px; width: 60px;" />

				</colgroup>
				<!-- 表头行 -->
				<tr style="background-color: #fdf6e3;">
					<th class="sticky-col1" align="center">下單日期</th>
					<th class="sticky-col2" align="center">品牌</th>
					<th class="sticky-col3" align="center">客戶型體</th>
					<th class="sticky-col4" align="center">內部訂單</th>
					<th class="sticky-col5" align="center">確認出貨日期</th>
					<th class="sticky-col6" align="center">圖片</th>
					<th class="sticky-col7" align="center">數量</th>
					<th class="sticky-col8" align="center">底部代號</th>
					<th class="sticky-col9" align="center">成品類型</th>
					<th class="sticky-col10" align="center">顏色</th>




					<th align="center">皮料狀況</th>
					<th align="center">副料狀況</th>
					<th align="center">裁斷</th>
					<th align="center">層皮</th>
					<th align="center">三合一</th>
					<th align="center">削邊</th>
					<th align="center">CNC</th>
					<th align="center">打磨</th>
					<th align="center">沿條</th>
				</tr>
				<!-- 数据行 -->
				<tr v-for="(item, index) in dataList" :key="item" class="datalist-tr">
					<td class="sticky-col1" align="center"> {{ item.get_DATE  && item.get_DATE .split(' ')[0] }}</td>
					<td class="sticky-col2" align="center">{{ item.brand_NO }}</td>
					<td class="sticky-col3" align="center">{{ item.parentsmatno }}</td>

					<td class="clickable-cell sticky-col4 " align="center" @click="showDetail(item.sorder_NO)">
						{{ item.sorder_NO }}
					</td>
					<td class="sticky-col5" align="center">{{ item.out_CDATE  && item.out_CDATE .split(' ')[0]  }}</td>
					<td class="sticky-col6" align="center">
						<image v-if="item.pic_base64" :src="item.pic_base64" @click="previewImage(item.pic_base64)"
							class="img" mode="aspectFit"></image>

					</td>
					<td class="sticky-col7" align="center">{{ item.ord_QTY }}</td>
					<td class="sticky-col8" align="center">{{ item.model_NAME }}</td>
					<td class="sticky-col9" align="center">{{ item.sole }}</td>
					<td class="sticky-col10" align="center">{{ item.chn_COLOR }}</td>


					<!-- <td align="center">{{ item.commitment }}</td> -->


					<td align="center">{{ item.pl_STATUS }}</td>
					<td align="center">{{ item.fl_STATUS }}</td>


					<td align="center">
						{{ item.t_5D_FLAG === 'Y' ?  (item.t_5D_CPT_QTY === 0 ? 'OK' : item.t_5D_CPT_QTY) : '' }}
					</td>
					<td align="center">
						{{ item.t_5P_FLAG === 'Y' ?  (item.t_5P_CPT_QTY === 0 ? 'OK' : item.t_5P_CPT_QTY) : '' }}
					</td>
					<td align="center">
						{{ item.t_5T_FLAG === 'Y' ?  (item.t_5T_CPT_QTY === 0 ? 'OK' : item.t_5T_CPT_QTY) : '' }}
					</td>
					<td align="center">
						{{ item.t_5Q_FLAG === 'Y' ?  (item.t_5Q_CPT_QTY === 0 ? 'OK' : item.t_5Q_CPT_QTY)  : '' }}
					</td>

					<td align="center">
						{{ item.t_5R_FLAG === 'Y' ?  (item.t_5R_CPT_QTY === 0 ? 'OK' : item.t_5R_CPT_QTY)  : '' }}
					</td>
					<td align="center">
						{{ item.t_5U_FLAG === 'Y' ?  (item.t_5U_CPT_QTY === 0 ? 'OK' : item.t_5U_CPT_QTY)  : '' }}
					</td>
					<td align="center">
						{{ item.t_5S_FLAG === 'Y' ?  (item.t_5S_CPT_QTY === 0 ? 'OK' : item.t_5S_CPT_QTY)  : '' }}
					</td>
				</tr>
			</table>
		</scroll-view>

		<!-- 底部合计表格 -->
		<view class="footer-table">
			<!-- #ifdef H5 -->
			<scroll-view class="table-container2" :scroll-left="scrollLeft" scroll-x="true" :show-scrollbar="true"
				:enhanced="true" :show-scrollbar-y="false" :show-scrollbar-x="true" @scroll="syncFooterScroll"
				ref="footerScrollContainer">
			<!-- #endif -->
				<!-- #ifdef APP-PLUS -->
				<scroll-view class="table-container2" :scroll-left="scrollLeft" scroll-x="true" :show-scrollbar="false"
					:enhanced="true" :show-scrollbar-y="false" :show-scrollbar-x="false" @scroll="syncFooterScroll"
					ref="footerScrollContainer">
				<!-- #endif -->
					<table>
						<colgroup>
							<col style="min-width: 90px; width: 90px;" />
							<col style="min-width: 60px; width: 60px;" />
							<col style="min-width: 90px; width: 90px;" />
							<col style="min-width: 130px; width: 130px;" />
							<col style="min-width: 90px; width: 90px;" />
							<col style="min-width: 60px; width: 60px;" />
							<col style="min-width: 60px; width: 60px;" />
							<col style="min-width: 200px; width: 200px;" />
							<col style="min-width: 100px; width: 100px;" />

							<col style="min-width: 150px; width: 150px;" />
							<col style="min-width: 60px; width: 60px;" />
							<col style="min-width: 60px; width: 60px;" />
							<col style="min-width: 60px; width: 60px;" />
							<col style="min-width: 60px; width: 60px;" />
							<col style="min-width: 60px; width: 60px;" />
							<col style="min-width: 60px; width: 60px;" />
							<col style="min-width: 60px; width: 60px;" />
							<col style="min-width: 60px; width: 60px;" />
							<col style="min-width: 60px; width: 60px;" />

						</colgroup>
						<tr>
							<td class="sticky-col1" align="center">合計</td>
							<td class="sticky-col2" align="center">-</td>
							<td class="sticky-col3" align="center">-</td>
							<td class="sticky-col4" align="center">-</td>
							<td class="sticky-col5" align="center">-</td>
							<td class="sticky-col6" align="center">-</td>
							<td class="sticky-col7" align="center">{{ totals.totalQty }}</td>
							<td class="sticky-col8" align="center">-</td>
							<td class="sticky-col9" align="center">-</td>
							<td class="sticky-col10" align="center">-</td>
							<td align="center">-</td>
							<td align="center">-</td>
							<td align="center">{{ totals.total5D }}</td>
							<td align="center">{{ totals.total5P }}</td>
							<td align="center">{{ totals.total5T }}</td>
							<td align="center">{{ totals.total5Q }}</td>
							<td align="center">{{ totals.total5R }}</td>
							<td align="center">{{ totals.total5U }}</td>
							<td align="center">{{ totals.total5S }}</td>
						</tr>
					</table>
				</scroll-view>
		</view>

		<view class="picture-popup">
			<uni-popup ref="picturePopup" type="center">
				<view class="watermark">
					<img :src="preViewPicture" alt="" class="img">
				</view>
			</uni-popup>
		</view>

		<!-- 添加详情弹框 -->
		<uni-popup ref="detailPopup" type="center">
			<view class="detail-popup">
				<view class="detail-title">訂單詳情</view>
				<view class="detail-table-container">
					<table class="detail-table">
						<tr>
							<th width="15%" align="center">內部訂單</th>
							<th width="15%" align="center">客戶訂單</th>
							<th width="15%" align="center">訂單文件號</th>
							<th width="15%" align="center">工作單號</th>
							<th width="6%" align="center">訂單數量</th>
							<th width="6%" align="center">入庫數</th>
							<th width="6%" align="center">出庫數</th>
							<th width="6%" align="center">出貨狀態</th>
							<th width="6%" align="center">請款狀態</th>
							<th width="8%" align="center">出貨日</th>
						</tr>
						<tr v-for="(item, index) in detailList" :key="index">
							<td align="center">{{ item.order_NO }}</td>
							<td align="center">{{ item.cust_ORDER }}</td>
							<td align="center">{{ item.order_AUTO }}</td>
							<td align="center">{{ item.commitment }}</td>
							<td align="center">{{ item.ord_QTY }}</td>
							<td align="center">{{ item.in_QTY }}</td>
							<td align="center">{{ item.out_QTY }}</td>
							<td align="center">{{ item.out_FLAG }}</td>
							<td align="center">{{ item.paid_STATUS }}</td>
							<td align="center">
								{{ item.out_CDATE ? item.out_CDATE.split(' ')[0] : '' }}
							</td>
						</tr>
					</table>
				</view>
				<view class="detail-footer">
					<button @click="closeDetail" class="close-btn">關閉</button>
				</view>
			</view>
		</uni-popup>

	</view>

</template>

<script setup>
	import {
		onMounted,
		ref,
		reactive,
		watch,
		computed,
		nextTick,
		onUnmounted
	} from 'vue'
	import {
		onPullDownRefresh
	} from '@dcloudio/uni-app'
	import {
		onShow
	} from '@dcloudio/uni-app';
	import urlPrefix from '@/pages/common/urlPrefix.js'



	//日期选择开始
	const date = new Date();
	date.setHours(date.getHours() - 24);
	const startTime = ref(date.getTime())
	const datetimePickerSt = ref()
	const startTimeText = ref('')


	function bindStartDateChange(e) {
		startTime.value = new Date(e.detail.value).getTime();
		startTimeText.value = e.detail.value;
	}

	function bindEndDateChange(e) {
		endTime.value = new Date(e.detail.value).getTime();
		endTimeText.value = e.detail.value;
	}




	//日期选择结束
	const date2 = new Date();
	date2.setHours(date2.getHours() - 24);
	const endTime = ref(date2.getTime())
	const datetimePickerEnd = ref()
	const endTimeText = ref()


	const preViewPicture = ref()
	const picturePopup = ref();

	function previewImage(url) {
		preViewPicture.value = url;
		picturePopup.value.open();
	}





	const dataList = ref([])
	const detailList = ref([])
	const mergeInfo = ref([]) // 用于跟踪合并状态的数组
	const tableRef = ref();



	// //日期选择
	// const date = new Date();
	// date.setHours(date.getHours() - 24);
	// const startTime = ref(date.getTime())
	// const datetimePickerSt = ref()
	// const startTimeText = ref('')

	// function openSt() {
	// 	datetimePickerSt.value.open();
	// }


	// function confirmSt(e) {
	// 	console.log('confirm', e);
	// 	// 创建一个新的日期对象e
	// 	var date = new Date(e.value);

	// 	// 获取年、月、日
	// 	var year = date.getFullYear();
	// 	var month = date.getMonth() + 1; // 月份从0开始，需要加1
	// 	var day = date.getDate();

	// 	// 格式化日期为yyyy/MM/DD的样式
	// 	// var formattedDate = year + '/' + (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day);
	// 	var formattedDate = year + '/' + (month < 10 ? '0' + month : month);
	// 	console.log("formattedDate:" + formattedDate)
	// 	startTimeText.value = formattedDate;
	// 	console.log("startTimeText.value:" + startTimeText.value)
	// }


	
	function exportData() {

		uni.showLoading({
			title: '下载中',
			mask: true // 设置遮罩层
		});
		// 使用uni.request发送GET请求
		uni.request({
			url: urlPrefix + "/api/matting/bottomOrderExport",
			method: 'GET',
			data: {
				beginTime: startTimeText.value,
				endTime: endTimeText.value,
			},
			responseType: 'arraybuffer',
			timeout: 600000,
			success: (res) => {
				if (res.statusCode === 200) {
	
					// 创建Blob对象
					const blob = new Blob([res.data], {
						type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
					});
	
					// 创建一个下载链接
					const downloadElement = document.createElement('a');
					const href = window.URL.createObjectURL(blob); // 创建下载的链接
					downloadElement.href = href;
					downloadElement.download = '皮底訂單生產狀況表.xlsx'; // 下载后文件名
					document.body.appendChild(downloadElement);
					downloadElement.click(); // 点击下载
					document.body.removeChild(downloadElement); // 下载完成移除元素
					window.URL.revokeObjectURL(href); // 释放掉blob对象
					uni.hideLoading();
				} else {
					// 处理错误情况
					console.error('导出Excel失败:', res);
					uni.hideLoading();
				}
			},
			fail: (error) => {
				// 处理请求失败
				console.error('请求失败:', error);
				uni.hideLoading();
			}
		});
	}










	function query() {
		getData();
	}

	//获取数据
	async function getData(shouldIncludeParams = true) {

		uni.showLoading({
			title: '加載中', // 加载框显示的文本
			mask: true // 设置为true，阻止用户点击
		});

		if (tableRef.value) {
			tableRef.value.clearSelection();
		}
		const requestData = shouldIncludeParams ? {
			beginTime: startTimeText.value,
			endTime: endTimeText.value,
		} : {};

		await uni.request({
			url: urlPrefix + "/api/matting/bottomOrderList",
			data: requestData,
			method: "GET"
		}).then(res => {
			dataList.value = res.data.data;
			mergeInfo.value = calculateMergeInfo(dataList.value)
			uni.hideLoading();
		}).catch(err => {
			console.log(err)
			uni.showToast({
				title: '获取数据失败..',
				icon: "error"
			});
		})
	}

	const detailPopup = ref(null);

	// 显示详情
	async function showDetail(orderNo) {
		if (!orderNo) return;

		const [modelNo, mline] = orderNo.split('-');
		if (!modelNo || !mline) return;

		await getDetail(modelNo, mline);
		detailPopup.value.open();
	}

	// 关闭详情
	function closeDetail() {
		detailPopup.value.close();
	}

	// 修改 getDetail 方法，添加 async
	async function getDetail(modelNo, mline) {
		const requestData = {
			modelNo: modelNo,
			mline: mline,
		};

		await uni.request({
			url: urlPrefix + "/api/matting/bottomOrderItem",
			data: requestData,
			method: "GET"
		}).then(res => {
			detailList.value = res.data.data;
		}).catch(err => {
			console.log(err)
			uni.showToast({
				title: '获取数据失败..',
				icon: "error"
			});
		})
	}

	//预加载
	onMounted(async () => {
		// #ifdef H5
		showScrollbarX.value = true;
		// #endif

		// #ifdef APP-PLUS
		showScrollbarX.value = false;
		// #endif

		// 创建一个新的日期对象e
		var date = new Date();
		// 获取年、月、日
		var year = date.getFullYear();
		var month = date.getMonth() + 1; // 月份从0开始，需要加1
		var day = date.getDate();

		// 格式化日期为yyyy/MM/DD的样式
		var formattedDate = year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day :
			day);

		endTimeText.value = formattedDate;
		startTimeText.value = formattedDate;
		await getData(false)

	})


	// 计算合并信息
	function calculateMergeInfo(data) {
		let mergeInfo = []
		let lastPdLine = null
		let count = 0

		data.forEach((item, index) => {
			if (item.pd_line !== lastPdLine) {
				if (lastPdLine !== null) {
					mergeInfo.push({
						start: index - count,
						count: count
					})
				}
				lastPdLine = item.pd_line
				count = 1
			} else {
				count++
			}
		})

		// 添加最后一个合并信息
		if (lastPdLine !== null) {
			mergeInfo.push({
				start: data.length - count,
				count: count
			})
		}

		return mergeInfo
	}

	// 添加辅助函数到 script 部分
	function shouldDisplayPdLine(rowIndex) {
		const info = mergeInfo.value.find(info => info.start === rowIndex)
		return info !== undefined
	}

	function getRowspan(rowIndex) {
		const info = mergeInfo.value.find(info => info.start === rowIndex)
		return info ? info.count : 1
	}

	//返回首页
	function back() {
		let back = getCurrentPages();
		if (back && back.length > 1) {
			uni.navigateBack({
				delta: 1
			});
		} else {
			history.back();
		}
	}

	// 使用 ref 创建响应式的引用
	const scrollContainer = ref(null);
	const scrollLeft = ref(0); // 初始滚动位置

	// 添加底部表格的引用
	const footerScrollContainer = ref(null);

	// 修改同步滚动相关的代码
	const isMainScrolling = ref(false);
	const isFooterScrolling = ref(false);
	let scrollTimer = null;

	function syncMainScroll(event) {
		if (isFooterScrolling.value) return;

		const left = event.detail.scrollLeft;
		isMainScrolling.value = true;
		scrollLeft.value = left;

		// 使用 requestAnimationFrame 提高同步精度
		if (scrollTimer) {
			cancelAnimationFrame(scrollTimer);
		}

		scrollTimer = requestAnimationFrame(() => {
			// #ifdef H5
			if (footerScrollContainer.value?.$el) {
				footerScrollContainer.value.$el.scrollLeft = left;
			}
			// #endif

			// #ifdef APP-PLUS
			if (footerScrollContainer.value) {
				footerScrollContainer.value.scrollTo({
					left: left,
					duration: 0
				});
			}
			// #endif

			setTimeout(() => {
				isMainScrolling.value = false;
			}, 100);
		});
	}

	function syncFooterScroll(event) {
		if (isMainScrolling.value) return;

		const left = event.detail.scrollLeft;
		isFooterScrolling.value = true;
		scrollLeft.value = left;

		// 使用 requestAnimationFrame 提高同步精度
		if (scrollTimer) {
			cancelAnimationFrame(scrollTimer);
		}

		scrollTimer = requestAnimationFrame(() => {
			// #ifdef H5
			if (scrollContainer.value?.$el) {
				scrollContainer.value.$el.scrollLeft = left;
			}
			// #endif

			// #ifdef APP-PLUS
			if (scrollContainer.value) {
				scrollContainer.value.scrollTo({
					left: left,
					duration: 0
				});
			}
			// #endif

			setTimeout(() => {
				isFooterScrolling.value = false;
			}, 100);
		});
	}

	// 在组件卸载时清理
	onUnmounted(() => {
		isMainScrolling.value = false;
		isFooterScrolling.value = false;
		if (scrollTimer) {
			cancelAnimationFrame(scrollTimer);
		}
	});

	// 修改计算属性，添加新的合计项
	const totals = computed(() => {
		return dataList.value.reduce((sums, item) => {
			return {
				totalQty: sums.totalQty + (Number(item.ord_QTY) || 0),
				total5D: sums.total5D + (item.t_5D_FLAG === 'Y' ? Number(item.t_5D_CPT_QTY) || 0 : 0),
				total5P: sums.total5P + (item.t_5P_FLAG === 'Y' ? Number(item.t_5P_CPT_QTY) || 0 : 0),
				total5T: sums.total5T + (item.t_5T_FLAG === 'Y' ? Number(item.t_5T_CPT_QTY) || 0 : 0),
				total5Q: sums.total5Q + (item.t_5Q_FLAG === 'Y' ? Number(item.t_5Q_CPT_QTY) || 0 : 0),
				total5R: sums.total5R + (item.t_5R_FLAG === 'Y' ? Number(item.t_5R_CPT_QTY) || 0 : 0),
				total5U: sums.total5U + (item.t_5U_FLAG === 'Y' ? Number(item.t_5U_CPT_QTY) || 0 : 0),
				total5S: sums.total5S + (item.t_5S_FLAG === 'Y' ? Number(item.t_5S_CPT_QTY) || 0 : 0),
				total5Y: sums.total5Y + (item.t_5Y_FLAG === 'Y' ? Number(item.t_5Y_CPT_QTY) || 0 : 0)
			}
		}, {
			totalQty: 0,
			total5D: 0,
			total5P: 0,
			total5T: 0,
			total5Q: 0,
			total5R: 0,
			total5U: 0,
			total5S: 0,
			total5Y: 0
		})
	})

	// 添加平台判断
	const showScrollbarX = ref(false);
</script>


<style lang="scss">
	page {
		background-color: #FFFFFF;
		height: 100vh;
		overflow: hidden; // 禁用页面滚动
	}



	// .uni-scroll-view {
	//     position: relative;
	//     -webkit-overflow-scrolling: touch;
	//     width: 100%;

	//     max-height: inherit;
	// }

	// .vertical-line {
	//   border-left: 1px solid chartreuse; /* 黑色竖线，可以根据需要调整颜色和宽度 */
	//   height: 100px; /* 竖线的高度，根据需要调整 */
	//   width: 0.1px; /* 宽度为0，只显示边框 */
	// }

	.top-container {
		margin-left: 2%;
		margin-top: 14px;
		display: flex;
		align-items: center;
		/* 垂直居中对齐 */
		height: 60px;
		/* 容器高度设为视口高度，以便垂直居中 */

	}

	.title {
		font-size: 22px;
		margin: 0 auto;
		/* 使标题水平居中 */
		padding: 0;
		/* 移除默认的内边距 */
	}

	.return-button {}

	.select_time {
		margin-left: 2.5%;
		display: flex;
		align-items: center;
		justify-content: flex-start;

		.select_time_title {
			font-size: 16px;
			margin-right: 10px;
		}

	}

	.container {
		width: 95%;
		margin: 0 auto;
		display: flex;
		flex-direction: column;
		position: fixed;
		top: calc(130px);
		bottom: 10px;
		left: 2.5%;
		right: 2.5%;
	}

	// 添加固定竖线样式
	.fixed-lines {
		display: none;
	}

	.v-line {
		display: none;
	}

	// 修改表格容器样式
	.table-scroll {
		position: relative;
		flex: 1;
		width: 100%;
		height: calc(100vh - 350px);
		background-color: #fff;
		border: 1px solid #ccc;
		overflow-x: auto;
		overflow-y: auto;

		/* 隐藏所有滚动条 */
		&::-webkit-scrollbar {
			width: 0 !important;
			height: 0 !important;
			display: none !important;
		}

		&::-webkit-scrollbar-thumb {
			display: none !important;
		}

		&::-webkit-scrollbar-track {
			display: none !important;
		}

		/* Firefox */
		scrollbar-width: none !important;
		/* IE */
		-ms-overflow-style: none !important;

		/* 添加额外的浏览器兼容性支持 */
		&> ::-webkit-scrollbar {
			width: 0 !important;
			height: 0 !important;
			display: none !important;
		}

		&>.uni-scroll-view::-webkit-scrollbar {
			width: 0 !important;
			height: 0 !important;
			display: none !important;
		}

		.uni-scroll-view {
			scrollbar-width: none !important;
			-ms-overflow-style: none !important;


			&::-webkit-scrollbar {
				width: 0 !important;
				height: 0 !important;
				display: none !important;
			}
		}

		table {
			width: 100%;
			min-width: 1500px;
			table-layout: fixed;
			border-collapse: separate;
			border-spacing: 0;
		}
	}

	/* 修改scroll-view组件的滚动条样式 */
	::v-deep .uni-scroll-view {
		&.table-scroll {
			&::-webkit-scrollbar {
				width: 0 !important;
				height: 0 !important;
				display: none !important;
			}

			scrollbar-width: none !important;
			-ms-overflow-style: none !important;
		}
	}

	// 修改表头样式
	th {
		position: sticky;
		top: 0;
		background-color: #5B9BD5 !important;
		color: #fff;
		z-index: 11;
	}

	// 修改固定列样式
	td.sticky-col1,
	td.sticky-col2,
	td.sticky-col3,
	td.sticky-col4,
	td.sticky-col5,
	td.sticky-col6,
	td.sticky-col7,
	td.sticky-col8,
	td.sticky-col9,
	td.sticky-col10,

	th.sticky-col1,
	th.sticky-col2,
	th.sticky-col3,
	th.sticky-col4,
	th.sticky-col5,
	th.sticky-col6,
	th.sticky-col7,
	th.sticky-col8,
	th.sticky-col9,
	th.sticky-col10 {
		position: sticky;
		z-index: 10;
		border-right: none;
		box-shadow: inset -1px 0 0 #ccc, 1px 0 0 #ccc;
	}

	// 固定列的表头样式
	th.sticky-col1,
	th.sticky-col2,
	th.sticky-col3,
	th.sticky-col4,
	th.sticky-col5,
	th.sticky-col6,
	th.sticky-col7,
	th.sticky-col8,
	th.sticky-col9,
	th.sticky-col10 {
		z-index: 12;
		background-color: #5B9BD5 !important;
	}

	// 修改固定列的数据单元格样式
	td.sticky-col1,
	td.sticky-col2,
	td.sticky-col3,
	td.sticky-col4,
	td.sticky-col5,
	td.sticky-col6,
	td.sticky-col7,
	td.sticky-col8,
	td.sticky-col9,
	td.sticky-col10 {
		background-color: #fff;
		z-index: 10;

		&.clickable-cell {
			color: #0066cc !important;
			text-decoration: underline !important;

			&:hover {
				color: #004499 !important;
			}
		}
	}

	// 固定列位置
	.sticky-col1 {
		left: 0;
	}

	.sticky-col2 {
		left: 90px;
	}

	.sticky-col3 {
		left: 150px;
	}

	.sticky-col4 {
		left: 240px;
	}

	.sticky-col5 {
		left: 370px;
	}

	.sticky-col6 {
		left: 460px;
	}

	.sticky-col7 {
		left: 520px;
	}

	.sticky-col8 {
		left: 580px;
	}

	.sticky-col9 {
		left: 780px;
	}

	.sticky-col10 {
		left: 880px;
	}

	// 修改表格基础样式
	table {
		border-collapse: separate;
		border-spacing: 0;
		width: 100%;
		table-layout: fixed;
		white-space: nowrap;
	}

	th,
	td {
		font-size: 12px;
		border: 1px solid #ccc;
		padding: 8px 4px;
		height: auto;
		min-height: 44px;
		box-sizing: border-box;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: normal;
	}

	// 特定列的样式
	td.sticky-col8,
	td.sticky-col10 {
		white-space: normal;
		word-wrap: break-word;
		word-break: break-all;
		height: auto;
		min-height: 44px;
		line-height: 1.4;
		padding: 8px 4px;
	}

	// 对应的表头样式
	th.sticky-col8,
	th.sticky-col10 {
		height: auto;
		min-height: 44px;
		white-space: normal;
		word-wrap: break-word;
		line-height: 1.4;
	}

	// 修改其他列保持单行显示
	td:not(.sticky-col8):not(.sticky-col10),
	th:not(.sticky-col8):not(.sticky-col10) {
		white-space: nowrap;
	}

	// 合计行样式
	.footer-table {
		width: 100%;
		margin-top: 10px;
		background-color: #fff;
		border: 1px solid #ccc;
		flex-shrink: 0;
		height: 60px;

		/* #ifdef H5 */
		.table-container2 {
			display: block;
			width: 100%;
			height: 100%;
			overflow-x: auto;
			overflow-y: hidden;

			&::-webkit-scrollbar {
				height: 8px;
				width: 0;
				display: block !important;
			}

			&::-webkit-scrollbar-thumb {
				background-color: #888;
				border-radius: 4px;
				display: block !important;
			}

			&::-webkit-scrollbar-track {
				background-color: #f1f1f1;
				display: block !important;
			}

			/* Firefox */
			scrollbar-width: thin;
			/* IE */
			-ms-overflow-style: auto;
		}

		/* #endif */

		/* #ifdef APP-PLUS */
		.table-container2 {
			display: block;
			width: 100%;
			height: 100%;
			overflow-x: auto;
			overflow-y: hidden;

			&::-webkit-scrollbar {
				width: 0 !important;
				height: 0 !important;
				display: none !important;
			}

			scrollbar-width: none !important;
			-ms-overflow-style: none !important;
		}

		/* #endif */

		td {
			background-color: #f8f9fa;
			font-weight: bold;
			border: 1px solid #ccc;
			height: 44px;
			padding: 8px 4px;
			font-size: 12px;

			&.sticky-col1,
			&.sticky-col2,
			&.sticky-col3,
			&.sticky-col4,
			&.sticky-col5,
			&.sticky-col6,
			&.sticky-col7,
			&.sticky-col8,
			&.sticky-col9,
			&.sticky-col10 {
				position: sticky;
				background-color: #f8f9fa !important;
				z-index: 10;
				border-right: none;
				box-shadow: inset -1px 0 0 #ccc, 1px 0 0 #ccc;
			}
		}

		table {
			width: 100%;
			min-width: 1500px; // 与主表格保持一致
			table-layout: fixed;
			border-collapse: separate;
			border-spacing: 0;
		}
	}

	/* 设置状态栏背景颜色 */
	.status-bar {
		height: var(--status-bar-height);
		/* 使用uni-app提供的变量 */
		width: 100%;
		background-color: #fff;
		/* 设置你想要的颜色 */
		position: fixed;
		top: 0;
		left: 0;
		z-index: 9999;
	}

	.back {
		width: 36px;
		height: 36px;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		left: 5%;
		top: 5%;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		cursor: pointer;
		z-index: 1;
	}

	// 隐藏所有滚动条
	// .table-scroll,
	// .table-container2,
	// ::v-deep .uni-scroll-view {
	//   &::-webkit-scrollbar {
	//     display: none !important;
	//     width: 0 !important;
	//     height: 0 !important;
	//   }
	//   scrollbar-width: none;
	//   -ms-overflow-style: none;
	//   overflow: -moz-scrollbars-none;
	// }

	// 删除底部重复的固定列样式（从第500行开始的样式）
	.sticky-col1,
	.sticky-col2,
	.sticky-col3,
	.sticky-col4,
	.sticky-col5,
	.sticky-col6,
	.sticky-col7,
	.sticky-col8,
	.sticky-col9,
	.sticky-col10 {
		&[align="center"] {
			// 删除 color: #333 设置
		}
	}


	.preview-image {
		width: 100%;
		height: 200px;
		object-fit: contain;
	}

	.picture-popup {
		.watermark {
			position: relative;
			transition: all 0.15s ease-in-out;

			&::before {
				content: "STELLA";
				position: absolute;
				top: 2px;
				left: 2px;
				color: black;
				font-size: 18px;
				font-weight: bold;
				text-shadow: 0 0 5px white;
				z-index: 1;
				pointer-events: none;
			}

			&:active {
				transform: scale(1.5);
			}

			.img {
				min-width: 100px;
				min-height: 100px;
				max-width: 100vw;
				max-height: 50vw;
				border-radius: 10px;
				box-shadow: 0 0 10px white;

				&:active {
					box-shadow: 0 0 1px white;
				}
			}
		}
	}


	.img {
		max-width: 50px;
		max-height: 50px;
		/* 	height: 50px;*/
		border-radius: 10px;
		box-shadow: 0 0 5px gray;
		transition: all 0.05s ease-in-out;
		cursor: pointer;

		&:active {
			box-shadow: 0 0 1px gray;
			transform: scale(0.97);
		}
	}

	.clickable-cell {
		cursor: pointer !important;
		color: #0066cc !important;
		text-decoration: underline !important;

		&:hover {
			color: #004499 !important;
		}
	}

	.detail-popup {
		background: #fff;
		border-radius: 8px;
		padding: 20px;
		width: 90vw;
		max-width: 1450px;
		height: 80vh; // 固定高度
		display: flex;
		flex-direction: column;

		.detail-title {
			font-size: 18px;
			font-weight: bold;
			margin-bottom: 20px;
			text-align: center;
			flex-shrink: 0; // 防止标题被压缩
		}

		.detail-table-container {
			flex: 1; // 占据剩余空间
			overflow-y: auto;
			margin-bottom: 20px;
			position: relative; // 为固定表头提供定位上下文

			&::-webkit-scrollbar {
				width: 8px;
			}

			&::-webkit-scrollbar-thumb {
				background-color: #888;
				border-radius: 4px;
			}

			&::-webkit-scrollbar-track {
				background-color: #f1f1f1;
			}
		}

		.detail-table {
			width: 100%;
			border-collapse: collapse;

			th,
			td {
				border: 1px solid #ddd;
				padding: 8px, 0px;
				font-size: 14px;
				min-width: 90px;
				text-align: center;

				// &:nth-child(1),
				// &:nth-child(3),
				// &:nth-child(4) {
				// 	min-width: 150px;
				// 	width: 150px;
				// }

				&:nth-child(2) {
					min-width: 180px;
					width: 180px;
				}
			}

			th {
				background-color: #5B9BD5;
				color: white;
				position: sticky;
				top: 0;
				z-index: 1;
			}

			tr:nth-child(even) {
				background-color: #f9f9f9;
			}

			tr:hover {
				background-color: #f5f5f5;
			}
		}

		.detail-footer {
			text-align: right;
			padding-top: 15px;
			border-top: 1px solid #eee;
			flex-shrink: 0;

			.close-btn {
				width: 100px;
				height: 50px;
				line-height: 50px;
				background-color: #5B9BD5;
				color: white;
				border: none;
				border-radius: 4px;
				cursor: pointer;
				font-size: 13px;

				&:hover {
					background-color: #4a8ac4;
				}

				&:active {
					transform: scale(0.98);
				}
			}
		}
	}

	.uni-scroll-view {
		position: relative;
		-webkit-overflow-scrolling: touch;
		width: 100%;
		/* height:100%; */
		max-height: inherit
	}
</style>