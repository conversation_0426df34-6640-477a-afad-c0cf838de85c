<template>
  <view class="email-sender">
    <!-- 邮件类型选择 -->
    <view class="email-type-section">
      <view class="section-header">
        <text class="section-title">邮件发送</text>
      </view>
      
      <view class="type-tabs">
        <view 
          v-for="(type, key) in emailTypes" 
          :key="key"
          :class="['type-tab', { active: activeType === key }]"
          @click="switchType(key)"
        >
          <text class="tab-text">{{ type.name }}</text>
        </view>
      </view>
    </view>

    <!-- 快速发送区域 -->
    <view class="quick-send-section">
      <view class="section-header">
        <text class="section-title">快速发送</text>
        <text class="section-desc">{{ emailTypes[activeType].desc }}</text>
      </view>
      
      <view class="quick-form">
        <view class="form-row">
          <view class="form-item">
            <text class="label">业务ID *</text>
            <input 
              v-model="quickForm.businessId" 
              placeholder="请输入业务ID" 
              class="input"
            />
          </view>
          
          <view v-if="needVersion" class="form-item">
            <text class="label">版本 *</text>
            <picker 
              :value="versionIndex" 
              :range="versionOptions" 
              @change="onVersionChange"
            >
              <view class="picker-text">
                {{ quickForm.version || '请选择版本' }}
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="label">发送用户</text>
            <input 
              v-model="quickForm.sendUser" 
              placeholder="发送用户" 
              class="input"
            />
          </view>
          
          <view class="form-actions">
            <button @click="quickSend" :disabled="!canQuickSend" class="btn btn-primary">
              快速发送
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 自定义发送区域 -->
    <view class="custom-send-section">
      <view class="section-header">
        <text class="section-title">自定义发送</text>
        <text class="section-desc">支持自定义模板变量的邮件发送</text>
      </view>
      
      <view class="custom-form">
        <view class="form-group">
          <text class="form-label">邮件类型 *</text>
          <picker 
            :value="customTypeIndex" 
            :range="customTypeOptions" 
            @change="onCustomTypeChange"
          >
            <view class="picker-text">
              {{ getCustomTypeName(customForm.emailType) }}
            </view>
          </picker>
        </view>
        
        <view class="form-group">
          <text class="form-label">业务ID *</text>
          <input 
            v-model="customForm.businessId" 
            placeholder="请输入业务ID" 
            class="form-input"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">发送用户</text>
          <input 
            v-model="customForm.sendUser" 
            placeholder="发送用户" 
            class="form-input"
          />
        </view>
        
        <!-- 模板变量 -->
        <view class="template-variables">
          <text class="form-label">模板变量</text>
          <view class="variables-grid">
            <view 
              v-for="(variable, key) in templateVariables" 
              :key="key"
              class="variable-item"
            >
              <text class="variable-label">{{ variable.label }}:</text>
              <input 
                v-model="customForm.templateVariables[key]" 
                :placeholder="variable.placeholder"
                class="variable-input"
              />
            </view>
          </view>
          
          <view class="add-variable">
            <view class="add-form">
              <input 
                v-model="newVariable.key" 
                placeholder="变量名" 
                class="add-input"
              />
              <input 
                v-model="newVariable.value" 
                placeholder="变量值" 
                class="add-input"
              />
              <button @click="addVariable" class="btn btn-add">添加</button>
            </view>
          </view>
        </view>
        
        <view class="form-actions">
          <button @click="customSend" :disabled="!canCustomSend" class="btn btn-primary">
            发送邮件
          </button>
          <button @click="resetCustomForm" class="btn btn-secondary">
            重置表单
          </button>
        </view>
      </view>
    </view>

    <!-- 批量发送区域 -->
    <view class="batch-send-section">
      <view class="section-header">
        <text class="section-title">批量发送</text>
        <text class="section-desc">支持批量发送多个邮件</text>
      </view>
      
      <view class="batch-form">
        <view class="batch-list">
          <view 
            v-for="(item, index) in batchList" 
            :key="index"
            class="batch-item"
          >
            <view class="item-content">
              <text class="item-type">{{ getEmailTypeName(item.emailType) }}</text>
              <text class="item-business">{{ item.businessId }}</text>
              <text class="item-user">{{ item.sendUser }}</text>
            </view>
            <button @click="removeBatchItem(index)" class="btn btn-remove">删除</button>
          </view>
        </view>
        
        <view class="add-batch">
          <view class="add-form">
            <picker 
              :value="batchTypeIndex" 
              :range="customTypeOptions" 
              @change="onBatchTypeChange"
            >
              <view class="picker-text">
                {{ getCustomTypeName(batchForm.emailType) }}
              </view>
            </picker>
            
            <input 
              v-model="batchForm.businessId" 
              placeholder="业务ID" 
              class="add-input"
            />
            
            <input 
              v-model="batchForm.sendUser" 
              placeholder="发送用户" 
              class="add-input"
            />
            
            <button @click="addBatchItem" class="btn btn-add">添加到列表</button>
          </view>
        </view>
        
        <view class="batch-actions">
          <button @click="batchSend" :disabled="batchList.length === 0" class="btn btn-primary">
            批量发送 ({{ batchList.length }})
          </button>
          <button @click="clearBatchList" class="btn btn-secondary">
            清空列表
          </button>
        </view>
      </view>
    </view>

    <!-- 发送结果 -->
    <view v-if="sendResults.length > 0" class="results-section">
      <view class="section-header">
        <text class="section-title">发送结果</text>
      </view>
      
      <view class="results-list">
        <view 
          v-for="(result, index) in sendResults" 
          :key="index"
          :class="['result-item', result.success ? 'success' : 'error']"
        >
          <view class="result-info">
            <text class="result-type">{{ result.type }}</text>
            <text class="result-business">{{ result.businessId }}</text>
            <text class="result-time">{{ result.time }}</text>
          </view>
          <view class="result-status">
            <text :class="['status-text', result.success ? 'success' : 'error']">
              {{ result.success ? '发送成功' : '发送失败' }}
            </text>
            <text v-if="!result.success" class="error-message">{{ result.message }}</text>
          </view>
        </view>
      </view>
      
      <view class="results-actions">
        <button @click="clearResults" class="btn btn-secondary">清空结果</button>
      </view>
    </view>

    <!-- 加载提示 -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-content">
        <text class="loading-text">{{ loadingText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { 
  sendQuoteEmail,
  sendEstimateEmail, 
  sendPVersionEmail,
  sendUpdateNotifyEmail,
  sendCustomEmail,
  batchSendEmails,
  EMAIL_TYPES,
  EMAIL_TYPE_NAMES
} from '@/api/email.js';

export default {
  name: 'EmailSender',
  data() {
    return {
      // 邮件类型配置
      emailTypes: {
        [EMAIL_TYPES.QUOTE_V1]: {
          name: '报价邮件',
          desc: '发送报价相关邮件，支持V1-V5版本',
          needVersion: true,
          versions: ['V1', 'V2', 'V3', 'V4', 'V5']
        },
        [EMAIL_TYPES.ESTIMATE_Z]: {
          name: '预估邮件',
          desc: '发送预估相关邮件，支持Z版和ZZ版',
          needVersion: true,
          versions: ['Z', 'ZZ']
        },
        [EMAIL_TYPES.P_VERSION]: {
          name: 'P版邮件',
          desc: '发送P版邮件',
          needVersion: false
        },
        [EMAIL_TYPES.UPDATE_NOTIFY]: {
          name: '更新通知',
          desc: '发送版本更新通知邮件',
          needVersion: false
        }
      },
      
      // 当前选中的邮件类型
      activeType: EMAIL_TYPES.QUOTE_V1,
      
      // 快速发送表单
      quickForm: {
        businessId: '',
        version: '',
        sendUser: 'ADMIN'
      },
      
      // 自定义发送表单
      customForm: {
        emailType: EMAIL_TYPES.QUOTE_V1,
        businessId: '',
        sendUser: 'ADMIN',
        templateVariables: {}
      },
      
      // 批量发送
      batchForm: {
        emailType: EMAIL_TYPES.QUOTE_V1,
        businessId: '',
        sendUser: 'ADMIN'
      },
      batchList: [],
      
      // 模板变量
      templateVariables: {
        businessId: { label: '业务ID', placeholder: '请输入业务ID' },
        userName: { label: '用户名', placeholder: '请输入用户名' },
        deptName: { label: '部门名称', placeholder: '请输入部门名称' },
        currentDate: { label: '当前日期', placeholder: '请输入日期' }
      },
      
      // 新增变量
      newVariable: {
        key: '',
        value: ''
      },
      
      // 发送结果
      sendResults: [],
      
      // 加载状态
      loading: false,
      loadingText: '发送中...'
    };
  },
  
  computed: {
    /**
     * 是否需要版本选择
     */
    needVersion() {
      return this.emailTypes[this.activeType]?.needVersion || false;
    },
    
    /**
     * 版本选项
     */
    versionOptions() {
      return this.emailTypes[this.activeType]?.versions || [];
    },
    
    /**
     * 版本索引
     */
    versionIndex() {
      return this.versionOptions.indexOf(this.quickForm.version);
    },
    
    /**
     * 自定义邮件类型选项
     */
    customTypeOptions() {
      return Object.values(EMAIL_TYPE_NAMES);
    },
    
    /**
     * 自定义邮件类型索引
     */
    customTypeIndex() {
      const types = Object.keys(EMAIL_TYPE_NAMES);
      return types.indexOf(this.customForm.emailType);
    },
    
    /**
     * 批量邮件类型索引
     */
    batchTypeIndex() {
      const types = Object.keys(EMAIL_TYPE_NAMES);
      return types.indexOf(this.batchForm.emailType);
    },
    
    /**
     * 是否可以快速发送
     */
    canQuickSend() {
      const hasBusinessId = !!this.quickForm.businessId;
      const hasVersion = !this.needVersion || !!this.quickForm.version;
      return hasBusinessId && hasVersion;
    },
    
    /**
     * 是否可以自定义发送
     */
    canCustomSend() {
      return !!this.customForm.businessId && !!this.customForm.emailType;
    }
  },
  
  mounted() {
    // 初始化模板变量
    this.initTemplateVariables();
  },
  
  methods: {
    /**
     * 切换邮件类型
     */
    switchType(type) {
      this.activeType = type;
      this.quickForm.version = '';
    },
    
    /**
     * 版本选择变化
     */
    onVersionChange(event) {
      const index = event.detail.value;
      this.quickForm.version = this.versionOptions[index];
    },
    
    /**
     * 自定义邮件类型变化
     */
    onCustomTypeChange(event) {
      const index = event.detail.value;
      const types = Object.keys(EMAIL_TYPE_NAMES);
      this.customForm.emailType = types[index];
    },
    
    /**
     * 批量邮件类型变化
     */
    onBatchTypeChange(event) {
      const index = event.detail.value;
      const types = Object.keys(EMAIL_TYPE_NAMES);
      this.batchForm.emailType = types[index];
    },
    
    /**
     * 快速发送
     */
    async quickSend() {
      if (!this.canQuickSend) {
        uni.showToast({
          title: '请填写必填项',
          icon: 'none'
        });
        return;
      }
      
      try {
        this.loading = true;
        this.loadingText = '发送中...';
        
        let response;
        const { businessId, version, sendUser } = this.quickForm;
        
        switch (this.activeType) {
          case EMAIL_TYPES.QUOTE_V1:
          case EMAIL_TYPES.QUOTE_V2:
          case EMAIL_TYPES.QUOTE_V3:
          case EMAIL_TYPES.QUOTE_V4:
          case EMAIL_TYPES.QUOTE_V5:
            response = await sendQuoteEmail(version, businessId, sendUser);
            break;
          case EMAIL_TYPES.ESTIMATE_Z:
          case EMAIL_TYPES.ESTIMATE_ZZ:
            response = await sendEstimateEmail(version, businessId, sendUser);
            break;
          case EMAIL_TYPES.P_VERSION:
            response = await sendPVersionEmail(businessId, sendUser);
            break;
          case EMAIL_TYPES.UPDATE_NOTIFY:
            response = await sendUpdateNotifyEmail(businessId, sendUser);
            break;
        }
        
        this.addSendResult({
          type: this.emailTypes[this.activeType].name,
          businessId,
          success: response.code === 1,
          message: response.message,
          time: new Date().toLocaleString()
        });
        
        if (response.code === 1) {
          uni.showToast({
            title: '发送成功',
            icon: 'success'
          });
          this.resetQuickForm();
        } else {
          uni.showToast({
            title: response.message || '发送失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('快速发送失败:', error);
        this.addSendResult({
          type: this.emailTypes[this.activeType].name,
          businessId: this.quickForm.businessId,
          success: false,
          message: '网络错误',
          time: new Date().toLocaleString()
        });
        uni.showToast({
          title: '发送失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 自定义发送
     */
    async customSend() {
      if (!this.canCustomSend) {
        uni.showToast({
          title: '请填写必填项',
          icon: 'none'
        });
        return;
      }
      
      try {
        this.loading = true;
        this.loadingText = '发送中...';
        
        const response = await sendCustomEmail(this.customForm);
        
        this.addSendResult({
          type: this.getCustomTypeName(this.customForm.emailType),
          businessId: this.customForm.businessId,
          success: response.code === 1,
          message: response.message,
          time: new Date().toLocaleString()
        });
        
        if (response.code === 1) {
          uni.showToast({
            title: '发送成功',
            icon: 'success'
          });
          this.resetCustomForm();
        } else {
          uni.showToast({
            title: response.message || '发送失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('自定义发送失败:', error);
        this.addSendResult({
          type: this.getCustomTypeName(this.customForm.emailType),
          businessId: this.customForm.businessId,
          success: false,
          message: '网络错误',
          time: new Date().toLocaleString()
        });
        uni.showToast({
          title: '发送失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 批量发送
     */
    async batchSend() {
      if (this.batchList.length === 0) {
        uni.showToast({
          title: '请添加要发送的邮件',
          icon: 'none'
        });
        return;
      }
      
      try {
        this.loading = true;
        this.loadingText = `批量发送中... (0/${this.batchList.length})`;
        
        const response = await batchSendEmails(this.batchList);
        
        if (response.code === 1) {
          uni.showToast({
            title: '批量发送成功',
            icon: 'success'
          });
          
          // 添加批量发送结果
          this.batchList.forEach(item => {
            this.addSendResult({
              type: this.getEmailTypeName(item.emailType),
              businessId: item.businessId,
              success: true,
              message: '批量发送成功',
              time: new Date().toLocaleString()
            });
          });
          
          this.clearBatchList();
        } else {
          uni.showToast({
            title: response.message || '批量发送失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('批量发送失败:', error);
        uni.showToast({
          title: '批量发送失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 添加批量发送项
     */
    addBatchItem() {
      if (!this.batchForm.businessId) {
        uni.showToast({
          title: '请输入业务ID',
          icon: 'none'
        });
        return;
      }
      
      this.batchList.push({ ...this.batchForm });
      this.batchForm.businessId = '';
    },
    
    /**
     * 删除批量发送项
     */
    removeBatchItem(index) {
      this.batchList.splice(index, 1);
    },
    
    /**
     * 清空批量列表
     */
    clearBatchList() {
      this.batchList = [];
    },
    
    /**
     * 添加模板变量
     */
    addVariable() {
      if (!this.newVariable.key || !this.newVariable.value) {
        uni.showToast({
          title: '请输入变量名和变量值',
          icon: 'none'
        });
        return;
      }
      
      this.customForm.templateVariables[this.newVariable.key] = this.newVariable.value;
      this.newVariable = { key: '', value: '' };
    },
    
    /**
     * 初始化模板变量
     */
    initTemplateVariables() {
      this.customForm.templateVariables = {
        businessId: this.customForm.businessId,
        userName: '',
        deptName: '',
        currentDate: new Date().toLocaleDateString()
      };
    },
    
    /**
     * 重置快速发送表单
     */
    resetQuickForm() {
      this.quickForm = {
        businessId: '',
        version: '',
        sendUser: 'ADMIN'
      };
    },
    
    /**
     * 重置自定义发送表单
     */
    resetCustomForm() {
      this.customForm = {
        emailType: EMAIL_TYPES.QUOTE_V1,
        businessId: '',
        sendUser: 'ADMIN',
        templateVariables: {}
      };
      this.initTemplateVariables();
    },
    
    /**
     * 添加发送结果
     */
    addSendResult(result) {
      this.sendResults.unshift(result);
      // 只保留最近20条结果
      if (this.sendResults.length > 20) {
        this.sendResults = this.sendResults.slice(0, 20);
      }
    },
    
    /**
     * 清空发送结果
     */
    clearResults() {
      this.sendResults = [];
    },
    
    /**
     * 获取邮件类型名称
     */
    getEmailTypeName(type) {
      return EMAIL_TYPE_NAMES[type] || type;
    },
    
    /**
     * 获取自定义邮件类型名称
     */
    getCustomTypeName(type) {
      return EMAIL_TYPE_NAMES[type] || '请选择邮件类型';
    }
  }
};
</script>

<style scoped>
.email-sender {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 通用样式 */
.section-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #007bff;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.section-desc {
  font-size: 14px;
  color: #666;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-primary:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-add {
  background-color: #28a745;
  color: white;
  padding: 6px 12px;
  font-size: 12px;
}

.btn-remove {
  background-color: #dc3545;
  color: white;
  padding: 4px 8px;
  font-size: 12px;
}

/* 邮件类型选择 */
.email-type-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.type-tabs {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.type-tab {
  padding: 10px 20px;
  border: 2px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.type-tab.active {
  border-color: #007bff;
  background-color: #007bff;
}

.type-tab.active .tab-text {
  color: white;
}

.tab-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 快速发送区域 */
.quick-send-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quick-form .form-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  font-weight: 500;
}

.input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 200px;
}

.picker-text {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  min-width: 120px;
  text-align: center;
  color: #333;
}

.form-actions {
  display: flex;
  gap: 10px;
}

/* 自定义发送区域 */
.custom-send-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.custom-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.form-input {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

/* 模板变量 */
.template-variables {
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 15px;
  background-color: #f9f9f9;
}

.variables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.variable-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.variable-label {
  font-size: 13px;
  color: #555;
  min-width: 80px;
}

.variable-input {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
}

.add-variable {
  border-top: 1px solid #ddd;
  padding-top: 15px;
}

.add-form {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.add-input {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  width: 150px;
}

/* 批量发送区域 */
.batch-send-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.batch-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 15px;
}

.batch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
}

.batch-item:last-child {
  border-bottom: none;
}

.item-content {
  display: flex;
  gap: 20px;
  align-items: center;
}

.item-type {
  font-size: 13px;
  color: #007bff;
  font-weight: 500;
  min-width: 100px;
}

.item-business {
  font-size: 13px;
  color: #333;
  min-width: 120px;
}

.item-user {
  font-size: 13px;
  color: #666;
  min-width: 80px;
}

.batch-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 15px;
}

/* 发送结果 */
.results-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.results-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 15px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item.success {
  background-color: #f8fff8;
  border-left: 4px solid #28a745;
}

.result-item.error {
  background-color: #fff8f8;
  border-left: 4px solid #dc3545;
}

.result-info {
  display: flex;
  gap: 15px;
  align-items: center;
}

.result-type {
  font-size: 13px;
  color: #007bff;
  font-weight: 500;
  min-width: 100px;
}

.result-business {
  font-size: 13px;
  color: #333;
  min-width: 120px;
}

.result-time {
  font-size: 12px;
  color: #999;
  min-width: 140px;
}

.result-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.status-text.success {
  color: #28a745;
  font-weight: 500;
}

.status-text.error {
  color: #dc3545;
  font-weight: 500;
}

.error-message {
  font-size: 12px;
  color: #dc3545;
  margin-top: 2px;
}

.results-actions {
  display: flex;
  justify-content: center;
}

/* 加载提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-content {
  background: white;
  padding: 20px 40px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.loading-text {
  font-size: 16px;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .email-sender {
    padding: 10px;
  }

  .quick-form .form-row {
    flex-direction: column;
    align-items: stretch;
  }

  .form-item {
    flex-direction: column;
    align-items: stretch;
  }

  .input, .picker-text {
    width: 100%;
  }

  .type-tabs {
    flex-direction: column;
  }

  .variables-grid {
    grid-template-columns: 1fr;
  }

  .add-form {
    flex-direction: column;
  }

  .add-input {
    width: 100%;
  }

  .item-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .result-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .result-status {
    align-items: flex-start;
  }
}
</style>
