import request from '@/utils/request';

// 获取组别列表
export function getGroupList(deptNo, date) {
  return request({
    url: '/workorder/groups',
    method: 'get',
    params: { deptNo, date }
  });
}

// 获取工单列表
export function getOrderList(madeDept, date) {
  return request({
    url: '/workorder/orders',
    method: 'get',
    params: { madeDept, date }
  });
}

// 更新工单选择状态
export function updateOrderSelection(params) {
  return request({
    url: '/workorder/selection',
    method: 'post',
    data: params
  });
}

// 更新组别目标PPH
export function updateGroupTarget(params) {
  return request({
    url: '/workorder/target',
    method: 'post',
    data: params
  });
} 