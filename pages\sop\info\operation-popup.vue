<!-- 制程弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap, proSeqMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 提示弹框
const tipPopup = ref()

// 制程
const operation = inject('operation')
// 制程列表
const operationList = ref([])
// 制程类型列表
const operationTypeList = ref(['6', '4', '5', '8', '1'])
// 详细制程
const proSeq = inject('proSeq')
// 制程弹框
const operationPopup = ref()

// 获取制程列表
async function getOperationList() {
  await uni.request({
    url: urlPrefix + '/sop/getOperationList',
    method: 'GET'
  }).then(res => {
    if (res.data.code) {
      operationList.value = res.data.data ? res.data.data : []
    } else {
      operationList.value = []
      tipPopup.value.showTipPopup('warn', '暂无制程列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示制程弹框
async function showOperationPopup() {
  if (operationList.value.length === 0) {
    uni.showLoading({
      title: '加载中...',
      mask: true
    })
    
    await getOperationList()
    
    uni.hideLoading()
    
    if (operationList.value.length === 0) {
      return
    }
  }
  operationPopup.value.open()
}

// 选择制程
function selectOperation(param) {
  if (proSeq.value !== param.proSeq) {
    proSeq.value = param.proSeq
    operation.value = param.operation
    uni.setStorageSync('sop-operation', operation.value)
    uni.setStorageSync('sop-proSeq', proSeq.value)
  }
  operationPopup.value.close()
}

defineExpose({
  getOperationList,
  showOperationPopup
})
</script>

<template>
  <uni-popup
    ref="operationPopup"
    type="center"
    class="operation-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择制程
      </view>
      
      <view class="operation-list flex-row-start-center">
        <template
          v-for="(i, j) in operationTypeList"
          :key="j"
        >
          <view class="operation-type flex-row-start-center">
            {{ operationMap.get(i) ? operationMap.get(i) : '未知' }}
          </view>
          
          <view
            v-for="(item, index) in operationList"
            :key="index"
            v-show="item.operation === i"
            @click="selectOperation(item)"
            class="operation button flex-row-center"
            :style="{
              color: item.proSeq === proSeq ? 'royalblue' : 'darkslateblue'
            }"
          >
            {{ proSeqMap.get(item.proSeq) }}
            <!-- {{ item.process.replace(/^[0-9]*\.?[0-9]*/g, '') }} -->
          </view>
        </template>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.operation-popup {
  .container {
    width: 60vw;
    max-height: 80vh;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .operation-list {
      max-height: calc(80vh - 50px);
      overflow: auto;
      flex-wrap: wrap;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .operation-type {
        width: 100%;
        height: 40px;
        margin-left: 15px;
        font-size: 22px;
        font-weight: bold;
      }
      
      .operation {
        width: calc(25% - 30px);
        margin: 15px;
        height: 60px;
        color: darkslateblue;
        font-size: 20px;
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .flex-row-start-center {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
</style>