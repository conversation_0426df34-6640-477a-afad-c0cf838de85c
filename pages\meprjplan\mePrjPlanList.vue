<template>
	<view class="container">
		<view class="back">
			<uni-icons @click="back" type="back" size="36"></uni-icons>
		</view>

		<view class="search">
			<view style="width: 2%;margin-left: 5%;">
				<!-- <uni-data-select class="searchSelect" placeholder="部门" :clear="false" :localdata="depts"
                                 :value="selectWt"></uni-data-select> -->
			</view>
			<button style="line-height: 6.5vh;font-size: 16px;width: 15%;" class="search-brand"
					@click="factoryPopup.open()">
				<text v-if="selectFactory!='所有廠別'">廠別-</text>{{ selectFactory }}
			</button>

			<button style="line-height: 6.5vh;font-size: 16px;width: 15%;" class="search-brand"
				@click="deptPopup.open()">
				{{ dept }}
			</button>
			<button style="line-height: 6.5vh;width: 15%" class="search-brand" @click="brandPopup.open()">
				{{ brand }}
			</button>

			<!-- <button class="search-type" @click="searchType = !searchType"> -->
			<!--<button style="line-height: 6.5vh;" class="search-type">
				{{ searchType ? "型体" : "楦头" }}
			</button>-->

			<input ref="searchInput" v-show="searchType" v-model="inputModel" class="search-input" type="text"
				placeholder="请输入型体编号" @focus="modelListShow = true" @blur="hideModelList">

			<input v-show="!searchType" v-model="shoe_last" type="text" placeholder="请输入楦头编号"
				class="search-input-shoe-last" />

			<view v-show="inputModel.length > 0 && searchType" @click="inputModel = '',search('')" class="search-clear">
				<uni-icons type="clear" size="34"></uni-icons>
			</view>

			<view v-show="shoe_last.length > 0 && !searchType" @click="shoe_last = ''" class="search-clear-shoe-last">
				<uni-icons type="clear" size="34"></uni-icons>
			</view>

			<view v-show="!searchType" @click="getShoeLastModelList" class="search-icon-shoe-last"
				:style="{ marginLeft: shoe_last.length > 0 ? '11px' : '0' }">
				<uni-icons type="search" size="48"></uni-icons>
			</view>

			<view class="inpBr"
				:style="{ marginLeft: (inputModel.length > 0) ? '15px' : '4px', width: '10%' }">
				<button type="success" @click="addInfo()"
					style="background-color: #18bc37; color: white; font-weight: bold;height: 6.3vh;line-height: 6.3vh">新增
				</button>
			</view>
			<view class="inpBr" style="width: 10%;margin-right: 10rpx;">
				<button type="success" @click="emptyData"
					style="background-color: #e43d33; color: white; font-weight: bold;height: 6.3vh;line-height: 6.3vh">删除
				</button>
			</view>

			<transition name="list">
				<view v-show="modelListShow" class="search-list">
					<view class="search-box">
						<view v-for="(item, index) in searchModel" :key="index" @click="search(item.model_no)"
							class="search-item" :style="{ border: searchModel.length === 1 ? 'none' : '' }">
							{{ item.model_no }}
						</view>
						<view v-show="searchModel.length === 0" class="search-item" style="border: none;">
							暂无该型体数据
						</view>
					</view>
				</view>
			</transition>

			<transition name="list">
				<view v-show="shoeLastModelListShow" class="search-list-shoe-last">
					<view class="search-box-shoe-last">
						<view v-for="(item, index) in searchShoeLastModel" :key="index" @click="search(item.model_no)"
							class="search-item-shoe-last"
							:style="{ border: shoeLastModelList.length === 1 ? 'none' : '' }">
							{{ item.model_no }}
						</view>
						<view v-show="shoeLastModelList.length === 0" class="search-item-shoe-last"
							style="border: none;">
							暂无型体数据
						</view>
					</view>
				</view>
			</transition>
		</view>

		<view class="dept-popup">
			<uni-popup ref="deptPopup" type="center">
				<view class="dept-box">
					<view v-for="(item, index) in deptList" :key="index" v-show="item.data.length > 0"
						class="dept-part">
						<view class="dept-initial">{{ item.letter }}</view>
						<view v-for="(dept, index) in item.data" :key="index" @click="selectDept(dept)" class="dept">
							{{ dept }}
						</view>
					</view>
				</view>
			</uni-popup>
		</view>

		<view class="brand-popup">
			<uni-popup ref="brandPopup" type="center">
				<view class="brand-box">
					<view v-for="(item, index) in brandList" :key="index" v-show="item.data.length > 0"
						class="brand-part">
						<view class="brand-initial">{{ item.letter }}</view>
						<view v-for="(brand, index) in item.data" :key="index" @click="select(brand)" class="brand">
							{{ brand }}
						</view>
					</view>
				</view>
			</uni-popup>
		</view>

		<view class="brand-popup">
			<uni-popup ref="factoryPopup" type="center">
				<view class="brand-box">
					<view class="brand-part">
						<view class="brand-initial">廠别</view>
						<view v-for="(item, index) in factoryList" :key="index" @click="functionFactory(item)" class="brand">
							{{ item }}
						</view>
					</view>
				</view>
			</uni-popup>
		</view>

		<view class="table-container">
			<table>
				<!-- 表头行 -->

					<tr style="background-color: #fdf6e3;">
						<th style="width: 1vw;">
						</th>
						<th style="width: 10vw;" align="center">客户</th>
						<th style="width: 10vw;" align="center">部门</th>
						<th style="width: 15vw;" align="center">型体编号（点击复制）</th>
						<th style="width: 20vw;" align="center">楦头编号</th>
						<th style="width: 5vw;" align="center">项次数量</th>
						<th style="width: 10vw;" align="center">日期</th>
						<th style="width: 5vw;" align="center">创建人</th>
						<th style="width: 5vw;" align="center">厂别</th>
						<th style="width: 7.5vw;" align="center">编辑</th>
						<th style="width: 7.5vw;" align="center">预览</th>
						<!-- <th style="width: 7.5vw;" align="center">下载</th> -->
						<th v-if="isAuditUser" style="width: 7.5vw;" align="center">审核</th>
						<th v-if="systemInfo == 'windows'" style="width: 7.5vw;" align="center">下载Excel</th>
						<th v-if="systemInfo == 'windows'" style="width: 7.5vw;" align="center">下载品牌 Excel</th>
					</tr>

				<tr v-for="(item, index) in dataList" :checked="item.checked" :key="item.id" class="datalist-tr">
					<td style="width: 1vw;">
						<checkbox @click="selectItemStep(item)" :checked="item.isChecked" color="white"
							activeBackgroundColor="violet" borderColor="black" activeBorderColor="black"
							:disabled="insName != item.create_by"></checkbox>
					</td>
					<td align="center">{{ item.brand }}</td>
					<td align="center">{{ item.dept_name }}</td>
					<td style="text-decoration: underline;color: skyblue" align="center"
						@click="copyData(item.brand,item.dept_name,item.model_no,item.factory,item.id)">
						{{ item.model_no }}
					</td>
					<td align="center">{{ item.shoe_last }}</td>
					<td align="center">{{ item.item_num }}</td>
					<td align="center">{{ item.link_date }}</td>
					<td align="center">{{ item.create_by }}</td>
					<td align="center">{{ item.factory }}</td>
					<td align="center">
						<uv-button v-if="(insName == item.create_by && item.audit_flag !== 1) || isAuditUser" type="primary" text="编辑"
							@click="() => edit(item.brand,item.model_no,item.shoe_last, item.min_item, item.dept_name,item.create_by,item.id,item.factory)"></uv-button>
					</td>
					<td align="center">
						<uv-button type="primary" text="预览"
							@click="() => viewInfo(item.brand,item.model_no,item.shoe_last, item.min_item, item.dept_name,item.id,item.factory)"></uv-button>
					</td>
					<!-- <td align="center">
						<uv-button type="primary" text="下載PDF"
							@click="() => download(item.brand,item.model_no, item.item_num, item.dept_name,item.id)"></uv-button>
					</td> -->
					<td align="center" v-if="isAuditUser">
					    <uv-button v-if="item.audit_flag !== 1" type="primary" text="审核"
					               @click="() => audit(item.id)"></uv-button>
								   
						<uv-button v-if="item.audit_flag === 1" type="primary" text="反审核"
								  @click="() => revAudit(item.id)"></uv-button>
					</td>
                    <td align="center" v-if="systemInfo == 'windows' && (insName == item.create_by || isAuditUser)">
                        <uv-button type="primary" text="下載Excel"
                                   @click="() => downloadExcel(item.brand,item.model_no,item.shoe_last,item.dept_name,item.item_num,1,item.id)"></uv-button>
                    </td>
					<td align="center" v-if="systemInfo == 'windows' && (insName == item.create_by || isAuditUser)">
					    <uv-button v-if="'UA' == item.brand"  type="primary" text="UA Excel"
					               @click="() => downloadExcel(item.brand,item.model_no,item.shoe_last,item.dept_name,item.item_num,2,item.id)"></uv-button>
					</td>
				</tr>
			</table>
		</view>


		<view class="left-bottom">
			<uni-pagination show-icon="true" :total="pageCount" :current="firstPageNo" :pageSize="firstPageSize"
				@change="firstChange"></uni-pagination>
		</view>

		<view class="tip-popup">
			<uni-popup ref="tipPopup" type="message">
				<uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
			</uni-popup>
		</view>
	</view>

</template>

<script setup>
import {onMounted, ref, watch} from 'vue'
import {onPullDownRefresh, onShow} from '@dcloudio/uni-app'
import urlPrefix from '@/pages/common/urlPrefix.js'
// 同步获取系统信息
const systemInfo = uni.getSystemInfoSync().platform;

//第几页
	const firstPageNo = ref(1)
	const firstPageSize = ref(10)
	const pageCount = ref(0)

	//表单数据
	const searchModel = ref([])
	const shoeLastModelList = ref([])
	const dataList = ref([])
	const searchShoeLastModel = ref([])
	const shoeLastModelListShow = ref(false)
	const searchInput = ref()
	const modelList = ref([])

	//消息提示
	const tipPopup = ref()
	const tipType = ref('')
	const tipMessage = ref('')

	//扫描状态

	//详情弹窗
	const modelData = ref()
	const modelPopup = ref()
	const selectWt = ref()

	//参数
	const model_no = ref('')
	const inputModel = ref('')
	const itemNo = ref('')
	const id = ref('')
	const shoe_last = ref('')
	const brand = ref('所有品牌')
	const brandList = ref([])

	const dept = ref('所有部門')
	const deptList = ref([{
			'data': ['所有部門'],
			'letter': '所有部門'
		},
		{
			'data': ['底部SOP-成型', '底部SOP-中底', '底部SOP-中底皮', '底部SOP-大底', '底部SOP-单底', '底部SOP-包粘'],
			'letter': '底部SOP'
		},
		{
			'data': ['鞋面SOP-鞋面'],
			'letter': '鞋面SOP'
		}
	])

	const selectFactory = ref(uni.getStorageSync("currentFactory"))
    const factoryList = ref([])


	//下拉选择显示
	const modelListShow = ref(false)
	const searchType = ref(true)
	const brandPopup = ref()
	const deptPopup = ref()
	const factoryPopup = ref()

	const insUs = uni.getStorageSync("loUserNo")
	const insName = ref(uni.getStorageSync("loginUserName"))
	const iuser = ref("")

	const depts = ref([{
			value: 1,
			text: "成型"
		},
		{
			value: 2,
			text: "半成品"
		},
		{
			value: 3,
			text: "鞋面"
		}
	])

	function  functionFactory(item) {
		selectFactory.value = item
        getData('');
        factoryPopup.value.close()
    }

	function hideModelList() {
		setTimeout(() => {
			modelListShow.value = false;
		}, 200); // 延
	}

	function copyData(brand, dept, model,factory) {
		console.log(dept + model)
		let info = {
			"brand": brand,
			"dept": dept,
			"model": model,
			"factory":factory

		}

		console.log(info);
		uni.navigateTo({
			url: `/pages/meprjplan/copyModel?info=${JSON.stringify(info)}`,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}

	// 跳转至添加界面
	function addInfo() {
		const currentFactory = uni.getStorageSync("currentFactory");
		if (currentFactory !== 'FS') {
			showTip('warn', '只有FS厂别可以新增！')
			return
		}
		if (model_no.value.length === 0) {
			showTip('warn', '请选择型体！')
			return
		}

		let info = {
			model_no: model_no.value,
			brand: brand.value,
			shoeLast: shoe_last.value,
			item_no: itemNo.value,
			type: 'add'
		}

		uni.navigateTo({
			url: `/pages/meprjplan/add-info?info=${JSON.stringify(info)}`,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}

	function viewInfo(brand, modelNo,shoeLast, itemNum, dept_name,id,factory) {
		if (itemNum == null || itemNum == '') {
			showTip('error', '当前数据没有明细，请先新增！');
			return;
		}
		let info = {
		    id:id,
			model_no: modelNo,
			brand: brand,
			shoeLast: shoeLast,
			item_no: itemNum,
			dept: dept_name,
			type: 'view',
			factory:factory
		}

		let viewUrl;
		if (dept_name.indexOf("底部SOP") > -1) {
			viewUrl = `/pages/meprjplan/view-info-db?info=${JSON.stringify(info)}`;
		} else if (dept_name.indexOf("鞋面SOP") > -1) {
			if(factory === 'FS'){
				viewUrl = `/pages/meprjplan/view-info-mb-sf?info=${JSON.stringify(info)}`;
			}else{
				viewUrl = `/pages/meprjplan/view-info-mb?info=${JSON.stringify(info)}`;
			}
		}

		uni.navigateTo({
			url: viewUrl,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}


    function download(brand, modelNo, itemNum, dept_name,id) {
        if (itemNum == null || itemNum == '') {
            showTip('error', '当前数据没有明细，请先新增！');
            return;
        }
        let type = 1;
        console.log("dept_name：" + dept_name)
        if (dept_name.indexOf("底部SOP") > -1) {
            // viewUrl = `/pages/meprjplan/view-info-db?info=${JSON.stringify(info)}`;
            type = 1
        } else if (dept_name.indexOf("鞋面SOP") > -1) {
            // viewUrl = `/pages/meprjplan/view-info-mb?info=${JSON.stringify(info)}`;
            type = 2;
        }
        uni.showLoading({
            title: '加载中',
            mask: true // 设置遮罩层
        });
        uni.request({
            url: urlPrefix + "/pdf",
            method: "GET",
            data: {
                brand: brand,
                modelNo: modelNo,
                deptName: dept_name,
                type: type,
				id:id
            },
            responseType: 'arraybuffer',
            timeout: 600000,
        }).then(res => {
            const blob = new Blob([res.data], {type: 'application/pdf'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = dept_name + '-' + modelNo + '-' + brand + ".pdf";
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            uni.hideLoading();
        }).catch(err => {
            // 隐藏加载提示
            uni.hideLoading();
            console.log(err)
        })
    }

function downloadExcel(brandTemp, model_noTemp, shoe_lastTemp, dept_nameTemp,itemNum,downloadType,id) {
	if (itemNum == null || itemNum == '') {
	    showTip('error', '当前数据没有明细，请先新增！');
	    return;
	}
	let type = 1;
	console.log("dept_name：" + dept_nameTemp)
	if (dept_nameTemp.indexOf("底部SOP") > -1) {
	    type = 1
	} else if (dept_nameTemp.indexOf("鞋面SOP") > -1) {
	    type = 2;
	}
	var url = urlPrefix + "/export/excel";
	if(downloadType == 2){
		if(dept_nameTemp.indexOf("底部") > -1 && dept_nameTemp.indexOf("成型") > -1){
			url = urlPrefix + "/cxexport/excel";
		}else if(dept_nameTemp.indexOf("底部") > -1){
			url = urlPrefix + "/bcpexport/excel";
		}else if(dept_nameTemp.indexOf("鞋面") > -1){
			url = urlPrefix + "/mbpfcexport/excel";
		}
	}
    uni.showLoading({
        title: '下载中',
        mask: true // 设置遮罩层
    });
    uni.request({
        url: url,
        data: {
            modelNo: model_noTemp,
            dept: dept_nameTemp,
            id:id,
            brand: brandTemp,
            shoeLast: shoe_lastTemp,
            type: type
        },
        method: "GET",
        responseType: 'arraybuffer', // 确保返回的是二进制数据,
        timeout: 600000
    }).then(res => {
        const blob = new Blob([res.data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = dept_nameTemp + '-' + model_noTemp + ".xlsx";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        uni.hideLoading();
    }).catch(err => {
        console.log(err);
        uni.hideLoading();
    });
}

	// 跳转至添加界面
	function edit(brand, model, shoeLast, item_num, dept_name, create_by,id,factory) {
		if (item_num == null || item_num == '') {
			showTip('error', '當前數據沒有明細，請先新增！');
			return;
		}
		let info = {
		    id:id,
			model_no: model,
			brand: brand,
			shoeLast: shoeLast,
			item_no: item_num,
			dept: dept_name,
            factory:factory
		}

		uni.navigateTo({
			url: `/pages/meprjplan/edit-info?info=${JSON.stringify(info)}`,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}

	// 提示信息
	function showTip(type, message) {
		tipType.value = type
		tipMessage.value = message
		tipPopup.value.open()
	}

	//下拉刷新
	onPullDownRefresh(() => { //通过 onPullDownRefresh  可以监听到下拉刷新的动作
		uni.startPullDownRefresh({
			success() {
				//console.log(urlPrefix)
				getPageCount()
				getData()
				uni.stopPullDownRefresh() //停止当前页面下拉刷新。
			},
			fail() {}
		})
	})

	function emptyData(e) {
		uni.showModal({
			title: '提示',
			content: '确定要删除这条记录吗？',
			confirmColor: "#ff0000",
			success: function(res) {
				if (res.confirm) {
					console.log('用户点击确定');
					if (selectIndex.value.length > 0) {
						uni.request({
							url: urlPrefix + "/pccmeprjplanhd/delete",
							data: selectIndex.value.join(','),
							method: "DELETE"
						}).then(res => {
							if (res.statusCode != 200) {
							    showTip('error', res.data.message);
								return;
							}
							showTip('success', '操作成功！')
							getData('');
						}).catch(err => {
							console.log(err)
							uni.showToast({
								title: '删除数据失败..',
								icon: "error"
							});
						})
					} else {
						showTip("error", "请选中数据！")
					}
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}

	const selectIndex = ref([]);

	function selectItemStep(selectedItem) {
		let isAnyChecked = false;
		dataList.value.forEach(item => {
			if (item.id === selectedItem.id) {
				if(insName.value == item.create_by){
					item.isChecked = !item.isChecked;
					if (item.isChecked) {
						selectIndex.value = [item.id];
						isAnyChecked = true;
					} else {
						selectIndex.value = [];
					}
				}
			} else {
				item.isChecked = false;
			}
		});

		if (!isAnyChecked) {
			selectIndex.value = [];
		}
	}

	function initAddPageParam() {
		inputCount.value = [1];
		inputCountNum.value = 1;
	}

	function search(model) {
		model_no.value = model;
		inputModel.value = model;
		firstPageNo.value = 1;
		getData(model);
	}

	// 监视输入框中的 model_no，更新搜索提示列表
	watch(inputModel, () => {
		searchModel.value = modelList.value.filter(item => item.model_no.includes(inputModel.value.toUpperCase()))
			.slice(0, 50)
	})

	// 获取品牌列表
	function getBrands() {
		uni.request({
			url: urlPrefix + "/first/getBrandsPlus",
			method: "POST"
		}).then(res => {
			brandList.value = res.data.data ? res.data.data : []
		}).catch(err => {
			console.log(err)
		})
	}


// 获取品牌列表
function getFactorys() {
    uni.request({
        url: urlPrefix + "/pccmeprjplanhd/queryFactory",
        method: "GET"
    }).then(res => {
        factoryList.value = res.data.data ? res.data.data : []
		factoryList.value.push("所有廠別")
		factoryList.value.reverse();
    }).catch(err => {
        console.log(err)
    })
}


	function getShoeLastModelList() {
		uni.request({
			url: urlPrefix + "/first/getListByShoeLast",
			method: "POST",
			data: {
				brand_no: brand.value,
				shoe_last: shoe_last.value
			}
		}).then(res => {
			shoeLastModelList.value = res.data.data ? res.data.data : []
			searchShoeLastModel.value = shoeLastModelList.value.slice(0, 50)
			shoeLastModelListShow.value = true
		}).catch(err => {
			console.log(err)
		})
	}

	// 选择部門
	function selectDept(param) {
		firstPageNo.value = 1;
		dept.value = param;
		getData('');
		deptPopup.value.close()
	}

	// 选择品牌
	function select(param) {
		model_no.value = ""
		shoe_last.value = ""
		brand.value = param
		brandPopup.value.close()

		uni.request({
			url: urlPrefix + "/first/getList",
			method: "POST",
			data: {
				brand_no: brand.value
			}
		}).then(res => {
			modelList.value = res.data.data ? res.data.data : []
			searchModel.value = modelList.value.filter(item => item.model_no.includes(model_no.value
					.toUpperCase()))
				.slice(0, 50)
		}).catch(err => {
			console.log(err)
		})

		modelList.value = []
		
		getData('');
	}

	//返回首页
	function back() {
		let back = getCurrentPages();
		if (back && back.length > 1) {
			uni.navigateBack({
				delta: 1
			});
		} else {
			history.back();
		}
	}

	async function firstChange(e) {
		firstPageNo.value = e.current;
		await uni.request({
			url: urlPrefix + "/pccmeprjplanhd/query",
			data: {
				"pageNo": firstPageNo.value,
				"pageSize": firstPageSize.value,
				"dept": dept.value,
				"loginUser": insName.value,
				"userFactory":uni.getStorageSync('currentFactory'),
                "selectFactory":selectFactory.value

			},
			method: "GET"
		}).then(res => {
			//console.log(res.data);
			dataList.value = res.data.data.list
		}).catch(err => {
			console.log(err)
		})
	}

	const tableRef = ref();
	const isAuditUser =ref(false);
	function isAuditer(){
		uni.request({
			url: urlPrefix + "/menu/buttonQuery",
			data: {
				"url": '/pccmeprjplanhd/audit',
				"loginUser": insName.value
			},
			method: "GET"
		}).then(res => {
			isAuditUser.value = res.data.data;
		}).catch(err => {
			console.log(err)
			uni.showToast({
				title: '获取数据失败..',
				icon: "error"
			});
		})
	}
	
	function audit(id){
		uni.request({
			url: urlPrefix + "/pccmeprjplanhd/audit",
			data: {
				"id": id
			},
			method: "POST"
		}).then(res => {
			showTip('success', '操作成功！')
			getData(model_no.value);
		}).catch(err => {
			console.log(err)
			uni.showToast({
				title: '审核失败..',
				icon: "error"
			});
		})
	}
	
	function revAudit(id){
		uni.request({
			url: urlPrefix + "/pccmeprjplanhd/revAudit",
			data: {
				"id": id
			},
			method: "POST"
		}).then(res => {
			showTip('success', '操作成功！')
			getData(model_no.value);
		}).catch(err => {
			console.log(err)
			uni.showToast({
				title: '审核失败..',
				icon: "error"
			});
		})
	}
	
	//获取数据
	async function getData(model) {
		selectIndex.value = [];
		isAuditer();
		await uni.request({
			url: urlPrefix + "/pccmeprjplanhd/query",
			data: {
				"pageNo": firstPageNo.value,
				"pageSize": firstPageSize.value,
				"model": model,
				"dept": dept.value,
				"brand": brand.value,
				"loginUser": insName.value,
                "userFactory":uni.getStorageSync('currentFactory'),
                "selectFactory":selectFactory.value
			},
			method: "GET"
		}).then(res => {
			// 深拷贝 dataList 以触发 Vue 的响应式系统
			dataList.value = JSON.parse(JSON.stringify(res.data.data.list));
			pageCount.value = res.data.data.total;
			shoe_last.value = res.data.data.total;
			if (res.data.data.list.length > 0) {
				shoe_last.value = res.data.data.list[0].shoe_last;
			}
			itemNo.value = 0;
		}).catch(err => {
			console.log(err)
			uni.showToast({
				title: '获取数据失败..',
				icon: "error"
			});
		})
	}

	//登录校验
	function loginCheck() {
		if (uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == '') {
			uni.navigateTo({
				url: `/pages/login/login`,
				animationType: 'pop-in',
				animationDuration: 200
			})
		}
	}

	//预加载
	onMounted(async () => {
		insName.value = uni.getStorageSync("loginUserName");
		await getBrands();
		await getFactorys();
		select(brand.value);
		// await getData(model_no.value);
		//await search()
	})

	//预加载
	onShow(async () => {
		loginCheck();
		await getData(model_no.value);
	})
</script>

<style lang="scss">
	page {
		width: 100%;
		height: 100%;
		padding: 2.5% 2% 1.5% 2%;
		box-sizing: border-box;
		background-color: #fdf6e3;
	}

	.back {
		width: 50px;
		height: 50px;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		position: absolute;
		left: 1.5%;
		top: 3%;
		cursor: pointer;
		z-index: 1;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		transition: all 0.05s ease-in-out;
	}

	.container {
		width: 100%;
		height: 100%;
		padding: 1%;
		box-sizing: border-box;
		border-radius: 10px;
		box-shadow: 0 0 1px 5px #dddddd;
		position: relative;
	}

	.right-top-top {
		display: flex;
	}

	.inpBr {
		width: 15%;
		margin-left: 10rpx;
		height: 6.3vh;
		box-sizing: border-box;
	}

	.left-bottom {
		width: 100%;
		height: 10%;
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
	}

	.uni-pagination__total {
		margin-right: 0.4375rem !important;
	}

	.uni-pagination__num-tag {
		min-width: 1.875rem !important;
		background-color: #F0F0F0 !important;
	}

	.page--active {
		color: white !important;
		background-color: deeppink !important;
	}
	.search:only-child{
		height: 3rem;
	}

	.search {
		width: 100%;
		min-height: 10%;
		margin-bottom: 1%;
		box-sizing: border-box;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		position: relative;

		.searchSelect {
			width: 100%;
			margin-left: 10%;
			margin-right: 0.5%;
			font-size: 24px;
			font-weight: bold;
			padding: 10px;
			border-radius: 10px;
		}

		.search-brand {
			width: 10%;
			margin-left: 0.5%;
			margin-right: 0.5%;
			background: linear-gradient(to right bottom, orangered, pink);
			color: white;
			font-size: 16px;
			font-weight: bold;
			border-radius: 10px;
		}

		.search-type {
			width: 8%;
			margin-left: 0.5%;
			margin-right: 1%;
			background-color: #333;
			color: white;
			font-size: 16px;
			font-weight: bold;
			border-radius: 10px;
		}

		.search-input {
			width: 30%;
			padding: 10px;
			height: 6.4vh;
			box-sizing: border-box;
			border: 2px solid gray;
			font-size: 16px;
			color: black;
			background-color: white;
			border-radius: 5px;
		/*	margin-right: 5%;*/

			&:hover {
				border: 2px solid black;
			}
		}

		.search-input-shoe-last {
			width: 35%;
			padding: 10px;
			border: 2px solid gray;
			font-size: 24px;
			color: black;
			background-color: white;
			border-radius: 5px;
			&:hover {
				border: 2px solid black;
			}
		}

		.search-clear {
			margin-left: -45px;
			margin-top: 2px;
			cursor: pointer;
			z-index: 1;
			position: relative;
			/*right: 10%;*/
		}

		.search-clear-shoe-last {
			margin-left: -45px;
			margin-top: 2px;
			cursor: pointer;
			z-index: 1;
		}

		.search-icon-shoe-last {
			width: 5%;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			z-index: 1;
		}

		.search-list {
			width: calc(40% + 20px + 4px);
			height: 270px;
			position: absolute;
			left: 45%;
			top: 120%;
			z-index: 10;

			.search-box {
				max-height: 270px;
				text-align: center;
				border-radius: 5px;
				color: white;
				font-size: 18px;
				font-weight: bold;
				background-color: gray;
				overflow: auto;

				.search-item {
					box-sizing: border-box;
					padding: 15px 0;
					border-bottom: 1px solid white;
					cursor: pointer;

					&:hover {
						background-color: #aaaaaa;
					}
				}
			}

			&::before {
				content: "";
				position: absolute;
				left: calc(50% - 10px);
				top: -10px;
				border-top: 0px solid transparent;
				border-left: 10px solid transparent;
				border-bottom: 10px solid gray;
				border-right: 10px solid transparent;
			}
		}

		.search-list-shoe-last {
			width: calc(35% + 20px + 4px);
			height: 270px;
			position: absolute;
			left: 30%;
			top: 120%;
			z-index: 1;

			.search-box-shoe-last {
				max-height: 270px;
				text-align: center;
				border-radius: 5px;
				color: white;
				font-size: 18px;
				font-weight: bold;
				background-color: gray;
				overflow: auto;

				.search-item-shoe-last {
					box-sizing: border-box;
					padding: 15px 0;
					border-bottom: 1px solid white;
					cursor: pointer;

					&:hover {
						background-color: #aaaaaa;
					}
				}
			}

			&::before {
				content: "";
				position: absolute;
				left: calc(50% - 10px);
				top: -10px;
				border-top: 0px solid transparent;
				border-left: 10px solid transparent;
				border-bottom: 10px solid gray;
				border-right: 10px solid transparent;
			}
		}
	}


	.brand-popup {
		.brand-box {
			width: 80vw;
			height: 80vh;
			border-radius: 1vw;
			background-color: #fdf6e3;
			overflow: auto;

			.brand-part {
				width: 100%;
				margin-bottom: 1%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.brand-initial {
					width: 100%;
					margin-left: 1.5%;
					margin-top: 1%;
					font-size: 32px;
					font-weight: bold;
				}

				.brand {
					width: 18%;
					height: 8vh;
					margin: 1%;
					display: flex;
					justify-content: center;
					align-items: center;
					box-sizing: border-box;
					border-radius: 1vw;
					cursor: pointer;
					font-size: 24px;
					font-weight: bold;
					color: white;
					background: linear-gradient(to right bottom, pink, blue);

					&:hover {
						color: black;
						background: linear-gradient(to right bottom, lightpink, lightblue);
					}
				}
			}
		}
	}

	.dept-popup {
		.dept-box {
			width: 80vw;
			height: 80vh;
			border-radius: 1vw;
			background-color: #fdf6e3;
			overflow: auto;

			.dept-part {
				width: 100%;
				margin-bottom: 1%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.dept-initial {
					width: 100%;
					margin-left: 1.5%;
					margin-top: 1%;
					font-size: 28px;
					font-weight: bold;
				}

				.dept {
					width: 18%;
					height: 8vh;
					margin: 1%;
					display: flex;
					justify-content: center;
					align-items: center;
					box-sizing: border-box;
					border-radius: 1vw;
					cursor: pointer;
					font-size: 16px;
					font-weight: bold;
					color: white;
					background: linear-gradient(to right bottom, pink, blue);

					&:hover {
						color: black;
						background: linear-gradient(to right bottom, lightpink, lightblue);
					}
				}
			}
		}
	}


	.add-part-popup {
		.add-part {
			width: 100vw;
			height: 100vh;
			overflow: auto;
			padding: 2.5% 2% 1.5% 2%;
			box-sizing: border-box;
			background-color: #fdf6e3;

			.add-part-box {
				width: 100%;
				min-height: 100%;
				border-radius: 10px;
				box-shadow: 0 0 1px 5px #dddddd;
				box-sizing: border-box;
				position: relative;

				.back {
					width: 50px;
					height: 50px;
					display: flex;
					justify-content: space-evenly;
					align-items: center;
					position: absolute;
					left: 2.5%;
					top: 4.5%;
					cursor: pointer;
					border-radius: 50%;
					box-shadow: 0 0 5px gray;
					z-index: 1;
				}

				.submit {
					display: flex;
					justify-content: space-evenly;
					align-items: center;
					position: absolute;
					right: 2.5%;
					top: 4.5%;
					cursor: pointer;
					z-index: 1;
				}

				.title {
					margin-bottom: 1%;
					font-size: 24px;
					font-weight: bold;
					text-align: center;
					padding: 16px;
				}

				.add-part-data {
					width: 100%;
					position: relative;
					display: flex;
					justify-content: flex-start;
					align-items: flex-start;
					flex-wrap: wrap;

					.add-part-attribute {
						width: 28%;
						margin-left: 8%;
						margin-bottom: 4%;
						background-color: #fdf6e3;

						.uni-easyinput {
							width: 80%;
							margin-left: 16px;

							&:deep(.uni-easyinput__content-input) {
								height: 40px;
								font-size: 20px;
								font-weight: bold;
							}
						}

						.uni-stat__select {
							width: 80%;
							margin-left: 16px;

							&:deep(.uni-select) {
								height: 40px;
								font-size: 20px;
								font-weight: bold;
							}

							&:deep(.uni-select__input-placeholder) {
								font-size: 20px;
							}


						}

						.uni-data-checklist {
							margin-left: 16px;

							&:deep(.checklist-box) {
								margin-top: 0px;
							}

							&:deep(.checklist-text) {
								padding: 8px;
								font-size: 20px;
								font-weight: bold;
							}
						}

						.uni-numbox {
							margin-left: 16px;
							height: 40px;

							&:deep(.uni-numbox-btns) {
								width: 40px;
								box-sizing: border-box;

								.uni-numbox--text {
									font-weight: bold;
								}
							}

							&:deep(.uni-numbox__value) {
								width: 60px;
								height: 40px;
								font-size: 20px;
								font-weight: bold;
							}
						}
					}
				}
			}
		}
	}

	#leftImgView {
		width: 23%;
		height: 600px;
		margin-left: 2%;
		margin-top: 5%;
		background-color: #fdf6e3;
	}

	#rightContent {
		margin-top: 3%;
		width: 75%;
		display: flex;
		flex-wrap: wrap;
	}

	.addInput {
		font-size: 20px;
		width: 80%;
		height: 250px;
		margin-left: 8%;
		margin-bottom: 20px;
		background-color: white;
		border: 2px solid #000;
		/* 设置边框颜色和宽度 */
		border-radius: 10px;
		/* 设置边框圆角 */

		&:deep(.uni-easyinput__content-textarea) {
			height: 235px;
			font-size: 20px;
			font-weight: bold;
		}

		&:deep(.uni-easyinput__placeholder-class) {
			font-size: 20px;
		}
	}

	.addInputParent {
		display: flex;
		align-items: center;

		.uni-icons {
			margin-left: 2%;
			margin-right: 2%;
		}
	}

	.table-container {
		max-height: 72vh;
		/* 容器高度 */
		width: 100%;
		overflow-x: auto;
		/* 启用横向滚动 */
		background-color: white;
		border: 2px solid #ddd;
	}

	table {
		color: #606266;
		border-collapse: collapse;
	/*	margin: 20px 0;*/
		font-size: 18px;
		width: 100%;
		/* 确保表格宽度大于容器宽度 */
		border-radius: 5px;
		box-sizing: border-box;
		text-align: center;
		white-space: nowrap;
	}

	td {
		font-size: 14px;
		border-bottom: 1px #EBEEF5 solid;
		border-right: 1px #EBEEF5 solid;
		padding: 8px 10px;
		border-bottom: 1px solid #EBEEF5;
		/* 表格底部边框 */
		height: 44px;
		/* 固定行高 */
		box-sizing: border-box;
		/* 包含内边距和边框 */
	}

	th {
		font-size: 14px;
		padding: 8px 10px;
		border-right: 1px solid #EBEEF5;
		/* 表格底部边框 */
		height: 44px;
		/* 固定行高 */
		box-sizing: border-box;
		color: #333;
		/* 表头文字颜色 */
	}

	tr {
		font-size: 14px;
		height: 44px;
		/* 固定行高 */
	}

	tr:hover {
		background-color: #f1f1f1;
		/* 行悬停效果 */
	}

	uv-button {
		width: 80px;
		/* 固定按钮宽度 */
		box-sizing: border-box;
		/* 包含内边距和边框 */
	}

	th{
		background-color: #f2f2f2; /* 表头背景色 */
		position: sticky; /* 固定表头 */
		top: 0; /* 固定在顶部 */
		z-index: 2; /* 确保表头在其他内容之上 */
		will-change: transform; /* 提高渲染性能，防止抖动 */
	}

	 th::after{
		content: "";
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 1px;
		background-color: #EBEEF5;
	}

</style>
