{
	"pages": [
		{
			"path": "pages/login/login",
			"style": {
				"navigationStyle": "custom", // 隐藏系统导航栏
				"navigationBarTextStyle": "white" // 状态栏字体为白色，只能为 white-白色，black-黑色 二选一
			}
		},
		{
			"path": "pages/login/upgrade",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"bounce": "none",
					"animationType":"none",//取消窗口动画
					"background": "transparent" // 设置背景透明
				}
			}
		},
		{
			"path": "pages/menu/menu",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom" // 隐藏系统导航栏

			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"app-plus": {
					"softinputmode": "adjustPan"
				},
				"navigationBarTitleText": "型體數據",
				"pageOrientation":"landscape",
				"navigationStyle": "custom"
			}
		}
	,{
			"path" : "pages/exdata/exdata",
			"style" :
			{
				"navigationBarTitleText": "生產異常",
				"navigationStyle": "custom",
				"pageOrientation":"landscape",
				"enablePullDownRefresh": true
			}

		}
	,{
			"path" : "pages/ptdata/yptDtr",
			"style" :
			{
				"navigationBarTitleText": "已配套待投入",
				"navigationStyle": "custom",
				"pageOrientation":"landscape",
				"enablePullDownRefresh": true
			}

		}
	,{
			"path" : "pages/ptdata/yptDps",
			"style" :
			{
				"navigationBarTitleText": "已配套待配送",
				"navigationStyle": "custom",
				"pageOrientation":"landscape",
				"enablePullDownRefresh": true
			}

		},{
			"path" : "pages/ptdata/wptCx",
			"style" :
			{
				"navigationBarTitleText": "未配套库存查询",
				"navigationStyle": "custom",
				"pageOrientation":"landscape",
				"enablePullDownRefresh": true
			}
		},{
			"path" : "pages/ptdata/yptXtqr",
			"style" :
			{
				"navigationBarTitleText": "已配套楦头确认",
				"navigationStyle": "custom",
				"pageOrientation":"landscape",
				"enablePullDownRefresh": true
			}

		},{
			"path" : "pages/ptdata/cxYtr",
			"style" :
			{
				"navigationBarTitleText": "成型已投入未產出查詢",
				"navigationStyle": "custom",
				"pageOrientation":"landscape",
				"enablePullDownRefresh": true
			}

		}
	,{
			"path" : "pages/util/util",
			"style" :
			{
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}

		},
		{
			"path": "pages/process/process"
		},
		{
			"path": "pages/process/add-info"
		},
		{
			"path": "pages/process/update-info"
		},
		{
			"path": "pages/process/add-step"
		},
		{
			"path": "pages/process/update-step"
		},
		{
			"path": "pages/process/template-import"
		},
		{
			"path": "pages/process/copy-process"
		},
		{
			"path" : "pages/report/reportMain",
			"style" :
			{
				"navigationBarTitleText" : "报表",
				"enablePullDownRefresh" : false,
				"app-plus": {
					"background": "#2d3653"
				}
			}
		},
		{
			"path" : "pages/menu/userRole",
			"style" :
			{
				"navigationBarTitleText" : "菜单管理",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/meprjplan/mePrjPlanList",
			"style" :
			{
				"navigationBarTitleText" : "ME工程编制-列表",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/meprjplan/add-info",
			"style" :
			{
				"navigationBarTitleText" : "ME工程编制-新增",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/meprjplan/edit-info",
			"style" :
			{
				"navigationBarTitleText" : "ME工程编制-编辑",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/meprjplan/view-info",
			"style" :
			{
				"navigationBarTitleText" : "ME工程编制-查看",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/meprjplan/view-info-mb",
			"style" :
			{
				"navigationBarTitleText" : "ME工程编制面部-查看",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/meprjplan/view-info-mb-sf",
			"style" :
			{
				"navigationBarTitleText" : "ME工程编制面部双峰-查看",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/meprjplan/view-info-db",
			"style" :
			{
				"navigationBarTitleText" : "ME工程编制底部-查看",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/prodProg/cutProgress",
			"style" :
			{
				"navigationBarTitleText" : "裁断生产进度表",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/prodProg/qdpt",
			"style" :
			{
				"navigationBarTitleText" : "前段配套生产进度表",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/prodProg/wlpt",
			"style" :
			{
				"navigationBarTitleText" : "仓库物料配套",
				"enablePullDownRefresh" : false
			}
		},
		{
					"path" : "pages/meprjplan/webview",
					"style" : 
					{
						"navigationBarTitleText" : "编辑图片",
						"enablePullDownRefresh" : false
		
					}
		},
		{
					"path" : "pages/meprjplan/actions",
					"style" : 
					{
						"navigationBarTitleText" : "动作管理",
						"enablePullDownRefresh" : false
		
					}
		},
		{
			"path" : "pages/meprjplan/copyModel",
			"style" :
			{
				"navigationBarTitleText" : "型体复制",
				"enablePullDownRefresh" : false

			}
		},
		{
			"path" : "pages/meprjplan/preview",
			"style" :
			{
				"navigationBarTitleText" : "图片预览",
				"enablePullDownRefresh" : false,
				"navigationStyle": "custom"
		
			}
		},
		{
			"path" : "pages/prodProg/qdpt-mb",
			"style" :
			{
				"navigationBarTitleText" : "鞋面配套待投入進度表",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/prodProg/pendingUpperProd",
			"style" :
			{
				"navigationBarTitleText" : "鞋面配套待产出進度表",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/trans/trans",
			"style" :
			{
				"navigationBarTitleText" : "智能翻译",
				"enablePullDownRefresh" : false
			}
		},
		{
			"path" : "pages/meprjplan/editProgressRoute",
			"style" : 
			{
				"navigationBarTitleText" : "编辑成长轨迹"
			}
		},
		{
			"path" : "pages/meprjplan/progressRoute",
			"style" : 
			{
				"navigationBarTitleText" : "查看成长轨迹"
			}
		},
		{
			"path" : "pages/meprjplan/progressRouteList",
			"style" : 
			{
				"navigationBarTitleText" : "成长轨迹列表"
			}
		},
		{
			"path" : "pages/newmodel/newModel",
			"style" : 
			{
				"navigationBarTitleText" : "新型体开发"
			}
		},
		{
			"path" : "pages/meprjplan/formingAccessories",
			"style" : 
			{
				"navigationBarTitleText" : "成型生产配套"
			}
		},
		{
			"path" : "pages/meprjplan/bottomOrder",
			"style" : 
			{
				"navigationBarTitleText" : "皮底訂單生產狀況表"
			}
		},
		{
			"path" : "pages/meprjplan/fitList",
			"style" : 
			{
				"navigationBarTitleText" : "FIT樣品進度"
			}
		},
		{
			"path" : "pages/meprjplan/fitListNew",
			"style" : 
			{
				"navigationBarTitleText" : "FIT樣品進度(面部)"
			}
		},
		{
			"path" : "pages/meprjplan/dayuan",
			"style" : 
			{
				"navigationBarTitleText" : "大圖樣品進度表"
			}
		},
		{
			"path" : "pages/meprjplan/shoeUpperScheduling",
			"style" : 
			{
				"navigationBarTitleText" : "鞋面小組排程表"
			}
		},
		{
			"path" : "pages/prodctrl/schedProd",
			"style" : 
			{
				"navigationBarTitleText" : "生產排程（可排程生產）"
			}
		},
		{
			"path" : "pages/prodctrl/pendingProd",
			"style" : 
			{
				"navigationBarTitleText" : "生產排程（待排程生產）"
			}
		},
		{
			"path" : "pages/factorymanager/devTrackingTop",
			"style" : 
			{
				"navigationBarTitleText" : "版師追蹤（面）"
			}
		},
		{
			"path" : "pages/factorymanager/devTrackingBottom",
			"style" : 
			{
				"navigationBarTitleText" : "版師追蹤（底）"
			}
		},
		{
			"path" : "pages/factorymanager/devTrackingWarnTop",
			"style" : 
			{
				"navigationBarTitleText" : "版師開板異常預警（面）"
			}
		},
		{
			"path" : "pages/factorymanager/devTrackingWarnBottom",
			"style" : 
			{
				"navigationBarTitleText" : "版師開板異常預警（底）"
			}
		},	{
			"path" : "pages/analysis/technicalDept",
			"style" : 
			{
				"navigationBarTitleText" : "技術部數據分析(面)"
			}
		},	{
			"path" : "pages/analysis/technicalDeptBottom",
			"style" : 
			{
				"navigationBarTitleText" : "技術部數據分析(底)"
			}
		},	{
			"path" : "pages/factorymanager/retryAlertTop",
			"style" :
			{
				"navigationBarTitleText" : "重做预警（面）"
			}
		},	{
			"path" : "pages/factorymanager/retryAlertBottom",
			"style" :
			{
				"navigationBarTitleText" : "重做预警（底）"
			}
		},
        {
          "path": "pages/sop/sop-info",
          "style": {
            "navigationBarTitleText": "工序信息"
          }
        },
        {
          "path": "pages/sop/sop-flow",
          "style": {
            "navigationBarTitleText": "工序流程"
          }
        },
        {
          "path": "pages/sop/sop-overall",
          "style": {
            "navigationBarTitleText": "工序流程整体"
          }
        },
        {
          "path": "pages/sop/sop-preview",
          "style": {
            "navigationBarTitleText": "流程预览"
          }
        },
        {
          "path": "pages/sop/sop-translation",
          "style": {
            "navigationBarTitleText": "SOP 预估版翻译"
          }
        },
        {
          "path": "pages/sop/sop-press",
          "style": {
            "navigationBarTitleText": "鞋面热冷压规格"
          }
        },
        {
          "path": "pages/sop/sop-press-update",
          "style": {
            "navigationBarTitleText": "鞋面热冷压规格（修改）"
          }
        },
        {
        	"path" : "pages/webview/webview",
        	"style" : 
        	{
        		"navigationBarTitleText" : "",
        		"enablePullDownRefresh" : false
        	}
        },{
			"path" : "pages/workorder/index",
			"style" : 
			{
				"navigationBarTitleText" : "鞋面小組排程表"
			}
		},{
			"path" : "pages/student-record/index",
			"style" : 
			{
				"navigationBarTitleText" : "制鞋学员一览表",
				"navigationStyle": "custom",
				"pageOrientation":"landscape",
				"enablePullDownRefresh" : false
			}
		},{
			"path" : "pages/student-record/edit",
			"style" : 
			{
				"navigationBarTitleText" : "学员记录编辑",
				"navigationStyle": "custom",
				"pageOrientation":"landscape",
				"enablePullDownRefresh" : false
			}
		},{
			"path" : "pages/student-record/view",
			"style" :
			{
				"navigationBarTitleText" : "学员记录查看",
				"navigationStyle": "custom",
				"pageOrientation":"landscape",
				"enablePullDownRefresh" : false
			}
		},{
			"path" : "pages/email/index",
			"style" :
			{
				"navigationBarTitleText" : "邮件推送管理系统",
				"navigationStyle": "custom",
				"pageOrientation":"landscape",
				"enablePullDownRefresh" : true,
				"backgroundTextStyle": "dark"
			}
		}
	],
	"easycom": {
		"autoscan": true,
		"custom": {
			"^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
		}
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"app-plus": {
			"background": "#efeff4"
		},
		// 取消导航栏
		"navigationStyle": "custom",
		// 横屏配置
		"pageOrientation": "landscape"
	}
}
