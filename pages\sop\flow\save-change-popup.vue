<!-- 保存工序流程更改弹框 -->
<script setup>
import { ref, inject } from 'vue'

// 传递消息
const emit = defineEmits(['save-change', 'not-save-change'])

// 保存工序流程更改弹框
const saveChangePopup = ref()

// 工序流程详情
const flowDetail = inject('flowDetail')

// 是否为返回按钮
const isBack = ref(false)

// 显示保存工序流程更改弹框
function showSaveChangePopup(param) {
  isBack.value = param
  saveChangePopup.value.open()
}

// 保存工序流程更改
function saveChange(param1, param2) {
  emit('save-change', param1, param2)
  saveChangePopup.value.close()
}

// 不保存工序流程更改
function notSaveChange(param) {
  emit('not-save-change', param)
  saveChangePopup.value.close()
}

defineExpose({
  showSaveChangePopup
})
</script>

<template>
  <uni-popup
    ref="saveChangePopup"
    type="center"
    :is-mask-click="false"
    class="save-change-popup"
  >
    <view class="container">
      <view class="title">
        提示
      </view>
      
      <view class="context">
        是否保存当前更改？
      </view>
      
      <view class="operate">
        <view @click="notSaveChange(isBack)" class="cancel button">
          否
        </view>
        
        <view @click="saveChange(flowDetail, isBack)" class="confirm button">
          是
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.save-change-popup {
  .container {
    padding: 10px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      padding: 5px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      color: red;
      font-size: 22px;
      font-weight: bold;
    }
    
    .context {
      padding: 30px 75px;
      color: black;
      font-size: 20px;
      font-weight: bold;
    }
    
    .operate {
      padding: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .cancel, .confirm {
        width: 145px;
        height: 45px;
        font-size: 20px;
      }
      
      .cancel {
        color: darkred;
      }
      
      .confirm {
        color: lightseagreen;
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
}
</style>