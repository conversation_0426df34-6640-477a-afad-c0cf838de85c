<!-- 图片弹框 -->
<script setup>
import { ref, onMounted } from 'vue'

// 图片弹框
const picturePopup = ref()
// 图片 url
const pictureUrl = ref('')

// 显示图片弹框
function showPicturePopup(url) {
  pictureUrl.value = url
  picturePopup.value.open()
}

defineExpose({
  showPicturePopup
})
</script>

<template>
  <uni-popup
    ref="picturePopup"
    type="center"
    class="picture-popup"
  >
    <view class="watermark">
      <img :src="pictureUrl" alt="" />
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.picture-popup {
  .watermark {
    position: relative;
    word-break: break-all;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    transition: all 0.15s ease;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    img {
      display: block;
      min-width: 200px;
      min-height: 200px;
      // max-height: 500px;
      // max-width: 700px;
      max-height: 60vh;
      max-width: 60vw;
      border-radius: 10px;
    }
    
    &:before {
      content: 'STELLA';
      position: absolute;
      top: 5px;
      left: 5px;
      color: white;
      font-size: 24px;
      font-weight: bold;
      text-shadow: 0 0 5px black;
      z-index: 1;
      pointer-events: none;
    }
    
    &:active {
      transform: scale(1.5);
      box-shadow: 0 0 1px white;
    }
  }
}
</style>