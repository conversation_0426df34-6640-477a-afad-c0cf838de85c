package com.zqn.email.config;

import com.zqn.email.service.EmailPushConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件配置类
 * @date 2025/01/22 10:00
 */
@Slf4j
@Configuration
public class EmailConfig {

    @Resource
    @Lazy
    private EmailPushConfigService emailPushConfigService;

    /**
     * 配置JavaMailSender
     * 从数据库动态获取SMTP配置，增强错误处理和连接稳定性
     */
    @Bean
    public JavaMailSender javaMailSender() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();

        try {
            // 从数据库获取SMTP配置
            Map<String, String> smtpConfig = emailPushConfigService.getSmtpConfig();

            // 设置SMTP服务器配置
            mailSender.setHost(smtpConfig.getOrDefault("host", "smtp.gmail.com"));
            mailSender.setPort(Integer.parseInt(smtpConfig.getOrDefault("port", "587")));
            mailSender.setUsername(smtpConfig.getOrDefault("username", ""));
            mailSender.setPassword(smtpConfig.getOrDefault("password", ""));

            // 设置邮件属性 - 增强连接稳定性配置
            Properties props = mailSender.getJavaMailProperties();
            props.put("mail.transport.protocol", "smtp");
            props.put("mail.smtp.auth", smtpConfig.getOrDefault("auth", "true"));
            props.put("mail.smtp.starttls.enable", smtpConfig.getOrDefault("starttls", "true"));
            props.put("mail.smtp.ssl.enable", smtpConfig.getOrDefault("ssl", "false"));

            // 连接超时和读取超时设置（毫秒）
            props.put("mail.smtp.connectiontimeout", "30000"); // 30秒连接超时
            props.put("mail.smtp.timeout", "60000");           // 60秒读取超时
            props.put("mail.smtp.writetimeout", "60000");      // 60秒写入超时

            // 连接池和重用设置
            props.put("mail.smtp.connectionpoolsize", "10");   // 连接池大小
            props.put("mail.smtp.connectionpooltimeout", "300000"); // 5分钟连接池超时

            // SSL/TLS 安全设置
            if ("true".equals(smtpConfig.get("ssl"))) {
                props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
                props.put("mail.smtp.socketFactory.port", smtpConfig.getOrDefault("port", "587"));
                props.put("mail.smtp.socketFactory.fallback", "false");
                props.put("mail.smtp.ssl.checkserveridentity", "true");
                props.put("mail.smtp.ssl.trust", "*");
            } else if ("true".equals(smtpConfig.get("starttls"))) {
                // STARTTLS 配置 - 不强制要求，允许降级到非加密连接
                props.put("mail.smtp.starttls.required", "false");
                props.put("mail.smtp.ssl.checkserveridentity", "false");
                props.put("mail.smtp.ssl.trust", "*");
            }

            // 调试模式 - 生产环境建议设置为false
            props.put("mail.debug", "true"); // 临时开启调试以排查问题

            // 邮件头设置
            props.put("mail.mime.charset", "UTF-8");
            props.put("mail.smtp.localhost", "localhost");

            log.info("JavaMailSender配置完成，SMTP主机: {}, 端口: {}, SSL: {}, STARTTLS: {}",
                    mailSender.getHost(), mailSender.getPort(),
                    smtpConfig.get("ssl"), smtpConfig.get("starttls"));

        } catch (Exception e) {
            log.error("配置JavaMailSender失败，使用默认配置", e);

            // 使用默认配置
            mailSender.setHost("smtp.gmail.com");
            mailSender.setPort(587);

            Properties props = mailSender.getJavaMailProperties();
            props.put("mail.transport.protocol", "smtp");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.connectiontimeout", "30000");
            props.put("mail.smtp.timeout", "60000");
            props.put("mail.smtp.writetimeout", "60000");
            props.put("mail.debug", "true");
        }

        return mailSender;
    }
}
