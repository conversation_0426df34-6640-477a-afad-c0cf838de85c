<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
    <link type="text/css" href="css/tui-color-picker.css"
        rel="stylesheet" />
    <link type="text/css" href="css/tui-image-editor.css" rel="stylesheet" />
    <style>
        html,
        body {
            height: 100%;
            box-sizing: border-box;
            margin: 0;
            background-color: #282828;
        }

        .tui-image-editor-container {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            min-height: 300px;
            height: 90% !important;
            position: relative;
            background-color: #282828;
            overflow: hidden;
            letter-spacing: .3px;
        }

        .tui-image-editor-container .tui-image-editor-help-menu.top {
            white-space: nowrap;
            width: 206px;
            height: 40px;
            position: fixed;
            bottom: 20px;
            left: 60px;
            top: auto;
            z-index: 99;
            transform: translateX(-50%);
        }

        .tui-image-editor-container .tui-image-editor-header {
            display: none;
        }

        .tui-image-editor-container .tui-image-editor-controls {
            position: absolute;
            background-color: #151515;
            width: 100%;
            height: 64px;
            display: table;
            bottom: 0;
            z-index: 2;
        }

        .tie-btn-zoomIn,
        .tie-btn-zoomOut,
        .tie-btn-hand,
        .tie-btn-history,
        .tie-btn-redo,
        .tie-btn-delete,
        .tie-btn-deleteAll {
            display: none !important;
        }

        .btn {
            visibility: hidden;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 10vh;
            margin-right: 20px;
        }

        .cancelReturn {
            color: #fff;
            margin-right: 20px;
        }

        .saveBtn {
            height: 30px;
            border-radius: 15px;
            background-color: #00a9ff;
            color: #fff;
            line-height: 30px;
            text-align: center;
            font-weight: 500;
            padding: 0 15px;
        }
		
		
		        .tui-image-editor-container .tui-image-editor-range {
		            position: relative;
		            top: 5px;
		            width: 166px;
		            height: 17px;
		            display: none;
		        }
		
		        .tui-image-editor-range-value {
		            display: none;
		        }
		
		        .tui-image-editor-container.right .tui-image-editor-range-wrap.tui-image-editor-newline label.range {
		            display: none;
		        }
		
    </style>
    <script type="text/javascript" src="js/fabric.js"></script>
    <script type="text/javascript" src="js/tui-code-snippet.min.js">
    </script>
    <script type="text/javascript" src="js/tui-color-picker.js">
    </script>
    <script type="text/javascript" src="js/FileSaver.min.js">
    </script>

    <script type="text/javascript" src="js/tui-image-editor.js"></script>
    <script type="text/javascript" src="js/black-theme.js"></script>



</head>

<body>
    <div id="tui-image-editor-container"></div>
    <div class="btn">
        <div class="cancelReturn">取消</div>
        <div class="saveBtn">保存</div>
    </div>

    <script>

        function convertImageToBase64(url) {
            return fetch(url)
                .then(response => response.blob())
                .then(blob => new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        resolve(e.target.result);
                    };
                    reader.onerror = function (e) {
                        reject(e);
                    };
                    reader.readAsDataURL(blob);
                }))
                .catch(error => {
                    console.error(error);
                });
        }

        window.onload = function () {
			    const urlParams = new URLSearchParams(window.location.search);
			    const base64Data = urlParams.get('data');
				// console.log("index:" + base64Data)
				msgFromUniapp(base64Data)
        }
		
    </script>
</body>
<script src="js/main.js"></script>

</html>
<script src="js/uni.webview.1.5.2.js"></script>