<!-- 工序信息生产类型弹框 -->
<script setup>
import { ref } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap, proSeqMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 工序信息生产类型弹框
const infoTypePopup = ref()

// 工序信息
const processInfo = ref()
// 生产类型列表
const infoTypeList = ref([])

// 获取生产类型列表
async function getInfoTypeList(param) {
  await uni.request({
    url: urlPrefix + '/sop/getInfoTypeList',
    method: 'POST',
    data: {
      operation: param
    }
  }).then(res => {
    if (res.data.code) {
      infoTypeList.value = res.data.data ? res.data.data : []
    } else {
      infoTypeList.value = []
      tipPopup.value.showTipPopup('warn', '暂无生产类型列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示工序信息生产类型弹框
async function showInfoTypePopup(param) {
  if (infoTypeList.value.length === 0) {
    uni.showLoading({
      title: '加载中...',
      mask: true
    })
    
    await getInfoTypeList(param.operation)
    
    uni.hideLoading()
    
    if (infoTypeList.value.length === 0) {
      return
    }
  }
  
  processInfo.value = param
  
  infoTypePopup.value.open()
}

// 选择生产类型
async function selectInfoType(param1, param2, param3, param4) {
  if (param4 === processInfo.value.rtgType) {
    infoTypePopup.value.close()
    return
  }
  
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateInfoType',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3,
      rtgType: param4,
      updUser: user
    }
  }).then(res => {
    if (res.data.code) {
      processInfo.value.rtgType = param4
      tipPopup.value.showTipPopup('success', '修改成功！')
      infoTypePopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  getInfoTypeList,
  showInfoTypePopup
})
</script>

<template>
  <uni-popup
    ref="infoTypePopup"
    type="center"
    class="info-type-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择生产类型
      </view>
      
      <view class="description flex-row-center">
        {{ operationMap.get(processInfo.operation) ? operationMap.get(processInfo.operation) : '未知' }} - {{ proSeqMap.get(processInfo.proSeq) ? proSeqMap.get(processInfo.proSeq) : '未知' }} - {{ processInfo.model }} - {{ processInfo.rtgCode }}
      </view>
      
      <view class="info-type-list">
        <view
          v-for="(item, index) in infoTypeList"
          :key="index"
          class="info-type flex-row-center"
        >
          <view
            @click="selectInfoType(processInfo.model, processInfo.operation, processInfo.rtgCode, item)"
            class="button"
            :style="{
              color: item === processInfo.rtgType ? 'mediumseagreen' : 'darkgreen'
            }"
          >
            {{ item }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.info-type-popup {
  .container {
    width: 430px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .description {
      width: 100%;
      height: 40px;
      font-size: 20px;
      font-weight: bold;
    }
    
    .info-type-list {
      min-height: 210px;
      max-height: 350px;
      overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .info-type {
        width: 100%;
        padding: 10px;
        
        .button {
          width: 270px;
          height: 50px;
          color: darkgreen;
          font-size: 20px;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>