-- 邮件推送系统完整安装脚本
-- 包含所有表结构创建和初始化数据
-- 执行顺序：配置表 -> 权限表 -> 模板表 -> 日志表 -> 初始化数据

-- ===========================================
-- 1. 邮件推送配置表
-- ===========================================
CREATE TABLE pcc_email_push_config (
    id NUMBER(10) PRIMARY KEY,                   -- 主键ID
    config_key VARCHAR2(100) NOT NULL UNIQUE,   -- 配置键
    config_value VARCHAR2(1000),                -- 配置值
    config_desc VARCHAR2(500),                  -- 配置描述
    config_type VARCHAR2(50) DEFAULT 'STRING',  -- 配置类型 (STRING, NUMBER, BOOLEAN, JSON)
    
    -- 状态控制
    status CHAR(1) DEFAULT 'Y',                 -- 状态 (Y-启用, N-禁用)
    remark VARCHAR2(500),                       -- 备注说明
    
    -- 审计字段
    create_user VARCHAR2(20),                   -- 创建人
    create_date DATE DEFAULT SYSDATE,          -- 创建时间
    update_user VARCHAR2(20),                  -- 更新人
    update_date DATE DEFAULT SYSDATE,          -- 更新时间
    
    -- 约束
    CONSTRAINT chk_config_status CHECK (status IN ('Y', 'N')),
    CONSTRAINT chk_config_type CHECK (config_type IN ('STRING', 'NUMBER', 'BOOLEAN', 'JSON'))
);

-- 创建索引
CREATE INDEX idx_pcc_email_config_key ON pcc_email_push_config(config_key);
CREATE INDEX idx_pcc_email_config_type ON pcc_email_push_config(config_type);
CREATE INDEX idx_pcc_email_config_status ON pcc_email_push_config(status);

-- 创建序列
CREATE SEQUENCE seq_pcc_email_push_config START WITH 1 INCREMENT BY 1;

-- 添加注释
COMMENT ON TABLE pcc_email_push_config IS '邮件推送配置表';
COMMENT ON COLUMN pcc_email_push_config.id IS '主键ID';
COMMENT ON COLUMN pcc_email_push_config.config_key IS '配置键';
COMMENT ON COLUMN pcc_email_push_config.config_value IS '配置值';
COMMENT ON COLUMN pcc_email_push_config.config_desc IS '配置描述';
COMMENT ON COLUMN pcc_email_push_config.config_type IS '配置类型 (STRING, NUMBER, BOOLEAN, JSON)';
COMMENT ON COLUMN pcc_email_push_config.status IS '状态 (Y-启用, N-禁用)';
COMMENT ON COLUMN pcc_email_push_config.remark IS '备注说明';
COMMENT ON COLUMN pcc_email_push_config.create_user IS '创建人';
COMMENT ON COLUMN pcc_email_push_config.create_date IS '创建时间';
COMMENT ON COLUMN pcc_email_push_config.update_user IS '更新人';
COMMENT ON COLUMN pcc_email_push_config.update_date IS '更新时间';

-- ===========================================
-- 2. 邮件推送权限配置表
-- ===========================================
CREATE TABLE pcc_email_push_permission (
    id NUMBER(10) PRIMARY KEY,                    -- 主键ID
    user_no VARCHAR2(20) NOT NULL,               -- 工号（关联sy_user.user_no）
    dept_name VARCHAR2(100),                     -- 部门名称
    email VARCHAR2(200),                         -- 邮箱地址
    
    -- 推送報價版本权限 (1-5版本)
    push_quote_v1 CHAR(1) DEFAULT 'N',          -- 推送報價1版權限 (Y-有权限, N-无权限, P-待定)
    push_quote_v2 CHAR(1) DEFAULT 'N',          -- 推送報價2版權限 (Y-有权限, N-无权限, P-待定)
    push_quote_v3 CHAR(1) DEFAULT 'N',          -- 推送報價3版權限 (Y-有权限, N-无权限, P-待定)
    push_quote_v4 CHAR(1) DEFAULT 'N',          -- 推送報價4版權限 (Y-有权限, N-无权限, P-待定)
    push_quote_v5 CHAR(1) DEFAULT 'N',          -- 推送報價5版權限 (Y-有权限, N-无权限, P-待定)
    
    -- 推送預估版本权限
    push_estimate_z CHAR(1) DEFAULT 'N',        -- 推送預估Z版權限 (Y-有权限, N-无权限, P-待定)
    push_estimate_zz CHAR(1) DEFAULT 'N',       -- 推送預估ZZ版權限 (Y-有权限, N-无权限, P-待定)
    
    -- 推送P版权限
    push_p_version CHAR(1) DEFAULT 'N',         -- 推送P版權限 (Y-有权限, N-无权限, P-待定)
    
    -- 更新版本邮件提醒
    update_email_notify CHAR(1) DEFAULT 'Y',    -- 更新版本邮件提醒 (Y-启用, N-禁用)
    
    -- 状态控制
    status CHAR(1) DEFAULT 'Y',                 -- 状态 (Y-启用, N-禁用)
    remark VARCHAR2(500),                       -- 备注说明
    
    -- 审计字段
    create_user VARCHAR2(20),                   -- 创建人
    create_date DATE DEFAULT SYSDATE,          -- 创建时间
    update_user VARCHAR2(20),                  -- 更新人
    update_date DATE DEFAULT SYSDATE,          -- 更新时间
    
    -- 约束
    CONSTRAINT chk_push_quote_v1 CHECK (push_quote_v1 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_quote_v2 CHECK (push_quote_v2 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_quote_v3 CHECK (push_quote_v3 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_quote_v4 CHECK (push_quote_v4 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_quote_v5 CHECK (push_quote_v5 IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_estimate_z CHECK (push_estimate_z IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_estimate_zz CHECK (push_estimate_zz IN ('Y', 'N', 'P')),
    CONSTRAINT chk_push_p_version CHECK (push_p_version IN ('Y', 'N', 'P')),
    CONSTRAINT chk_update_email_notify CHECK (update_email_notify IN ('Y', 'N')),
    CONSTRAINT chk_permission_status CHECK (status IN ('Y', 'N')),
    CONSTRAINT uk_user_no UNIQUE (user_no)
);

-- 创建索引
CREATE INDEX idx_pcc_email_push_user_no ON pcc_email_push_permission(user_no);
CREATE INDEX idx_pcc_email_push_dept ON pcc_email_push_permission(dept_name);
CREATE INDEX idx_pcc_email_push_email ON pcc_email_push_permission(email);
CREATE INDEX idx_pcc_email_push_status ON pcc_email_push_permission(status);

-- 创建序列
CREATE SEQUENCE seq_pcc_email_push_permission START WITH 1 INCREMENT BY 1;

-- 添加注释
COMMENT ON TABLE pcc_email_push_permission IS '邮件推送权限配置表';
COMMENT ON COLUMN pcc_email_push_permission.id IS '主键ID';
COMMENT ON COLUMN pcc_email_push_permission.user_no IS '工号（关联sy_user.user_no）';
COMMENT ON COLUMN pcc_email_push_permission.dept_name IS '部门名称';
COMMENT ON COLUMN pcc_email_push_permission.email IS '邮箱地址';
COMMENT ON COLUMN pcc_email_push_permission.push_quote_v1 IS '推送報價1版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN pcc_email_push_permission.push_quote_v2 IS '推送報價2版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN pcc_email_push_permission.push_quote_v3 IS '推送報價3版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN pcc_email_push_permission.push_quote_v4 IS '推送報價4版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN pcc_email_push_permission.push_quote_v5 IS '推送報價5版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN pcc_email_push_permission.push_estimate_z IS '推送預估Z版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN pcc_email_push_permission.push_estimate_zz IS '推送預估ZZ版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN pcc_email_push_permission.push_p_version IS '推送P版權限 (Y-有权限, N-无权限, P-待定)';
COMMENT ON COLUMN pcc_email_push_permission.update_email_notify IS '更新版本邮件提醒 (Y-启用, N-禁用)';
COMMENT ON COLUMN pcc_email_push_permission.status IS '状态 (Y-启用, N-禁用)';
COMMENT ON COLUMN pcc_email_push_permission.remark IS '备注说明';
COMMENT ON COLUMN pcc_email_push_permission.create_user IS '创建人';
COMMENT ON COLUMN pcc_email_push_permission.create_date IS '创建时间';
COMMENT ON COLUMN pcc_email_push_permission.update_user IS '更新人';
COMMENT ON COLUMN pcc_email_push_permission.update_date IS '更新时间';

-- ===========================================
-- 3. 邮件模板表
-- ===========================================
CREATE TABLE pcc_email_template (
    id NUMBER(10) PRIMARY KEY,                    -- 主键ID
    template_code VARCHAR2(50) NOT NULL UNIQUE,  -- 模板代码
    template_name VARCHAR2(200) NOT NULL,        -- 模板名称
    email_type VARCHAR2(50) NOT NULL,            -- 邮件类型 (QUOTE_V1, QUOTE_V2, QUOTE_V3, QUOTE_V4, QUOTE_V5, ESTIMATE_Z, ESTIMATE_ZZ, P_VERSION, UPDATE_NOTIFY)

    -- 模板内容
    subject_template VARCHAR2(500),              -- 主题模板（支持变量替换）
    content_template CLOB,                       -- 内容模板（支持变量替换，HTML格式）

    -- 模板变量说明
    variables_desc CLOB,                         -- 模板变量说明（JSON格式）

    -- 语言支持
    language VARCHAR2(10) DEFAULT 'zh_CN',       -- 语言 (zh_CN-简体中文, zh_TW-繁体中文, en_US-英文, vi_VN-越南文)

    -- 状态控制
    status CHAR(1) DEFAULT 'Y',                  -- 状态 (Y-启用, N-禁用)
    is_default CHAR(1) DEFAULT 'N',             -- 是否默认模板 (Y-是, N-否)
    sort_order NUMBER(3) DEFAULT 0,             -- 排序序号

    -- 备注信息
    remark VARCHAR2(500),                        -- 备注说明

    -- 审计字段
    create_user VARCHAR2(20),                    -- 创建人
    create_date DATE DEFAULT SYSDATE,           -- 创建时间
    update_user VARCHAR2(20),                   -- 更新人
    update_date DATE DEFAULT SYSDATE,           -- 更新时间

    -- 约束
    CONSTRAINT chk_template_status CHECK (status IN ('Y', 'N')),
    CONSTRAINT chk_template_default CHECK (is_default IN ('Y', 'N')),
    CONSTRAINT chk_template_email_type CHECK (email_type IN ('QUOTE_V1', 'QUOTE_V2', 'QUOTE_V3', 'QUOTE_V4', 'QUOTE_V5', 'ESTIMATE_Z', 'ESTIMATE_ZZ', 'P_VERSION', 'UPDATE_NOTIFY')),
    CONSTRAINT chk_template_language CHECK (language IN ('zh_CN', 'zh_TW', 'en_US', 'vi_VN'))
);

-- 创建索引
CREATE INDEX idx_pcc_email_template_code ON pcc_email_template(template_code);
CREATE INDEX idx_pcc_email_template_type ON pcc_email_template(email_type);
CREATE INDEX idx_pcc_email_template_status ON pcc_email_template(status);
CREATE INDEX idx_pcc_email_template_default ON pcc_email_template(is_default);
CREATE INDEX idx_pcc_email_template_lang ON pcc_email_template(language);

-- 创建序列
CREATE SEQUENCE seq_pcc_email_template START WITH 1 INCREMENT BY 1;

-- 添加注释
COMMENT ON TABLE pcc_email_template IS '邮件模板表';
COMMENT ON COLUMN pcc_email_template.id IS '主键ID';
COMMENT ON COLUMN pcc_email_template.template_code IS '模板代码';
COMMENT ON COLUMN pcc_email_template.template_name IS '模板名称';
COMMENT ON COLUMN pcc_email_template.email_type IS '邮件类型';
COMMENT ON COLUMN pcc_email_template.subject_template IS '主题模板';
COMMENT ON COLUMN pcc_email_template.content_template IS '内容模板';
COMMENT ON COLUMN pcc_email_template.variables_desc IS '模板变量说明';
COMMENT ON COLUMN pcc_email_template.language IS '语言';
COMMENT ON COLUMN pcc_email_template.status IS '状态';
COMMENT ON COLUMN pcc_email_template.is_default IS '是否默认模板';
COMMENT ON COLUMN pcc_email_template.sort_order IS '排序序号';
COMMENT ON COLUMN pcc_email_template.remark IS '备注说明';
COMMENT ON COLUMN pcc_email_template.create_user IS '创建人';
COMMENT ON COLUMN pcc_email_template.create_date IS '创建时间';
COMMENT ON COLUMN pcc_email_template.update_user IS '更新人';
COMMENT ON COLUMN pcc_email_template.update_date IS '更新时间';

-- ===========================================
-- 4. 邮件推送日志表
-- ===========================================
CREATE TABLE pcc_email_push_log (
    id NUMBER(10) PRIMARY KEY,                    -- 主键ID
    batch_id VARCHAR2(50),                       -- 批次ID（同一批发送的邮件使用相同批次ID）

    -- 邮件基本信息
    email_type VARCHAR2(50) NOT NULL,            -- 邮件类型 (QUOTE_V1, QUOTE_V2, QUOTE_V3, QUOTE_V4, QUOTE_V5, ESTIMATE_Z, ESTIMATE_ZZ, P_VERSION, UPDATE_NOTIFY)
    subject VARCHAR2(500),                       -- 邮件主题
    content CLOB,                                -- 邮件内容

    -- 收发件人信息
    from_email VARCHAR2(200),                    -- 发件人邮箱
    to_email VARCHAR2(200) NOT NULL,            -- 收件人邮箱
    cc_email VARCHAR2(1000),                    -- 抄送邮箱（多个用逗号分隔）
    bcc_email VARCHAR2(1000),                   -- 密送邮箱（多个用逗号分隔）

    -- 关联信息
    user_no VARCHAR2(20),                       -- 收件人工号
    dept_name VARCHAR2(100),                    -- 收件人部门
    business_id VARCHAR2(100),                  -- 业务ID（如订单号、报价单号等）
    business_type VARCHAR2(50),                 -- 业务类型

    -- 发送状态
    send_status VARCHAR2(20) DEFAULT 'PENDING', -- 发送状态 (PENDING-待发送, SENDING-发送中, SUCCESS-成功, FAILED-失败, RETRY-重试中)
    send_time DATE,                             -- 发送时间
    retry_count NUMBER(3) DEFAULT 0,            -- 重试次数
    max_retry NUMBER(3) DEFAULT 3,              -- 最大重试次数
    next_retry_time DATE,                       -- 下次重试时间

    -- 错误信息
    error_code VARCHAR2(50),                    -- 错误代码
    error_message VARCHAR2(2000),               -- 错误信息

    -- 附件信息
    attachment_info CLOB,                       -- 附件信息（JSON格式存储）

    -- 审计字段
    create_user VARCHAR2(20),                   -- 创建人
    create_date DATE DEFAULT SYSDATE,          -- 创建时间
    update_user VARCHAR2(20),                  -- 更新人
    update_date DATE DEFAULT SYSDATE,          -- 更新时间

    -- 约束
    CONSTRAINT chk_email_send_status CHECK (send_status IN ('PENDING', 'SENDING', 'SUCCESS', 'FAILED', 'RETRY')),
    CONSTRAINT chk_email_type CHECK (email_type IN ('QUOTE_V1', 'QUOTE_V2', 'QUOTE_V3', 'QUOTE_V4', 'QUOTE_V5', 'ESTIMATE_Z', 'ESTIMATE_ZZ', 'P_VERSION', 'UPDATE_NOTIFY'))
);

-- 创建索引
CREATE INDEX idx_pcc_email_log_batch ON pcc_email_push_log(batch_id);
CREATE INDEX idx_pcc_email_log_type ON pcc_email_push_log(email_type);
CREATE INDEX idx_pcc_email_log_status ON pcc_email_push_log(send_status);
CREATE INDEX idx_pcc_email_log_user ON pcc_email_push_log(user_no);
CREATE INDEX idx_pcc_email_log_business ON pcc_email_push_log(business_id);
CREATE INDEX idx_pcc_email_log_send_time ON pcc_email_push_log(send_time);
CREATE INDEX idx_pcc_email_log_create_date ON pcc_email_push_log(create_date);
CREATE INDEX idx_pcc_email_log_retry ON pcc_email_push_log(next_retry_time, send_status);

-- 创建序列
CREATE SEQUENCE seq_pcc_email_push_log START WITH 1 INCREMENT BY 1;

-- 添加注释
COMMENT ON TABLE pcc_email_push_log IS '邮件推送日志表';
COMMENT ON COLUMN pcc_email_push_log.id IS '主键ID';
COMMENT ON COLUMN pcc_email_push_log.batch_id IS '批次ID（同一批发送的邮件使用相同批次ID）';
COMMENT ON COLUMN pcc_email_push_log.email_type IS '邮件类型';
COMMENT ON COLUMN pcc_email_push_log.subject IS '邮件主题';
COMMENT ON COLUMN pcc_email_push_log.content IS '邮件内容';
COMMENT ON COLUMN pcc_email_push_log.from_email IS '发件人邮箱';
COMMENT ON COLUMN pcc_email_push_log.to_email IS '收件人邮箱';
COMMENT ON COLUMN pcc_email_push_log.cc_email IS '抄送邮箱';
COMMENT ON COLUMN pcc_email_push_log.bcc_email IS '密送邮箱';
COMMENT ON COLUMN pcc_email_push_log.user_no IS '收件人工号';
COMMENT ON COLUMN pcc_email_push_log.dept_name IS '收件人部门';
COMMENT ON COLUMN pcc_email_push_log.business_id IS '业务ID';
COMMENT ON COLUMN pcc_email_push_log.business_type IS '业务类型';
COMMENT ON COLUMN pcc_email_push_log.send_status IS '发送状态';
COMMENT ON COLUMN pcc_email_push_log.send_time IS '发送时间';
COMMENT ON COLUMN pcc_email_push_log.retry_count IS '重试次数';
COMMENT ON COLUMN pcc_email_push_log.max_retry IS '最大重试次数';
COMMENT ON COLUMN pcc_email_push_log.next_retry_time IS '下次重试时间';
COMMENT ON COLUMN pcc_email_push_log.error_code IS '错误代码';
COMMENT ON COLUMN pcc_email_push_log.error_message IS '错误信息';
COMMENT ON COLUMN pcc_email_push_log.attachment_info IS '附件信息';
COMMENT ON COLUMN pcc_email_push_log.create_user IS '创建人';
COMMENT ON COLUMN pcc_email_push_log.create_date IS '创建时间';
COMMENT ON COLUMN pcc_email_push_log.update_user IS '更新人';
COMMENT ON COLUMN pcc_email_push_log.update_date IS '更新时间';
