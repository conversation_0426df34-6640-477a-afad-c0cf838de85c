<!-- Outsole 弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 提示弹框
const tipPopup = ref()

// Outsole弹框
const osNoPopup = ref()

// Outsole
const osNo = inject('osNo')

// Outsole 列表
const osNoList = ref([])
// Outsole 输入框
const osNoInput = ref('')
// 是否聚焦 Outsole 输入框
const focusOsNoInput = ref(false)

// 获取 Outsole 列表
async function getOsNoList(param) {
  await uni.request({
    url: urlPrefix + '/sop/getOsNoList',
    method: 'POST',
    data: {
      brand: param
    }
  }).then(res => {
    if (res.data.code) {
      osNoList.value = res.data.data ? res.data.data : []
    } else {
      osNoList.value = []
    }
    osNoList.value.unshift('全部')
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示 Outsole 弹框
async function showOsNoPopup(param) {
  osNoInput.value = ''
  
  if (osNoList.value.length === 0) {
    uni.showLoading({
      title: '加载中...',
      mask: true
    })
    
    await getOsNoList(param)
    
    uni.hideLoading()
  }
  
  osNoPopup.value.open()
}

// 选择 Outsole
async function selectOsNo(param) {
  osNo.value = param
  osNoPopup.value.close()
}

defineExpose({
  getOsNoList,
  showOsNoPopup
})
</script>

<template>
  <uni-popup
    ref="osNoPopup"
    type="center"
    class="os-no-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择 Outsole
      </view>
      
      <view class="os-no-list">
        <view class="os-no flex-row-center">
          <input
            v-model="osNoInput"
            @focus="focusOsNoInput = true"
            @blur="focusOsNoInput = false"
            type="text"
            placeholder="请输入 Outsole"
            class="input"
            :style="{
              boxShadow: focusOsNoInput ? '0 0 5px blue' : '0 0 5px gray'
            }"
          />
          
          <view v-show="osNoInput" @click="osNoInput = ''" class="clear button">
            <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
          </view>
        </view>
        
        <view
          v-for="(item, index) in osNoList.filter(i => i.includes(osNoInput.toUpperCase())).splice(0, 50)"
          :key="index"
          class="os-no flex-row-center"
        >
          <view
            @click="selectOsNo(item)"
            class="button"
            :style="{
              color: osNo === item ? 'red' : 'black'
            }"
          >
            {{ item ? item : '/' }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.os-no-popup {
  .container {
    width: 430px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .os-no-list {
      height: 350px;
      overflow: auto;
      
      .os-no {
        width: 100%;
        padding: 10px;
        position: relative;
        
        .button {
          width: 270px;
          min-height: 50px;
          color: darkmagenta;
          font-size: 20px;
          padding: 5px;
        }
        
        .input {
          width: 270px;
          height: 50px;
          padding: 0 10px;
        }
        
        .clear {
          width: 60px;
          min-height: 40px;
          position: absolute;
          top: 15px;
          right: 85px;
        }
        
        &:first-child {
          position: sticky;
          top: 0;
          background-color: #fdf6e3;
          z-index: 1;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>