<!-- 模板导入弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 工序流程模板弹框
const flowTemplatePopup = ref()

// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getProcessFlowList = inject('getProcessFlowList')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')
// 流程下标
const flowIndex = inject('flowIndex')
// 是否自动切换流程下标
const isAutoChangeIndex = inject('isAutoChangeIndex')

// 型体
const model = ref('')
// 制程
const operation = ref('')
// 主要代码
const rtgCode = ref('')

// 模板列表
const templateList = ref([])
// 模板输入框
const templateInput = ref('')
// 是否聚焦模板输入框
const focusTemplateInput = ref(false)

// 获取模板列表
async function getTemplateList(param) {
  await uni.request({
    url: urlPrefix + '/sop/getTemplateList',
    method: 'POST',
    data: {
      operation: param
    }
  }).then(res => {
    if (res.data.code) {
      if (res.data.data) {
        let data = res.data.data
        templateList.value = []
        for (let item of data) {
          if (item.material && item.proSeq && item.typeDesc) {
            templateList.value.push({
              docNo: item.docNo,
              template: item.proSeq + ' | ' + item.typeDesc + ' | ' + item.material
            })
          }
        }
      } else {
        templateList.value = []
      }
    } else {
      templateList.value = []
      tipPopup.value.showTipPopup('warn', '暂无模板列表列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示模板弹框
async function showFlowTemplatePopup(param1, param2, param3) {
  model.value = param1
  operation.value = param2
  rtgCode.value = param3
  templateInput.value = ''
  
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await getTemplateList(operation.value)
  
  uni.hideLoading()
  
  if (templateList.value.length === 0) {
    return
  }
  
  flowTemplatePopup.value.open()
}

// 模板导入工序流程
async function templateImportFlow(param1, param2, param3, param4) {
  uni.showLoading({
    title: '导入中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/templateImportFlow',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3,
      docNo: param4,
      insUser: user,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getProcessFlowList(param1, param2, param3)
      tipPopup.value.showTipPopup('success', '导入成功！')
      flowTemplatePopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '添加失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  showFlowTemplatePopup
})
</script>

<template>
  <uni-popup
    ref="flowTemplatePopup"
    type="center"
    class="flow-template-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择模板
      </view>
      
      <view class="description flex-row-center">
        {{ operationMap.get(operation) ? operationMap.get(operation) : '未知' }} - {{ model }} - {{ rtgCode }}
      </view>
      
      <view class="flow-template-list">
        <view class="flow-template flex-row-center">
          <input
            v-model="templateInput"
            @focus="focusTemplateInput = true"
            @blur="focusTemplateInput = false"
            type="text"
            placeholder="请输入模板"
            class="input"
            :style="{
              boxShadow: focusTemplateInput ? '0 0 5px blue' : '0 0 5px gray'
            }"
          />
          
          <view v-show="templateInput" @click="templateInput = ''" class="clear button">
            <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
          </view>
        </view>
        
        <view
          v-for="(item, index) in templateList"
          :key="index"
          v-show="item.template && item.template.includes(templateInput.toUpperCase())"
          class="flow-template flex-row-center"
        >
          <view
            @click="templateImportFlow(model, operation, rtgCode, item.docNo)"
            class="button"
          >
            {{ item.template }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.flow-template-popup {
  .container {
    width: 500px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .description {
      width: 100%;
      height: 40px;
      font-size: 20px;
      font-weight: bold;
    }
    
    .flow-template-list {
      height: 460px;
      overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .flow-template {
        width: 100%;
        padding: 10px;
        position: relative;
        
        .button {
          width: 350px;
          min-height: 50px;
          color: darkmagenta;
          font-size: 20px;
          padding: 5px;
        }
        
        .input {
          width: 350px;
          height: 50px;
          padding: 0 10px;
        }
        
        .clear {
          width: 60px;
          min-height: 40px;
          position: absolute;
          top: 15px;
          right: 80px;
        }
        
        &:first-child {
          position: sticky;
          top: 0;
          background-color: #fdf6e3;
          z-index: 1;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>