# 邮件推送系统后端开发说明

## 概述

本文档说明邮件推送系统后端代码的结构、功能和使用方法。系统采用Spring Boot + MyBatis架构，支持Oracle数据库，提供完整的邮件推送权限管理和邮件发送功能。

## 代码结构

### 1. 实体类 (Entity)
位置：`src/main/java/com/zqn/modeldata2/entity/email/`

- **EmailPushPermission.java** - 邮件推送权限配置实体
- **EmailPushLog.java** - 邮件推送日志实体
- **EmailTemplate.java** - 邮件模板实体
- **EmailPushConfig.java** - 邮件推送配置实体
- **EmailPushRequest.java** - 邮件推送请求DTO
- **EmailPermissionQuery.java** - 权限查询DTO
- **EmailTypeEnum.java** - 邮件类型枚举
- **EmailSendStatusEnum.java** - 邮件发送状态枚举

### 2. Mapper接口
位置：`src/main/java/com/zqn/modeldata2/mapper/`

- **EmailPushPermissionMapper.java** - 权限配置数据访问接口
- **EmailPushLogMapper.java** - 邮件日志数据访问接口
- **EmailTemplateMapper.java** - 邮件模板数据访问接口
- **EmailPushConfigMapper.java** - 邮件配置数据访问接口

### 3. Service接口和实现
位置：`src/main/java/com/zqn/modeldata2/service/`

- **EmailPushPermissionService.java** - 权限管理服务接口
- **EmailPushService.java** - 邮件推送服务接口
- **EmailTemplateService.java** - 邮件模板服务接口
- **EmailPushConfigService.java** - 邮件配置服务接口

实现类位置：`src/main/java/com/zqn/modeldata2/service/impl/`

### 4. Controller控制器
位置：`src/main/java/com/zqn/modeldata2/controller/`

- **EmailPushPermissionController.java** - 权限管理控制器
- **EmailPushController.java** - 邮件推送控制器

### 5. Mapper XML文件
位置：`src/main/resources/mapper/`

- **EmailPushPermissionMapper.xml** - 权限配置SQL映射文件

## 主要功能

### 1. 权限管理功能

#### 查询权限列表
```http
GET /emailPushPermission/query?pageNo=1&pageSize=10&userNo=2310001100
```

#### 新增权限配置
```http
POST /emailPushPermission/add
Content-Type: application/json

{
    "userNo": "2310001100",
    "deptName": "SOP",
    "email": "<EMAIL>",
    "pushEstimateZ": "Y",
    "pushEstimateZz": "Y",
    "updateEmailNotify": "Y",
    "createUser": "ADMIN"
}
```

#### 更新权限配置
```http
POST /emailPushPermission/update
Content-Type: application/json

{
    "userNo": "2310001100",
    "pushQuoteV1": "Y",
    "updateUser": "ADMIN"
}
```

#### 检查用户权限
```http
GET /emailPushPermission/hasPermission?userNo=2310001100&emailType=QUOTE_V1
```

### 2. 邮件发送功能

#### 发送报价邮件
```http
POST /emailPush/sendQuote?quoteVersion=V1&businessId=ORDER001&sendUser=ADMIN
```

#### 发送预估邮件
```http
POST /emailPush/sendEstimate?estimateVersion=Z&businessId=ORDER001&sendUser=ADMIN
```

#### 发送P版邮件
```http
POST /emailPush/sendPVersion?businessId=ORDER001&sendUser=ADMIN
```

#### 发送更新通知邮件
```http
POST /emailPush/sendUpdateNotify?businessId=ORDER001&sendUser=ADMIN
```

#### 自定义邮件发送
```http
POST /emailPush/send
Content-Type: application/json

{
    "emailType": "QUOTE_V1",
    "businessId": "ORDER001",
    "templateVariables": {
        "businessId": "ORDER001",
        "userName": "张三",
        "deptName": "SOP",
        "currentDate": "2025-01-22"
    },
    "sendUser": "ADMIN"
}
```

### 3. 日志查询功能

#### 查询邮件发送日志
```http
GET /emailPush/queryLogs?pageNo=1&pageSize=10&emailType=QUOTE_V1&sendStatus=SUCCESS
```

#### 根据业务ID查询日志
```http
GET /emailPush/getLogsByBusinessId?businessId=ORDER001
```

#### 获取发送统计
```http
GET /emailPush/getStatistics?emailType=QUOTE_V1&startDate=2025-01-01&endDate=2025-01-31
```

## 邮件类型说明

系统支持以下邮件类型：

| 邮件类型 | 代码 | 说明 |
|---------|------|------|
| 推送報價1版 | QUOTE_V1 | 报价第1版本邮件 |
| 推送報價2版 | QUOTE_V2 | 报价第2版本邮件 |
| 推送報價3版 | QUOTE_V3 | 报价第3版本邮件 |
| 推送報價4版 | QUOTE_V4 | 报价第4版本邮件 |
| 推送報價5版 | QUOTE_V5 | 报价第5版本邮件 |
| 推送預估Z版 | ESTIMATE_Z | 预估Z版本邮件 |
| 推送預估ZZ版 | ESTIMATE_ZZ | 预估ZZ版本邮件 |
| 推送P版 | P_VERSION | P版本邮件 |
| 更新版本邮件提醒 | UPDATE_NOTIFY | 版本更新通知邮件 |

## 权限状态说明

- **Y** - 有权限，可以接收对应类型的邮件推送
- **N** - 无权限，不会接收对应类型的邮件推送
- **P** - 待定，需要进一步确认权限状态

## 发送状态说明

- **PENDING** - 待发送
- **SENDING** - 发送中
- **SUCCESS** - 发送成功
- **FAILED** - 发送失败
- **RETRY** - 重试中

## 开发注意事项

### 1. 数据库配置
- 确保Oracle数据库连接配置正确
- 执行SQL脚本创建表结构和初始化数据
- 配置MyBatis的Oracle方言

### 2. 事务管理
- 重要的数据操作已添加`@Transactional`注解
- 批量操作支持事务回滚

### 3. 异常处理
- 所有Service方法都包含异常处理
- 返回统一的R对象格式
- 记录详细的错误日志

### 4. 参数验证
- 使用`@Validated`和`@NotBlank`等注解进行参数验证
- 在Service层进行业务逻辑验证

### 5. 分页查询
- 使用PageHelper进行分页
- 支持动态条件查询

## 已完成的功能

### 1. 完整的Service实现
- ✅ `EmailPushPermissionServiceImpl` - 权限管理服务完整实现
- ✅ `EmailPushServiceImpl` - 邮件推送服务完整实现
- ✅ `EmailTemplateServiceImpl` - 邮件模板服务完整实现
- ✅ `EmailPushConfigServiceImpl` - 邮件配置服务完整实现
- ✅ `EmailSendServiceImpl` - 实际邮件发送服务实现

### 2. 完整的Mapper XML
- ✅ `EmailPushPermissionMapper.xml` - 权限配置SQL映射
- ✅ `EmailPushLogMapper.xml` - 邮件日志SQL映射
- ✅ `EmailTemplateMapper.xml` - 邮件模板SQL映射
- ✅ `EmailPushConfigMapper.xml` - 邮件配置SQL映射

### 3. 完整的Controller
- ✅ `EmailPushPermissionController` - 权限管理控制器
- ✅ `EmailPushController` - 邮件推送控制器
- ✅ `EmailTemplateController` - 邮件模板控制器
- ✅ `EmailPushConfigController` - 邮件配置控制器

### 4. JavaMail集成
- ✅ Spring Boot Mail Starter集成
- ✅ 实际的SMTP邮件发送功能
- ✅ 动态SMTP配置管理
- ✅ 邮件配置测试功能
- ✅ 邮箱地址格式验证
- ✅ 支持HTML邮件内容
- ✅ 支持抄送和密送
- ✅ 支持多种邮件服务商（Gmail、Outlook、企业邮箱等）

### 5. 定时任务
- ✅ `EmailRetryTask` - 自动重试失败邮件
- ✅ 自动清理历史邮件日志

## 核心特性

### 1. 邮件发送功能
- **多种邮件类型**: 支持9种不同的邮件类型
- **权限控制**: 基于用户权限的精确邮件推送
- **模板渲染**: 支持变量替换的邮件模板
- **批量发送**: 支持批量邮件发送
- **重试机制**: 自动重试失败的邮件

### 2. 配置管理
- **SMTP配置**: 完整的SMTP服务器配置管理
- **配置缓存**: 提高配置读取性能
- **配置测试**: 实时测试SMTP连接
- **配置导入导出**: 支持配置的批量管理

### 3. 模板管理
- **多语言支持**: 支持中文、英文、越南文等
- **模板渲染**: 支持变量替换和HTML内容
- **模板验证**: 验证模板语法正确性
- **模板复制**: 快速复制现有模板

### 4. 日志监控
- **完整日志**: 记录邮件发送的完整过程
- **状态跟踪**: 实时跟踪邮件发送状态
- **统计分析**: 提供邮件发送统计信息
- **日志清理**: 自动清理历史日志

## 扩展开发

### 1. 新增邮件类型
1. 在`EmailTypeEnum`中添加新的枚举值
2. 在权限表中添加对应的权限字段
3. 更新Mapper XML中的权限检查逻辑
4. 在Service中添加对应的发送方法

### 2. 新增模板变量
1. 在邮件模板中定义新的变量占位符
2. 在发送邮件时传入对应的变量值
3. 更新模板变量说明文档

### 3. 集成模板引擎
1. 集成Thymeleaf或FreeMarker模板引擎
2. 支持更复杂的模板语法
3. 支持条件判断和循环
4. 支持模板继承

### 4. 增强安全性
1. 添加邮件内容XSS防护
2. 实现邮件发送频率限制
3. 添加邮件内容审核机制
4. 支持邮件加密

## 测试建议

1. **单元测试** - 为Service层编写单元测试
2. **集成测试** - 测试Controller和数据库交互
3. **邮件发送测试** - 配置测试邮箱验证邮件发送功能
4. **权限测试** - 验证权限控制逻辑的正确性
5. **性能测试** - 测试批量邮件发送性能
6. **并发测试** - 测试高并发场景下的系统稳定性

## 部署说明

### 1. 数据库准备
```sql
-- 执行表结构创建脚本
@sql/install_email_push_system.sql

-- 执行初始化数据脚本
@sql/pcc_email_push_init_data.sql

-- 执行邮件配置初始化脚本
@sql/init_email_config.sql
```

### 2. Maven依赖
确保 `pom.xml` 中包含JavaMail依赖：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-mail</artifactId>
</dependency>
```

### 3. 应用配置
```yaml
# application.yml 添加邮件相关配置
spring:
  task:
    scheduling:
      enabled: true  # 启用定时任务
```

### 4. SMTP配置
通过管理界面或API配置SMTP参数：

```bash
# 更新SMTP配置
POST /emailPushConfig/updateSmtpConfig
Content-Type: application/json

{
    "host": "smtp.gmail.com",
    "port": "587",
    "username": "<EMAIL>",
    "password": "your-app-password",
    "auth": "true",
    "starttls": "true",
    "ssl": "false"
}

# 测试SMTP连接
POST /emailPushConfig/testSmtpConnection

# 发送测试邮件
POST /emailPush/testConfig?testEmail=<EMAIL>
```

### 5. 权限初始化
1. 导入用户权限配置数据
2. 配置邮件模板
3. 测试邮件发送功能

### 6. 监控配置
1. 配置日志监控
2. 设置邮件发送告警
3. 监控系统性能指标

## 性能优化建议

1. **配置缓存**: 已实现配置缓存，减少数据库查询
2. **批量处理**: 支持批量邮件发送，提高效率
3. **异步发送**: 可考虑使用消息队列实现异步邮件发送
4. **连接池**: 配置数据库连接池优化数据库性能
5. **索引优化**: 确保数据库表有合适的索引
