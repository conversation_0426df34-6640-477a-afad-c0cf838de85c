<template>
  <view class="image-popup" v-if="show" @click="handleBackdropClick">
    <view class="popup-content" @click.stop>
      <view class="image-container" :style="containerStyle">
        <image 
          :src="imageUrl" 
          :style="imageStyle" 
          mode="aspectFit" 
          class="popup-image"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
        />
      </view>
      <view class="controls">
        <view class="control-btn" @click="zoomIn">+</view>
        <view class="control-btn" @click="zoomOut">-</view>
        <view class="control-btn" @click="rotateLeft">↺</view>
        <view class="control-btn" @click="rotateRight">↻</view>
        <view class="control-btn close" @click="close">×</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ImagePopup',
  props: {
    show: {
      type: <PERSON>olean,
      default: false
    },
    imageUrl: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '90%'
    },
    height: {
      type: String,
      default: '80%'
    },
    closeOnBackdrop: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      scale: 1,
      rotation: 0,
      lastTouchDistance: 0,
      isDragging: false,
      startX: 0,
      startY: 0,
      translateX: 0,
      translateY: 0
    };
  },
  computed: {
    containerStyle() {
      return {
        width: this.width,
        height: this.height
      };
    },
    imageStyle() {
      return {
        transform: `rotate(${this.rotation}deg) scale(${this.scale}) translate(${this.translateX}px, ${this.translateY}px)`
      };
    }
  },
  methods: {
    close() {
      this.$emit('update:show', false);
      this.resetTransform();
    },
    handleBackdropClick() {
      if (this.closeOnBackdrop) {
        this.close();
      }
    },
    zoomIn() {
      this.scale = Math.min(this.scale + 0.2, 3);
    },
    zoomOut() {
      this.scale = Math.max(this.scale - 0.2, 0.5);
    },
    rotateLeft() {
      this.rotation = (this.rotation - 90) % 360;
    },
    rotateRight() {
      this.rotation = (this.rotation + 90) % 360;
    },
    resetTransform() {
      this.scale = 1;
      this.rotation = 0;
      this.translateX = 0;
      this.translateY = 0;
    },
    // 触摸事件处理
    handleTouchStart(event) {
      if (event.touches.length === 2) {
        // 双指触摸，计算初始距离用于缩放
        this.lastTouchDistance = this.getTouchDistance(event.touches);
      } else if (event.touches.length === 1) {
        // 单指触摸，准备拖动
        this.isDragging = true;
        this.startX = event.touches[0].clientX - this.translateX;
        this.startY = event.touches[0].clientY - this.translateY;
      }
    },
    handleTouchMove(event) {
      if (event.touches.length === 2) {
        // 双指移动，处理缩放
        const currentDistance = this.getTouchDistance(event.touches);
        const delta = currentDistance - this.lastTouchDistance;
        
        if (Math.abs(delta) > 10) {
          // 缩放系数
          const scaleFactor = 0.01;
          this.scale = Math.max(0.5, Math.min(3, this.scale + (delta * scaleFactor)));
          this.lastTouchDistance = currentDistance;
        }
      } else if (event.touches.length === 1 && this.isDragging) {
        // 单指移动，处理拖动
        this.translateX = event.touches[0].clientX - this.startX;
        this.translateY = event.touches[0].clientY - this.startY;
      }
    },
    handleTouchEnd() {
      this.isDragging = false;
      this.lastTouchDistance = 0;
    },
    getTouchDistance(touches) {
      // 计算两个触摸点之间的距离
      const dx = touches[1].clientX - touches[0].clientX;
      const dy = touches[1].clientY - touches[0].clientY;
      return Math.sqrt(dx * dx + dy * dy);
    }
  }
}
</script>

<style>
.image-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.popup-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80%;
  max-width: 80%;
  max-height: 80%;
}

.image-container {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  aspect-ratio: 1/1;
}

.popup-image {
  width: 100%;
  height: 100%;
  transition: transform 0.1s ease;
  will-change: transform;
  object-fit: cover;
}

.controls {
  display: flex;
  margin-top: 20px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 30px;
  padding: 5px;
}

.control-btn {
  width: 40px;
  height: 40px;
  background-color: rgba(25, 118, 210, 0.8);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  margin: 0 5px;
  cursor: pointer;
  user-select: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.control-btn:active {
  transform: scale(0.95);
  background-color: rgba(13, 71, 161, 0.8);
}

.control-btn.close {
  background-color: rgba(211, 47, 47, 0.8);
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
  .popup-content {
    width: 90%;
    max-width: 90%;
    max-height: 90%;
  }
  
  .controls {
    margin-top: 10px;
  }
  
  .control-btn {
    width: 36px;
    height: 36px;
    font-size: 18px;
    margin: 0 3px;
  }
}

/* 横屏模式 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .popup-content {
    flex-direction: row;
    width: 90%;
    max-height: 80%;
  }
  
  .image-container {
    width: 80%;
    height: 80%;
  }
  
  .controls {
    flex-direction: column;
    margin-top: 0;
    margin-left: 10px;
    height: 80%;
    justify-content: center;
  }
  
  .control-btn {
    margin: 5px 0;
  }
}
</style> 