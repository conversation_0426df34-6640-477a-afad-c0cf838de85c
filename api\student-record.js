import request from '@/utils/request';

// 获取学员记录列表（支持分页和筛选）
export function getStudentRecords(params = {}) {
  // 设置默认分页参数
  const defaultParams = {
    page: 1,
    size: 100, // 前端暂时使用较大的页面大小来获取所有数据
    ...params
  };
  
  return request({
    url: '/student-record',
    method: 'get',
    params: defaultParams
  });
}

// 获取单个学员记录详情
export function getStudentRecord(id) {
  return request({
    url: `/student-record/${id}`,
    method: 'get'
  });
}

// 创建学员记录
export function createStudentRecord(data) {
  return request({
    url: '/student-record',
    method: 'post',
    data
  });
}

// 更新学员记录
export function updateStudentRecord(data) {
  return request({
    url: `/student-record/${data.id}`,
    method: 'put',
    data
  });
}

// 删除学员记录
export function deleteStudentRecord(id) {
  return request({
    url: `/student-record/${id}`,
    method: 'delete'
  });
}

// 根据单号获取鞋图
export function getShoeImageByOrderNo(orderNo) {
  return request({
    url: '/student-record/shoe-image',
    method: 'get',
    params: { orderNo }
  });
}

// 上传图片
export function uploadImage(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request({
    url: '/student-record/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 批量上传图片
export function uploadMultipleImages(files) {
  const formData = new FormData();
  files.forEach((file, index) => {
    formData.append(`files[${index}]`, file);
  });
  return request({
    url: '/student-record/upload-multiple',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 获取二维码扫描结果对应的数据
export function getDataByQRCode(qrCodeData) {
  return request({
    url: '/student-record/qrcode-data',
    method: 'get',
    params: { qrCodeData }
  });
}

// 导出学员记录Excel
export function exportStudentRecordsExcel(params = {}) {
  return request({
    url: '/student-record/export/excel',
    method: 'get',
    params,
    responseType: 'blob' // 重要：设置响应类型为blob以处理文件下载
  });
}