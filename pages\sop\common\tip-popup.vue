<!-- 提示弹框 -->
<script setup>
import { ref } from 'vue'

// 提示弹框
const tipPopup = ref()
// 提示类型
const tipType = ref('')
// 提示消息
const tipMessage = ref('')

// 显示提示弹框
function showTipPopup(type, message) {
  tipType.value = type
  tipMessage.value = message
  tipPopup.value.open()
}

defineExpose({
  showTipPopup
})
</script>

<template>
  <uni-popup
    ref="tipPopup"
    type="message"
    class="tip-popup"
  >
    <uni-popup-message :type="tipType" :message="tipMessage" :duration="2000"></uni-popup-message>
  </uni-popup>
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.tip-popup {
  pointer-events: none;
  
  &:deep(.fixforpc-width) {
    min-width: 0;
    padding: 10px 20px;
    margin-top: calc(2.5% + 5px);
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
  }
  
  &:deep(.uni-popup-message-text) {
    font-size: 20px;
    font-weight: bold;
  }
}
</style>