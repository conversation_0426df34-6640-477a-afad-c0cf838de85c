<template>
	<view class="content">
		<view id="box">

			<view id="top" class="">
				<view class="top_le"></view>
				<view class="top_ri"></view>
			</view>
			<view class="title">
				<view>STELLA Manage</view>
				<view>
					<image class="titImg" src="../../static/stellalogoTm.png"></image>
				</view>
			</view>
			<view class="inp">
				<text>{{ $t('工号') }}</text>
				<input type="text" :placeholder="$t('请输入工号')" v-model="userName" />
			</view>
			<view class="inp">
				<text>{{ $t('密码') }}</text>
				<input type="password" :placeholder="$t('请输入密码')" v-model="passWord" />
			</view>
			<view class="but">
				<button type="default" @tap="all_ok">{{ $t('登录') }}</button>
			</view>

			<view class="fot">
				<text>{{vesion}}</text>
				<text style="display: inline-block;width: 30vw;"></text>
				<text @tap="dialogToggle">{{ $t('忘记密码') }}</text>
			</view>
      
      <view class="language">
        <view @click="changeLanguage()" class="button">
          {{ languageStore.language }}
        </view>
      </view>
		
				<uni-popup ref="alertDialog" type="dialog">
					<uni-popup-dialog :title="$t('消息')" :content="`${$t('忘记密码可致电：')}0769-38938288 - 5311${$t('联系管理员')}`"
						></uni-popup-dialog>
				</uni-popup>
				
			
		</view>

	</view>
</template>

<script>
  import urlPrefix from '@/pages/common/urlPrefix.js'
  import { useLanguageStore } from '@/stores/common.js'
	export default {
		data() {
      const languageStore = useLanguageStore()
      
			return {
        languageStore,
				title: this.$t('消息'),
				savePwd: true,
				userName:uni.getStorageSync("loginUserName"),
				passWord:uni.getStorageSync("loginPassWord"),
				vesion:""
			}
		},
		onLoad() { 
			plus.runtime.getProperty(plus.runtime.appid, (inf) => {
				const versionNO=inf.version//当前版本号
				this.vesion=this.$t('版本：')+versionNO
				//模拟接口获取最新版本号，版本号固定为整数
				uni.request({
				   // url: "http://***********:8200/update",  //
				   url: urlPrefix + "/update",  //
				   method: "GET"
				 }).then(res => {
					const jsonObj=res.data.data
					const newVersionName = jsonObj.versionName //线上最新版本名
					const newVersionCode = jsonObj.versionCode; //线上最新版本号
					const selfVersionCode = Number(uni.getSystemInfoSync().appVersionCode) //当前App版本号
					jsonObj["versionNow"]=versionNO;
					// uni.showToast({
					// 	title:(versionNO !== newVersionName),
					// 	icon:'none'
					// });
					//线上版本号高于当前，进行在线升级
					if (versionNO !== newVersionName) {
						let platform = uni.getSystemInfoSync().platform //手机平台
						//安卓手机弹窗升级
						if (platform === 'android') {
							uni.navigateTo({
								url: 'upgrade?items='+encodeURIComponent(JSON.stringify(jsonObj))
							})
						}
						//IOS无法在线升级提示到商店下载
						else {
							uni.showModal({
								title: this.$t('发现新版本') + ' ' + newVersionName,
								content: this.$t('请到App store进行升级'),
								showCancel: false
							})
						}
						
					}else{
						//保存密码自动登陆
						if(this.passWord!==""){
							this.all_ok()
						}
					}
					
				 }).catch(err => {
					console.log(err)
				 })
				
				 
				
			});
			
		},
		methods: {
			// 登录
			all_ok() {
			    //登录清空工厂定位
                uni.setStorageSync("currentFactory","");

				if(this.userName==""||this.passWord==""){
					uni.showToast({
						title:this.$t('工号或密码不可为空！'),
						icon:'none'
					});
					return;
				}
				uni.showLoading({
					title: this.$t('登录中...')
				});
				uni.request({
				   // url: "http://192.168.131.125:8080/WarehouseBackStage/UserInfo/GetUserInfo",
				   // method: "POST",
				   // data:{
				   // 	'account':this.userName,
				   // 	'password':this.passWord
				   // }
          url: urlPrefix + "/login/login",
          method: "POST",
          data: {
            "userId": this.userName,
            "password": this.passWord
          }
				 }).then(res => {
					 // const jsonObj=JSON.parse(res.data);
					 const jsonObj = res.data.data;
					
					 // if(jsonObj.ROW_COUNT == "0") {
					 if(!jsonObj || jsonObj.row_count == "0") {
						 uni.showToast({
								title:this.$t('工号或密码输入错误，请重新输入！'),
								icon:'none'
						 });
					 	return;
					 }
					 // if(jsonObj.INV_FLAG == "Y") {
					 if(jsonObj.inv_flag == "Y") {
						 uni.showToast({
								title:this.$t('用户已被停用，请联系管理员！'),
								icon:'none'
						 });
					 	return;
					 }else{
						uni.showToast({
							// title:'欢迎登陆-'+jsonObj.USER_DESC,
							title:this.$t('欢迎登录') + '-' +jsonObj.user_desc,
							icon:'success'
						});
						uni.setStorageSync("loginUserName",this.userName);
						uni.setStorageSync("loginPassWord",this.passWord);
						//存工厂信息20241008
                         uni.setStorageSync("currentFactory",res.data.data.factory)

						// uni.setStorageSync("loUserDesc",jsonObj.USER_DESC);
						// uni.setStorageSync("loUserNo",jsonObj.USER_NO);
						uni.setStorageSync("loUserDesc",jsonObj.user_desc);
						uni.setStorageSync("loUserNo",jsonObj.user_no);
						uni.navigateTo({
							url: '/pages/menu/menu'
						}); 
					 }

					 uni.hideLoading();
					
				 }).catch(err => {
				   uni.showToast({
						title:this.$t('登录超时或网络异常，请稍后重试！'),
						icon:'none'
				   });
				   console.log(err)
				 })
			},
			dialogToggle() {			// 忘记密码弹窗
				this.$refs.alertDialog.open()
			 },
			 //检查版本更新情况
			 checkVersion(versionIn) {
				 console.log(versionIn)
			 },
      changeLanguage() {
        if (this.languageStore.language === '简体中文') {
          this.languageStore.changeLanguage('Tiếng Việt', 'vi')
        } else if (this.languageStore.language === 'Tiếng Việt') {
          this.languageStore.changeLanguage('简体中文', 'zh_cn')
        }
      }
		}
	}
</script>

<style lang="scss" scoped>
	#box {
		position: relative;
		// color: $u-content-color;
	}

	.title {
		position: absolute;
		top: 130rpx;
		width: 100vw;
		text-align: center;
		font-size: 28rpx;

	}
	
	.titImg{
		width: 180rpx;
		height: 180rpx;
	}
	
	.title view:nth-child(1) {
		height: 54rpx;
		font-size: 56rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #198BFA;
		text-shadow: 0rpx 2rpx 24rpx rgba(58, 199, 118, 0.4);

	}

	.title view:nth-child(2) {
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #198BFA;
		margin-top: 40rpx;
	}



	#top {
		width: 100vw;
		height: 40vh;
		position: relative;
	}

	.top_ri {
		width: 100vw;
		height: 100vw;
		background: linear-gradient(141deg, #19C0FA 0%, #198BFA 100%);
		opacity: 0.1;
		border-radius: 50%;
		position: absolute;
		right: -49vw;
		top: -44.5vw;
	}

	.top_le {
		width: 100vw;
		height: 100vw;
		background: linear-gradient(141deg, #19C0FA 0%, #198BFA 100%);
		opacity: 0.1;
		border-radius: 50%;
		position: absolute;
		left: -34vw;
		top: -54vw;
	}

	.inp {
		padding-left: 5vw;
		position: relative;
		top: -4vh;

	}

	.inp text {
		font-size: 36rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #333333;
		margin-left: 5px;
	}

	input {
		width: 90vw;
		height: 96rpx;
		background: #FFFFFF;
		border: 1rpx solid #f4f4f4;
		box-shadow: 0px 2rpx 24rpx 0px rgba(0, 0, 0, 0.09);
		border-radius: 20rpx;
		margin-top: 10px;
		margin-bottom: 10px;
		text-indent: 20px;


		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #8E8E8E;
	}

	.but button {
		width: 619rpx;
		height: 92rpx;
		background: linear-gradient(90deg, #1989FA, #19C2FA);
		box-shadow: 0px 2px 24px 0px rgba(25, 137, 250, 0.4);
		border-radius: 46px;
		color: #FFFFFF;
		font-size: 40rpx;
		line-height: 92rpx;
	}

	.fot {
		width: 100vw;
		height: 26px;
		text-align: center;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #8E8E8E;
		margin-top: 15px;
	}
	
	.fot2 {
		width: 100vw;
		height: 25px;
		text-align: center;
		font-size: 20rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #8E8E8E;
		margin-top: 10px;
	}
  
  .language {
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .button {
      padding: 10px 30px;
      border-radius: 10px;
      box-shadow: 0 0 5px gray;
      font-size: 18px;
      font-weight: bold;
      transition: all 0.1s linear;
      z-index: 1;
      
      &:active {
        box-shadow: 0 0 2px gray;
        transform: scale(0.98);
      }
      
      /* #ifdef WEB */
      cursor: pointer;
      
      &:hover {
        background-color: #ddd;
      }
      /* #endif */
      
      /* #ifdef APP */
      &:active {
        background-color: #ddd;
      }
      /* #endif */
    }
  }
</style>
