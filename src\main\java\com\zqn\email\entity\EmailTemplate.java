package com.zqn.email.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件模板实体类
 * @date 2025/01/22 10:00
 */
@Data
public class EmailTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 模板代码
     */
    private String templateCode;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 邮件类型 (QUOTE_V1, QUOTE_V2, QUOTE_V3, QUOTE_V4, QUOTE_V5, ESTIMATE_Z, ESTIMATE_ZZ, P_VERSION, UPDATE_NOTIFY)
     */
    private String emailType;

    /**
     * 主题模板（支持变量替换）
     */
    private String subjectTemplate;

    /**
     * 内容模板（支持变量替换，HTML格式）
     */
    private String contentTemplate;

    /**
     * 模板变量说明（JSON格式）
     */
    private String variablesDesc;

    /**
     * 语言 (zh_CN-简体中文, zh_TW-繁体中文, en_US-英文, vi_VN-越南文)
     */
    private String language;

    /**
     * 状态 (Y-启用, N-禁用)
     */
    private String status;

    /**
     * 是否默认模板 (Y-是, N-否)
     */
    private String isDefault;

    /**
     * 排序序号
     */
    private Integer sortOrder;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}
