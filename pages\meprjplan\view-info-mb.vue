<script setup>
import {reactive, ref, computed} from 'vue'
import {onLoad,onShow} from '@dcloudio/uni-app'
import urlPrefix from '@/pages/common/urlPrefix.js'
import UvImage from "../../uni_modules/uv-image/components/uv-image/uv-image.vue";

const user = uni.getStorageSync("loUserNo")
const info = ref()

const inputCountNum = ref(1)
const inputCount = ref([{id: uniqueId(), text: ''}])
const items = ref([])
const versions = ref([])

const imageListMain1 = ref([])
const imageListMain2 = ref([])
const videoListMain = ref([])
const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')
const preViewPicture = ref();
const picturePopup = ref();
const videoPopup = ref();

const modelPicture = ref('')
const addPartDetail = reactive({
    id:null,
    brand: "",
    model_no: "",
    shoe_last: "",
    factory:"",
    dept: "",
    deptType: 1,
	shoe_make_head: '', //制鞋排头
	printmaker: '', //版师
	senior_technician: '', //高级技师
    create_date: "",
    create_by: "",
    pccMeProjectPlanDts: {
        item_no: 1,
        op_std: "", //操作标准
        self_check_points: "",//自检点
        op_std_en: "", //操作标准
        self_check_points_en: "",//自检点
        op_std_vn: "", //操作标准
        self_check_points_vn: "",//自检点
        op_std_id: "", //操作标准
        self_check_points_id: "",//自检点
        op_std_bd: "", //操作标准
        self_check_points_bd: "",//自检点
        op_std_ph: "", //操作标准
        self_check_points_ph: "",//自检点
        tools: "",//工具
        imgUrls: [],
        actions: "",//动作
        machine: "",//机器
        chemical_substance: "",//化学品
        temp: "",//温度
        time: "",//时间
        pressure: "",//压力
        glue: "",//胶水
        car_line: "",//车线
        margin: "",//边距
        needle_spacing: "",//针距
        spacing: "",//间距
        needle: "",//车针
        img_tit1: "",//图片备注
        img_tit2: "",//图片备注
        create_by: user,
        update_by: user
    }
})

// 创建一个计算属性来决定类名
const computedClass = computed(() => {
    return addPartDetail.factory === 'FS' ? 'watermark2' : 'watermark';
});

// 创建一个计算属性来决定类名
const popcomputedClass = computed(() => {
    return addPartDetail.factory === 'FS' ? 'watermark2' : 'watermark';
});

function picturePreView(img_url) {
    preViewPicture.value = img_url;
    picturePopup.value.open();
}

const tempUrl = ref('');
function openVideoPop(url){
	tempUrl.value = url;
	videoPopup.value.open();
}

// 获取鞋图
function getPicture() {
    uni.request({
        url: urlPrefix + "/process/getPicture",
        method: "POST",
        data: {
            model_no: addPartDetail.model_no
        }
    }).then(res => {
        modelPicture.value = res.data.data.model_pic ? res.data.data.model_pic : ""
    }).catch(err => {
        uni.showToast({
            icon: "error",
            title: "请检查网络！"
        })
    })
}

// 返回上一页
function back() {
    info.value = ''
    let back = getCurrentPages();
    if(back && back.length > 1) {
    	uni.navigateBack({  
    	  delta: 1  
    	});  
    } else {  
    	history.back();  
    }
}


// 上一项次
function last() {
    // 找到当前元素的索引
    const currentIndex = items.value.findIndex(item => item.value === addPartDetail.pccMeProjectPlanDts.item_no);
    // 如果当前索引大于0，则递减索引并更新当前元素的值
    if (currentIndex > 0) {
        addPartDetail.pccMeProjectPlanDts.item_no = items.value[currentIndex - 1].value;
        changeItem();
    } else if (currentIndex == 0) {
        addPartDetail.pccMeProjectPlanDts.item_no = items.value[currentIndex].value;
        changeItem();
    }
}

// 下一项次
function next() {
    // 找到当前元素的索引
    const currentIndex = items.value.findIndex(item => item.value === addPartDetail.pccMeProjectPlanDts.item_no);
    // 如果当前索引大于0，则递减索引并更新当前元素的值
    if (currentIndex > -1 && addPartDetail.pccMeProjectPlanDts.item_no < items.value[items.value.length - 1].value) {
        addPartDetail.pccMeProjectPlanDts.item_no = items.value[currentIndex + 1].value;
        changeItem();
    }
}

function changeItem() {
    getData(addPartDetail.model_no, addPartDetail.pccMeProjectPlanDts.item_no, '', addPartDetail.dept,addPartDetail.id);
}

function changeVersion(e) {
    getData(addPartDetail.model_no, addPartDetail.pccMeProjectPlanDts.item_no, addPartDetail.pccMeProjectPlanDts.version, addPartDetail.dept,addPartDetail.id);
}

//增加输入框
function addInput() {
    if (inputCount.value.length > 9) {
        showTip('error', '最多创建10条！')
    } else {
        inputCount.value.push({id: uniqueId(), text: ''});
    }
}

function uniqueId() {
    // 简单的 ID 生成器，每次调用递增
    return inputCountNum.value++;
}

async function getData(model_no, itemNo, version, dept,parentId) {
    await uni.request({
        url: urlPrefix + "/pccmeprjplandt/queryByItem",
        data: {
            "modelNo": model_no,
            "item": itemNo,
            "version": version,
            "dept": dept,
            parentId:parentId
        },
        method: "GET"
    }).then(res => {
		addPartDetail.shoe_make_head = res.data.data.shoe_make_head;
		addPartDetail.printmaker = res.data.data.printmaker;
		addPartDetail.senior_technician = res.data.data.senior_technician;
		
        addPartDetail.pccMeProjectPlanDts = res.data.data;
        addPartDetail.create_by = res.data.data.create_by;
        addPartDetail.create_date = res.data.data.create_date;

        let a = new Array;
        let b = new Array;
        for (let i = 0; i < res.data.data.imgUrls.length; i++) {
            if (res.data.data.imgUrls[i].type == 1) {
                a.push({"name": 'test.png', "extname": 'picture', "url": res.data.data.imgUrls[i].img_url})
            } else {
                b.push({"name": 'test.png', "extname": 'picture', "url": res.data.data.imgUrls[i].img_url})
            }
        }
        imageListMain1.value = a;
        imageListMain2.value = b;
		videoListMain.value = res.data.data.videoUrls;
        items.value = res.data.data.items;
        versions.value = res.data.data.versions;
    }).catch(err => {
        uni.showToast({
            title: '获取数据失败..' + err,
            icon: "error"
        });
    })

    getPicture();
}

function getDepartmentName(dept) {
    return dept.includes('-') ? dept.split('-')[1] : dept;
}

const customStyle = computed(() => ({
	width: '20vw',
	background: '#3c9cff',
	color: '#fff',
	borderRadius: '40rpx',
	// nvue中必须是下方的写法
	'border-top-right-radius': '40rpx',
	'border-bottom-left-radius': '40rpx',
	'border-bottom-right-radius': '40rpx'
}));

//登录校验
function loginCheck(){
	if(uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == ''){
		uni.navigateTo({
			url: `/pages/login/login`,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}
}

onLoad(async (props) => {
    info.value = JSON.parse(props.info);
    addPartDetail.id = info.value.id;
    addPartDetail.model_no = info.value.model_no;
    addPartDetail.shoe_last = info.value.shoeLast;
    addPartDetail.brand = info.value.brand;
    addPartDetail.pccMeProjectPlanDts.item_no = info.value.item_no;
    addPartDetail.factory = info.value.factory
    addPartDetail.dept = info.value.dept;
    info.value.ins_user = user;
    info.value.upd_user = user;
    await getData(info.value.model_no, info.value.item_no, '', info.value.dept,info.value.id);
})

onShow(async (props) => {
	loginCheck();
})
</script>

<template>
    <view class="add-info">
        <view class="back">
            <uni-icons @click="back()" type="back" size="36" color="#6fa2ce"></uni-icons>
        </view>
        <view class="title">
            <text class="title-text">{{ getDepartmentName(addPartDetail.dept) }}作业流程 Operation process</text>
            <uni-section title="版本" titleFontSize="20px" type="line" class="title-part-attribute">
                <uni-data-select v-model="addPartDetail.pccMeProjectPlanDts.version" :localdata="versions"
                                 :clear="false"
                                 style="width: 50px;margin-left: 5px;background-color: #fdf6e3;"
                                 @change="changeVersion"></uni-data-select>
            </uni-section>
            <uni-section title="项次" titleFontSize="20px" type="line" class="title-part-attribute2">
                <uni-data-select v-model="addPartDetail.pccMeProjectPlanDts.item_no" :localdata="items"
                                 :clear="false"
                                 style="width: 50px;margin-left: 0px;background-color: #fdf6e3;"
                                 @change="changeItem"></uni-data-select>
            </uni-section>
            <view class="picture">
                <view class="watermark">
                    <img
                        :src="'data:image/jpg;base64,' + modelPicture"
                        alt=""
                        class="img"
                    />
                </view>
            </view>
        </view>

        <view class="data">
            <view>
                <table class="demo-table">
                    <!-- 表头 -->
                    <tr>
                        <th class="title-th" colspan="2">客户 Brand:{{ addPartDetail.brand }}</th>
                        <th class="title-th" colspan="3">
                            Style:{{ addPartDetail.model_no }}/{{ addPartDetail.shoe_last }}
                        </th>
                        <th class="title-th" colspan="3">日期 Date:{{ addPartDetail.create_date }}</th>
                    </tr>

					<tr>
                        <th class="demo-th">项次 No.</th>
                        <th class="demo-th">流程步骤 Process</th>
                        <th class="demo-th" colspan="2">操作标准 The Operation Standard</th>
                        <th class="demo-th">工具 Tool</th>
						<th class="demo-th">胶水 glue</th>
						<th class="demo-th">机器<br>machine</th>
						<th class="demo-th">车线 thread</th>
                    </tr>
                    <!-- 内容行 -->
					<tr>
						<td class="right-td" rowspan="3">{{ addPartDetail.pccMeProjectPlanDts.item_no }}</td>
						<td class="process-td" rowspan="3">{{ addPartDetail.pccMeProjectPlanDts.actions }}</td>
						<td style="padding: 0px;" class="content-td" colspan="2" rowspan="3">
							<view class="vertical-align">
								<view style="padding-left: 10px;">
									<text>{{ addPartDetail.pccMeProjectPlanDts.op_std }}</text><br>
									<text>{{ addPartDetail.pccMeProjectPlanDts.op_std_en }}</text><br>
									<text>{{ addPartDetail.pccMeProjectPlanDts.op_std_vn }}</text><br>
									<text>{{ addPartDetail.pccMeProjectPlanDts.op_std_id }}</text><br>
									<text>{{ addPartDetail.pccMeProjectPlanDts.op_std_bd }}</text><br>
									<text>{{ addPartDetail.pccMeProjectPlanDts.op_std_ph }}</text><br>
								</view>
							</view>
						</td>
						<td class="right-td">{{ addPartDetail.pccMeProjectPlanDts.tools }}</td>
						<td class="right-td">{{ addPartDetail.pccMeProjectPlanDts.glue }}</td>
						<td class="right-td">{{ addPartDetail.pccMeProjectPlanDts.machine }}</td>
						<td class="right-td">{{ addPartDetail.pccMeProjectPlanDts.car_line }}</td>
					</tr>
					<tr>
						<th class="demo-th">边距 margin</th>
						<th class="demo-th">针距 stitch</th>
						<th class="demo-th">间距 space</th>
						<th class="demo-th">车针 needle</th>
					</tr>

					<!-- 内容行 -->
					<tr>
						<td class="right-td">{{ addPartDetail.pccMeProjectPlanDts.margin }}</td>
						<td class="right-td">{{ addPartDetail.pccMeProjectPlanDts.needle_spacing }}</td>
						<td class="right-td">{{ addPartDetail.pccMeProjectPlanDts.spacing }}</td>
						<td class="right-td">{{ addPartDetail.pccMeProjectPlanDts.needle }}</td>
					</tr>

                    <tr>
                        <td class="noBorder" colspan="4"></td>
                        <th class="demo-th" colspan="4" style="width: 39%;">自检点<br>Self-check point</th>
                    </tr>

                    <!-- 图片行 -->
                    <tr>
                        <td class="demo-td" colspan="4">
                            <view class="image-container">
                                <view  :class="computedClass" v-for="(img, index) in imageListMain1">
                                	<uv-image
                                		:key="img.name"
                                		:src="img.url"
                                		@click="picturePreView(img.url)"
                                		width="25vh"
                                		height="25vh"
                                	></uv-image>
                                </view>
								<view style="margin-bottom: 20px;" v-for="(video, index) in videoListMain">
									<uv-button :custom-style="customStyle" type="primary" text="播放视频" @click="openVideoPop(video.url)"></uv-button>
								</view>
                            </view>
                        </td>
                        <td class="content-td" colspan="4">
                            <text style="font-weight: normal;">
                                {{ addPartDetail.pccMeProjectPlanDts.self_check_points }}
                            </text>
                            <br>
                            <text style="font-weight: normal;">
                                {{ addPartDetail.pccMeProjectPlanDts.self_check_points_en }}
                            </text>
                            <br>
                            <text style="font-weight: normal;">
                                {{ addPartDetail.pccMeProjectPlanDts.self_check_points_vn }}
                            </text>
                            <br>
                            <text style="font-weight: normal;">
                                {{ addPartDetail.pccMeProjectPlanDts.self_check_points_id }}
                            </text>
                            <br>
                            <text style="font-weight: normal;">
                                {{ addPartDetail.pccMeProjectPlanDts.self_check_points_bd }}
                            </text>
                            <br>
                            <text style="font-weight: normal;">
                                {{ addPartDetail.pccMeProjectPlanDts.self_check_points_ph }}
                            </text>
                            <br>
                        </td>
                    </tr>
					<!-- 表尾 -->
                    <!-- <tr>
                        <th class="bottom-th" colspan="2">制鞋排頭: {{ addPartDetail.shoe_make_head }}</th>
                        <th class="bottom-th" colspan="3">版師: {{ addPartDetail.printmaker }}</th>
                        <th class="bottom-th" colspan="3">高級技師: {{ addPartDetail.senior_technician }}</th>
                    </tr> -->
                    <!-- 表尾 -->
                    <tr>
                        <th class="bottom-th" colspan="2">核准 Approval:</th>
                        <th class="bottom-th" colspan="3">审核 Check by:</th>
                        <th class="bottom-th" colspan="3">制表 Tab: {{ addPartDetail.create_by }}</th>
                    </tr>
                </table>
            </view>

            <view class="icons">
                <uni-icons @click="last()" type="arrow-left" size="36" color="#6fa2ce"></uni-icons>
                <uni-icons @click="next()" type="arrow-right" size="36" style="margin-left: 350px;"
                           color="#6fa2ce"></uni-icons>
            </view>
        </view>
    </view>

    <view class="picture-popup">
        <uni-popup
            ref="picturePopup"
            type="center">
            <view :class="popcomputedClass">
                <img
                    :src="preViewPicture"
                    alt=""
                    class="img"
                >
            </view>
        </uni-popup>
    </view>
	
	
	
	<view class="video-popup">
		<uni-popup ref="videoPopup" type="center">
			<view class="videotable">
				<view style="height: 100%;">
					<DomVideoPlayer ref="domVideoPlayer" :src="tempUrl" loop controls/>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<style lang="scss">
page {
    width: 100%;
    height: 100%;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;
}

.add-info {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0 0 1px 5px #ddd;
    position: relative;
    overflow: auto;

    :disabled {
        color: black;
    }

    .back {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 2.5%;
        top: 2.5%;
        border-radius: 50%;
        box-shadow: 0 0 5px gray;
        transition: all 0.05s ease-in-out;
        cursor: pointer;
        z-index: 1;

        &:active {
            transform: scale(0.97);
            box-shadow: 0 0 1px gray;
        }
    }

    .title {
        margin-bottom: 1%;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        padding: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }

    .title-text {
        flex-grow: 1;
        text-align: center;
    }

    .icons {
        position: fixed;
        bottom: 25px; /* 距离底部的距离，可以根据需要调整 */
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
    }

    .title-part-attribute {
        position: absolute;
        right: 80px; /* 根据需要调整位置 */
        display: flex;
        align-items: center;
        margin-right: 40px;
        background-color: #fdf6e3;
    }

    .title-part-attribute2 {
        position: absolute;
        right: 220px; /* 根据需要调整位置 */
        display: flex;
        align-items: center;
        margin-right: 40px;
        background-color: #fdf6e3;
    }
}

.title-th {
    font-size: 22px;

}

.right-td {
    width: 5vw;
    height: 5vw;
    border: 1px solid #000;
    padding: 8px;
    text-align: center;
    white-space: pre-line;
    font-size: 18px;
}

.noBorder {
    width: 5vw;
    height: 0vw;
    padding: 8px;
    text-align: center;
    white-space: pre-line;
    font-size: 18px;
    border-left: 1px solid #000;
    border-right: 1px solid #000;
}

.bottom-th {
    font-size: 18px;
}

.right-td .vertical-align {
    display: flex;
    flex-direction: column;
    justify-content: flex-start; /* 或者其他你需要的对齐方式 */
    align-items: center;
}

.content-td {
    width: 10vw;
    height: 12vw;
    border: 1px solid #000;
    padding: 8px;
    text-align: left;
    font-size: 18px;
}

.process-td {
    width: 10vw;
    height: 12vw;
    border: 1px solid #000;
    padding: 8px;
    text-align: center;
}

.image-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px; /* 图片之间的间距，可以根据需要调整 */
}

.demo-table {
    margin-top: 2vw;
    width: 100%;
    border-collapse: collapse;
}

.demo-th {
    border: 1px solid #000;
    padding: 8px;
    text-align: center;
    font-size: 24px;
}

.demo-td {
    padding: 8px;
    text-align: center;
    font-size: 24px;
    border-left: 1px solid #000;
    border-right: 1px solid #000;
    border-bottom: 1px solid #000;
    position: relative;
    top: -8vh;
    vertical-align: top;
}

.standard-image, .operation-image {
    width: 100%;
    height: auto;
}

// 图片预览


.picture-popup {
    .watermark {
        position: relative;
        transition: all 0.15s ease-in-out;

        &::before {
            content: "STELLA";
            position: absolute;
            top: 2px;
            left: 2px;
            color: black;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 0 0 5px white;
            z-index: 1;
            pointer-events: none;
        }

        &:active {
            transform: scale(1.5);
        }

        .img {
            min-width: 100px;
            min-height: 100px;
            max-width: 90vw;
            max-height: 90vh;
            border-radius: 10px;
            box-shadow: 0 0 10px white;

            &:active {
                box-shadow: 0 0 1px white;
            }
        }
    }

    .watermark2 {
        position: relative;
        transition: all 0.15s ease-in-out;

        &::before {
            content: "STELLA";
            position: absolute;
            top: 2px;
            left: 2px;
            color: black;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 0 0 5px white;
            z-index: 1;
            pointer-events: none;
        }

        &::after {
            content: "双峰兴昂";
            position: absolute;
            top: 18px;
            left: 2px;
            color: black;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 0 0 5px white;
            z-index: 1;
            pointer-events: none;
        }


        &:active {
            transform: scale(1.5);
        }

        .img {
            min-width: 100px;
            min-height: 100px;
            max-width: 90vw;
            max-height: 90vh;
            border-radius: 10px;
            box-shadow: 0 0 10px white;

            &:active {
                box-shadow: 0 0 1px white;
            }
        }
    }
}

	
.video-popup{
	.videotable{
		width: 80vw;
		height: 80vh;
		padding: 2vh;
		background-color: #fdf6e3;
		overflow-y: auto;
	}
}

.watermark {
    position: relative;
    transition: all 0.15s ease-in-out;

    &::before {
        content: "STELLA";
        position: absolute;
        top: 2px;
        left: 2px;
        color: white;
        font-size: 12px;
        font-weight: bold;
        text-shadow: 0 0 5px white;
        z-index: 1;
        pointer-events: none;
    }

    &:active {
        transform: scale(1.5);
    }
}

.watermark2 {
	position: relative;
	transition: all 0.15s ease-in-out;

	&::before {
		content: "STELLA";
		position: absolute;
		top: 2px;
		left: 2px;
		color: white;
		font-size: 12px;
		font-weight: bold;
		text-shadow: 0 0 5px white;
		z-index: 1;
		pointer-events: none;
	}
	
	&::after {
		content: "双峰兴昂";
		position: absolute;
		top: 18px;
		left: 2px;
		color: white;
		font-size: 12px;
		font-weight: bold;
		text-shadow: 0 0 5px white;
		z-index: 1;
		pointer-events: none;
	}

	&:active {
		transform: scale(1.5);
	}
}

.picture {
    height: 9%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 35px;
    left: calc(15% + 24px);
    cursor: pointer;

    .watermark {
        position: relative;

        &::before {
            content: "STELLA";
            position: absolute;
            top: 2px;
            left: 2px;
            color: white;
            font-size: 12px;
            font-weight: bold;
            text-shadow: 0 0 5px black;
            z-index: 1;
            pointer-events: none;
        }

        .img {
            max-width: 140px;
            max-height: 50px;
            border-radius: 10px;
            box-shadow: 0 0 5px gray;
            transition: all 0.05s ease-in-out;
            cursor: pointer;

            &:active {
                box-shadow: 0 0 1px gray;
                transform: scale(0.97);
            }
        }
    }
}
</style>