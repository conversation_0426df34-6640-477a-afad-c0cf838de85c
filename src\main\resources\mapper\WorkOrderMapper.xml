<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.production.mapper.WorkOrderMapper">

    <!-- 查询组别列表（不带日期参数） -->
    <select id="getGroupList" resultType="com.zqn.production.entity.WorkOrderGroup">
        select emp_name,a.made_dept,grp_no,substr(grp_no,1,1) area, b.in_qty as qty
        from BD_EMPLOYEE a,
             (select made_dept,sum(bar_qty) in_qty
              from mk_sorderbar
              where semi_no = 'U' and semi_su = '1'
                and trunc(bar_date) = trunc(sysdate)
              group by made_dept
             ) b
        where instr(dept_no, 'SP0100303') > 0 and pbar_flag = 'Y' and inv_flag = 'N'
          and grp_no is not null and a.made_dept = #{deptNo}
          and a.made_dept = b.made_dept(+)
    </select>

    <!-- 查询组别列表（带日期参数） -->
    <select id="getGroupListWithDate" resultType="com.zqn.production.entity.WorkOrderGroup">
        SELECT a.EMP_NAME,a.MADE_DEPT,a.GRP_NO,A.GRPM ,SUM(C.qty1) qty,SUM(d.qty4) QTY4
        FROM bd_employee a,
         (SELECT  x.made_dept,x.ord_no
          FROM mk_sorderbar x, (SELECT ord_no,MAX(bar_date) bar_date FROM mk_sorderbar WHERE semi_no ='U' GROUP BY ord_no) y
          WHERE semi_no='U' AND x.ord_no = y.ord_no AND x.bar_date = y.bar_date
         )  b,
        (SELECT A.ORD_NO,SUM(a.u1_qty-a.u2_qty) QTY1
        FROM gc_sorders a,gc_sorder b
        WHERE a.ord_no = b.ord_no  AND b.inv_flag ='N'
        AND a.u1_qty>0 AND a.u2_qty &lt; a.tot_qty AND a.cl_flag ='N'
                                              AND a.wo_flag ='Y'  AND a.c1_qty > 0
        AND b.prou_sets>0
        GROUP BY A.ORD_NO) C ,
        (SELECT A.ORD_NO,SUM(a.y1_qty-a.y2_qty) QTY4
        FROM gc_sorders a,gc_sorder b
        WHERE a.ord_no = b.ord_no  AND b.inv_flag ='N'
        AND  a.cl_flag ='N' AND a.wo_flag ='Y'  AND a.c1_qty >0
        AND bitand(b.prou_sets,2)>0
        AND b.prou_sets>0
        AND a.y1_qty>a.y2_qty
        GROUP BY A.ORD_NO) D
        WHERE INSTR(a.dept_no,'SP0100303')> 0 AND a.pbar_flag ='Y'
        AND a.INV_FLAG='N' AND a.GRP_NO IS NOT NULL  AND a.made_dept = b.made_dept(+)
        AND b.ord_no = c.ord_no(+) AND b.ord_no = d.ord_no(+) and grpm = #{deptNo}
        GROUP BY a.EMP_NAME,a.MADE_DEPT,a.GRP_NO,A.GRPM
    </select>

    <!-- 查询工单列表 -->
    <select id="getOrderList" resultType="com.zqn.production.entity.WorkOrder">
        SELECT  l.se_flag, x.made_dept, X.EMP_NAME,X.ORD_NO, X.DEV_TYPE,X.SHP_DATE,X.ord_qty as bar_qty,
                X.QTY1 as tot_qty,Z.PRD_DATE,Z.RUN_RATE, ROUND(fn_sy_div(k.bar_qty,Z.RUN_RATE)*100,2) rate1,x.model_no, x.t2_flag,
        RTRIM((SELECT F_LINKS(DISTINCT fn_sy_getxcodeval('NC026-1',PB_DESC)||'/')
        FROM mk_slastbar
        WHERE ord_no = X.ord_no /*AND SUBSTR(PB_ADDR,1,1)='C'*/ ),'/') PB_DESC
        FROM (
        SELECT  D.DEPT_NO,D.EMP_NO,d.emp_name,d.made_dept,a.ord_no,a.dev_type,c.max_shp_date  shp_date,a.ord_qty ,
        a.model_no,c.QTY1,CASE WHEN c.t2_qty= c.tot_qty THEN 'OK' ELSE NULL END t2_flag
        FROM gc_sorder a ,
         (SELECT  x.made_dept,x.ord_no
          FROM mk_sorderbar x, (SELECT ord_no,MAX(bar_date) bar_date FROM mk_sorderbar WHERE semi_no ='U' GROUP BY ord_no) y
          WHERE semi_no='U' AND x.ord_no = y.ord_no AND x.bar_date = y.bar_date
         )  b,
        (SELECT A.ORD_NO,SUM(a.u1_qty-a.u2_qty) QTY1,MIN(a.shp_date) min_shp_date,MAX(a.shp_date) max_shp_date,
        SUM(a.t2_qty) t2_qty,SUM(a.tot_qty) tot_qty
        FROM gc_sorders a,gc_sorder b
        WHERE a.ord_no = b.ord_no  AND b.inv_flag ='N'
        AND a.u1_qty>0 AND a.u2_qty &lt; a.tot_qty AND a.cl_flag ='N'
                                              AND a.wo_flag ='Y'  AND a.c1_qty >0
        AND b.prou_sets>0
        GROUP BY A.ORD_NO) c ,bd_empgrp d
        WHERE a.ord_no = b.ord_No AND b.ord_no = c.ord_no AND b.made_dept = d.made_dept
        ) x , BD_EMPGRPd z ,
        (SELECT a.made_dept,round( SUM(decode(d.ordu_sets,0,a.bar_qty,nxuser.fn_mk_semiper(d.ordu_sets, 'BC027') *bar_qty)),2) bar_qty
        FROM mk_sorderbar a, gc_sorder d
        WHERE a.ord_no = d.ord_no AND  semi_no='U' AND TYPE='O' AND semi_su ='2'
        AND TRUNC(bar_date) =  TRUNC(SYSDATE)
        GROUP BY  a.made_dept) k ,  (SELECT *FROM mk_ordsel WHERE TRUNC(se_date)=TRUNC(SYSDATE)) l
        WHERE x.dept_no = z.dept_no(+) AND x.made_dept = z.made_dept(+)
        AND   TRUNC(prd_datE) = TRUNC(SYSDATE)
        AND x.made_dept = k.made_dept(+) AND x.ord_no = l.ord_no(+)
        AND x.made_dept = #{madeDept}
        order by SHP_DATE
    </select>

    <!-- 更新工单选择状态 -->
    <update id="updateOrderSelection">
        UPDATE mk_ordsel
        SET se_flag = #{seFlag}
        WHERE ord_no = #{ordNo} AND TRUNC(se_date) = TRUNC(SYSDATE)
    </update>

    <!-- 插入工单选择记录 -->
    <insert id="insertOrderSelection">
        INSERT INTO mk_ordsel (ord_no, se_flag, se_date, ord_qty)
        VALUES (#{ordNo}, #{seFlag}, SYSDATE, #{ordQty})
    </insert>

    <!-- 更新组别目标PPH -->
    <update id="updateGroupTarget">
        UPDATE BD_EMPGRPD
        SET run_rate = #{runRate}
        WHERE made_dept = #{madeDept}
          and TRUNC(PRD_DATE) = TRUNC(SYSDATE)
    </update>

    <!-- callPdIniBdEmpgrpd -->

    <select id="callPdIniBdEmpgrpd" statementType="CALLABLE">
        {call pd_ini_BD_EMPGRPD(#{oReturn, mode=OUT, jdbcType=VARCHAR})}
    </select>
    
    <!-- 批量查询CK_SMODEL表中的型体图片 -->
    <select id="getBatchPicByCkSmodel" resultType="com.zqn.production.entity.WorkOrder">
        select model_no, model_pic from ck_smodel 
        where model_no in
        <foreach collection="modelNos" item="modelNo" open="(" separator="," close=")">
            #{modelNo}
        </foreach>
    </select>
    
    <!-- 批量查询CB_MODEL表中的型体图片 -->
    <select id="getBatchPicByCbModel" resultType="com.zqn.production.entity.WorkOrder">
        select model_no, model_pic from cb_model 
        where model_no in
        <foreach collection="modelNos" item="modelNo" open="(" separator="," close=")">
            #{modelNo}
        </foreach>
    </select>
</mapper>