
<template>
<view class="container">
	      <image  class="image-preview"  mode="widthFix"  :src="url" ></image>
</view>
	
	<button @click="goBack" class="go-back">返回</button>
	<button @click="editImage" class="edit-button">编辑图片</button>
  <ksp-cropper mode="free" :width="200" :height="140" :maxWidth="1024" :maxHeight="1024" :url="editImageSrc" @cancel="oncancel" @ok="onok"></ksp-cropper>
</template>



<script>
import KspCropper from "../../uni_modules/ksp-cropper/components/ksp-cropper/ksp-cropper.vue";
export default {
	
	onLoad: function(option) {
			   console.log('------------------onLoad-----------------------------')
	   // 从 URL 查询参数中获取 JSON 字符串
	   let objStr = decodeURIComponent(option.data);
		  
	   // 将 JSON 字符串转换回对象
	   let obj = JSON.parse(objStr);
	   // console.log(obj)
		this.currentImage = obj
		
	
	 },
	
	
props: ['currentImage'],
	  data() {
	    return {
	      editImageSrc: '' ,// 默认图片URL,
		  currentImage: ''
	    };
	  },
	  

  methods: {

	
    editImage() {
      // console.log('编辑图片' + this.currentImage.img.url);
	  this.editImageSrc = this.currentImage.img.url
    },
	goBack() {
		uni.navigateBack({
			delta: 1
		});
	},
	oncancel(){
		this.editImageSrc = ''
	},
	onok(e){
		this.editImageSrc = '';
		this.currentImage.path = e.path
		// 将数据存储到本地存储中
		uni.setStorage({
		  key: 'customData',
		  data: this.currentImage,
		  success() {
		    // 数据存储成功后，返回上一个页面
		    uni.navigateBack({
		      delta: 1
		    });
		  }
		});
	}
  },
computed: {
    url() {
		// console.log('111' + this.currentImage.img.url)
		// let urls = []
		// urls.push(this.currentImage.img.url)
		// uni.previewImage({
		// 	urls: urls,
		// 	current: index
		// });
      return this.currentImage.img.url;
    }
  },
};
</script>

<style>

.container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: #000;
}

.image-preview {
position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
 width: 90upx /1.2;
	height:inherit;
  object-fit: cover; /* 保持图片的宽高比，同时填充整个容器 */
  
}
.edit-button {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: #007aff;
  color: white;
  padding: 5px 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.go-back {
  position: absolute;
  bottom: 10px;
  right: 120px;
  background-color: #007aff;
  color: white;
  padding: 5px 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}
</style>