<template>
	<!-- <view style="background-image: /static/boxed-gb.jpg;"> -->
	<view>
	
	
<!-- 	<view class="top">
	  <view >
		<uni-icons @click="back" type="back" size="36"></uni-icons>
	  </view>
	
	</view> -->



	  <view class="container">
		   
		  <view class="top">
			  <view >
			  		<uni-icons @click="back" type="back" size="36" color="#fff"></uni-icons>
			  </view>
			  
			   <view class="title">
			  		<text>成長軌跡</text>
			   </view>
			  
		  </view>
		  
		 <view class="container-content">
			 

		  		    <text style="font-size: 18px;">基本信息</text>
			<view class="line" style="margin-top: 10px;"></view>

		  <view style="margin-top: 10px;" >

		<!-- 				<view class="line2"></view> -->
		  </view>
		  <view class="item" style="margin-top: 15px; padding-left: 40px; font-size: 16px;">
			  <view style="  display: flex;align-items: center;">
			      <view style="font-weight: bold;">主題: </view>
			  	    <view style="margin-left: 10px;">{{data.theme}}</view>
			  </view>
			 	  <view style="  display: flex;align-items: center; margin-top: 15px;">
			      <view style="font-weight: bold;">提案人: </view>
			  	    <view style="margin-left: 10px;">{{data.proposer}}</view>
			  </view>
			  
			  <view style="  display: flex;align-items: center; margin-top: 15px;">
			      <view style="font-weight: bold;">廠別: </view>
			  	    <view style="margin-left: 10px;">{{data.factory}}</view>
			  </view>
			  <view style="  display: flex;align-items: center; margin-top: 15px; margin-bottom: 15px;">
			    <view style="font-weight: bold;">部門: </view>
			    <view style="margin-left: 10px;">{{data.dept}}</view>
			  </view>


		  </view>
		  
		  		  <text style="font-size: 18px; margin-top: 10px;" >成長軌跡步驟</text>
			<view class="line" style="margin-top: 10px;"></view>
				

		
			
		  <view class="item"  >
			  <view class="description">STEP1  問題的明確化 Problem Statement</view>
			 <mp-html  class="html-content" :content="data.problem_statement" />
		  </view>
		  <view class="item">
		  			  <view class="description">STEP1  改善目的 Purpose</view>
		  			 <mp-html  class="html-content" :content="data.purpose" />
		  </view>
		  
		  <view class="item">
		  			  <view class="description">STEP2  現狀把握  Current States</view>
		  		  			<mp-html  class="html-content" :content="data.current_states" />
		  </view>
		  
		  
		<view class="item">
			<view class="description">STEP3  目標設定  Target Setting</view>
			<mp-html  class="html-content" :content="data.target_setting" />
		</view>
		  
		<view class="item">
		  <view class="description">STEP4   要因分析  Cause analysis</view>
		  			<mp-html  class="html-content" :content="data.cause_analysis" />
		</view>
		
		
		<view class="item">
		<view class="description">STEP5   對策立案   Contemeasures</view>
		  			<mp-html  class="html-content" :content="data.contemeasures" />
		</view>
		
		
		<view class="item">
 			  <view class="description">STEP6   實施計劃   Action Plan</view>
		  	  			<mp-html   class="html-content" :content="data.action_plan" />
		</view>
		
		
		<view class="item">
				  <view class="description">STEP7  结果確認  Results Evaluation</view>
		 	<mp-html  class="html-content" :content="data.results_evaluation" />
		</view>
		
		
		<view class="item">
				  <view class="description">STEP8  標準化  Standardization</view>
	<mp-html  class="html-content" :content="data.standardization" />
		</view>

	  </view>
	  </view>
	</view>		  
				  

</template>

<script>
	import urlPrefix from '@/pages/common/urlPrefix.js'
	import {replaceNbspWithSpace, alignMediaInHtml, alignMediaInHtmlWithRegex} from '@/uni_modules/sp-editor/utils'
export default {
  data() {
    return {
	  id:'',
      data: {},
    }
  },
  onLoad(options) {
	if(options.id){
		this.id = options.id;
	}
	    


  },
	mounted() {
		this.getData();
	},
  methods: {
	  
	  back(){
		  
		  let back = getCurrentPages();
		  if (back && back.length > 1) {
		  	uni.navigateBack({
		  		delta: 1
		  	});
		  } else {
		  	history.back();
		  }

				// uni.redirectTo({
				// 	  url: '/pages/meprjplan/progressRouteList'
				// 	});
	  },
	  
	  getData(){
	  	if(this.id){
	  		uni.request({
	  			url: urlPrefix + "/pccprogressroute/getById",
	  			data: {
	  				"id": this.id,
	  			},
	  			method: "GET"
	  		}).then(res => {
				this.data = res.data.data;
				this.data.problem_statement = alignMediaInHtmlWithRegex(this.data.problem_statement);		
				// console.log("33333333@" + this.data.purpose)
				this.data.purpose = alignMediaInHtmlWithRegex(this.data.purpose);	
				// console.log("33333333@@@@" + this.data.purpose)
				this.data.current_states = alignMediaInHtmlWithRegex(this.data.current_states);
				this.data.target_setting = alignMediaInHtmlWithRegex(this.data.target_setting);	

				
				this.data.cause_analysis = alignMediaInHtmlWithRegex(this.data.cause_analysis);
				this.data.contemeasures = alignMediaInHtmlWithRegex(this.data.contemeasures);
				this.data.action_plan = alignMediaInHtmlWithRegex(this.data.action_plan);
				this.data.results_evaluation = alignMediaInHtmlWithRegex(this.data.results_evaluation);
				this.data.standardization = alignMediaInHtmlWithRegex(this.data.standardization);
								// console.log("33333333@@@@" + this.data.standardization)


	  		
	  		}).catch(err => {
	  			console.log(err)
	  			uni.showToast({
	  				title: '获取数据失败..',
	  				icon: "error"
	  			});
	  		})
	  	}
	  	
	  },
	  
  }
}
</script>

<style>
	
	page{
		/* background-color: #FDF6E3; */
		background-image: url(/static/boxed-bg.jpg);
	}
	

	
	.top {
		/* margin-left: 20px; */
		 padding-top: var(--status-bar-height);
		height: 50px;
		background-color: #3C8DBC;
		  display: flex;
		  align-items: center; /* 垂直居中 */
		  justify-content: space-between; /* 分散对齐，但这里我们需要调整 */
		}
		.top view:first-child {
		  position: absolute;
		  left: 10px;
		}
		
		.title {
			  width: 100%;
			  text-align: center;
			line-height: 50px;
			height: 50px;
			color: #fff;
		   font-size: 22px;
		}
	
	.theme{
		line-height: 50px;
		height: 50px;
		color: #000;
		font-size: 22px;
	}
	
.container {
	background-color: #ECF0F5;
  margin: 0 auto;
  width:100%; 
}

.container-content{
	padding: 20px;
	background-color: #fff;
	  margin: 0 auto;
	  margin-top: 20px;
	  display: flex;
	  flex-direction: column; /* 设置主轴方向为垂直 */
	  width: 85%; /* 容器宽度为100% */	
	border-radius: 10px; /* 添加圆角，这里设置为10px */

}


/* H5应用样式 */
/* #ifdef H5 */
.container-content {
  width: 80%; /* 网页上容器宽度为70% */
}
/* #endif */




.item {
  /* margin-bottom: 20px; */
}

.description {

  font-size: 14px;
  font-weight: bold; 
  height: 45px;
  line-height: 45px;
}

.html-content {
	/* margin-top: 5px; */
	min-height: 300px;
  border: 1px solid #D4D7DF; 
    border-radius: 10px; 
	
	background-color: #fff;

}


.line {
  width: 100%; 
  height: 2px; 
  background-color: #00C0EF; 

}


.line2 {
	margin-top: 5px;
  width: 100%; 
  height: 1px; 
  background-color: #F4F4F4;  
}
	
</style>
