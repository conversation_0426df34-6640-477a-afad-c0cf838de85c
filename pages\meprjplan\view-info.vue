
<!--弃用页面-->

<script setup>
import {ref, watch, reactive, defineProps} from 'vue'
import {onLoad,onShow} from '@dcloudio/uni-app'
import urlPrefix from '@/pages/common/urlPrefix.js'
import UniEasyinput from "../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue";
import UvImage from "../../uni_modules/uv-image/components/uv-image/uv-image.vue";

const user = uni.getStorageSync("loUserNo")
const info = ref()

const inputCountNum = ref(1)
const inputCount = ref([{id: uniqueId(), text: ''}])
const items = ref([])
const versions = ref([])

const deptPart = ref([['ME底部','半成品','鞋面'],['成型','半成品']])

const depts = ref([
	['成型','半成品'],
    ['中底','中底皮','大底','包跟'],
	['鞋面']
])


function openPicker() {
	picker.value.open();
}

function confirm(e) {
	console.log('confirm', e);
	addPartDetail.deptname = e.value[0] + "-" + e.value[1];
	addPartDetail.dept = e.value[0] + "-" + e.value[1];
	changeDeptType();
	uni.request({
	    url: urlPrefix + "/pccmeprjplanhd/selectMax",
	    data: {
	        "modelNo": addPartDetail.model_no,
			"dept":addPartDetail.dept,
            "factory":uni.getStorageSync('currentFactory')
	    },
	    method: "GET"
	}).then(res => {
		addPartDetail.pccMeProjectPlanDts.item_no = res.data.data + 1;
	}).catch(err => {
	    console.log(err)
	    uni.showToast({
	        title: '获取数据失败..',
	        icon: "error"
	    });
	})
}

//修改部门
function changeDeptType(){
	if(addPartDetail.dept.indexOf('ME底部') > -1 || addPartDetail.dept.indexOf('半成品') > -1){
		addPartDetail.deptType = 1;
	}else{
		addPartDetail.deptType = 2;
	}
}

const addSpecificationDetail = reactive({
    model_no: "",
    procs_type: 1,
    procs_pic: "",
    s_size: 0,
    e_size: 0,
    remark: "",
    ins_user: user,
    ins_date: "",
    upd_user: user,
    upd_date: ""
})

const uploadImgs = ref([])
const imageListMain1 = ref([])
const imageListMain2 = ref([])
const tipPopup = ref()
const tipType = ref('')
const tipMessage = ref('')
const addPartDetail = reactive({
    brand: "",
    model_no: "",
    shoe_last: "",
    dept: "",
	deptType: 1,
    pccMeProjectPlanDts:{
        item_no: "",
        content: [],//明细内容
        tools: "",//工具
        imgUrls: [],
        actions: "",//动作
        machine: "",//机器
        chemical_substance: "",//化学品
        temp: "",//温度
        time: "",//时间
        pressure: "",//压力
        glue: "",//胶水
        car_line: "",//车线
        margin: "",//边距
        needle_spacing: "",//针距
        spacing: "",//间距
        needle: "",//车针
        img_tit1: "",//图片备注
        img_tit2: "",//图片备注
        create_by: user,
        update_by:user
    }
})

// 返回上一页
function back() {
    info.value = ''
    let back = getCurrentPages();
    if(back && back.length > 1) {
    	uni.navigateBack({  
    	  delta: 1  
    	});  
    } else {  
    	history.back();  
    }
}


// 上一项次
function last() {
	// 找到当前元素的索引
	const currentIndex = items.value.findIndex(item => item.value === addPartDetail.pccMeProjectPlanDts.item_no);
	// 如果当前索引大于0，则递减索引并更新当前元素的值
	if (currentIndex > 0) {
		addPartDetail.pccMeProjectPlanDts.item_no = items.value[currentIndex - 1].value;
		changeItem();
	}else if(currentIndex == 0){
		addPartDetail.pccMeProjectPlanDts.item_no = items.value[currentIndex].value;
		changeItem();
	}
}

// 下一项次
function next() {
    // 找到当前元素的索引
    const currentIndex = items.value.findIndex(item => item.value === addPartDetail.pccMeProjectPlanDts.item_no);
    // 如果当前索引大于0，则递减索引并更新当前元素的值
    if (currentIndex > -1 && addPartDetail.pccMeProjectPlanDts.item_no < items.value[items.value.length-1].value) {
    	addPartDetail.pccMeProjectPlanDts.item_no = items.value[currentIndex + 1].value;
    	changeItem();
    }
}

// 提示信息
function showTip(type, message) {
    tipType.value = type
    tipMessage.value = message
    tipPopup.value.open()
}

//增加输入框
function addInput() {
    if (inputCount.value.length > 9) {
        showTip('error', '最多创建10条！')
    } else {
        inputCount.value.push({id: uniqueId(), text: ''});
    }
}

function changeItem(){
	getData(addPartDetail.model_no,addPartDetail.pccMeProjectPlanDts.item_no,'',addPartDetail.dept);
}

function changeVersion(e){
	getData(addPartDetail.model_no,addPartDetail.pccMeProjectPlanDts.item_no,addPartDetail.pccMeProjectPlanDts.version,addPartDetail.dept);
}

function uniqueId() {
    // 简单的 ID 生成器，每次调用递增
    return inputCountNum.value++;
}

async function getData(model_no,itemNo,version,dept) {
	await uni.request({
		url: urlPrefix + "/pccmeprjplandt/queryByItem",
		data: {
			"modelNo": model_no,
			"item":itemNo,
			"version": version,
			"dept": dept
		},
		method: "GET"
	}).then(res => {
		var contentList = res.data.data.content;
		if (contentList.length > 0) {
			inputCount.value = [{}];
			for (let i = 0; i < contentList.length; i++) {
				if (i > 0) {
					addInput();
				}
				inputCount.value[i].id = uniqueId();
				inputCount.value[i].text = contentList[i];
			}
		}else{
			inputCount.value = [{}];
		}
		addPartDetail.pccMeProjectPlanDts = res.data.data;

		let a = new Array;
		let b = new Array;
		for (let i = 0; i < res.data.data.imgUrls.length; i++) {
			if(res.data.data.imgUrls[i].type == 1){
				a.push({"name":'test.png',"extname":'picture',"url":res.data.data.imgUrls[i].img_url})
			}else{
				b.push({"name":'test.png',"extname":'picture',"url":res.data.data.imgUrls[i].img_url})
			}
		}
		imageListMain1.value = a;
		imageListMain2.value = b;
		items.value = res.data.data.items;
		versions.value = res.data.data.versions;
	}).catch(err => {
		uni.showToast({
			title: '获取数据失败..',
			icon: "error"
		});
	})
	changeDeptType();
}

function addProcessInfo(){
    addPartDetail.pccMeProjectPlanDts.content = [];
    for (let i = 0; i < inputCount.value.length; i++) {
        addPartDetail.pccMeProjectPlanDts.content.push(inputCount.value[i].text);
    }
    //处理图片
    addPartDetail.pccMeProjectPlanDts.imgUrls = [];
    for (let i = 0; i < uploadImgs.value.length; i++) {
        addPartDetail.pccMeProjectPlanDts.imgUrls.push({"img_url":uploadImgs.value[i].value,"type":uploadImgs.value[i].type});
    }
    uni.request({
        url: urlPrefix + "/pccmeprjplanhd/update",
        data: addPartDetail,
        method: "POST"
    }).then(res => {
		if(res.statusCode !=200){
			showTip('error', res.data.message);
		}else{
			// back();
		}
    }).catch(err => {
        console.log(err)
        uni.showToast({
            title: '保存数据失败..',
            icon: "error"
        });
    })
}

//登录校验
function loginCheck(){
	if(uni.getStorageSync("loginUserName") == null || uni.getStorageSync("loginUserName") == ''){
		uni.navigateTo({
			url: `/pages/login/login`,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}
}

onLoad(async (props) => {
    info.value = JSON.parse(props.info);
    addPartDetail.model_no = info.value.model_no;
    addPartDetail.shoe_last = info.value.shoeLast;
    addPartDetail.brand = info.value.brand;
	addPartDetail.pccMeProjectPlanDts.item_no = info.value.item_no;
    addPartDetail.dept = info.value.dept;
    info.value.ins_user = user;
    info.value.upd_user = user;
    await getData(info.value.model_no,info.value.item_no,'',info.value.dept);
})
	
onShow(async (props) => {
	loginCheck();
})
</script>

<template>
    <view class="add-info">
        <view class="back">
            <uni-icons @click="back()" type="back" size="36" color="#6fa2ce"></uni-icons>
        </view>
        <view class="title">
            <uni-icons @click="last()" type="arrow-left" size="36" style="margin-right: 80px;" color="#6fa2ce"></uni-icons>
            查看ME工程信息
            <uni-icons @click="next()" type="arrow-right" size="36" style="margin-left: 80px;" color="#6fa2ce"></uni-icons>
        </view>

        <view class="data">
            <view class="top">
                <uni-section title="客户" titleFontSize="20px" type="line" class="title-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.brand" :value="addPartDetail.brand"></uni-easyinput>
                </uni-section>

                <uni-section title="部门" titleFontSize="20px" type="line" class="title-part-attribute"
                             style="margin-left: 2%;">
					<uni-easyinput disabled v-model="addPartDetail.dept" :value="addPartDetail.dept"></uni-easyinput>
                </uni-section>

                <uni-section title="型体编号" titleFontSize="20px" type="line" class="title-part-attribute"
                             style="margin-left: 2%;">
                    <uni-easyinput disabled :value="addPartDetail.model_no"></uni-easyinput>
                </uni-section>

                <uni-section title="楦头编号" titleFontSize="20px" type="line" class="title-part-attribute">
                    <uni-easyinput disabled :value="addPartDetail.shoe_last"></uni-easyinput>
                </uni-section>

                <uni-section title="项次" titleFontSize="20px" type="line" class="title-part-attribute">
					<uni-data-select v-model="addPartDetail.pccMeProjectPlanDts.item_no" :localdata="items"
					                 :clear="false"
					                 style="width: 80%;margin-left: 16px;background-color: white;" @change="changeItem"></uni-data-select>
                </uni-section>
				<uni-section title="版本" titleFontSize="20px" type="line" class="title-part-attribute">
					<uni-data-select v-model="addPartDetail.pccMeProjectPlanDts.version" :localdata="versions"
					                 :clear="false"
					                 style="width: 80%;margin-left: 16px;background-color: white;" @change="changeVersion"></uni-data-select>
				</uni-section>
            </view>
            <view id="leftImgView">
                <uni-section title="动作" titleFontSize="20px" type="line" class="action">
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.actions" type="textarea" maxlength="-1" :placeholder="`请输入`" style="width: 100%;margin-left: 0"/>
                </uni-section>
                <view>
                    <uni-section title="图片" titleFontSize="20px" type="line" class="add-specification-attribute">
                        <uni-easyinput disabled style="margin-top: 0;margin-bottom: 15px" placeholder="分类一" v-model="addPartDetail.pccMeProjectPlanDts.img_tit1"></uni-easyinput>
                        <uni-file-picker disabled v-model="imageListMain1" limit="9"></uni-file-picker>

                        <uni-easyinput disabled style="margin-top: 0;margin-bottom: 15px" placeholder="分类一" v-model="addPartDetail.pccMeProjectPlanDts.img_tit2"></uni-easyinput>
                        <uni-file-picker disabled v-model="imageListMain2" limit="9"></uni-file-picker>
                    </uni-section>
                </view>
            </view>

            <view id="rightContent">
                <uni-section title="工具" titleFontSize="20px" type="line" class="add-part-attribute">
<!--                    <span v-text="addPartDetail.pccMeProjectPlanDts.tools"></span>-->
<!--                    <uv-button type="primary" text="工具" @click="selectTools()"></uv-button>-->
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.tools"></uni-easyinput>
                </uni-section>
                <uni-section  v-show="addPartDetail.deptType == 1" title="化学品" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.chemical_substance"></uni-easyinput>
                </uni-section>
                <uni-section title="机器" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.machine"></uni-easyinput>
                </uni-section>
                <uni-section  v-show="addPartDetail.deptType == 1" title="温度" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.temp"></uni-easyinput>
                </uni-section>
                <uni-section  v-show="addPartDetail.deptType == 1" title="压力" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.pressure"></uni-easyinput>
                </uni-section>
                <uni-section  v-show="addPartDetail.deptType == 1" title="时间" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.time"></uni-easyinput>
                </uni-section>


                <uni-section v-show="addPartDetail.deptType ==2" title="胶水" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.glue"></uni-easyinput>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType ==2" title="车线" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.car_line"></uni-easyinput>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType ==2" title="边距" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.margin"></uni-easyinput>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType ==2" title="针距" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.needle_spacing"></uni-easyinput>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType ==2" title="间距" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.spacing"></uni-easyinput>
                </uni-section>
                <uni-section v-show="addPartDetail.deptType ==2" title="车针" titleFontSize="20px" type="line" class="add-part-attribute">
                    <uni-easyinput disabled v-model="addPartDetail.pccMeProjectPlanDts.needle"></uni-easyinput>
                </uni-section>

                <view style="width: 100%;">
                    <view v-for="input in inputCount" :key="input.id" style="display: flex; align-items: center; width: 100%; margin: 8px 0;">
                      <view style="flex: 1; position: relative;">
                        <uni-easyinput disabled autoHeight type="textarea" v-model="input.text" maxlength="-1" :placeholder="`请输入`"
                                       class="addInput"/>
                      </view>
                    </view>
				</view>
            </view>

        </view>
    </view>

    <view class="tip-popup">
        <uni-popup ref="tipPopup" type="message">
            <uni-popup-message :type="tipType" :message="tipMessage" :duration="1500"></uni-popup-message>
        </uni-popup>
    </view>
</template>

<style lang="scss">
page {
    width: 100%;
    height: 100%;
    padding: 2.5% 2% 1.5% 2%;
    box-sizing: border-box;
    background-color: #fdf6e3;
}

.add-info {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0 0 1px 5px #ddd;
    position: relative;
    overflow: auto;
	
	:disabled{
		color: black;
	}

    .back {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 2.5%;
        top: 2.5%;
        border-radius: 50%;
        box-shadow: 0 0 5px gray;
        transition: all 0.05s ease-in-out;
        cursor: pointer;
        z-index: 1;

        &:active {
            transform: scale(0.97);
            box-shadow: 0 0 1px gray;
        }
    }

    .submit {
        width: 50px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: 2.5%;
        top: 2.5%;
        border-radius: 50%;
        box-shadow: 0 0 5px gray;
        transition: all 0.05s ease-in-out;
        cursor: pointer;
        z-index: 1;

        &:active {
            transform: scale(0.97);
            box-shadow: 0 0 1px gray;
        }
    }

    .title {
        margin-bottom: 1%;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        padding: 16px;
    }

    .data {
        width: 100%;
        position: relative;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;

        .title-part-attribute {
            width: 28%;
            margin-left: 2%;
            background-color: #fdf6e3;

            .uni-easyinput {
                width: 90%;
                margin-bottom: 5px;

                &:deep(.uni-easyinput__content-input) {
                    height: 40px;
                    font-size: 20px;
                    font-weight: bold;
                }
            }
        }

        .add-part-attribute {
            width: 20%;
            margin-left: 8%;
            margin-bottom: 1%;
            background-color: #fdf6e3;


            .uni-easyinput {
                width: 100%;
                margin-bottom: 5px;

                &:deep(.uni-easyinput__content-input) {
                    height: 40px;
                    font-size: 20px;
                    font-weight: bold;
                }
            }
        }

        .uni-stat__select {
            width: 80%;
            margin-left: 16px;

            &:deep(.uni-select) {
                height: 40px;
                font-size: 20px;
                font-weight: bold;
            }

            &:deep(.uni-select__input-placeholder) {
                font-size: 20px;
            }


        }

        .uni-data-checklist {

            &:deep(.checklist-box) {
                margin-top: 0px;
            }

            &:deep(.checklist-text) {
                padding: 8px;
                font-size: 20px;
                font-weight: bold;
            }
        }

        .uni-numbox {
            margin-left: 16px;
            height: 40px;

            &:deep(.uni-numbox-btns) {
                width: 40px;
                box-sizing: border-box;

                .uni-numbox--text {
                    font-weight: bold;
                }
            }

            &:deep(.uni-numbox__value) {
                width: 60px;
                height: 40px;
                font-size: 20px;
                font-weight: bold;
            }
        }

        .top{
            display: flex;
            width: 100%;
        }

        #leftImgView {
            width: 23%;
            height: 600px;
            margin-left: 2%;
            margin-top: 1%;
            background-color: #fdf6e3;

            .action{
                margin-bottom: 2%;
                width: 100%;
                background-color: #fdf6e3;

                &:deep(.uni-easyinput__content-textarea) {
                    height: 15vh;
                    font-size: 18px;
                    font-weight: bold;
                }
            }
        }

        #rightContent {
            margin-top: 1%;
            width: 75%;
            display: flex;
            flex-wrap: wrap;
        }

        .addInput {
            font-size: 20px;
            width: 90%;
            margin-left: 8%;
            margin-bottom: 20px;
            background-color: white;
            border: 2px solid #000; /* 设置边框颜色和宽度 */
            border-radius: 10px; /* 设置边框圆角 */
			
			&:deep(.uni-easyinput__content) {
			    border-radius: 10px; /* 设置边框圆角 */
			}

            &:deep(.uni-easyinput__content-textarea) {
                font-size: 20px;
                font-weight: bold;
            }

            &:deep(.uni-easyinput__placeholder-class) {
                font-size: 20px;
            }
        }

        .addInputParent {
            display: flex;
            align-items: center;

            .uni-icons {
                padding: 5px;
            }
        }
    }
}

.tip-popup {
    &:deep(.fixforpc-width) {
        min-width: 0;
        margin-top: 40px;
        padding: 10px 20px;
    }

    &:deep(.uni-popup-message-text) {
        font-size: 18px;
        font-weight: bold;
    }
}

.list-enter-active, .list-leave-active {
    transition: all 0.25s ease-in-out;
}

.list-enter-from, .list-leave-to {
    opacity: 0;
}

.list-enter-to, .list-leave-from {
    opacity: 1;
}

.toolPopupClass{
    #userTable{
        width: 50vw;
        height: 60vh;
        overflow: auto;
        padding: 2.5% 2% 1.5% 2%;
        box-sizing: border-box;
        background-color: #fdf6e3;
    }

}

#userTable :deep(.uni-section){
    background-color: #fdf6e3;
}

</style>