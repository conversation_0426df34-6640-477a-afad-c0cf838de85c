-- 初始化邮件推送配置数据
-- 包含SMTP服务器配置和其他邮件相关配置

-- 插入SMTP配置
INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'SMTP_HOST', 'smtp.gmail.com', 'SMTP服务器主机地址', 'STRING', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'SMTP_PORT', '587', 'SMTP服务器端口', 'NUMBER', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'SMTP_USERNAME', '', 'SMTP用户名（邮箱地址）', 'STRING', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'SMTP_PASSWORD', '', 'SMTP密码或应用专用密码', 'STRING', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'SMTP_AUTH', 'true', '是否启用SMTP认证', 'BOOLEAN', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'SMTP_STARTTLS', 'true', '是否启用STARTTLS', 'BOOLEAN', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'SMTP_SSL', 'false', '是否启用SSL', 'BOOLEAN', 'Y', 'SYSTEM', SYSDATE);

-- 插入邮件发送相关配置
INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'EMAIL_FROM', '<EMAIL>', '默认发件人邮箱', 'STRING', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'MAX_RETRY_COUNT', '3', '邮件发送最大重试次数', 'NUMBER', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'RETRY_INTERVAL', '30', '邮件重试间隔（分钟）', 'NUMBER', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'BATCH_SIZE', '50', '批量发送邮件数量', 'NUMBER', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'LOG_RETENTION_DAYS', '30', '邮件日志保留天数', 'NUMBER', 'Y', 'SYSTEM', SYSDATE);

-- 插入邮件模板相关配置
INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'DEFAULT_LANGUAGE', 'zh_CN', '默认邮件语言', 'STRING', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'EMAIL_ENCODING', 'UTF-8', '邮件编码格式', 'STRING', 'Y', 'SYSTEM', SYSDATE);

-- 插入系统功能开关配置
INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'EMAIL_PUSH_ENABLED', 'true', '是否启用邮件推送功能', 'BOOLEAN', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'AUTO_RETRY_ENABLED', 'true', '是否启用自动重试功能', 'BOOLEAN', 'Y', 'SYSTEM', SYSDATE);

INSERT INTO pcc_email_push_config (id, config_key, config_value, config_desc, config_type, status, create_user, create_date) VALUES
(seq_pcc_email_push_config.nextval, 'LOG_CLEANUP_ENABLED', 'true', '是否启用日志自动清理', 'BOOLEAN', 'Y', 'SYSTEM', SYSDATE);

COMMIT;

-- 查询插入的配置
SELECT config_key, config_value, config_desc, config_type 
FROM pcc_email_push_config 
WHERE create_user = 'SYSTEM' 
ORDER BY config_key;
