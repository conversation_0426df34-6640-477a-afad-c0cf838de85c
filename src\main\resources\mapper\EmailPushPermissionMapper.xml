<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.email.mapper.EmailPushPermissionMapper">

    <resultMap id="ResultMap" type="com.zqn.email.entity.EmailPushPermission">
        <result property="id" column="id" jdbcType="NUMERIC"/>
        <result property="userNo" column="user_no" jdbcType="VARCHAR"/>
        <result property="deptName" column="dept_name" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="pushQuoteV1" column="push_quote_v1" jdbcType="CHAR"/>
        <result property="pushQuoteV2" column="push_quote_v2" jdbcType="CHAR"/>
        <result property="pushQuoteV3" column="push_quote_v3" jdbcType="CHAR"/>
        <result property="pushQuoteV4" column="push_quote_v4" jdbcType="CHAR"/>
        <result property="pushQuoteV5" column="push_quote_v5" jdbcType="CHAR"/>
        <result property="pushEstimateZ" column="push_estimate_z" jdbcType="CHAR"/>
        <result property="pushEstimateZz" column="push_estimate_zz" jdbcType="CHAR"/>
        <result property="pushPVersion" column="push_p_version" jdbcType="CHAR"/>
        <result property="updateEmailNotify" column="update_email_notify" jdbcType="CHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="DATE"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="DATE"/>
    </resultMap>

    <!-- 根据条件查询邮件推送权限列表 -->
    <select id="selectByCondition" resultMap="ResultMap">
        SELECT id, user_no, dept_name, email, push_quote_v1, push_quote_v2, push_quote_v3, 
               push_quote_v4, push_quote_v5, push_estimate_z, push_estimate_zz, push_p_version,
               update_email_notify, status, remark, create_user, create_date, update_user, update_date
        FROM pcc_email_push_permission
        WHERE 1=1
        <if test="userNo != null and userNo != ''">
            AND user_no LIKE '%' || #{userNo} || '%'
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE '%' || #{deptName} || '%'
        </if>
        <if test="email != null and email != ''">
            AND email LIKE '%' || #{email} || '%'
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="includeNoEmail != null and includeNoEmail.equals('N')">
            AND email IS NOT NULL AND email != ''
        </if>
        <if test="emailType != null and emailType != ''">
            <choose>
                <when test="emailType == 'QUOTE_V1'">
                    AND push_quote_v1 = 'Y'
                </when>
                <when test="emailType == 'QUOTE_V2'">
                    AND push_quote_v2 = 'Y'
                </when>
                <when test="emailType == 'QUOTE_V3'">
                    AND push_quote_v3 = 'Y'
                </when>
                <when test="emailType == 'QUOTE_V4'">
                    AND push_quote_v4 = 'Y'
                </when>
                <when test="emailType == 'QUOTE_V5'">
                    AND push_quote_v5 = 'Y'
                </when>
                <when test="emailType == 'ESTIMATE_Z'">
                    AND push_estimate_z = 'Y'
                </when>
                <when test="emailType == 'ESTIMATE_ZZ'">
                    AND push_estimate_zz = 'Y'
                </when>
                <when test="emailType == 'P_VERSION'">
                    AND push_p_version = 'Y'
                </when>
                <when test="emailType == 'UPDATE_NOTIFY'">
                    AND update_email_notify = 'Y'
                </when>
            </choose>
        </if>
        ORDER BY create_date DESC
    </select>

    <!-- 根据工号查询邮件推送权限 -->
    <select id="selectByUserNo" resultMap="ResultMap">
        SELECT id, user_no, dept_name, email, push_quote_v1, push_quote_v2, push_quote_v3, 
               push_quote_v4, push_quote_v5, push_estimate_z, push_estimate_zz, push_p_version,
               update_email_notify, status, remark, create_user, create_date, update_user, update_date
        FROM pcc_email_push_permission
        WHERE user_no = #{userNo}
    </select>

    <!-- 根据邮件类型查询有权限的用户列表 -->
    <select id="selectByEmailType" resultMap="ResultMap">
        SELECT id, user_no, dept_name, email, push_quote_v1, push_quote_v2, push_quote_v3, 
               push_quote_v4, push_quote_v5, push_estimate_z, push_estimate_zz, push_p_version,
               update_email_notify, status, remark, create_user, create_date, update_user, update_date
        FROM pcc_email_push_permission
        WHERE status = 'Y'
        <choose>
            <when test="emailType == 'QUOTE_V1'">
                AND push_quote_v1 = 'Y'
            </when>
            <when test="emailType == 'QUOTE_V2'">
                AND push_quote_v2 = 'Y'
            </when>
            <when test="emailType == 'QUOTE_V3'">
                AND push_quote_v3 = 'Y'
            </when>
            <when test="emailType == 'QUOTE_V4'">
                AND push_quote_v4 = 'Y'
            </when>
            <when test="emailType == 'QUOTE_V5'">
                AND push_quote_v5 = 'Y'
            </when>
            <when test="emailType == 'ESTIMATE_Z'">
                AND push_estimate_z = 'Y'
            </when>
            <when test="emailType == 'ESTIMATE_ZZ'">
                AND push_estimate_zz = 'Y'
            </when>
            <when test="emailType == 'P_VERSION'">
                AND push_p_version = 'Y'
            </when>
            <when test="emailType == 'UPDATE_NOTIFY'">
                AND update_email_notify = 'Y'
            </when>
        </choose>
        ORDER BY dept_name, user_no
    </select>

    <!-- 根据邮件类型和部门查询有权限的用户列表 -->
    <select id="selectByEmailTypeAndDept" resultMap="ResultMap">
        SELECT id, user_no, dept_name, email, push_quote_v1, push_quote_v2, push_quote_v3, 
               push_quote_v4, push_quote_v5, push_estimate_z, push_estimate_zz, push_p_version,
               update_email_notify, status, remark, create_user, create_date, update_user, update_date
        FROM pcc_email_push_permission
        WHERE status = 'Y'
        <if test="deptName != null and deptName != ''">
            AND dept_name = #{deptName}
        </if>
        <choose>
            <when test="emailType == 'QUOTE_V1'">
                AND push_quote_v1 = 'Y'
            </when>
            <when test="emailType == 'QUOTE_V2'">
                AND push_quote_v2 = 'Y'
            </when>
            <when test="emailType == 'QUOTE_V3'">
                AND push_quote_v3 = 'Y'
            </when>
            <when test="emailType == 'QUOTE_V4'">
                AND push_quote_v4 = 'Y'
            </when>
            <when test="emailType == 'QUOTE_V5'">
                AND push_quote_v5 = 'Y'
            </when>
            <when test="emailType == 'ESTIMATE_Z'">
                AND push_estimate_z = 'Y'
            </when>
            <when test="emailType == 'ESTIMATE_ZZ'">
                AND push_estimate_zz = 'Y'
            </when>
            <when test="emailType == 'P_VERSION'">
                AND push_p_version = 'Y'
            </when>
            <when test="emailType == 'UPDATE_NOTIFY'">
                AND update_email_notify = 'Y'
            </when>
        </choose>
        ORDER BY user_no
    </select>

    <!-- 插入邮件推送权限 -->
    <insert id="insert" parameterType="com.zqn.email.entity.EmailPushPermission">
        INSERT INTO pcc_email_push_permission (
            id, user_no, dept_name, email, push_quote_v1, push_quote_v2, push_quote_v3, 
            push_quote_v4, push_quote_v5, push_estimate_z, push_estimate_zz, push_p_version,
            update_email_notify, status, remark, create_user, create_date, update_user, update_date
        ) VALUES (
            seq_pcc_email_push_permission.nextval, #{userNo}, #{deptName}, #{email}, 
            #{pushQuoteV1}, #{pushQuoteV2}, #{pushQuoteV3}, #{pushQuoteV4}, #{pushQuoteV5},
            #{pushEstimateZ}, #{pushEstimateZz}, #{pushPVersion}, #{updateEmailNotify},
            #{status}, #{remark}, #{createUser}, #{createDate}, #{updateUser}, #{updateDate}
        )
    </insert>

    <!-- 根据工号更新邮件推送权限 -->
    <update id="updateByUserNo" parameterType="com.zqn.email.entity.EmailPushPermission">
        UPDATE pcc_email_push_permission
        SET dept_name = #{deptName},
            email = #{email},
            push_quote_v1 = #{pushQuoteV1},
            push_quote_v2 = #{pushQuoteV2},
            push_quote_v3 = #{pushQuoteV3},
            push_quote_v4 = #{pushQuoteV4},
            push_quote_v5 = #{pushQuoteV5},
            push_estimate_z = #{pushEstimateZ},
            push_estimate_zz = #{pushEstimateZz},
            push_p_version = #{pushPVersion},
            update_email_notify = #{updateEmailNotify},
            status = #{status},
            remark = #{remark},
            update_user = #{updateUser},
            update_date = #{updateDate}
        WHERE user_no = #{userNo}
    </update>

    <!-- 根据工号删除邮件推送权限 -->
    <delete id="deleteByUserNo">
        DELETE FROM pcc_email_push_permission WHERE user_no = #{userNo}
    </delete>

    <!-- 批量插入邮件推送权限 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO pcc_email_push_permission (
            id, user_no, dept_name, email, push_quote_v1, push_quote_v2, push_quote_v3, 
            push_quote_v4, push_quote_v5, push_estimate_z, push_estimate_zz, push_p_version,
            update_email_notify, status, remark, create_user, create_date, update_user, update_date
        )
        <foreach collection="permissions" item="item" separator="UNION ALL">
            SELECT seq_pcc_email_push_permission.nextval, #{item.userNo}, #{item.deptName}, #{item.email}, 
                   #{item.pushQuoteV1}, #{item.pushQuoteV2}, #{item.pushQuoteV3}, #{item.pushQuoteV4}, #{item.pushQuoteV5},
                   #{item.pushEstimateZ}, #{item.pushEstimateZz}, #{item.pushPVersion}, #{item.updateEmailNotify},
                   #{item.status}, #{item.remark}, #{item.createUser}, #{item.createDate}, #{item.updateUser}, #{item.updateDate}
            FROM dual
        </foreach>
    </insert>

    <!-- 根据用户工号列表查询权限信息 -->
    <select id="selectByUserNos" resultMap="ResultMap">
        SELECT id, user_no, dept_name, email, push_quote_v1, push_quote_v2, push_quote_v3, 
               push_quote_v4, push_quote_v5, push_estimate_z, push_estimate_zz, push_p_version,
               update_email_notify, status, remark, create_user, create_date, update_user, update_date
        FROM pcc_email_push_permission
        WHERE user_no IN
        <foreach collection="userNos" item="userNo" open="(" separator="," close=")">
            #{userNo}
        </foreach>
        ORDER BY dept_name, user_no
    </select>

    <!-- 查询所有部门列表 -->
    <select id="selectAllDepts" resultType="java.lang.String">
        SELECT DISTINCT dept_name
        FROM pcc_email_push_permission
        WHERE dept_name IS NOT NULL AND dept_name != ''
        ORDER BY dept_name
    </select>

    <!-- 统计权限配置数量 -->
    <select id="countByCondition" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM pcc_email_push_permission
        WHERE 1=1
        <if test="userNo != null and userNo != ''">
            AND user_no LIKE '%' || #{userNo} || '%'
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE '%' || #{deptName} || '%'
        </if>
        <if test="email != null and email != ''">
            AND email LIKE '%' || #{email} || '%'
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="includeNoEmail != null and includeNoEmail.equals('N')">
            AND email IS NOT NULL AND email != ''
        </if>
    </select>

</mapper>
