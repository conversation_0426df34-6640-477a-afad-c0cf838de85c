<template>
  <view class="container">
    <view class="header">
      <view class="header-left">
        <button class="back-btn" @click="goBack">
          <text class="back-icon">←</text>
          <text class="back-text">返回</text>
        </button>
      </view>
      <view class="header-center">
        <text class="title">查看学员记录</text>
      </view>
      <view class="header-right">
        <button class="edit-btn" @click="editRecord">
          <text class="edit-icon">✏️</text>
          <text class="edit-text">编辑</text>
        </button>
      </view>
    </view>
    
    <view class="content">
      <scroll-view scroll-y class="form-container">
        <view class="form-section">
          <!-- 基本信息 -->
          <view class="section-title">基本信息</view>
          
          <!-- 基本信息布局：左侧表单，右侧鞋图 -->
          <view class="form-layout">
            <!-- 左侧表单区域 -->
            <view class="form-left">
              <!-- 第一行：日期和单号 -->
              <view class="form-row">
                <view class="form-item">
                  <text class="form-label">日期</text>
                  <view class="readonly-field">{{ formData.recordDate || '-' }}</view>
                </view>
                
                <view class="form-item">
                  <text class="form-label">单号/数据</text>
                  <view class="readonly-field">{{ formData.orderNo || '-' }}</view>
                </view>
              </view>
              
              <!-- 第二行：学员选择 -->
              <view class="form-row">
                <view class="form-item">
                  <text class="form-label">学员</text>
                  <view class="readonly-field">{{ formData.student || '-' }}</view>
                </view>
              </view>
              
              <!-- 第三行：订单数量和型体 -->
              <view class="form-row" v-if="(formData.ordQty !== null && formData.ordQty !== undefined && formData.ordQty !== '') || (formData.modelNo && formData.modelNo.trim())">
                <!-- 订单数量字段 -->
                <view class="form-item" v-if="formData.ordQty !== null && formData.ordQty !== undefined && formData.ordQty !== ''">
                  <text class="form-label">订单数量</text>
                  <view class="readonly-field">{{ formData.ordQty }}</view>
                </view>
                
                <!-- 型体字段 -->
                <view class="form-item" v-if="formData.modelNo && formData.modelNo.trim()">
                  <text class="form-label">型体</text>
                  <view class="readonly-field">{{ formData.modelNo }}</view>
                </view>
              </view>
            </view>
            
            <!-- 右侧鞋图区域 -->
            <view class="form-right">
              <view class="form-item shoe-pic-item">
                <text class="form-label">鞋图预览</text>
                <view class="image-display-area-compact">
                  <view v-if="formData.shoePic" class="image-preview-compact">
                    <image 
                      :src="getImageUrl(formData.shoePic)"
                      mode="aspectFit" 
                      class="preview-image-compact"
                      @click="previewImage(formData.shoePic)"
                    />
                  </view>
                  <view v-else class="no-image-placeholder-compact">
                    <text class="placeholder-icon-compact">📷</text>
                    <text class="placeholder-text-compact">无鞋图</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 动作图片 -->
        <view class="form-section">
          <view class="section-title">动作图片</view>
          
          <view class="form-item full-width">
            <text class="form-label">实物动作</text>
            <view class="image-gallery">
              <view v-if="formData.actionPics && formData.actionPics.length > 0" class="image-list">
                <view 
                  v-for="(pic, index) in formData.actionPics" 
                  :key="index" 
                  class="image-item">
                  <image 
                    :src="getImageUrl(pic)"
                    mode="aspectFill" 
                    class="gallery-image"
                    @click="previewImage(pic)"
                  />
                </view>
              </view>
              <view v-else class="no-images">
                <text class="no-images-text">暂无实物动作图片</text>
              </view>
            </view>
          </view>
          
          <view class="form-item full-width">
            <text class="form-label">改善动作</text>
            <view class="image-gallery">
              <view v-if="formData.improvePics && formData.improvePics.length > 0" class="image-list">
                <view 
                  v-for="(pic, index) in formData.improvePics" 
                  :key="index" 
                  class="image-item">
                  <image 
                    :src="getImageUrl(pic)"
                    mode="aspectFill" 
                    class="gallery-image"
                    @click="previewImage(pic)"
                  />
                </view>
              </view>
              <view v-else class="no-images">
                <text class="no-images-text">暂无改善动作图片</text>
              </view>
            </view>
          </view>
          
          <view class="form-item full-width">
            <text class="form-label">成品</text>
            <view class="image-gallery">
              <view v-if="formData.productPics && formData.productPics.length > 0" class="image-list">
                <view 
                  v-for="(pic, index) in formData.productPics" 
                  :key="index" 
                  class="image-item">
                  <image 
                    :src="getImageUrl(pic)"
                    mode="aspectFill" 
                    class="gallery-image"
                    @click="previewImage(pic)"
                  />
                </view>
              </view>
              <view v-else class="no-images">
                <text class="no-images-text">暂无成品图片</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 文字信息 -->
        <view class="form-section">
          <view class="section-title">文字信息</view>
          
          <view class="form-item full-width">
            <text class="form-label">学员感想</text>
            <view class="readonly-textarea">
              {{ formData.studentThoughts || '暂无感想记录' }}
            </view>
          </view>
          
          <view class="form-item full-width">
            <text class="form-label">教官评语</text>
            <view class="readonly-textarea">
              {{ formData.communicationIssues || '暂无教官评语记录' }}
            </view>
          </view>
          
          <view class="form-item">
            <text class="form-label">动作学会</text>
            <view class="learned-display">
              <view 
                class="learned-status" 
                :class="getLeariedStatusClass(formData.learned)"
              >
                <view class="learned-icon">{{ formData.learned ? '✓' : '✗' }}</view>
                <text class="learned-text">{{ formData.learned ? '是' : '否' }}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 图片预览组件 -->
    <image-popup
      v-model:show="popupVisible"
      :imageUrl="previewImageUrl"
    />
  </view>
</template>

<script>
import ImagePopup from '@/components/ImagePopup.vue';
import urlPrefix from '@/pages/common/urlPrefix.js';

export default {
  components: {
    ImagePopup
  },
  data() {
    return {
      recordId: null,
      formData: {
        recordDate: '',
        orderNo: '',
        student: '',
        ordQty: null,
        modelNo: '',
        shoePic: '',
        actionPics: [],
        improvePics: [],
        productPics: [],
        studentThoughts: '',
        communicationIssues: '',
        learned: false
      },
      previewImageUrl: '',
      popupVisible: false
    }
  },
  onLoad(options) {
    if (options.id) {
      this.recordId = options.id;
      this.loadRecord();
    } else {
      uni.showToast({
        title: '缺少记录ID',
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    async loadRecord() {
      try {
        uni.showLoading({
          title: '加载记录中',
          mask: true
        });
        
        const res = await uni.request({
          url: urlPrefix + `/student-record/${this.recordId}`,
          method: 'GET'
        });
        
        uni.hideLoading();
        
        if (res.data && res.data.code === 1 && res.data.data) {
          const record = res.data.data;
          this.formData = {
            recordDate: record.recordDate || '',
            orderNo: record.orderNo || '',
            student: record.student || '',
            ordQty: record.ordQty || null,
            modelNo: record.modelNo || '',
            shoePic: record.shoePic || '',
            actionPics: record.actionPics || [],
            improvePics: record.improvePics || [],
            productPics: record.productPics || [],
            studentThoughts: record.studentThoughts || '',
            communicationIssues: record.communicationIssues || '',
            learned: record.learned || false
          };
          
          // 如果有工单号但没有鞋图，尝试自动获取鞋图
          if (this.formData.orderNo && this.formData.orderNo.trim() && !this.formData.shoePic) {
            setTimeout(() => {
              this.getShoeImageByOrderNo(this.formData.orderNo);
            }, 500);
          }
        } else {
          throw new Error(res.data?.msg || res.data?.message || '获取记录失败');
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '获取记录失败',
          icon: 'none'
        });
        console.error('获取记录失败:', error);
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    },
    
    async getShoeImageByOrderNo(orderNo) {
      if (!orderNo || !orderNo.trim()) {
        return;
      }
      
      try {
        const res = await uni.request({
          url: urlPrefix + '/student-record/shoe-image?orderNo=' + encodeURIComponent(orderNo),
          method: 'GET'
        });
        console.log('获取鞋图响应:', res);
        
        // 处理后端响应格式 - 现在返回的是StudentRecord对象
        if (res.data && res.data.code === 1 && res.data.data) {
          const studentRecord = res.data.data;
          
          // 更新订单数量和型体信息（只有在后端返回了数据时才更新）
          if (studentRecord.ordQty !== null && studentRecord.ordQty !== undefined) {
            this.formData.ordQty = studentRecord.ordQty;
          }
          if (studentRecord.modelNo && studentRecord.modelNo.trim()) {
            this.formData.modelNo = studentRecord.modelNo;
          }
          
          // 更新鞋图信息
          if (studentRecord.shoePic && studentRecord.shoePic.trim()) {
            // 后端返回的shoePic可能是base64数据或文件路径
            const shoePicData = studentRecord.shoePic;
            
            // 如果是base64数据但没有前缀，添加前缀
            if (!shoePicData.startsWith('data:') && !shoePicData.startsWith('http://') && !shoePicData.startsWith('https://')) {
              // 假设是base64数据，添加前缀
              this.formData.shoePic = 'data:image/jpeg;base64,' + shoePicData;
            } else {
              this.formData.shoePic = shoePicData;
            }
          }
        } else {
          // 静默处理，不显示错误提示，因为这是自动获取
          console.log('未找到该单号的鞋图信息:', res.data?.msg || res.data?.message);
        }
      } catch (error) {
        console.error('获取鞋图异常:', error);
        // 静默处理错误，不影响用户体验
      }
    },
    
    getImageUrl(imagePath) {
      if (!imagePath) return '';
      // 如果已经是完整URL，直接返回
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        return imagePath;
      }
      // 如果是base64格式，直接返回
      if (imagePath.startsWith('data:')) {
        return imagePath;
      }
      // 否则生成完整的文件访问URL
      return `${urlPrefix}/api/files/get?url=${imagePath}`;
    },
    
    previewImage(imagePath) {
      if (!imagePath) return;
      this.previewImageUrl = this.getImageUrl(imagePath);
      this.popupVisible = true;
    },
    
    editRecord() {
      uni.navigateTo({
        url: `/pages/student-record/edit?id=${this.recordId}`
      });
    },
    
    goBack() {
      uni.navigateBack();
    },
    
    getLeariedStatusClass(learned) {
      if (learned) {
        return 'learned-yes';
      } else {
        return 'learned-no';
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20px;
  height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f9fafb;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
  padding: 10px 5px;
  background: linear-gradient(135deg, #fdf6e3, #eee8d5);
  color: #657b83;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-left {
  width: 10%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 2%;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-right {
  width: 10%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: 2%;
}

.back-btn, .edit-btn {
  display: flex;
  align-items: center;
  border: none;
  color: #657b83;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.back-btn {
  background-color: rgba(101, 123, 131, 0.2);
}

.back-btn:hover {
  background-color: rgba(101, 123, 131, 0.3);
}

.edit-btn {
  background-color: rgba(147, 161, 161, 0.3);
}

.edit-btn:hover {
  background-color: rgba(147, 161, 161, 0.4);
}

.back-icon, .edit-icon {
  font-size: 18px;
  margin-right: 5px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 1px;
  text-align: center;
}

.content {
  flex: 1;
  overflow: hidden;
}

.form-container {
  height: 100%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  padding-bottom: 60px;
}

.form-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #657b83;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #eee8d5;
}

.form-layout {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.form-left {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-right {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  min-width: 180px;
  max-width: 200px;
}

.form-right .form-item {
  margin-bottom: 0;
}

.form-row {
  display: flex;
  gap: 20px;
  align-items: flex-end;
}

.form-item {
  flex: 1;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.form-item.full-width {
  width: 100%;
}

.form-item.shoe-pic-item {
  flex: 0 0 auto;
  width: auto;
  min-width: 160px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #657b83;
  margin-bottom: 8px;
}

.readonly-field {
  width: 100%;
  height: 40px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 0 12px;
  font-size: 14px;
  background-color: #f9f9f9;
  color: #333;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  cursor: default;
}

.readonly-textarea {
  width: 100%;
  min-height: 80px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px;
  font-size: 14px;
  background-color: #f9f9f9;
  color: #333;
  box-sizing: border-box;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.image-display-area-compact {
  width: 120px;
  height: 120px;
  border: 1px solid #e0e7ff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: #f8f9fa;
}

.image-preview-compact {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image-compact {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: pointer;
  border-radius: 4px;
}

.no-image-placeholder-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  color: #8c9db5;
  text-align: center;
  padding: 4px;
}

.placeholder-icon-compact {
  font-size: 16px;
  opacity: 0.5;
}

.placeholder-text-compact {
  font-size: 12px;
  font-weight: 500;
}

.image-gallery {
  width: 100%;
  margin-top: 10px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: flex-start;
  justify-content: flex-start;
}

.image-item {
  position: relative;
  width: 100px;
  height: 100px;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
  margin: 0;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s;
}

.gallery-image:hover {
  transform: scale(1.05);
}

.no-images {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  border: 1px dashed #ddd;
  border-radius: 6px;
  background-color: #f9f9f9;
}

.no-images-text {
  color: #8c9db5;
  font-size: 14px;
  font-style: italic;
}

.learned-display {
  margin-top: 10px;
}

.learned-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 16px;
  min-width: 80px;
  justify-content: center;
}

.learned-yes {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.learned-no {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.learned-icon {
  font-size: 18px;
  font-weight: bold;
}

.learned-text {
  font-size: 16px;
  font-weight: 600;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .form-layout {
    flex-direction: column;
    gap: 15px;
  }
  
  .form-right {
    max-width: none;
    justify-content: flex-start;
  }
  
  .form-right .form-item {
    width: 100%;
  }
  
  .image-list {
    justify-content: center;
  }
  
  .form-container {
    padding: 15px;
    padding-bottom: 80px;
  }
  
  .image-item {
    width: 80px;
    height: 80px;
  }
}

@media screen and (orientation: landscape) {
  .container {
    padding: 15px;
    box-sizing: border-box;
    width: 100%;
  }
  
  .header {
    margin-bottom: 15px;
  }
  
  .form-container {
    padding: 15px;
    box-sizing: border-box;
    width: 100%;
  }
  
  .form-section {
    margin-bottom: 25px;
  }
  
  .form-layout {
    align-items: flex-end;
  }
}
</style> 