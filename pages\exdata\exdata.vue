<template>
	<view class="container">
	 <view class="right-top-top">
		<uni-icons class="back" @click="back" type="back" size="36" style="margin-left:1vw;margin-top:10rpx;"></uni-icons>
		<uni-title
		  type="h2"
		  :title="t('异常数据')"
		  align="center"
		  style="margin-top:-0.7vw;margin-left: 18vw;"
		></uni-title>
		<text style="color: #c5c8c8;margin-right:0.3vw;margin-top:0.88vw;">{{switchDay==true?t('当日'):t('全部')}}</text><uv-switch inactive-color="#c5c8c8" :value="switchDay" @change="asyncChange3"  style="margin-right:0.8vw;margin-top:0.6vw;"></uv-switch>
		<text style="color: #c5c8c8;margin-right:0.3vw;margin-top:0.88vw;">{{switchMo=='Y'?t('已结案'):t('未结案')}}</text><uv-switch inactive-color="#c5c8c8" :value="switchMo=='Y'?true:false" @change="asyncChange2"  style="margin-right:0.8vw;margin-top:0.6vw;"></uv-switch>
		<uni-icons @click="toggleShow" :type="stus==1?'auth-filled':'person-filled'" size="32" style="margin-right:1.5vw;margin-top:0.6vw;"></uni-icons>
	
	</view>
	<view class="propertyBox">
		<view  @click="openBrand(1)" class="inpBr">
			<uni-easyinput disabled v-model="ypdh"  :placeholder="t('样品单号')"></uni-easyinput>
		</view>
		<view  @click="openBrand(2)" class="inpBr">
			<uni-easyinput disabled v-model="zrbm"  :placeholder="t('责任部门')"></uni-easyinput>
		</view>
		<view  @click="openBrand(3)" class="inpBr">
			<uni-easyinput disabled v-model="ycsm"  :placeholder="t('异常说明')"></uni-easyinput>
		</view>
		<view  @click="openBrand(4)" class="inpBr">
			<uni-easyinput disabled v-model="ycdd"  :placeholder="t('异常地点')"></uni-easyinput>
		</view>
		<view class="inpBr">
			<uni-easyinput type="number" @blur="getQty" v-model="wtsl" :placeholder="t('问题数量')"></uni-easyinput>
		</view>	
		<view class="inpBr">
			<uni-data-select :placeholder="t('问题说明')" :clear="false"
		        :localdata="wtsm1" :value="selectWt" @change="selectChang"
		      ></uni-data-select>
		</view>	
		<view class="inpBr" style="width: 10%;margin-top:-10rpx;">
			<button type="success" @click="addSaveData" style="background-color: #18bc37; color: white; font-weight: bold;">{{ t('保存') }}</button>
		</view>	
		<view class="inpBr" style="width: 10%;margin-top:-10rpx;margin-right: 10rpx;">
		<button type="success" @click="emptyData" style="background-color: #e43d33; color: white; font-weight: bold;">{{ t('清除') }}</button>
		</view>	
	</view>

	  <view style="height: 79%;margin-top:20rpx;">
		<zb-table
			:show-header="true"
			:columns="column"
			:stripe="true"
			:fit="true"
			ref="zbTable"
			@rowClick="rowClick"
			:border="true"
			@edit="xlGetData"
			@dele="dele"
			:data="dataList"></zb-table>
			
			<view class="left-bottom">
				<uni-pagination
				  show-icon="true"
				  :total="pageCount"
				  :current="firstPageNo"
				  :pageSize="firstPageSize"
				  @change="firstChange"
				></uni-pagination>
			</view>
	</view>
	
	<!--单击单行弹出详情-->
	<view class="modelPopup">
	  <uni-popup
	    ref="modelPopup"
	    type="center"
	  >
	    <view v-if="modelData" class="modelBox">
	      <uni-title
	        :title="t('单号详情')"
	        type="h2"
	        align="center"
	      ></uni-title>
	      
	      <view class="modelData">
	        <uni-section :title="t('样品单号')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.ord_no }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('型体代号')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.model_no }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('阶段')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.phase }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('责任部门')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.pb_dept }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('异常说明')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.pb_desc ? modelData.pb_desc : "--" }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('问题说明1')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.pb_desc1 ? modelData.pb_desc1 : "--" }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('异常地点')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.pb_addr ? modelData.pb_addr : "--" }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('问题数量')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.pb_qty ? modelData.pb_qty : "--" }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('样品类型')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.dev_type ? modelData.dev_type : "--" }}
	          </text>
	        </uni-section>
	        
	        <uni-section :title="t('解决方法')" titleFontSize="1.25rem" type="line" class="modelAttribute">
			  <uni-easyinput type="textarea" disabled style="font-size: 1rem; margin-left: 1rem;width:80%;" :value="modelData.methoed"></uni-easyinput>
	        </uni-section>
	        
	        <uni-section :title="t('结案日期')" titleFontSize="1.25rem" type="line" class="modelAttribute">
	          <text style="font-size: 1rem; margin-left: 1rem;">
	            {{ modelData.cl_date ? modelData.cl_date : "--" }}
	          </text>
	        </uni-section>
			
			<uni-section :title="t('结案状态')" titleFontSize="1.25rem" type="line" class="modelAttribute">
			  <uv-switch :value="modelData.cl_flag=='Y'?true:false" disabled style="margin-left: 1rem;"></uv-switch>
			</uni-section>
	      </view>
	    </view>
	  </uni-popup>
	</view>
	<!--单击单行弹出修改界面-->
	<view class="updateSpecificationPopup">
	  <uni-popup
	    ref="updateSpecificationPopup"
	    type="center"
	    
	  >
	    <view class="updateSpecificationBox">
	      <uni-title
	        :title="t('修改信息')"
	        type="h2"
	        align="center"
	      ></uni-title>
	      <view class="updateSpecificationData">
	        <uni-section :title="t('样品单号')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
	          <uni-easyinput :value="updateSpecificationDetail.ord_no" disabled></uni-easyinput>
	        </uni-section>
			
			<uni-section :title="t('责任部门')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.pb_dept }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('异常地点')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
				{{ updateSpecificationDetail.pb_addr }}
			  </text>
			</uni-section>
			
			<uni-section :title="t('添加日期')" titleFontSize="1.25rem" type="line" class="updateSpecificationAttribute">
			  <text style="font-size: 1rem; margin-left: 1rem;">
			    {{ updateSpecificationDetail.ins_date}}
			  </text>
			</uni-section>
	        
	       <uni-section :title="t('结案状态')" titleFontSize="1.25rem" type="circle" class="updateSpecificationAttribute">
			  <uv-switch :value="updateSpecificationDetail.cl_flag=='Y'?true:false" asyncChange @change="asyncChange" style="margin-left: 1rem;"></uv-switch>
	       </uni-section>
		   
		    <uni-section :title="t('解决方法')" titleFontSize="1.25rem" type="circle" class="updateSpecificationAttribute">
			   <uni-easyinput @blur="saveMethon" type="textarea" autoHeight style="font-size: 1rem; margin-left: 1rem;width:80%;" v-model="updateSpecificationDetail.methoed"></uni-easyinput>
			</uni-section>
	        
	        <!--<view class="submit">
	          <button @click="submitUpdateSpecification" style="margin: 0 1rem;background-color: #18bc37; color: white; font-weight: bold;">保存修改</button>
	          <button @click="updateSpecificationPopup.close()" style="margin: 0 1rem;background-color: #e43d33; color: white; font-weight: bold;">取消修改</button>
	        </view>-->
	      </view>
	    </view>
	  </uni-popup>
	</view>
	

	</view>
</template>

<script setup>
  import { onMounted,ref, reactive } from 'vue'
  import { onPullDownRefresh } from '@dcloudio/uni-app'
  import { useI18n } from 'vue-i18n'
  import urlPrefix from '@/pages/common/urlPrefix.js'
  
  // 国际化
  const { t } = useI18n()
  
	// const urlPrefix = "http://***********:8200";
	//const urlPrefix = "http://**********:8080";
	const ypdh=ref()
	const zrbm=ref()
	const ycsm=ref()
	const ycdd=ref()
	const wtsl=ref(0)
	const selectWt=ref()
	const wtsm1=ref([])
	const switchMo=ref('N')
	const switchDay=ref(false)
	//第几页
	const firstPageNo=ref(1)
	const firstPageSize=ref(20)
	const pageCount=ref(0)
	
	//表单数据
	const dataList=ref([])
	
	//扫描状态
	const brandCode=ref(0)
	
	//详情弹窗
	const modelData = ref()
	const modelPopup = ref()
	
	const insUs=uni.getStorageSync("loUserNo")
	const insName=uni.getStorageSync("loginUserName")
	const iuser=ref("")
	
	//修改弹窗
	const updateSpecificationPopup = ref()
	const updateSpecificationDetail = reactive({
	  ord_no: "",
	  pb_dept: "",
	  pb_addr: "",
	  methoed: "",
	  cl_flag: "",
	  upd_user: insUs,
	  ins_date: ""
	})
	
	//删除参数
	const deleSpecificationDetail = reactive({
	  ord_no: "",
	  pb_dept: "",
	  pb_addr: "",
	  upd_user: insUs,
	  ins_date: ""
	})
	
	//下拉刷新
	 onPullDownRefresh (()=>{	//通过 onPullDownRefresh  可以监听到下拉刷新的动作
			uni.startPullDownRefresh({
				success(){
					//console.log(urlPrefix)
					getPageCount();
					getData();
					uni.stopPullDownRefresh()	//停止当前页面下拉刷新。
				},fail(){}
			})
	    }) 
	
	//保存data
	const saveData = reactive({
	  "ord_no": "",
	  "pb_qty": "",
	  "pb_desc1":"",
	  "pb_dept": "",
	  "pb_desc": "",
	  "pb_addr": "",
	  "ins_user": insUs
	})
    const stus=ref(0)
	//点击图标显示自己的资料
	function toggleShow(){
		stus.value=stus.value==0?1:0
		console.log(stus)
		iuser.value=iuser.value?"":insUs
		firstPageNo.value=1
		getPageCount();
		getData();
	}
	
	//点击切换当日或全部
	async function asyncChange3(e) {
		switchDay.value=e?true:false
		firstPageNo.value=1
		getPageCount();
		getData();
		//console.log(switchMo.value)
	}
	
	//点击切换结案未结案
	async function asyncChange2(e) {
		switchMo.value=e?'Y':'N'
		firstPageNo.value=1
		getPageCount();
		getData();
		//console.log(switchMo.value)
	}
	
	//点击下拉监听
	async function asyncChange(e) {
		updateSpecificationDetail.pb_dept = updateSpecificationDetail.pb_dept.split("-")[0]
		updateSpecificationDetail.pb_addr = updateSpecificationDetail.pb_addr.split("-")[0]
		updateSpecificationDetail.cl_flag=e?'Y':'N'
		
		await uni.request({
		  url: urlPrefix + "/scan/updateState",
		  method: "POST",
		  data: updateSpecificationDetail
		}).then(res => {
				  console.log(res.data)
		  if (res.data.code) {
		    uni.showToast({
		    	title: t('结案修改成功！')
		    });
		  } else {
		    uni.showToast({
		    	title: t('结案修改失败！'),
		    	icon:"error"
		    });
		  }
		}).catch(err => {
			console.log(err)
		})
		getPageCount();
		getData();
		//  uni.showModal({
		// 	title:提示',
		// 	content:continfo,
		// 	success: function (res) {
		// 	console.log(confm);
		// 			if (res.confirm) {
						
		// 				updateSpecificationDetail.cl_flag=e?'Y':'N'
						
		// 			}else if (res.cancel) {
		// 				if(confm)
		// 				updateSpecificationDetail.cl_flag='N'
		// 				else
		// 				updateSpecificationDetail.cl_flag='Y'
		// 			}
				
				
		// 	}
		// });
		//console.log(updateSpecificationDetail.cl_flag);
		
	}
	
	
	//点击保存解决方法
	async function saveMethon(e) {
		updateSpecificationDetail.pb_dept = updateSpecificationDetail.pb_dept.split("-")[0]
		updateSpecificationDetail.pb_addr = updateSpecificationDetail.pb_addr.split("-")[0]
		await uni.request({
		  url: urlPrefix + "/scan/updateMethod",
		  method: "POST",
		  data: updateSpecificationDetail
		}).then(res => {
				  console.log(res.data)
		  if (res.data.code) {
		    uni.showToast({
		    	title: t('保存成功！')
		    });
		  } else {
		    uni.showToast({
		    	title: t('保存失败！'),
		    	icon:"error"
		    });
		  }
		}).catch(err => {
			console.log(err)
		})
		getPageCount();
		getData();
	}
	
	
	//修改保存
	async function submitUpdateSpecification() {
		
		updateSpecificationDetail.pb_dept = updateSpecificationDetail.pb_dept.split("-")[0]
		updateSpecificationDetail.pb_addr = updateSpecificationDetail.pb_addr.split("-")[0]
	  await uni.request({
	    url: urlPrefix + "/scan/updateException",
	    method: "POST",
	    data: updateSpecificationDetail
	  }).then(res => {
		  console.log(res.data)
	    if (res.data.code) {
	      uni.showToast({
	      	title: t('修改成功！')
	      });
		  getPageCount();
		  getData();
	    } else {
	      uni.showToast({
	      	title: t('修改失败！'),
	      	icon:"error"
	      });
	    }
	  }).catch(err => {
	    console.log(err)
	  })
	  
	  updateSpecificationPopup.value.close()
	}
	
	//选中改变
	function selectChang(e){
		selectWt.value=e
		saveData.pb_desc1=selectWt.value
	}
	
	//选中改变
	function getQty(){
		saveData.pb_qty=wtsl.value
	}
	
	//添加一条资料
	async function addSaveData() {
		console.log(saveData)
		if(brandCode.value!=4){
			let titleVa=t('请完成前四个扫描后进行保存');
			switch (brandCode.value){
				case 0:
					titleVa=t('请扫描样品单！')
					break;
				case 1:
					titleVa=t('请扫描责任部门！')
					break;
				case 2:
					titleVa=t('请扫描异常说明！')
					break;
				case 3:
					titleVa=t('请扫描异常地点！')
					break;	
				default:
					break;
			}
			uni.showToast({
				title: titleVa,
				icon:"error"
			});
			return;
		}
		//判断数量不能小于0等于0
		if(wtsl.value<=0){
			uni.showToast({
				title: t('问题数量不能小于等于0'),
				icon:"error"
			});
			return;
		}
		uni.showModal({
			content: t('确认保存该笔异常吗？'),
			success: (res) => {
				if (res.confirm) {
					uni.request({
					   url: urlPrefix + "/scan/addException",
					   method: "POST",
					   data: saveData
					 }).then(res => {
						console.log(res.data)
					   if (res.data.code) {
					     uni.showToast({
					     	title: t('保存成功！')
					     });
						 getPageCount();
						 getData();
						 ypdh.value=""
						 zrbm.value=""
						 ycsm.value=""
						 ycdd.value=""
						 wtsl.value=0
						 wtsm1.value=[]
					   } else {
					     uni.showToast({
					     	title: t('保存失败，请稍后重试！'),
					     	icon:"error"
					     });
					   }
					 }).catch(err => {
					   console.log(err)
					 })
					
				}
			}
		})
	
	}
	
	const column=ref([
          { name: 'ord_no', label: t('样品单号'),fixed:true,emptyString:'--' },
		  { name: 'model_no', label: t('型体代号'),sorter:false},
		  { name: '',fixed:true, type:'operation',label: t('删除'),renders:[
			 {
			   name:t('删除'),
			   type:'warn',
			   func:'dele'// func 代表子元素点击的事件 父元素接收的事件 父元素 @edit
			 }
		   ]},
          { name: 'phase', label: t('阶段')},
          { name: 'pb_dept', label: t('责任部门')},
          { name: 'pb_desc', label: t('异常说明') },
          { name: 'pb_desc1', label: t('问题说明1') },
          { name: 'pb_addr', label: t('异常地点') },
          { name: 'pb_qty', label: t('问题数量'), sorter:true},
          { name: 'dev_type', label: t('样品类型') },
		  { name: 'methoed', label: t('解决方法') },
		  { name: 'cl_flag', label: t('结案状态'),filters:{Y:t('已结案'),N:t('未结案')} },
        
		  { name: 'cl_date', label: t('结案日期'), sorter:true},
		  { name: 'ins_user', label: t('添加人'), sorter:true},
		  { name: 'ins_date', label: t('添加日期'), sorter:true}
        ]);

	
	function xlGetData(e){
		console.log(e)
		updateSpecificationDetail.ord_no = e.ord_no
		updateSpecificationDetail.pb_dept = e.pb_dept
		updateSpecificationDetail.pb_addr = e.pb_addr
		updateSpecificationDetail.methoed = e.methoed
		updateSpecificationDetail.cl_flag = e.cl_flag
		updateSpecificationDetail.ins_date = e.ins_date
		updateSpecificationPopup.value.open()
	}
	
	async function dele(e){
		console.log(e)
		if(e.ins_user != insUs){
			uni.showToast({
				title: t('不可删除其他人员添加的单号！'),
				icon:"error"
			});
			return;
		}
		
		deleSpecificationDetail.ord_no = e.ord_no
		deleSpecificationDetail.pb_dept = e.pb_dept
		deleSpecificationDetail.pb_addr = e.pb_addr
		deleSpecificationDetail.ins_date = e.ins_date
		
		deleSpecificationDetail.pb_dept = deleSpecificationDetail.pb_dept.split("-")[0]
		deleSpecificationDetail.pb_addr = deleSpecificationDetail.pb_addr.split("-")[0]
		
		uni.showModal({
			title:t('提示'),
			content:t('确定删除该笔资料吗？'),
			success: function (res) {
					if (res.confirm) {
					uni.request({
						  url: urlPrefix + "/scan/deleteException",
						  method: "POST",
						  data: deleSpecificationDetail
						}).then(res => {
								  console.log(res.data)
						  if (res.data.code) {
						    uni.showToast({
						    	title: t('删除成功！')
						    });
								  getPageCount();
								  getData();
						  } else {
						    uni.showToast({
						    	title: t('删除失败！'),
						    	icon:"error"
						    });
						  }
						}).catch(err => {
						  console.log(err)
						})
					}else if (res.cancel) {
						return
					}
			}
		});

	}
	
	//返回首页
	function back() {
	  uni.navigateBack({
	  	delta: 1,
	  	animationType: 'pop-out',
	  	animationDuration: 200
	  })
	}
	
	function emptyData(){
		ypdh.value=""
		zrbm.value=""
		ycsm.value=""
		ycdd.value=""
		wtsl.value=0
		wtsm1.value=[]
		uni.showToast({
			title: t('清空成功！')
		});
	}
			
	async function firstChange(e){
		firstPageNo.value=e.current;
		await uni.request({
			   url: urlPrefix + "/scan/getScanException",
			   data:{
					"page_no": firstPageNo.value,
					"page_size": firstPageSize.value,
					"user_id":insName,
					"ins_user": iuser.value,
					"cl_flag": switchMo.value,
					"today": switchDay.value
				},
			   method: "POST"
			 }).then(res => {
				//console.log(res.data);
				dataList.value = res.data.data
			 }).catch(err => {
			   console.log(err)
			 })
	}	
	
	//点击详情
	function rowClick(e){
		console.log(e)
		updateSpecificationDetail.ord_no = e.ord_no
		updateSpecificationDetail.pb_dept = e.pb_dept
		updateSpecificationDetail.pb_addr = e.pb_addr
		updateSpecificationDetail.methoed = e.methoed
		updateSpecificationDetail.cl_flag = e.cl_flag
		updateSpecificationDetail.ins_date = e.ins_date
		updateSpecificationPopup.value.open()
	}	

	//获取数据
	async function getData(){
		//console.log(iuser.value);
		await uni.request({
			   url: urlPrefix + "/scan/getScanException",
			   data:{
					"page_no": firstPageNo.value,
					"page_size": firstPageSize.value,
					"user_id":insName,
					"ins_user": iuser.value,
					"cl_flag": switchMo.value,
					"today": switchDay.value
				},
			   method: "POST"
			 }).then(res => {
				console.log(res.data);
				dataList.value = res.data.data
			 }).catch(err => {
			   console.log(err)
			   uni.showToast({
			   	title: t('获取数据失败！'),
				icon:"error"
			   });
			 })
	}
	
	//获取总行数
	async function getPageCount(){
		//console.log(1);
		await uni.request({
			   url: urlPrefix + "/scan/getCount",
			   method: "POST",
			   data:{
			   			"ins_user": iuser.value,
						"user_id":insName,
						"cl_flag": switchMo.value,
						"today": switchDay.value
			   		}
			 }).then(res => {
				//console.log(res.data);
				pageCount.value = res.data.data.count
			 }).catch(err => {
			   console.log(err)
			 })
	}
	//预加载
	onMounted(async () => {
	  await getPageCount()
	  await getData()
	  //await search()
	})
	
	//点击扫描
	let yiC = "";
	async function openBrand(e){
		plus.screen.lockOrientation('landscape-primary');//锁定横屏
		var mpaasScanModule = uni.requireNativePlugin("Mpaas-Scan-Module")
		mpaasScanModule.mpaasScan({
		                        // 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
		                        'scanType':  ['qrCode','barCode'],
		                        // 是否隐藏相册，默认false不隐藏
		                        'hideAlbum': false
		                    },
		                    (res) => {
								//plus.screen.lockOrientation('landscape-primary');
								//console.log(JSON.stringify(res))
								
								
							//console.log('条码Message：' + res.resp_message);
							//console.log('条码内容：' + res.resp_result);
							switch (e){
								case 1:
									ypdh.value=res.resp_result;
									saveData.ord_no=ypdh.value
									//问题数量
									uni.request({
									   url: urlPrefix + "/scan/getBarCodeA",
									   method: "POST",
									   data: {
												ord_no: res.resp_result
											}
									 }).then(res => {
										 console.log(res.data)
										wtsl.value = res.data.data.ord_qty
										saveData.pb_qty = wtsl.value
									 }).catch(err => {
									   console.log(err)
									 })
									 brandCode.value=1;
									break;
									case 2:
										if(wtsl.value==""){
											uni.showToast({
												title: t('请先扫描样品单号！'),
												icon:"error"
											});
											return;
										}
										//责任部门
										uni.request({
										   url: urlPrefix + "/scan/getBarCodeB",
										   method: "POST",
										   data: {
													column_seq: res.resp_result
												}
										 }).then(res => {
											zrbm.value = res.data.data.column_no
											saveData.pb_dept= res.data.data.column_seq
										 }).catch(err => {
										   console.log(err)
										 })
										 brandCode.value=2;
									break;
									case 3:
										if(zrbm.value==""){
											uni.showToast({
												title: t('请先扫描责任部门！'),
												icon:"error"
												
											});
											return;
										}
										//异常说明
										uni.request({
										  url: urlPrefix + "/scan/getBarCodeC",
										  method: "POST",
										  data: {
												column_seq: res.resp_result
												}
										}).then(res => {
											ycsm.value = res.data.data.column_no
											saveData.pb_desc= res.data.data.column_seq
											yiC = res.data.data.column_seq
											console.log(yiC)
										}).catch(err => {
										  console.log(err)
										})
										brandCode.value=3;
									break;
									case 4:
										if(ycsm.value==""){
											uni.showToast({
												title: t('请先扫描异常说明！'),
												icon:"error"
											});
											return;
										}
										//异常地点
										uni.request({
										  url: urlPrefix + "/scan/getBarCodeD",
										  method: "POST",
										  data: {
												column_seq: res.resp_result
												}
										}).then(res => {
											ycdd.value = res.data.data.column_no
											saveData.pb_addr= res.data.data.column_seq
											uni.request({
											  url: urlPrefix + "/scan/getOption",
											  method: "POST",
											  data: {
													    pb_desc: yiC,
													    pb_addr: res.data.data.column_seq
													}
											}).then(res => {
												wtsm1.value = res.data.data
											}).catch(err => {
											  console.log(err)
											})
										}).catch(err => {
										  console.log(err)
										})
										brandCode.value=4;
									break;
								default:
									break;
							}
		                })
		
		
		// 调起条码扫描
		// uni.scanCode({ 
		// 	scanType: ['barCode','qrCode'],
		// 	autoDecodeCharset:true,
		// 	barCodeInput:true,
		// 	success: function (res) {

				
		// 	}
		// });

	
	}
	
</script>

<style lang="scss">
	page {
	  width: 100%;
	  height: 100%;
	  padding: 2.5% 2% 1.5% 2%;
	  box-sizing: border-box;
	  background-color: white;
	}
	.container {
		width: 100%;
	height: 100%;
	box-sizing: border-box;
	border-radius: 10px;
	box-shadow: 0 0 1px 5px #dddddd;
	}
	
	::v-deep{
	  .uni-card{
	    margin: 8px!important;
	    padding: 0!important;
	    .uni-card__content{
	      padding: 0!important;
	    }
	  }
	}
	.right-top-top{
		display: flex;
	}
.propertyBox{
	margin-top: 2rpx;
	flex-flow: row;
	justify-content: flex-start;
	display: flex;
	position: -webkit-sticky;
	top: var(--window-top);
}
.inpBr{
	width: 15%;
	margin-left:10rpx;
}
	.left-bottom {
	width: 100%;
	height: 10%;
	display: flex;
	justify-content: center;
	align-items: center;

	.uni-pagination__total {
	  margin-right: 0.4375rem !important;
	}

	.uni-pagination__num-tag {
	  min-width: 1.875rem !important;
	  background-color: #F0F0F0 !important;
	}

	.page--active {
	  color: white !important;
	  background-color: deeppink !important;
	}
	}
	
	.modelPopup {
	  .modelBox {
	    width: 75vw;
	    height: 90vh;
	    border-radius: 1vw;
	    background-color: white;
	    
	    .modelData {
	      width: 100%;
	      height: 90%;
	      display: flex;
	      justify-content: center;
	      align-items: flex-start;
	      flex-wrap: wrap;
	      
	      .modelAttribute {
	        width: 30%;
	        margin-left: 3.3%;
	      }
	    }
	  }
	}
	
	.updateSpecificationPopup {
	  .updateSpecificationBox {
	    width: 75vw;
	    height: 90vh;
	    border-radius: 1vw;
	    background-color: white;
	    
	    .updateSpecificationData {
	      width: 100%;
	      height: 100%;
	      position: relative;
	      display: flex;
	      justify-content: center;
	      align-items: flex-start;
	      flex-wrap: wrap;
	      
	      .updateSpecificationAttribute {
	        width: 35%;
	        margin-left: 15%;
	        
	        .uni-easyinput {
	          width: 70%;
	          margin-left: 1rem;
	        }
	        
	        .uni-stat__select {
	          width: 70%;
	          margin-left: 1rem;
	        }
	        
	        .uni-numbox {
	          margin-left: 1rem;
	        }
	        
	        .uni-file-picker {
	          margin-left: 1rem;
	        }
	      }
	    }
	  }
	}
	
	.submit {
	  display: flex;
	  justify-content: center;
	  align-items: center;
	}

	.back {
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		position: absolute;
		left: 1.5%;
		width: 50px;
		height: 50px;
		top: 5%;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		cursor: pointer;
		z-index: 1;
	}

</style>
