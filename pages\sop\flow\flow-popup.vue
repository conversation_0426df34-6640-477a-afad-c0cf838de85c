<!-- 工序流程弹框 -->
<script setup>
import { ref, provide, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import FlowTemplatePopup from '@/pages/sop/flow/flow-template-popup.vue'
import FlowSequencePopup from '@/pages/sop/flow/flow-sequence-popup.vue'
import FlowSectionPopup from '@/pages/sop/flow/flow-section-popup.vue'
import SortSectionPopup from '@/pages/sop/flow/sort-section-popup.vue'
import DeleteFlowPopup from '@/pages/sop/flow/delete-flow-popup.vue'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 传递消息
const emit = defineEmits(['change-flow-index'])

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 工序流程弹框
const flowPopup = ref()

// 工序流程模板弹框
const flowTemplatePopup = ref()

// 工序流程序号弹框
const flowSequencePopup = ref()

// 工序流程加工段弹框
const flowSectionPopup = ref()

// 排序加工段弹框
const sortSectionPopup = ref()

// 删除工序流程弹框
const deleteFlowPopup = ref()

// 型体
const model = ref('')
// 制程
const operation = ref('')
// 主要代码
const rtgCode = ref('')

// 流程下标
const flowIndex = inject('flowIndex')
// 是否自动切换流程下标
const isAutoChangeIndex = inject('isAutoChangeIndex')
// 工序信息
const processInfo = inject('processInfo')

// 工序流程列表
const processFlowList = ref([])

// 工序流程下拉刷新状态
const processFlowTriggered = ref(false)

// 是否选择所有工序流程
const isSelectedAllProcessFlow = ref(false)

// 滚动 id
const scrollId = ref('')

// 获取工序流程列表
async function getProcessFlowList(param1, param2, param3) {
  isSelectedAllProcessFlow.value = false
  
  await uni.request({
    url: urlPrefix + '/sop/getProcessFlowList',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3
    }
  }).then(res => {
    if (res.data.code) {
      processFlowList.value = res.data.data ? res.data.data : []
      if (flowIndex.value > processFlowList.value.length - 1) {
        flowIndex.value = processFlowList.value.length - 1
        isAutoChangeIndex.value = true
      }
    } else {
      processFlowList.value = []
      flowIndex.value = -1
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示工序流程
async function showFlowPopup(param1, param2, param3, param4) {
  model.value = param1
  operation.value = param2
  rtgCode.value = param3
  scrollId.value = ''
  
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await getProcessFlowList(param1, param2, param3)
  
  uni.hideLoading()
  
  if (processFlowList.value.length > 0) {
    setTimeout(() => {
      scrollId.value = 'skey' + (param4 - 1)
    }, 1000)
  }
  
  flowPopup.value.open()
}

// 模板导入工序流程
function flowTemplateImport(param1, param2, param3) {
  if (processFlowList.value.length > 0) {
    tipPopup.value.showTipPopup('warn', '请先清空工序流程！')
    return
  }
  
  flowTemplatePopup.value.showFlowTemplatePopup(param1, param2, param3)
}

// 添加工序流程
async function addProcessFlow(param1, param2, param3) {
  uni.showLoading({
    title: '添加中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/addProcessFlow',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3,
      insUser: user,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getProcessFlowList(param1, param2, param3)
      scrollId.value = 'skey' + (processFlowList.value.length - 1)
      flowIndex.value = processFlowList.value.length - 1
      flowPopup.value.close()
      tipPopup.value.showTipPopup('success', '添加成功！')
    } else {
      tipPopup.value.showTipPopup('error', '添加失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 插入工序流程
async function insertProcessFlow(param1, param2) {
  uni.showLoading({
    title: '添加中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/insertProcessFlow',
    method: 'POST',
    data: {
      model: param1.model,
      operation: param1.operation,
      rtgCode: param1.rtgCode,
      skey: param1.skey,
      insUser: user,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getProcessFlowList(param1.model, param1.operation, param1.rtgCode)
      scrollId.value = 'skey' + (param2 + 1)
      if (param2 + 1 === flowIndex.value) {
        emit('change-flow-index')
      } else {
        flowIndex.value = param2 + 1
      }
      flowPopup.value.close()
      tipPopup.value.showTipPopup('success', '添加成功！')
    } else {
      tipPopup.value.showTipPopup('error', '添加失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 删除工序流程
function deleteProcessFlow(param1, param2, param3) {
  let selectedProcessFlowList = []
  for (let item of processFlowList.value) {
    if (item.isSelected) {
      selectedProcessFlowList.push(item)
    }
  }
  if (selectedProcessFlowList.length === 0) {
    tipPopup.value.showTipPopup('warn', '请勾选要删除的选项！')
    return
  }
  deleteFlowPopup.value.showDeleteFlowPopup(param1, param2, param3, selectedProcessFlowList)
}

// 工序流程下拉刷新
async function processFlowOnRefresh() {
  if (processFlowTriggered.value) {
    return
  }
  
  processFlowTriggered.value = true
  await getProcessFlowList(model.value, operation.value, rtgCode.value)
  setTimeout(() => {
    processFlowTriggered.value = false
    tipPopup.value.showTipPopup('success', '列表已刷新！')
  }, 500)
}

// 选择工序流程
function selectItemProcessFlow(param) {
  param.isSelected = !param.isSelected
  let selectedCount = 0
  for (let item of processFlowList.value) {
    if (item.isSelected) {
      selectedCount++
    }
  }
  if (selectedCount === processFlowList.value.length) {
    isSelectedAllProcessFlow.value = true
  } else {
    isSelectedAllProcessFlow.value = false
  }
}

// 选择所有工序流程
function selectAllProcessFlow() {
  if (processFlowList.value.length === 0) {
    return
  }
  isSelectedAllProcessFlow.value = !isSelectedAllProcessFlow.value
  for (let item of processFlowList.value) {
    item.isSelected = isSelectedAllProcessFlow.value
  }
}

// 进入流程详情
function enterFlowDetail(param) {
  flowIndex.value = param
  flowPopup.value.close()
}

provide('scrollId', scrollId)
provide('processFlowList', processFlowList)
provide('getProcessFlowList', getProcessFlowList)
provide('isSelectedAllProcessFlow', isSelectedAllProcessFlow)

defineExpose({
  processFlowList,
  getProcessFlowList,
  showFlowPopup
})
</script>

<template>
  <uni-popup
    ref="flowPopup"
    type="center"
    :is-mask-click="false"
    class="flow-popup"
  >
    <view class="container">
      <view class="top-bar flex-row-start-center">
        <view
          @click="flowPopup.close()"
          class="cancel button"
        >
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <view class="title flex-row-center">
          工序流程
        </view>
        
        <view
          @click="flowTemplateImport(model, operation, rtgCode)"
          class="import button"
          :style="{
            visibility: user === processInfo.insUser ? 'visible' : 'hidden'
          }"
        >
          <uni-icons type="folder-add" size="30" color="crimson"></uni-icons>
        </view>
        
        <view
          @click="addProcessFlow(model, operation, rtgCode)"
          class="add button"
          :style="{
            visibility: user === processInfo.insUser ? 'visible' : 'hidden'
          }"
        >
          <uni-icons type="plusempty" size="30" color="green"></uni-icons>
        </view>
        
        <view
          @click="deleteProcessFlow(model, operation, rtgCode)"
          class="delete button"
          :style="{
            visibility: user === processInfo.insUser ? 'visible' : 'hidden'
          }"
        >
          <uni-icons type="trash" size="30" color="red"></uni-icons>
        </view>
      </view>
      
      <view class="main">
        <!-- <scroll-view
          :scroll-y="true"
          :scroll-into-view="scrollId"
          :scroll-with-animation="true"
          :refresher-enabled="true"
          :refresher-triggered="processFlowTriggered"
          :refresher-threshold="80"
          refresher-background="#fdf6e3"
          @refresherrefresh="processFlowOnRefresh()"
          class="process-flow-table"
        > -->
        <scroll-view
          :scroll-y="true"
          :scroll-into-view="scrollId"
          :scroll-with-animation="true"
          class="process-flow-table"
        >
          <table>
            <thead>
              <tr>
                <th v-show="user === processInfo.insUser" style="width: 5%;">
                  <checkbox
                    @click="selectAllProcessFlow()"
                    :checked="isSelectedAllProcessFlow"
                    :disabled="processFlowList.length === 0"
                    color="white"
                    activeBackgroundColor="violet"
                    borderColor="gray"
                    activeBorderColor="black"
                  ></checkbox>
                </th>
                <th v-show="user === processInfo.insUser" style="width: 7%;">新增</th>
                <th style="width: 8%;">序号</th>
                <th style="width: 9%;">
                  <view v-show="processFlowList.length > 0 && user === processInfo.insUser" class="sort-section">
                    <view
                      @click="sortSectionPopup.showSortSectionPopup(model, operation, rtgCode, processFlowList.length)"
                      class="button"
                    >
                      加工段
                    </view>
                  </view>
                  <text v-show="processFlowList.length === 0 || user !== processInfo.insUser">
                    加工段
                  </text>
                </th>
                <th style="width: 28%;">工序名称</th>
                <th style="width: 28%;">动作</th>
                <th style="width: 6%;">版本</th>
                <th style="width: 9%;">详情</th>
              </tr>
            </thead>
            
            <tbody v-show="processFlowList.length > 0">
              <tr
                v-for="(item, index) in processFlowList"
                :key="index"
                :id="'skey' + item.skey"
                :style="{
                  backgroundColor: index % 2 === 0 ? '#edfbe4' : '#fdf6e3',
                  filter: scrollId === ('skey' + (item.skey - 1)) ? 'hue-rotate(90deg)' : 'none'
                }"
              >
                <td v-show="user === processInfo.insUser">
                  <checkbox
                    @click="selectItemProcessFlow(item)"
                    :checked="item.isSelected"
                    color="white"
                    activeBackgroundColor="violet"
                    borderColor="gray"
                    activeBorderColor="black"
                  ></checkbox>
                </td>
                <td v-show="user === processInfo.insUser">
                  <view class="insert">
                    <uni-icons
                      @click="insertProcessFlow(item, index)"
                      type="plusempty"
                      size="30"
                      color="seagreen"
                      class="button"
                    ></uni-icons>
                  </view>
                </td>
                <td>
                  <view class="sequence">
                    <view
                      v-show="user === processInfo.insUser"
                      @click="flowSequencePopup.showFlowSequencePopup(item, processFlowList.length)"
                      class="button"
                    >
                      {{ item.skey }}
                    </view>
                    <text v-show="user !== processInfo.insUser" style="color: black;">
                      {{ item.skey }}
                    </text>
                  </view>
                </td>
                <td>
                  <view class="section">
                    <view
                      v-show="user === processInfo.insUser"
                      @click="flowSectionPopup.showFlowSectionPopup(item, processFlowList.length)"
                      class="button"
                    >
                      {{ item.wkGroup ? item.wkGroup : '请选择' }}
                    </view>
                    <text v-show="user !== processInfo.insUser" style="color: black;">
                      {{ item.wkGroup ? item.wkGroup : '/' }}
                    </text>
                  </view>
                </td>
                <td>
                  <view class="name">
                    {{ item.seqName }}
                  </view>
                </td>
                <td>
                  <view class="action">
                    {{ item.actions }}
                  </view>
                </td>
                <td>
                  <view class="version">
                    {{ item.version ? ('v' + item.version) : '' }}
                  </view>
                </td>
                <td>
                  <view class="enter">
                    <uni-icons
                      @click="enterFlowDetail(index)"
                      type="redo"
                      size="30"
                      color="darkviolet"
                      class="button"
                    ></uni-icons>
                  </view>
                </td>
              </tr>
            </tbody>
            
            <tbody v-show="processFlowList.length === 0">
              <tr>
                <td colspan="8">暂无数据</td>
              </tr>
            </tbody>
          </table>
        </scroll-view>
      </view>
    </view>
  </uni-popup>
  
  <flow-template-popup ref="flowTemplatePopup" />
  <flow-sequence-popup ref="flowSequencePopup" @change-flow-index="emit('change-flow-index')" />
  <flow-section-popup ref="flowSectionPopup" @change-flow-index="emit('change-flow-index')" />
  <sort-section-popup ref="sortSectionPopup" @change-flow-index="emit('change-flow-index')" />
  <delete-flow-popup ref="deleteFlowPopup" @change-flow-index="emit('change-flow-index')" />
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  font-size: 18px;
  box-sizing: border-box;
}

.flow-popup {
  .container {
    width: 1100px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .top-bar {
      width: 100%;
      height: 70px;
      padding: 10px 10px 5px 10px;
      
      .title {
        width: calc(100% - 510px);
        font-size: 22px;
        font-weight: bold;
      }
      
      .cancel, .import, .add, .delete {
        width: 75px;
        height: 50px;
      }
      
      .cancel {
        margin-right: 180px;
      }
      
      .import, .add {
        margin-right: 20px;
      }
    }
    
    .main {
      width: 100%;
      padding: 10px;
      
      .process-flow-table {
        width: 100%;
        height: 500px;
        border-radius: 10px;
        box-shadow: 0 0 5px gray;
        overflow: hidden;
        
        &:deep(.uni-scroll-view) {
          // /* #ifdef WEB */
          // &::-webkit-scrollbar {
          //   display: none;
          // }
          // /* #endif */
        }
        
        table {
          width: 100%;
          max-height: 100%;
          border-radius: 10px;
          border-spacing: 0;
          
          tr {
            width: 100%;
            height: 60px;
          }
          
          th, td {
            text-align: center;
            border-right: 1px solid #ccc;
            border-bottom: 1px solid #ccc;
            
            &:last-child {
              border-right: none;
            }
            
            &:deep(.uni-checkbox-input) {
              width: 30px;
              height: 30px;
              margin: 0;
              border-width: 2px;
            }
          }
          
          th {
            position: sticky;
            top: 0;
            z-index: 1;
            background-color: #fdf6e3;
            /* #ifdef APP */
            border-top: 1px solid #ccc;
            /* #endif */
            
            .sort-section {
              padding: 10px;
              color: seagreen;
              
              .button {
                height: 40px;
              }
            }
          }
          
          td {
            .name, .action, .version {
              padding: 5px;
            }
            
            .insert, .sequence, .section, .enter {
              padding: 10px;
            }
            
            .sequence, .section {
              color: seagreen;
            }
            
            .button {
              height: 40px;
            }
          }
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .flex-row-start-center {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
</style>