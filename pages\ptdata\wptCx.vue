<template>
	<view class="container">
	 <view class="right-top-top">
		<uni-icons @click="back" class="back" type="back" size="36" style="margin-left:1vw;margin-top:10rpx;"></uni-icons>
		<uni-title
		  type="h2"
		  :title="t('未配套库存查询')"
		  align="center"
		  style="margin-top:-0.7vw;margin-left: 5vw;"
		></uni-title>
		<text style="color: #c5c8c8;margin-right:0.5vw;margin-top:1.22vw;">{{Qstatus==1?t('按架位'):t('全部数')}}</text><uv-switch inactive-color="#c5c8c8" :value="Qstatus==1?true:false" @change="asyncChange"  style="margin-right:1vw;margin-top:1vw;"></uv-switch>
	</view>


	<view class="cTr" style="height: 86%;margin-top:20rpx;">
		<zb-table
			:show-header="true"
			:columns="column"
			:stripe="true"
			ref="zbTable"
			highlight="true"
			show-summary
			@rowClick="rowClick"
			@currentChange="currentChange"
			:summary-method="getSummaries"
			:border="true"
			:cell-style="cellStyle"
			:data="dataList"></zb-table>
	</view>

	<!--单击单行弹出界面-->
	<view class="updateSpecificationPopup">
	  <uni-popup
	    ref="updateSpecificationPopup"
	    type="center"
		style="height: 80%;"
	  >
	  
	    <view class="updateSpecificationBox">

	      <uni-title
	        :title="t('库存明细')"
	        type="h2"
	        align="center"
	      ></uni-title>
		  <uni-icons type="closeempty" @click="backDrom" size="32" style="position: absolute;top: 25rpx;right: 1vw;"></uni-icons>
	      <view class="updateSpecificationData">
			
			<zb-table
				:show-header="true"
				:columns="column2"
				:summary-method="getSummaries"
				:stripe="true"
				ref="zbTable"
				show-summary
				:border="true"
				:data="dataList2"></zb-table>
					
	      </view>
	    </view>
	  </uni-popup>
	</view>
	
  
	</view>
</template>

<script setup>
  import { onMounted,ref, reactive } from 'vue'
  import { onPullDownRefresh } from '@dcloudio/uni-app'
  import { useI18n } from 'vue-i18n'
  import urlPrefix from '@/pages/common/urlPrefix.js'
  
  // 国际化
  const { t } = useI18n()
  
	// const urlPrefix = "http://***********:8200";
	//const urlPrefix = "http://**********:8080";
	const ypdh=ref()
	const zrbm=ref()
	const ycsm=ref()
	const ycdd=ref()
	const wtsl=ref(0)
	const selectWt=ref()
	const wtsm1=ref([])
	const switchDay=ref(false)
	//第几页
	const firstPageNo=ref(1)
	const firstPageSize=ref(20)
	const pageCount=ref(0)
	
	//表单数据
	const dataList=ref([])
	const dataList2=ref([])
	
	//扫描状态
	const brandCode=ref(0)
	
	//详情弹窗
	const modelData = ref()
	
	//状态
	const Qstatus = ref(0)
	
	const insUs=uni.getStorageSync("loUserNo")
	const insName=uni.getStorageSync("loginUserName")
	const iuser=ref("")
	
	//修改弹窗
	const updateSpecificationPopup = ref()
	const updateSpecificationDetail = reactive({
	  pat_flag: "",
	  model_no: "",
	  shp_date:"",
	  ord_no: "",
	  dev_type: "",
	  tot_qty: "",
	  ushelf_no: "",
	  bshelf_no6: "",
	  bshelf_no2: "",
	  bshelf_no1: "",
	  bshelf_no3: ""
	})
	
	//删除参数
	const deleSpecificationDetail = reactive({
	  ord_no: "",
	  pb_dept: "",
	  pb_addr: "",
	  upd_user: insUs,
	  ins_date: ""
	})
	
	//下拉刷新
	 onPullDownRefresh (()=>{	//通过 onPullDownRefresh  可以监听到下拉刷新的动作
			uni.startPullDownRefresh({
				success(){
					getData();
					uni.stopPullDownRefresh()	//停止当前页面下拉刷新。
				},fail(){}
			})
	    }) 
	
	//保存data
	const saveData = reactive({
	  "ord_no": "",
	  "pb_qty": "",
	  "pb_desc1":"",
	  "pb_dept": "",
	  "pb_desc": "",
	  "pb_addr": "",
	  "ins_user": insUs
	})
    const stus=ref(0)

	const column=ref([
          { name: 'pm', label: t('排名'),emptyString:'--',sorter:true},
		  { name: 'brand_no', label: t('品牌')},
		  { name: 'npat_cou', label: t('单数'),emptyString:' '},
		  { name: 'tot_qty', label: t('双数')},
          { name: 'u_qty', label: t('鞋面'),sorter:true,emptyString:'/'},
		  { name: 'b_qty1', label: t('中底皮'),emptyString:'/'},
		  { name: 'b_qty2', label: t('中底'),emptyString:'/'},
		  { name: 'b_qty3', label: t('包粘'),emptyString:'/'},
          { name: 'b_qty6', label: t('大底'),emptyString:'/'}
        ]);
		
	const column2=ref([
		  { name: 'model_pic', label: t('鞋图'),fixed:'true',type:"img",align: "center",width:'90'},
		  { name: 'shp_date', label: t('出货日'),fixed:'true',width:'125'},
		  { name: 'model_no', label: t('型体编号'),fixed:'true',emptyString:' ',width:'150'},
		  { name: 'ord_no', label: t('样品单号'),fixed:'true',width:'185'},
	      { name: 'dev_type', label: t('样品类型'),fixed:'true',width:'152',emptyString:'/'},
		  { name: 'last_no', label: t('楦头编号'),sorter:true,emptyString:'/',width:'120'},
		  { name: 'tot_qty', label: t('双数'),emptyString:'/'},
		  { name: 'u_qty', label: t('鞋面'),emptyString:'/'},
	      { name: 'b_qty1', label: t('中底皮'),emptyString:'/'},
		  { name: 'b_qty2', label: t('中底'),emptyString:'/'},
		  { name: 'b_qty3', label: t('包粘'),emptyString:'/'},	
		  { name: 'b_qty6', label: t('大底'),emptyString:'/'}
	    ]);
	
		

	const deleCsInfos = reactive({
	  mating_no: ""
	})
	
	//判断颜色
	function cellStyle({row, column, rowIndex, columnIndex}){
    }
	
	//返回首页
	function back() {
	  uni.navigateBack({
	  	delta: 1,
	  	animationType: 'pop-out',
	  	animationDuration: 200
	  })
	}	
	
	//关闭弹窗
	function backDrom() {
	  dataList2.value=[];
	  updateSpecificationPopup.value.close()
	}	
	
	//选中当前行
	function currentChange(row,index){
	  
	}
	
	//点击切换按架位
	async function asyncChange(e) {
		Qstatus.value=e?1:0
		getData();
	}
	
	//表尾合计
	function getSummaries(param){
	  const { columns, data } = param;
	  const sums = [];
	  columns.forEach((column, index) => {
	    if (index === 0) {
	      sums[index] = t('合计');
	      return;
	    }		
	    if(column.name==='tot_qty'||column.name==='tot_qty'||column.name==='u_qty'||column.name==='b_qty1'||column.name==='b_qty2'||column.name==='b_qty3'||column.name==='b_qty6'||column.name==='npat_cou'){
	      const values = data.map(item => Number(item[column.name]));
	      if (!values.every(value => isNaN(value))) {
	        sums[index] = values.reduce((prev, curr) => {
	          const value = Number(curr);
	          if (!isNaN(value)) {
	            return prev + curr;
	          } else {
	            return prev;
	          }
	        }, 0);
	        sums[index] += '  ';
	      }
	    }else{
	      sums[index] = ' ';
	    }
	  });
	  return sums;
	}
	
	
	//点击详情
	function rowClick(e){
		updateSpecificationDetail.ord_no = e.ord_no
		uni.request({
			   url: urlPrefix + "/match/getNoMatchDetail",
			   data:{
					"brand_no": e.brand_no,
					"status": Qstatus.value
				},
			   method: "POST"
			 }).then(res => {
				 
				dataList2.value = res.data.data
				for(let i=0;i<dataList2.value.length;i++){
					dataList2.value[i].model_pic='data:image/png;base64,'+dataList2.value[i].model_pic
				}
				
			 }).catch(err => {
			   console.log(err)
			   uni.showToast({
			   	title: t('获取数据失败！'),
				icon:"error"
			   });
			 })
		updateSpecificationPopup.value.open()
	}	

	//获取数据
	async function getData(){
		//console.log(iuser.value);
		await uni.request({
			   url: urlPrefix + "/match/getNoMatch",
			   data:{
					"status": Qstatus.value
				},
			   method: "POST"
			 }).then(res => {
				 
				dataList.value = res.data.data
				
				// for(let i=0;i<dataList.value.length;i++){
				// 	dataList.value[i].model_pic='data:image/png;base64,'+dataList.value[i].model_pic
				// }
				
			 }).catch(err => {
			   console.log(err)
			   uni.showToast({
			   	title: t('获取数据失败！'),
				icon:"error"
			   });
			 })
	}

	//预加载
	onMounted(async () => {
	  await getData()
	  //await search()
	})
	
</script>

<style lang="scss">
	page {
	  width: 100%;
	  height: 100%;
	  padding: 2.5% 2% 1.5% 2%;
	  box-sizing: border-box;
	  background-color: white;
	}
	
	.item-tr{
	  height: 45px;
	  font-size: 18px;
	  font-weight:500;

	}
	
	.cTr .item-tr{
		background-color: seagreen;
	}
	
	.container {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		border-radius: 10px;
		box-shadow: 0 0 1px 5px #dddddd;
		
	}
	
	::v-deep{
	  .uni-card{
	    margin: 8px!important;
	    padding: 0!important;
	    .uni-card__content{
	      padding: 0!important;
	    }
	  }
	}
	.right-top-top{
		display: flex;
	}
.propertyBox{
	margin-top: 2rpx;
	flex-flow: row;
	justify-content: flex-start;
	display: flex;
	position: -webkit-sticky;
	top: var(--window-top);
}
.inpBr{
	width: 15%;
	margin-left:10rpx;
}
	.left-bottom {
	width: 100%;
	height: 10%;
	display: flex;
	justify-content: center;
	align-items: center;

	.uni-pagination__total {
	  margin-right: 0.4375rem !important;
	}

	.uni-pagination__num-tag {
	  min-width: 1.875rem !important;
	  background-color: #F0F0F0 !important;
	}

	.page--active {
	  color: white !important;
	  background-color: deeppink !important;
	}
	}
	
	.updateSpecificationPopup {
	  .updateSpecificationBox {
	    width: 96vw;
	    height: 90vh;
		
	    border-radius: 1vw;
	    background-color: white;
	    
	    .updateSpecificationData {
	      width: 100%;
	      position: relative;
	      display: flex;
	      justify-content: center;
	      align-items: flex-start;
	      flex-wrap: wrap;
		  height: 89%;
	      
	      .updateSpecificationAttribute {
	        width: 35%;
	        margin-left: 15%;
	        
	        .uni-easyinput {
	          width: 70%;
	          margin-left: 1rem;
	        }
	        
	        .uni-stat__select {
	          width: 70%;
	          margin-left: 1rem;
	        }
	        
	        .uni-numbox {
	          margin-left: 1rem;
	        }
	        
	        .uni-file-picker {
	          margin-left: 1rem;
	        }
	      }
	    }
	  }
	}
	
	.submit {
	  display: flex;
	  justify-content: center;
	  align-items: center;
	}

	.back {
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		position: absolute;
		left: 1.5%;
		width: 50px;
		height: 50px;
		top: 5%;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		cursor: pointer;
		z-index: 1;
	}

</style>


