# 邮件推送系统表结构设计说明

## 概述

本邮件推送系统设计用于管理各部门用户的邮件推送权限配置，支持多种版本的报价和预估邮件推送功能。系统包含4个核心表，以pcc开头命名，符合系统命名规范。

## 表结构设计

### 1. pcc_email_push_permission (邮件推送权限配置表)

**用途**: 管理各部门用户的邮件推送权限配置

**主要字段**:
- `user_no`: 工号，关联sy_user.user_no
- `dept_name`: 部门名称
- `email`: 邮箱地址
- `push_quote_v1~v5`: 推送报价1-5版权限 (Y-有权限, N-无权限, P-待定)
- `push_estimate_z`: 推送预估Z版权限
- `push_estimate_zz`: 推送预估ZZ版权限  
- `push_p_version`: 推送P版权限
- `update_email_notify`: 更新版本邮件提醒

**特点**:
- 支持工号唯一约束
- 支持权限状态：Y(有权限)、N(无权限)、P(待定)
- 包含完整的审计字段

### 2. pcc_email_push_log (邮件推送日志表)

**用途**: 记录邮件发送历史和状态跟踪

**主要字段**:
- `batch_id`: 批次ID，同一批发送的邮件使用相同批次ID
- `email_type`: 邮件类型 (QUOTE_V1, QUOTE_V2, ESTIMATE_Z等)
- `send_status`: 发送状态 (PENDING, SENDING, SUCCESS, FAILED, RETRY)
- `retry_count`: 重试次数
- `business_id`: 业务ID（如订单号、报价单号等）

**特点**:
- 支持批量发送跟踪
- 支持重试机制
- 支持附件信息存储(JSON格式)
- 完整的错误信息记录

### 3. pcc_email_template (邮件模板表)

**用途**: 管理不同类型邮件的模板内容

**主要字段**:
- `template_code`: 模板代码（唯一）
- `email_type`: 邮件类型
- `subject_template`: 主题模板（支持变量替换）
- `content_template`: 内容模板（HTML格式，支持变量替换）
- `language`: 语言支持 (zh_CN, zh_TW, en_US, vi_VN)

**特点**:
- 支持多语言模板
- 支持变量替换功能
- 支持默认模板设置
- 模板变量说明(JSON格式)

### 4. pcc_email_push_config (邮件推送配置表)

**用途**: 管理邮件推送系统的全局配置

**主要字段**:
- `config_key`: 配置键（唯一）
- `config_value`: 配置值
- `config_type`: 配置类型 (STRING, NUMBER, BOOLEAN, JSON)

**预置配置**:
- SMTP服务器配置
- 邮件发送参数
- 重试机制配置
- 批量发送配置

## 权限配置说明

根据提供的Excel数据，系统支持以下权限配置：

### 部门权限分布

1. **SOP部门**: 主要拥有预估Z版、预估ZZ版权限，部分用户有P版权限
2. **化工部门**: 主要负责更新邮件提醒
3. **線外加工部门**: 部分用户有报价V1版权限
4. **底部部门**: 主要拥有报价V1版权限
5. **面部部门**: 主要拥有报价V1版权限
6. **IE工程部**: 主要负责更新邮件提醒

### 权限状态说明

- **Y**: 有权限，可以接收对应类型的邮件推送
- **N**: 无权限，不会接收对应类型的邮件推送
- **P**: 待定，需要进一步确认权限状态

## 安装说明

### 1. 执行安装脚本
```sql
-- 执行完整安装脚本
@install_email_push_system.sql
```

### 2. 初始化权限数据
```sql
-- 执行权限初始化脚本
@pcc_email_push_init_data.sql
```

### 3. 配置SMTP参数
```sql
-- 更新SMTP配置
UPDATE pcc_email_push_config SET config_value = '您的SMTP服务器' WHERE config_key = 'SMTP_HOST';
UPDATE pcc_email_push_config SET config_value = '您的SMTP用户名' WHERE config_key = 'SMTP_USERNAME';
UPDATE pcc_email_push_config SET config_value = '您的SMTP密码' WHERE config_key = 'SMTP_PASSWORD';
```

## 使用示例

### 1. 查询用户权限
```sql
SELECT user_no, dept_name, email, 
       push_quote_v1, push_estimate_z, push_estimate_zz, push_p_version
FROM pcc_email_push_permission 
WHERE user_no = '2310001100';
```

### 2. 更新用户权限
```sql
UPDATE pcc_email_push_permission 
SET push_quote_v1 = 'Y', update_user = 'ADMIN', update_date = SYSDATE
WHERE user_no = '2310001100';
```

### 3. 查询邮件发送日志
```sql
SELECT batch_id, email_type, to_email, send_status, send_time
FROM pcc_email_push_log 
WHERE business_id = 'ORDER001'
ORDER BY create_date DESC;
```

## 注意事项

1. **数据完整性**: 确保user_no与sy_user表中的工号对应
2. **邮箱验证**: 建议在应用层对邮箱格式进行验证
3. **权限管理**: 建议通过应用程序界面管理权限，避免直接修改数据库
4. **日志清理**: 定期清理历史邮件日志，避免表数据过大
5. **模板管理**: 邮件模板支持HTML格式，注意XSS安全防护

## 扩展性

系统设计考虑了未来扩展需求：
- 支持新增邮件类型
- 支持多语言模板
- 支持复杂的权限配置
- 支持批量操作和重试机制
