<template>
	<view class="container">
		<view class="top">
		  <view class="back">
			<uni-icons @click="back"  type="back" size="36"></uni-icons>
		  </view>
		  
		  <view style="flex:1"></view>
		  
		<view style="  display: flex;">
			  
			  <button type="success" @click="addInfo()"
			  			class="addButton"
			  			style="background-color: #18bc37; color: white; font-weight: bold;">
			  			新增
			  </button>
			  
			  <button type="success" @click="emptyData"
			  			class="addButton"
			  			style="background-color: #e43d33; color: white; font-weight: bold; margin-left: 10px;">
			  			删除
			  </button>
		  </view>
		  

		</view>


	
		
		<view class="table-container">
			<table>
				<!-- 表头行 -->
				<tr style="background-color: #fdf6e3;">
					<th style="width: 1vw;">
		
					</th>
					<th style="width: 10vw;" align="center">主题</th>
									<th style="width: 10vw;" align="center">提案人</th>
					<th style="width: 10vw;" align="center">厂别</th>
					<th style="width: 15vw;" align="center">部门</th>
					<th style="width: 20vw;" align="center">日期</th>
					<th style="width: 5vw;" align="center">创建人</th>
					<th style="width: 5vw;" align="center">操作</th>
					<th style="width: 5vw;" align="center">操作</th>
				</tr>
				<tr v-for="(item, index) in dataList" :key="item"  class="datalist-tr">
					<td style="width: 1vw;">
					<checkbox @click="selectItemStep(item)" :checked="item.isChecked" color="white"
									activeBackgroundColor="violet" borderColor="black" activeBorderColor="black"
									:disabled="insName != item.create_by"></checkbox>
					</td>
					<td align="center">{{ item.theme }}</td>
					<td align="center">{{ item.proposer }}</td>
					<td align="center">{{ item.factory }}</td>
	
					<td align="center">{{ item.dept }}</td>
					<td align="center">{{ item.create_date }}</td>
					<td align="center">{{ item.create_by }}</td>
					
					<td align="center">
						<uv-button type="primary" text="编辑"
							@click="() => edit(item.id)"></uv-button>
					</td>
				
				<td align="center">
					<uv-button type="primary" text="预览"
						@click="() => viewInfo(item.id)"></uv-button>
				</td>
		   
				</tr>
			</table>
		</view>
		


		<view class="left-bottom">
			<uni-pagination show-icon="true" :total="pageCount" :current="firstPageNo" :pageSize="firstPageSize"
				@change="firstChange"></uni-pagination>
		</view>


	</view>

</template>

<script setup>
	import {
		onMounted,
		ref,
		reactive,
		watch
	} from 'vue'
	import {
		onPullDownRefresh
	} from '@dcloudio/uni-app'
	import {
		onShow
	} from '@dcloudio/uni-app';
	import urlPrefix from '@/pages/common/urlPrefix.js'

	//第几页
	const firstPageNo = ref(1)
	const firstPageSize = ref(10)
	
	

	// // #ifdef H5
	// firstPageSize.value = 12; // 网页版设置为10
	// // #endif
	
	// // #ifdef APP-PLUS
	// firstPageSize.value = 9; // App版设置为9
	// // #endif

	
	const pageCount = ref(0)

	//表单数据
	const searchModel = ref([])
	const shoeLastModelList = ref([])
	const dataList = ref([])
	const searchShoeLastModel = ref([])
	const shoeLastModelListShow = ref(false)
	const searchInput = ref()
	const modelList = ref([])

	// //消息提示
	// const tipPopup = ref()
	// const tipType = ref('')
	// const tipMessage = ref('')

	//扫描状态

	//详情弹窗
	const modelData = ref()
	const modelPopup = ref()
	const selectWt = ref()

	//参数
	const model_no = ref('')
	const itemNo = ref('')
	const id = ref('')
	const shoe_last = ref('')
	const brand = ref('AB')
	const brandList = ref([])

	//下拉选择显示
	const modelListShow = ref(false)
	const searchType = ref(true)
	const brandPopup = ref()

	// const insUs = uni.getStorageSync("loUserNo")
	// const insName = uni.getStorageSync("loginUserName")
	// const iuser = ref("")

	// const depts = ref([{
	// 		value: 1,
	// 		text: "成型"
	// 	},
	// 	{
	// 		value: 2,
	// 		text: "半成品"
	// 	},
	// 	{
	// 		value: 3,
	// 		text: "鞋面"
	// 	}
	// ])
	
	
	const selectIndex = ref([]);
	
	function selectItemStep(selectedItem) {
		let isAnyChecked = false;
		dataList.value.forEach(item => {
			if (item.id === selectedItem.id) {
				if(insName.value == item.create_by){
					item.isChecked = !item.isChecked;
					if (item.isChecked) {
						selectIndex.value = [item.id];
						isAnyChecked = true;
					} else {
						selectIndex.value = [];
					}
				}
			} else {
				item.isChecked = false;
			}
		});
	
		if (!isAnyChecked) {
			selectIndex.value = [];
		}
	}


	// 跳转至添加界面
	function addInfo() {
		// if (model_no.value.length === 0) {
		// 	showTip('warn', '请选择型体！')
		// 	return
		// }

		// let info = {
		// 	model_no: model_no.value,
		// 	brand: brand.value,
		// 	shoeLast: shoe_last.value,
		// 	item_no: itemNo.value,
		// 	type: 'add'
		// }

		// console.log(info);
		uni.navigateTo({
			url: `/pages/meprjplan/editProgressRoute`,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}

	function viewInfo(id) {
		
		uni.navigateTo({
		        url: '/pages/meprjplan/progressRoute?id='+id
		      });
		
		// 	// #ifdef APP-PLUS
		//       uni.navigateTo({
		//         url: '/pages/meprjplan/app?id='+id
		//       });
		//       // #endif
		
		//       // #ifndef APP-PLUS
		//       uni.navigateTo({
		//         url: '/pages/meprjplan/web?id='+id
		//       });
		//       // #endif
		
		
		
		// let info = {
		// 	model_no: modelNo,
		// 	brand: brand,
		// 	shoeLast: shoe_last.value,
		// 	item_no: itemNum,
		// 	dept: dept_name
		// }
		
		// let viewUrl;
		// if(dept_name.indexOf("ME底部") > -1){
		// 	viewUrl = `/pages/meprjplan/view-info-db?info=${JSON.stringify(info)}`;
		// } else if(dept_name.indexOf("鞋面") > -1){
		// 	viewUrl = `/pages/meprjplan/view-info-mb?info=${JSON.stringify(info)}`;
		// } else if(dept_name.indexOf("半成品") > -1){
		// 	viewUrl = `/pages/meprjplan/view-info-mb?info=${JSON.stringify(info)}`;
		// }
		
		// uni.navigateTo({
		// 	url: viewUrl,
		// 	animationType: 'pop-in',
		// 	animationDuration: 200
		// })
	}

	// 跳转至添加界面
	function edit(id) {

		// let info = {
		// 	model_no: model,
		// 	brand: brand,
		// 	shoeLast: shoe_last.value,
		// 	item_no: item_num,
		// 	dept: dept_name
		// }

		uni.navigateTo({
			url: `/pages/meprjplan/editProgressRoute?id=` + id,
			animationType: 'pop-in',
			animationDuration: 200
		})
	}

	// 提示信息
	function showTip(type, message) {
		tipType.value = type
		tipMessage.value = message
		tipPopup.value.open()
	}

	//下拉刷新
	onPullDownRefresh(() => { //通过 onPullDownRefresh  可以监听到下拉刷新的动作
		uni.startPullDownRefresh({
			success() {
				//console.log(urlPrefix)
				getPageCount()
				getData()
				uni.stopPullDownRefresh() //停止当前页面下拉刷新。
			},
			fail() {}
		})
	})

	function emptyData(e) {
		if (selectIndex.value.length > 0) {
			
			  uni.showLoading({
			              title: '正在删除...'
			            });
			
			
			console.log("selectIndex.value.join(',')" + selectIndex.value.join(','))
			uni.request({
				url: urlPrefix + "/pccprogressroute/delete",
				data: selectIndex.value.join(','),
				method: "DELETE"
			}).then(res => {
				getData('');
				     // 关闭加载提示框
				        uni.hideLoading();
			}).catch(err => {
				console.log(err)
					        uni.hideLoading();
				uni.showToast({
					title: '保存数据失败..',
					icon: "error"
				});
			})
		}
	}

	const insName = ref(uni.getStorageSync("loginUserName"))

	function selectionChange(e) {
		selectIndex.value = [];
		let arrList = [] // 选择的行数据
		let selectedIndexs = [] // 选择的下标数组
		// console.log("@@@@@@@@@@@@@" + JSON.stringify(e))
		selectedIndexs = e.detail.index
		// 将数组中的值，作为table表格数组的下标来获取对应的当前行数据
		selectedIndexs.map(i => {
			arrList.push(dataList.value[dataList.value.length - i - 1])
		})
		for (var i = 0; i < arrList.length; i++) {
			selectIndex.value.push(arrList[i].id);
		}
		// console.log(JSON.stringify(selectIndex.value));
	}

	function initAddPageParam() {
		inputCount.value = [1];
		inputCountNum.value = 1;
	}

	function search(model) {
		model_no.value = model;
		getData(model);
	}

	// 监视输入框中的 model_no，更新搜索提示列表
	watch(model_no, () => {
		searchModel.value = modelList.value.filter(item => item.model_no.includes(model_no.value.toUpperCase()))
			.slice(
				0, 50)
	})



	

	//返回首页
	function back() {
	let back = getCurrentPages();
	console.log('back.length：' + back.length)
	if (back && back.length > 1) {
		uni.navigateBack({
			delta: 1
		});
	} else {
		history.back();
	}
	}

	async function firstChange(e) {
		firstPageNo.value = e.current;
		await uni.request({
			url: urlPrefix + "/pccprogressroute/query",
			data: {
				"pageNo": firstPageNo.value,
				"pageSize": firstPageSize.value
			},
			method: "GET"
		}).then(res => {
			//console.log(res.data);
			dataList.value = res.data.data.list
		}).catch(err => {
			console.log(err)
		})
	}

	const tableRef = ref();
	//获取数据
	async function getData(model) {
		if (tableRef.value) {
			tableRef.value.clearSelection();
		}
		await uni.request({
			url: urlPrefix + "/pccprogressroute/query",
			data: {
				"pageNo": firstPageNo.value,
				"pageSize": firstPageSize.value,

			},
			method: "GET"
		}).then(res => {
			dataList.value = res.data.data.list;
			pageCount.value = res.data.data.total;
			shoe_last.value = res.data.data.total;
			if (res.data.data.list.length > 0) {
				shoe_last.value = res.data.data.list[0].shoe_last;
			}
			itemNo.value = 0;
		}).catch(err => {
			console.log(err)
			uni.showToast({
				title: '获取数据失败..',
				icon: "error"
			});
		})
	}

	//预加载
	onMounted(async () => {
		await getData('')

	})

	//预加载
	onShow(async () => {
		await getData(model_no.value);
	})
</script>

<style lang="scss">
	// page {
	// 	width: 100%;
	// 	height: 100%;
	// 	padding: 2.5% 2% 1.5% 2%;
	// 	box-sizing: border-box;
	// 	background-color: #fdf6e3;
	// }

	.back {
		// display: flex;
		// justify-content: space-evenly;
		// align-items: center;
		// position: absolute;
		// left: 1.5%;
		// top: 3%;
		// cursor: pointer;
		// z-index: 1;
	}

	// .container {
	// 	width: 100%;
	// 	height: 100%;
	// 	padding: 1%;
	// 	box-sizing: border-box;
	// 	border-radius: 10px;
	// 	box-shadow: 0 0 1px 5px #dddddd;
	// 	position: relative;
	// }

	// .right-top-top {
	// 	display: flex;
	// }

	// .inpBr {
	// 	width: 15%;
	// 	margin-left: 10rpx;
	// }

	// .left-bottom {
	// 	margin-top: 70rpx;
	// 	width: 100%;
	// 	height: 10%;
	// 	display: flex;
	// 	justify-content: center;
	// 	align-items: center;
	// }

	// .uni-pagination__total {
	// 	margin-right: 0.4375rem !important;
	// }

	// .uni-pagination__num-tag {
	// 	min-width: 1.875rem !important;
	// 	background-color: #F0F0F0 !important;
	// }

	// .page--active {
	// 	color: white !important;
	// 	background-color: deeppink !important;
	// }


	// .search {
	// 	width: 100%;
	// 	min-height: 10%;
	// 	margin-bottom: 1%;
	// 	box-sizing: border-box;
	// 	display: flex;
	// 	justify-content: flex-start;
	// 	align-items: center;
	// 	position: relative;

	// 	.searchSelect {
	// 		width: 100%;
	// 		margin-left: 10%;
	// 		margin-right: 0.5%;
	// 		font-size: 24px;
	// 		font-weight: bold;
	// 		padding: 10px;
	// 		border-radius: 10px;
	// 	}

	// 	.search-brand {
	// 		width: 10%;
	// 		margin-left: 0.5%;
	// 		margin-right: 0.5%;
	// 		background: linear-gradient(to right bottom, orangered, pink);
	// 		color: white;
	// 		font-size: 24px;
	// 		font-weight: bold;
	// 		border-radius: 10px;
	// 	}

	// 	.search-type {
	// 		width: 8%;
	// 		margin-left: 0.5%;
	// 		margin-right: 1%;
	// 		background-color: #333;
	// 		color: white;
	// 		font-size: 24px;
	// 		font-weight: bold;
	// 		border-radius: 10px;
	// 	}

	// 	.search-input {
	// 		width: 40%;
	// 		padding: 10px;
	// 		border: 2px solid gray;
	// 		font-size: 24px;
	// 		color: black;
	// 		background-color: white;
	// 		border-radius: 5px;

	// 		&:hover {
	// 			border: 2px solid black;
	// 		}
	// 	}

	// 	.search-input-shoe-last {
	// 		width: 35%;
	// 		padding: 10px;
	// 		border: 2px solid gray;
	// 		font-size: 24px;
	// 		color: black;
	// 		background-color: white;
	// 		border-radius: 5px;

	// 		&:hover {
	// 			border: 2px solid black;
	// 		}
	// 	}

	// 	.search-clear {
	// 		margin-left: -45px;
	// 		margin-top: 2px;
	// 		cursor: pointer;
	// 		z-index: 1;
	// 	}

	// 	.search-clear-shoe-last {
	// 		margin-left: -45px;
	// 		margin-top: 2px;
	// 		cursor: pointer;
	// 		z-index: 1;
	// 	}

	// 	.search-icon-shoe-last {
	// 		width: 5%;
	// 		display: flex;
	// 		justify-content: center;
	// 		align-items: center;
	// 		cursor: pointer;
	// 		z-index: 1;
	// 	}

	// 	.search-list {
	// 		width: calc(40% + 20px + 4px);
	// 		height: 270px;
	// 		position: absolute;
	// 		left: 30%;
	// 		top: 120%;
	// 		z-index: 1;

	// 		.search-box {
	// 			max-height: 270px;
	// 			text-align: center;
	// 			border-radius: 5px;
	// 			color: white;
	// 			font-size: 18px;
	// 			font-weight: bold;
	// 			background-color: gray;
	// 			overflow: auto;

	// 			.search-item {
	// 				box-sizing: border-box;
	// 				padding: 15px 0;
	// 				border-bottom: 1px solid white;
	// 				cursor: pointer;

	// 				&:hover {
	// 					background-color: #aaaaaa;
	// 				}
	// 			}
	// 		}

	// 		&::before {
	// 			content: "";
	// 			position: absolute;
	// 			left: calc(50% - 10px);
	// 			top: -10px;
	// 			border-top: 0px solid transparent;
	// 			border-left: 10px solid transparent;
	// 			border-bottom: 10px solid gray;
	// 			border-right: 10px solid transparent;
	// 		}
	// 	}

	// 	.search-list-shoe-last {
	// 		width: calc(35% + 20px + 4px);
	// 		height: 270px;
	// 		position: absolute;
	// 		left: 30%;
	// 		top: 120%;
	// 		z-index: 1;

	// 		.search-box-shoe-last {
	// 			max-height: 270px;
	// 			text-align: center;
	// 			border-radius: 5px;
	// 			color: white;
	// 			font-size: 18px;
	// 			font-weight: bold;
	// 			background-color: gray;
	// 			overflow: auto;

	// 			.search-item-shoe-last {
	// 				box-sizing: border-box;
	// 				padding: 15px 0;
	// 				border-bottom: 1px solid white;
	// 				cursor: pointer;

	// 				&:hover {
	// 					background-color: #aaaaaa;
	// 				}
	// 			}
	// 		}

	// 		&::before {
	// 			content: "";
	// 			position: absolute;
	// 			left: calc(50% - 10px);
	// 			top: -10px;
	// 			border-top: 0px solid transparent;
	// 			border-left: 10px solid transparent;
	// 			border-bottom: 10px solid gray;
	// 			border-right: 10px solid transparent;
	// 		}
	// 	}
	// }


	// .brand-popup {
	// 	.brand-box {
	// 		width: 80vw;
	// 		height: 80vh;
	// 		border-radius: 1vw;
	// 		background-color: #fdf6e3;
	// 		overflow: auto;

	// 		.brand-part {
	// 			width: 100%;
	// 			margin-bottom: 1%;
	// 			display: flex;
	// 			justify-content: flex-start;
	// 			align-items: center;
	// 			flex-wrap: wrap;

	// 			.brand-initial {
	// 				width: 100%;
	// 				margin-left: 1.5%;
	// 				margin-top: 1%;
	// 				font-size: 32px;
	// 				font-weight: bold;
	// 			}

	// 			.brand {
	// 				width: 18%;
	// 				height: 8vh;
	// 				margin: 1%;
	// 				display: flex;
	// 				justify-content: center;
	// 				align-items: center;
	// 				box-sizing: border-box;
	// 				border-radius: 1vw;
	// 				cursor: pointer;
	// 				font-size: 24px;
	// 				font-weight: bold;
	// 				color: white;
	// 				background: linear-gradient(to right bottom, pink, blue);

	// 				&:hover {
	// 					color: black;
	// 					background: linear-gradient(to right bottom, lightpink, lightblue);
	// 				}
	// 			}
	// 		}
	// 	}
	// }


	// .add-part-popup {
	// 	.add-part {
	// 		width: 100vw;
	// 		height: 100vh;
	// 		overflow: auto;
	// 		padding: 2.5% 2% 1.5% 2%;
	// 		box-sizing: border-box;
	// 		background-color: #fdf6e3;

	// 		.add-part-box {
	// 			width: 100%;
	// 			min-height: 100%;
	// 			border-radius: 10px;
	// 			box-shadow: 0 0 1px 5px #dddddd;
	// 			box-sizing: border-box;
	// 			position: relative;

	// 			.back {
	// 				display: flex;
	// 				justify-content: space-evenly;
	// 				align-items: center;
	// 				position: absolute;
	// 				left: 2.5%;
	// 				top: 4.5%;
	// 				cursor: pointer;
	// 				z-index: 1;
	// 			}

	// 			.submit {
	// 				display: flex;
	// 				justify-content: space-evenly;
	// 				align-items: center;
	// 				position: absolute;
	// 				right: 2.5%;
	// 				top: 4.5%;
	// 				cursor: pointer;
	// 				z-index: 1;
	// 			}

	// 			.title {
	// 				margin-bottom: 1%;
	// 				font-size: 24px;
	// 				font-weight: bold;
	// 				text-align: center;
	// 				padding: 16px;
	// 			}

	// 			.add-part-data {
	// 				width: 100%;
	// 				position: relative;
	// 				display: flex;
	// 				justify-content: flex-start;
	// 				align-items: flex-start;
	// 				flex-wrap: wrap;

	// 				.add-part-attribute {
	// 					width: 28%;
	// 					margin-left: 8%;
	// 					margin-bottom: 4%;
	// 					background-color: #fdf6e3;

	// 					.uni-easyinput {
	// 						width: 80%;
	// 						margin-left: 16px;

	// 						&:deep(.uni-easyinput__content-input) {
	// 							height: 40px;
	// 							font-size: 20px;
	// 							font-weight: bold;
	// 						}
	// 					}

	// 					.uni-stat__select {
	// 						width: 80%;
	// 						margin-left: 16px;

	// 						&:deep(.uni-select) {
	// 							height: 40px;
	// 							font-size: 20px;
	// 							font-weight: bold;
	// 						}

	// 						&:deep(.uni-select__input-placeholder) {
	// 							font-size: 20px;
	// 						}


	// 					}

	// 					.uni-data-checklist {
	// 						margin-left: 16px;

	// 						&:deep(.checklist-box) {
	// 							margin-top: 0px;
	// 						}

	// 						&:deep(.checklist-text) {
	// 							padding: 8px;
	// 							font-size: 20px;
	// 							font-weight: bold;
	// 						}
	// 					}

	// 					.uni-numbox {
	// 						margin-left: 16px;
	// 						height: 40px;

	// 						&:deep(.uni-numbox-btns) {
	// 							width: 40px;
	// 							box-sizing: border-box;

	// 							.uni-numbox--text {
	// 								font-weight: bold;
	// 							}
	// 						}

	// 						&:deep(.uni-numbox__value) {
	// 							width: 60px;
	// 							height: 40px;
	// 							font-size: 20px;
	// 							font-weight: bold;
	// 						}
	// 					}
	// 				}
	// 			}
	// 		}
	// 	}
	// }

	// #leftImgView {
	// 	width: 23%;
	// 	height: 600px;
	// 	margin-left: 2%;
	// 	margin-top: 5%;
	// 	background-color: #fdf6e3;
	// }

	// #rightContent {
	// 	margin-top: 3%;
	// 	width: 75%;
	// 	display: flex;
	// 	flex-wrap: wrap;
	// }

	// .addInput {
	// 	font-size: 20px;
	// 	width: 80%;
	// 	height: 250px;
	// 	margin-left: 8%;
	// 	margin-bottom: 20px;
	// 	background-color: white;
	// 	border: 2px solid #000;
	// 	/* 设置边框颜色和宽度 */
	// 	border-radius: 10px;
	// 	/* 设置边框圆角 */

	// 	&:deep(.uni-easyinput__content-textarea) {
	// 		height: 235px;
	// 		font-size: 20px;
	// 		font-weight: bold;
	// 	}

	// 	&:deep(.uni-easyinput__placeholder-class) {
	// 		font-size: 20px;
	// 	}
	// }

	// .addInputParent {
	// 	display: flex;
	// 	align-items: center;

	// 	.uni-icons {
	// 		margin-left: 2%;
	// 		margin-right: 2%;
	// 	}
	// }
	
	// .top {
	//   display: flex;
	//   justify-content: space-between; /* 使子元素分布在容器的两端 */
	//   align-items: center; /* 垂直居中 */
	// }
	// .addButton {
	//   margin-right: 0; /* 自动外边距将按钮推到最右边 */
	// }
	
	
	
	page {
			width: 100%;
			height: 100%;
			padding: 2.5% 2% 1.5% 2%;
			box-sizing: border-box;
			background-color: #fdf6e3;
		}
	
		.back {
			width: 50px;
			height: 50px;
			display: flex;
			justify-content: space-evenly;
			align-items: center;
			position: absolute;
			left: 1.5%;
			top: 3%;
			cursor: pointer;
			z-index: 1;
		}
		
	.top {
  display: flex;
  justify-content: space-between; /* 使得子元素分布在两端 */
  align-items: center; /* 使得子元素垂直居中 */
  padding: 10px; /* 添加一些内边距 */
	}


	
	.addButton {
	  /* 由于已经在内联样式中设置了背景颜色和字体颜色，这里可以省略这些属性 */
	  // padding: 5px 12px; /* 添加一些内边距 */
	  border: none; /* 移除边框 */
	  border-radius: 5px; /* 添加圆角边框 */
	  cursor: pointer; /* 鼠标悬停时显示手形光标 */
	}
	
		.container {
			width: 100%;
			height: 100%;
			padding: 1%;
			box-sizing: border-box;
			border-radius: 10px;
			box-shadow: 0 0 1px 5px #dddddd;
			position: relative;
		}
	
		.right-top-top {
			display: flex;
		}
	
		.inpBr {
			width: 15%;
			margin-left: 10rpx;
		}
	
		.left-bottom {
			width: 100%;
			height: 10%;
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
		}
	
		.uni-pagination__total {
			margin-right: 0.4375rem !important;
		}
	
		.uni-pagination__num-tag {
			min-width: 1.875rem !important;
			background-color: #F0F0F0 !important;
		}
	
		.page--active {
			color: white !important;
			background-color: deeppink !important;
		}
	
	
		.search {
			width: 100%;
			min-height: 10%;
			margin-bottom: 1%;
			box-sizing: border-box;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			position: relative;
	
			.searchSelect {
				width: 100%;
				margin-left: 10%;
				margin-right: 0.5%;
				font-size: 24px;
				font-weight: bold;
				padding: 10px;
				border-radius: 10px;
			}
	
			.search-brand {
				width: 10%;
				margin-left: 0.5%;
				margin-right: 0.5%;
				background: linear-gradient(to right bottom, orangered, pink);
				color: white;
				font-size: 24px;
				font-weight: bold;
				border-radius: 10px;
			}
	
			.search-type {
				width: 8%;
				margin-left: 0.5%;
				margin-right: 1%;
				background-color: #333;
				color: white;
				font-size: 24px;
				font-weight: bold;
				border-radius: 10px;
			}
	
			.search-input {
				width: 40%;
				padding: 10px;
				border: 2px solid gray;
				font-size: 24px;
				color: black;
				background-color: white;
				border-radius: 5px;
	
				&:hover {
					border: 2px solid black;
				}
			}
	
			.search-input-shoe-last {
				width: 35%;
				padding: 10px;
				border: 2px solid gray;
				font-size: 24px;
				color: black;
				background-color: white;
				border-radius: 5px;
	
				&:hover {
					border: 2px solid black;
				}
			}
	
			.search-clear {
				margin-left: -45px;
				margin-top: 2px;
				cursor: pointer;
				z-index: 1;
			}
	
			.search-clear-shoe-last {
				margin-left: -45px;
				margin-top: 2px;
				cursor: pointer;
				z-index: 1;
			}
	
			.search-icon-shoe-last {
				width: 5%;
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				z-index: 1;
			}
	
			.search-list {
				width: calc(40% + 20px + 4px);
				height: 270px;
				position: absolute;
				left: 30%;
				top: 120%;
				z-index: 1;
	
				.search-box {
					max-height: 270px;
					text-align: center;
					border-radius: 5px;
					color: white;
					font-size: 18px;
					font-weight: bold;
					background-color: gray;
					overflow: auto;
	
					.search-item {
						box-sizing: border-box;
						padding: 15px 0;
						border-bottom: 1px solid white;
						cursor: pointer;
	
						&:hover {
							background-color: #aaaaaa;
						}
					}
				}
	
				&::before {
					content: "";
					position: absolute;
					left: calc(50% - 10px);
					top: -10px;
					border-top: 0px solid transparent;
					border-left: 10px solid transparent;
					border-bottom: 10px solid gray;
					border-right: 10px solid transparent;
				}
			}
	
			.search-list-shoe-last {
				width: calc(35% + 20px + 4px);
				height: 270px;
				position: absolute;
				left: 30%;
				top: 120%;
				z-index: 1;
	
				.search-box-shoe-last {
					max-height: 270px;
					text-align: center;
					border-radius: 5px;
					color: white;
					font-size: 18px;
					font-weight: bold;
					background-color: gray;
					overflow: auto;
	
					.search-item-shoe-last {
						box-sizing: border-box;
						padding: 15px 0;
						border-bottom: 1px solid white;
						cursor: pointer;
	
						&:hover {
							background-color: #aaaaaa;
						}
					}
				}
	
				&::before {
					content: "";
					position: absolute;
					left: calc(50% - 10px);
					top: -10px;
					border-top: 0px solid transparent;
					border-left: 10px solid transparent;
					border-bottom: 10px solid gray;
					border-right: 10px solid transparent;
				}
			}
		}
	
	
		.brand-popup {
			.brand-box {
				width: 80vw;
				height: 80vh;
				border-radius: 1vw;
				background-color: #fdf6e3;
				overflow: auto;
	
				.brand-part {
					width: 100%;
					margin-bottom: 1%;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					flex-wrap: wrap;
	
					.brand-initial {
						width: 100%;
						margin-left: 1.5%;
						margin-top: 1%;
						font-size: 32px;
						font-weight: bold;
					}
	
					.brand {
						width: 18%;
						height: 8vh;
						margin: 1%;
						display: flex;
						justify-content: center;
						align-items: center;
						box-sizing: border-box;
						border-radius: 1vw;
						cursor: pointer;
						font-size: 24px;
						font-weight: bold;
						color: white;
						background: linear-gradient(to right bottom, pink, blue);
	
						&:hover {
							color: black;
							background: linear-gradient(to right bottom, lightpink, lightblue);
						}
					}
				}
			}
		}
	
		.dept-popup {
			.dept-box {
				width: 80vw;
				height: 80vh;
				border-radius: 1vw;
				background-color: #fdf6e3;
				overflow: auto;
	
				.dept-part {
					width: 100%;
					margin-bottom: 1%;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					flex-wrap: wrap;
	
					.dept-initial {
						width: 100%;
						margin-left: 1.5%;
						margin-top: 1%;
						font-size: 28px;
						font-weight: bold;
					}
	
					.dept {
						width: 18%;
						height: 8vh;
						margin: 1%;
						display: flex;
						justify-content: center;
						align-items: center;
						box-sizing: border-box;
						border-radius: 1vw;
						cursor: pointer;
						font-size: 16px;
						font-weight: bold;
						color: white;
						background: linear-gradient(to right bottom, pink, blue);
	
						&:hover {
							color: black;
							background: linear-gradient(to right bottom, lightpink, lightblue);
						}
					}
				}
			}
		}
	
	
		.add-part-popup {
			.add-part {
				width: 100vw;
				height: 100vh;
				overflow: auto;
				padding: 2.5% 2% 1.5% 2%;
				box-sizing: border-box;
				background-color: #fdf6e3;
	
				.add-part-box {
					width: 100%;
					min-height: 100%;
					border-radius: 10px;
					box-shadow: 0 0 1px 5px #dddddd;
					box-sizing: border-box;
					position: relative;
	
					.back {
						width: 50px;
						height: 50px;
						display: flex;
						justify-content: space-evenly;
						align-items: center;
						position: absolute;
						left: 2.5%;
						top: 4.5%;
						cursor: pointer;
						z-index: 1;
					}
	
					.submit {
						display: flex;
						justify-content: space-evenly;
						align-items: center;
						position: absolute;
						right: 2.5%;
						top: 4.5%;
						cursor: pointer;
						z-index: 1;
					}
	
					.title {
						margin-bottom: 1%;
						font-size: 24px;
						font-weight: bold;
						text-align: center;
						padding: 16px;
					}
	
					.add-part-data {
						width: 100%;
						position: relative;
						display: flex;
						justify-content: flex-start;
						align-items: flex-start;
						flex-wrap: wrap;
	
						.add-part-attribute {
							width: 28%;
							margin-left: 8%;
							margin-bottom: 4%;
							background-color: #fdf6e3;
	
							.uni-easyinput {
								width: 80%;
								margin-left: 16px;
	
								&:deep(.uni-easyinput__content-input) {
									height: 40px;
									font-size: 20px;
									font-weight: bold;
								}
							}
	
							.uni-stat__select {
								width: 80%;
								margin-left: 16px;
	
								&:deep(.uni-select) {
									height: 40px;
									font-size: 20px;
									font-weight: bold;
								}
	
								&:deep(.uni-select__input-placeholder) {
									font-size: 20px;
								}
	
	
							}
	
							.uni-data-checklist {
								margin-left: 16px;
	
								&:deep(.checklist-box) {
									margin-top: 0px;
								}
	
								&:deep(.checklist-text) {
									padding: 8px;
									font-size: 20px;
									font-weight: bold;
								}
							}
	
							.uni-numbox {
								margin-left: 16px;
								height: 40px;
	
								&:deep(.uni-numbox-btns) {
									width: 40px;
									box-sizing: border-box;
	
									.uni-numbox--text {
										font-weight: bold;
									}
								}
	
								&:deep(.uni-numbox__value) {
									width: 60px;
									height: 40px;
									font-size: 20px;
									font-weight: bold;
								}
							}
						}
					}
				}
			}
		}
	
		#leftImgView {
			width: 23%;
			height: 600px;
			margin-left: 2%;
			margin-top: 5%;
			background-color: #fdf6e3;
		}
	
		#rightContent {
			margin-top: 3%;
			width: 75%;
			display: flex;
			flex-wrap: wrap;
		}
	
		.addInput {
			font-size: 20px;
			width: 80%;
			height: 250px;
			margin-left: 8%;
			margin-bottom: 20px;
			background-color: white;
			border: 2px solid #000;
			/* 设置边框颜色和宽度 */
			border-radius: 10px;
			/* 设置边框圆角 */
	
			&:deep(.uni-easyinput__content-textarea) {
				height: 235px;
				font-size: 20px;
				font-weight: bold;
			}
	
			&:deep(.uni-easyinput__placeholder-class) {
				font-size: 20px;
			}
		}
	
		.addInputParent {
			display: flex;
			align-items: center;
	
			.uni-icons {
				margin-left: 2%;
				margin-right: 2%;
			}
		}
	
		.table-container {
			height: 72vh;
			/* 容器高度 */
			width: 100%;
			overflow-x: auto;
			/* 启用横向滚动 */
		}
	
		table {
			font-size: 14px;
			color: #606266;
			border-collapse: collapse;
			margin: 20px 0;
			font-size: 18px;
			text-align: left;
			width: 100%;
			/* 确保表格宽度大于容器宽度 */
			border-radius: 5px;
			box-sizing: border-box;
			text-align: center;
			white-space: nowrap;
		}
	
		th,
		td {
			font-size: 14px;
			border: 1px #EBEEF5 solid;
			padding: 8px 10px;
			border-bottom: 1px solid #ddd;
			/* 表格底部边框 */
			height: 44px;
			/* 固定行高 */
			box-sizing: border-box;
			/* 包含内边距和边框 */
		}
	
		th {
			color: #333;
			/* 表头文字颜色 */
		}
	
		tr {
			font-size: 14px;
			height: 44px;
			/* 固定行高 */
		}
	
		tr:hover {
			background-color: #f1f1f1;
			/* 行悬停效果 */
		}
	
		uv-button {
			width: 80px;
			/* 固定按钮宽度 */
			box-sizing: border-box;
			/* 包含内边距和边框 */
		}

	.back {
		width: 50px;
		height: 50px;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		position: absolute;
		left: 1.5%;
		top: 5%;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		cursor: pointer;
		z-index: 1;
	}


</style>