
window.msgFromUniapp = (appData) => {
  console.log("msgFromUniapp");
  initEditor(appData);
};

function UniAppJSBridgeFunc() {
	
		// console.log("--------UniAppJSBridgeFunc----------------------------")

}


document.addEventListener("UniAppJSBridgeReady", function () {
  // console.log("初始化uniapp的API成功");
  UniAppJSBridgeFunc();
  
});


  
 //    document.addEventListener('DOMContentLoaded', () => {
		
	// 	const urlParams = new URLSearchParams(window.location.search);
	// 	const id = urlParams.get('id');
	// 	console.log(typeof(id) +"----" + id)
	// 	if(id !== 'undefined'){
	// 		window.id = id;
	// 	}
	// 	window.urlPrefix = urlParams.get('urlPrefix');
	// 	console.log("接受到id:" + window.id);
	// 	console.log("接受到id:" + urlPrefix);
		
		  
	// 	  // 获取所有单选按钮
	// 	  var deptRadios = document.getElementsByName('deptradiogroup');
	// 	  // 为每个单选按钮添加事件监听器
	// 	  for(var i = 0; i < deptRadios.length; i++) {
	// 	    deptRadios[i].addEventListener('change', function() {
	// 	      // 检查哪个单选按钮被选中
	// 	      for(var i = 0; i < deptRadios.length; i++) {
	// 	        if(deptRadios[i].checked) {
	// 	          // 显示选中的值
	// 	          console.log('Selected value:', deptRadios[i].value);
	// 	  				  dept = deptRadios[i].value;
	// 	          break;
	// 	        }
	// 	      }
	// 	    });
	// 	  }
	
	

	//   editor1 = new FroalaEditor("#edit1", {
	//     language: 'zh_CN',
	//     height: 500,
	//     toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor', 'emoticons', 'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	//     quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	//     imageUploadURL: window.urlPrefix + "/api/files/upload2",
	//     videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	//     colorPicker: true,
	//     imageUpload: true,
	// 	imageInsertButtons: ['imageUpload'], 
	// 	videoInsertButtons: ['videoUpload'], 
	// 	  // backgroundColor: '#FDF6E3',
	// 	// imageResize: true,
	// 	// imageResizeWithPercent: true,
	// 	// imageDefaultWidth: '20%',

	//   });
	  
	  
	//   editor11 = new FroalaEditor("#edit11", {
	//     language: 'zh_CN',
	//     height: 500,
	//     toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor', 'emoticons', 'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	//     quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	//     imageUploadURL: window.urlPrefix + "/api/files/upload2",
	//     videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	//     colorPicker: true,
	//     imageUpload: true,
	//   		imageInsertButtons: ['imageUpload'], 
	//   		videoInsertButtons: ['videoUpload'], 
	  		
	//   		// imageResize: true,
	//   		// imageResizeWithPercent: true,
	//   		// imageDefaultWidth: '20%',
	  
	//   });
	  
	//   // editor1.on('image.inserted', function ($img, response) {
	//   //   $img.css('width', '30%');
	//   // });
  
	//    editor2 = new FroalaEditor("#edit2", {
	//     language: 'zh_CN',
	//     height: 500,
	//     toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor', 'emoticons', 'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	//     quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	//     imageUploadURL: window.urlPrefix + "/api/files/upload2",
	//     videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	//     colorPicker: true,
	//     imageUpload: true,
	// 	imageInsertButtons: ['imageUpload'], 
	// 	videoInsertButtons: ['videoUpload'], 
	// 	// imageResize: true,
	// 	// imageResizeWithPercent: true,
	// 	// imageDefaultWidth: '20%',
	//   });
	  
	  
	//    editor3 = new FroalaEditor("#edit3", {
	//     language: 'zh_CN',
	//     height: 500,
	//     toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor', 'emoticons', 'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	//     quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	//     imageUploadURL: window.urlPrefix + "/api/files/upload2",
	//     videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	//     colorPicker: true,
	//     imageUpload: true,
	// 	imageInsertButtons: ['imageUpload'], 
	// 	videoInsertButtons: ['videoUpload'], 
	// 	// imageResize: true,
	// 	// imageResizeWithPercent: true,
	// 	// imageDefaultWidth: '20%',
	//   });
	  
	  
	//    editor4 = new FroalaEditor("#edit4", {
	//     language: 'zh_CN',
	//     height: 500,
	//     toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor', 'emoticons', 'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	//     quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	//     imageUploadURL: window.urlPrefix + "/api/files/upload2",
	//     videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	//     colorPicker: true,
	//     imageUpload: true,
	// 	imageInsertButtons: ['imageUpload'], 
	// 	videoInsertButtons: ['videoUpload'], 
	// 	// imageResize: true,
	// 	// imageResizeWithPercent: true,
	// 	// imageDefaultWidth: '20%',
	//   });
	  
	//    editor5 = new FroalaEditor("#edit5", {
	//     language: 'zh_CN',
	//     height: 500,
	//     toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor', 'emoticons', 'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	//     quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	//     imageUploadURL: window.urlPrefix + "/api/files/upload2",
	//     videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	//     colorPicker: true,
	//     imageUpload: true,
	// 	imageInsertButtons: ['imageUpload'], 
	// 	videoInsertButtons: ['videoUpload'], 
	// 	// imageResize: true,
	// 	// imageResizeWithPercent: true,
	// 	// imageDefaultWidth: '20%',
	//   });
	  
	//    editor6 = new FroalaEditor("#edit6", {
	//     language: 'zh_CN',
	//     height: 500,
	//     toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor', 'emoticons', 'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	//     quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	//     imageUploadURL: window.urlPrefix + "/api/files/upload2",
	//     videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	//     colorPicker: true,
	//     imageUpload: true,
	// 	imageInsertButtons: ['imageUpload'], 
	// 	videoInsertButtons: ['videoUpload'], 
	// 	// imageResize: true,
	// 	// imageResizeWithPercent: true,
	// 	// imageDefaultWidth: '20%',
	//   });
	  
	//    editor7 = new FroalaEditor("#edit7", {
	//     language: 'zh_CN',
	//     height: 500,
	//     toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor', 'emoticons', 'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	//     quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	//     imageUploadURL: window.urlPrefix + "/api/files/upload2",
	//     videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	//     colorPicker: true,
	//     imageUpload: true,
	// 	imageInsertButtons: ['imageUpload'], 
	// 	videoInsertButtons: ['videoUpload'], 
	// 	// imageResize: true,
	// 	// imageResizeWithPercent: true,
	// 	// imageDefaultWidth: '20%',
	//   });
	  
	//    editor8 = new FroalaEditor("#edit8", {
	//     language: 'zh_CN',
	//     height: 500,
	//     toolbarButtons: ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', '|', 'color', 'textColor', 'emoticons', 'inlineStyle', 'paragraphStyle', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '-', 'insertLink', 'insertImage', 'insertVideo', 'insertTable', '|', 'insertHR', 'undo', 'redo', 'clearFormatting'],
	//     quickInsertButtons: ['image','video', 'table', 'ol', 'ul'], // 移除了 image
	//     imageUploadURL: window.urlPrefix + "/api/files/upload2",
	//     videoUploadURL: window.urlPrefix + "/api/files/uploadVideo",
	//     colorPicker: true,
	//     imageUpload: true,
	// 	imageInsertButtons: ['imageUpload'], 
	// 	videoInsertButtons: ['videoUpload'], 
	// 	// imageResize: true,
	// 	// imageResizeWithPercent: true,
	// 	// imageDefaultWidth: '20%',
	//   });
	  
	//   if(window.id  && window.id !== 'undefined'){
	//   		  fetchData(id);
	//   }
	  
 //    });
	
	
	
	// function fetchData(id) {
	//   fetch(window.urlPrefix + '/pccprogressroute/getById?id=' + id)
	//     .then(response => {
	//       if (!response.ok) {
	//         throw new Error('网络响应错误');
	//       }
	//       return response.json();
	//     })
	//     .then(data => {
	//       // console.log("data:" + JSON.stringify(data));
	// 	  // var jsonData = JSON.parse(data);
	// 		editor1.html.set(data.data.problem_statement)
	// 		editor11.html.set(data.data.purpose)
	// 		editor2.html.set(data.data.current_states)
	// 		editor3.html.set(data.data.target_setting)
	// 		editor4.html.set(data.data.cause_analysis)
	// 		editor5.html.set(data.data.contemeasures)
	// 		editor6.html.set(data.data.action_plan)
	// 		editor7.html.set(data.data.results_evaluation)
	// 		editor8.html.set(data.data.standardization)
			
	// 	 document.getElementById('theme').value = data.data.theme;
			
	// 	  // document.getElementById('purpose').value = data.data.purpose;
		  
		  
	// 	        // 获取所有单选按钮
	// 	        const factoryRadios = document.getElementsByName('factoryradiogroup');
		  
	// 	        // 遍历单选按钮，根据返回的数据设置选中的单选按钮
	// 	        for (const radio of factoryRadios) {
	// 	          if (radio.value === data.data.factory) {
	// 	            radio.checked = true;
	// 				factory = data.data.factory;
	// 	            break;
	// 	          }
	// 	        }
				
	// 			// 获取所有单选按钮
	// 			const deptRadios = document.getElementsByName('deptradiogroup');
						  
	// 			// 遍历单选按钮，根据返回的数据设置选中的单选按钮
	// 			for (const radio of deptRadios) {
	// 			  if (radio.value === data.data.dept) {
	// 			    radio.checked = true;
	// 					dept = data.data.dept;
	// 			    break;
	// 			  }
	// 			}
			
			
	//     })
	//     .catch(error => {
	//       console.error('请求失败:', error);
	//     });
	// };
	
	
	// function saveData(data) {
	//  // 调用保存数据的API
	//      fetch(window.urlPrefix + '/pccprogressroute/create', {
	//        method: 'POST', 
	//        headers: {
	//          'Content-Type': 'application/json', 
	//        },
	//        body: JSON.stringify(data) 
	//      })
	//    .then(function(response) {
	//      // 检查响应状态
	//      console.log('Response status:', response.status);
	     
	//      // 尝试解析响应体为文本
	//      return response.text().then(function(text) {
	//        return {
	//          status: response.status,
	//          statusText: response.statusText,
	//          body: text
	//        };
	//      });
	//    })
	//    .then(function(data) {
	//      // 处理返回的数据
	//      if (data.status >= 200 && data.status < 300) {
	//        // 如果是成功的响应，尝试将响应体解析为JSON
	//        try {
	//          // data.body = JSON.parse(data.body);
	// 		 console.log('保存数据成功');
			 
			 
	// 		   window.parent.postMessage({
	// 		 	key: "success"
	// 		   }, '*'); 
			 	  
	// 		 	webUni.postMessage({
	// 		 	  data: {
	// 		 		action: "save",
	// 		 		data: "success",
	// 		 	  },
	// 		 	});
			 
	//        } catch (e) {
	//          // 如果解析失败，则保持为文本
	//        }
	
	//      }else if(data.status == 400) {
	
	// 		   var jsonData = JSON.parse(data.body);
	// 			 var msg = jsonData.msg;
	// 			 console.log('Message:', msg);
	//      }
	//    })
	//    .catch(function(error) {
	//      // 处理网络错误或其他问题
	//      console.error('There has been a problem with your fetch operation:', error);
	//    });
	// };
	
	
	// function updateData(data) {
	//      fetch(window.urlPrefix + '/pccprogressroute/update', {
	//        method: 'POST', 
	//        headers: {
	//          'Content-Type': 'application/json', 
	//        },
	//        body: JSON.stringify(data) 
	//      })
	//    .then(function(response) {
	//      // 检查响应状态
	//      console.log('Response status:', response.status);
	     
	//      // 尝试解析响应体为文本
	//      return response.text().then(function(text) {
	//        return {
	//          status: response.status,
	//          statusText: response.statusText,
	//          body: text
	//        };
	//      });
	//    })
	//    .then(function(data) {
	//      // 处理返回的数据
	//      if (data.status >= 200 && data.status < 300) {
	//        // 如果是成功的响应，尝试将响应体解析为JSON
	//        try {
	//          // data.body = JSON.parse(data.body);
	// 		 console.log('保存数据成功');
			 
	// 		 window.parent.postMessage({
	// 		 			 	key: "success"
	// 		 }, '*'); 
			 			 	  
	// 		 			 	webUni.postMessage({
	// 		 			 	  data: {
	// 		 			 		action: "save",
	// 		 			 		data: "success",
	// 		 			 	  },
	// 		 			 	});
			 
			 
			 
			 
			 
	//        } catch (e) {
	//          // 如果解析失败，则保持为文本
	//        }
	
	//      }else if(data.status == 400) {
	
	// 		   var jsonData = JSON.parse(data.body);
	// 			 var msg = jsonData.msg;
	// 			 console.log('Message:', msg);
	//      }
	//    })
	//    .catch(function(error) {
	//      // 处理网络错误或其他问题
	//      console.error('There has been a problem with your fetch operation:', error);
	//    });
	// };
	
	
	
	// // 获取内容的函数
	// function getContent() {
	// 		  var theme = document.getElementById('theme').value;
			  
	// 		  // var purpose = document.getElementById('purpose').value;
			  
	// 		  console.log("theme" + theme)
	// console.log("factory" + factory)
	//   console.log("dept" + dept)
	// 			  var content1 = editor1.html.get(true)
	// 			  var content11 = editor11.html.get(true)
	// 			  var content2 = editor2.html.get(true)			  
	// 			  var content3 = editor3.html.get(true)			  
	// 			  var content4 = editor4.html.get(true)			  
	// 			  var content5 = editor5.html.get(true)
	// 			  var content6 = editor6.html.get(true)			  
	// 			  var content7 = editor7.html.get(true)			  
	// 			  var content8 = editor8.html.get(true)
	// 			   var data = {
	// 					  "problem_statement": content1,
	// 						"current_states": content2,
	// 						"target_setting": content3,
	// 						"cause_analysis":content4,
	// 						"contemeasures":content5,
	// 						"action_plan": content6,
	// 						"results_evaluation": content7,
	// 						"standardization": content8,
							
							
	// 						  "theme": theme,
	// 						  "factory": factory,
	// 						  "dept": dept,
	// 						  "purpose": content11
							
	// 						  // "create_by": "创建人",
	// 						  // "update_by": "修改人"
	// 			      };
	// 				  if (typeof window.id !== 'undefined' && window.id !== null && window.id !== 'undefined') {
	// 					  data.id = window.id;
	// 					  updateData(data);  
	// 				  }else{
	// 						saveData(data); 
	// 				  }
	
	// }
	