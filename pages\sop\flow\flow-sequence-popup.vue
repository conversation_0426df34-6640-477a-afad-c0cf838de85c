<!-- 工序流程序号弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 传递消息
const emit = defineEmits(['change-flow-index'])

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 工序流程序号弹框
const flowSequencePopup = ref()

// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getProcessFlowList = inject('getProcessFlowList')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')
// 流程下标
const flowIndex = inject('flowIndex')
// 是否自动切换流程下标
const isAutoChangeIndex = inject('isAutoChangeIndex')

// 工序流程
const processFlow = ref()

// 工序流程数
const flowNumber = ref(0)

// 滚动 id
const scrollId = ref('')

// 显示工序流程序号弹框
async function showFlowSequencePopup(param1, param2) {
  processFlow.value = param1
  flowNumber.value = param2
  scrollId.value = ''
  flowSequencePopup.value.open()
  setTimeout(() => {
    scrollId.value = 'item' + param1.skey
  }, 1000)
}

// 选择序号
async function selectFlowSequence(param1, param2, param3, param4, param5, param6) {
  if (param4 === param6) {
    flowSequencePopup.value.close()
    return
  }
  
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateFlowSequence',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3,
      skey: param4,
      wkGroup: param5,
      targetSkey: param6,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getProcessFlowList(param1, param2, param3)
      emit('change-flow-index')
      tipPopup.value.showTipPopup('success', '修改成功！')
      flowSequencePopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  showFlowSequencePopup
})
</script>

<template>
  <uni-popup
    ref="flowSequencePopup"
    type="center"
    class="flow-sequence-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择序号
      </view>
      
      <view class="description flex-row-center">
        {{ operationMap.get(processFlow.operation) ? operationMap.get(processFlow.operation) : '未知' }} - {{ processFlow.model }} - {{ processFlow.rtgCode }} - {{ processFlow.skey }}
      </view>
      
      <scroll-view
        :scroll-y="true"
        :scroll-into-view="scrollId"
        :scroll-with-animation="true"
        class="flow-sequence-list"
      >
        <view
          v-for="(item, index) in flowNumber"
          :key="index"
          :id="'item' + item"
          class="flow-sequence flex-row-center"
        >
          <view
            @click="selectFlowSequence(processFlow.model, processFlow.operation, processFlow.rtgCode, processFlow.skey, processFlow.wkGroup, item)"
            class="button"
            :style="{
              color: item === processFlow.skey ? 'mediumseagreen' : 'darkgreen'
            }"
          >
            {{ item }}
          </view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.flow-sequence-popup {
  .container {
    width: 430px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .description {
      width: 100%;
      height: 40px;
      font-size: 20px;
      font-weight: bold;
    }
    
    .flow-sequence-list {
      min-height: 70px;
      max-height: 350px;
      // overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .flow-sequence {
        width: 100%;
        padding: 10px;
        
        .button {
          width: 270px;
          height: 50px;
          color: darkgreen;
          font-size: 20px;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>