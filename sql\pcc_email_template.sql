-- 邮件模板表
-- 用于管理不同类型邮件的模板内容

CREATE TABLE pcc_email_template (
    id NUMBER(10) PRIMARY KEY,                    -- 主键ID
    template_code VARCHAR2(50) NOT NULL UNIQUE,  -- 模板代码
    template_name VARCHAR2(200) NOT NULL,        -- 模板名称
    email_type VARCHAR2(50) NOT NULL,            -- 邮件类型 (QUOTE_V1, QUOTE_V2, QUOTE_V3, QUOTE_V4, QUOTE_V5, ESTIMATE_Z, ESTIMATE_ZZ, P_VERSION, UPDATE_NOTIFY)
    
    -- 模板内容
    subject_template VARCHAR2(500),              -- 主题模板（支持变量替换）
    content_template CLOB,                       -- 内容模板（支持变量替换，HTML格式）
    
    -- 模板变量说明
    variables_desc CLOB,                         -- 模板变量说明（JSON格式）
    
    -- 语言支持
    language VARCHAR2(10) DEFAULT 'zh_CN',       -- 语言 (zh_CN-简体中文, zh_TW-繁体中文, en_US-英文, vi_VN-越南文)
    
    -- 状态控制
    status CHAR(1) DEFAULT 'Y',                  -- 状态 (Y-启用, N-禁用)
    is_default CHAR(1) DEFAULT 'N',             -- 是否默认模板 (Y-是, N-否)
    sort_order NUMBER(3) DEFAULT 0,             -- 排序序号
    
    -- 备注信息
    remark VARCHAR2(500),                        -- 备注说明
    
    -- 审计字段
    create_user VARCHAR2(20),                    -- 创建人
    create_date DATE DEFAULT SYSDATE,           -- 创建时间
    update_user VARCHAR2(20),                   -- 更新人
    update_date DATE DEFAULT SYSDATE,           -- 更新时间
    
    -- 约束
    CONSTRAINT chk_template_status CHECK (status IN ('Y', 'N')),
    CONSTRAINT chk_template_default CHECK (is_default IN ('Y', 'N')),
    CONSTRAINT chk_template_email_type CHECK (email_type IN ('QUOTE_V1', 'QUOTE_V2', 'QUOTE_V3', 'QUOTE_V4', 'QUOTE_V5', 'ESTIMATE_Z', 'ESTIMATE_ZZ', 'P_VERSION', 'UPDATE_NOTIFY')),
    CONSTRAINT chk_template_language CHECK (language IN ('zh_CN', 'zh_TW', 'en_US', 'vi_VN'))
);

-- 创建索引
CREATE INDEX idx_pcc_email_template_code ON pcc_email_template(template_code);
CREATE INDEX idx_pcc_email_template_type ON pcc_email_template(email_type);
CREATE INDEX idx_pcc_email_template_status ON pcc_email_template(status);
CREATE INDEX idx_pcc_email_template_default ON pcc_email_template(is_default);
CREATE INDEX idx_pcc_email_template_lang ON pcc_email_template(language);

-- 创建序列
CREATE SEQUENCE seq_pcc_email_template START WITH 1 INCREMENT BY 1;

-- 添加注释
COMMENT ON TABLE pcc_email_template IS '邮件模板表';
COMMENT ON COLUMN pcc_email_template.id IS '主键ID';
COMMENT ON COLUMN pcc_email_template.template_code IS '模板代码';
COMMENT ON COLUMN pcc_email_template.template_name IS '模板名称';
COMMENT ON COLUMN pcc_email_template.email_type IS '邮件类型';
COMMENT ON COLUMN pcc_email_template.subject_template IS '主题模板';
COMMENT ON COLUMN pcc_email_template.content_template IS '内容模板';
COMMENT ON COLUMN pcc_email_template.variables_desc IS '模板变量说明';
COMMENT ON COLUMN pcc_email_template.language IS '语言';
COMMENT ON COLUMN pcc_email_template.status IS '状态';
COMMENT ON COLUMN pcc_email_template.is_default IS '是否默认模板';
COMMENT ON COLUMN pcc_email_template.sort_order IS '排序序号';
COMMENT ON COLUMN pcc_email_template.remark IS '备注说明';
COMMENT ON COLUMN pcc_email_template.create_user IS '创建人';
COMMENT ON COLUMN pcc_email_template.create_date IS '创建时间';
COMMENT ON COLUMN pcc_email_template.update_user IS '更新人';
COMMENT ON COLUMN pcc_email_template.update_date IS '更新时间';

-- 插入默认模板数据
INSERT INTO pcc_email_template (id, template_code, template_name, email_type, subject_template, content_template, variables_desc, language, is_default, create_user) VALUES
(seq_pcc_email_template.nextval, 'QUOTE_V1_DEFAULT', '报价V1版本默认模板', 'QUOTE_V1', 
'【报价通知】${businessId} - 报价V1版本', 
'<html><body><h3>报价通知</h3><p>您好，</p><p>报价单号：${businessId}</p><p>报价版本：V1</p><p>请查收附件中的报价信息。</p><p>如有疑问，请及时联系。</p><br/><p>系统自动发送，请勿回复。</p></body></html>',
'{"businessId":"业务ID/报价单号","userName":"用户姓名","deptName":"部门名称","currentDate":"当前日期"}',
'zh_CN', 'Y', 'SYSTEM');

INSERT INTO pcc_email_template (id, template_code, template_name, email_type, subject_template, content_template, variables_desc, language, is_default, create_user) VALUES
(seq_pcc_email_template.nextval, 'ESTIMATE_Z_DEFAULT', '预估Z版本默认模板', 'ESTIMATE_Z', 
'【预估通知】${businessId} - 预估Z版本', 
'<html><body><h3>预估通知</h3><p>您好，</p><p>预估单号：${businessId}</p><p>预估版本：Z版</p><p>请查收附件中的预估信息。</p><p>如有疑问，请及时联系。</p><br/><p>系统自动发送，请勿回复。</p></body></html>',
'{"businessId":"业务ID/预估单号","userName":"用户姓名","deptName":"部门名称","currentDate":"当前日期"}',
'zh_CN', 'Y', 'SYSTEM');

INSERT INTO pcc_email_template (id, template_code, template_name, email_type, subject_template, content_template, variables_desc, language, is_default, create_user) VALUES
(seq_pcc_email_template.nextval, 'UPDATE_NOTIFY_DEFAULT', '版本更新通知默认模板', 'UPDATE_NOTIFY', 
'【版本更新】${businessId} - 版本更新通知', 
'<html><body><h3>版本更新通知</h3><p>您好，</p><p>单号：${businessId}</p><p>版本已更新，请及时查看最新信息。</p><p>更新时间：${currentDate}</p><br/><p>系统自动发送，请勿回复。</p></body></html>',
'{"businessId":"业务ID","userName":"用户姓名","deptName":"部门名称","currentDate":"当前日期"}',
'zh_CN', 'Y', 'SYSTEM');

COMMIT;
