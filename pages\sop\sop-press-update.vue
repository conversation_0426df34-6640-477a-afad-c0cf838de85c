<script setup>
import { ref, watch, onMounted } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'
import PicturePopup from '@/pages/sop/common/picture-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()
// 图片弹框
const picturePopup = ref()

// 接收参数
const props = defineProps({
  info: {
    type: String,
    default: '{}'
  }
})

// 工序信息
const processInfo = ref(JSON.parse(props.info))

// 型体编号
const model_no = ref(processInfo.value.model)
// 制程
const operation = ref(processInfo.value.operation)
// 主要代码
const rtg_code = ref(processInfo.value.rtgCode)
// 部件名称
const component_name = ref('')
// 部件列表
const componentList = ref([])
// 厂商
const vendor = ref('')
// 流程条件
const process_condition = ref('')

// 是否可以修改
const canChange = ref(user === processInfo.value.insUser)

// 厚度列表
const thicknessList = ['0.5MM', '1MM', '2MM', '5MM', '10MM', '12MM', '15MM']
// 硬度列表
const hardnessList = ['30A', '35A']
// 颜色列表
const colorList = ['白色', '红色', '棕色']
// 更换频率列表
const changeFrequencyList = ['4000次', '8000次']
// 温度列表
const tempList = ['90℃-95℃', '100℃-105℃', '110℃-115℃', '120℃-125℃', '130℃-135℃', '135℃-140℃', '95±5℃', '100±5℃']
// 时间列表
const timeList = ['? 秒']
// 压力列表
const pressureList = ['0.1KG', '5KG', '10KG', '15KG', '20KG', '25KG', '30KG', '35KG', '40KG', '45KG', '50KG', '55KG', '60KG']

// 热冷压规格列表
const pressList = ref([
  {
    model_no: model_no.value,
    operation: operation.value,
    rtg_code: rtg_code.value,
    component_name: component_name.value,
    vendor: '',
    process_condition: '',
    press_type: 1,
    top_thickness: '',
    top_hardness: '',
    top_color: '',
    top_change_frequency: '',
    top_mc_set_temp: '',
    top_actual_temp: '',
    bottom_thickness: '',
    bottom_hardness: '',
    bottom_color: '',
    bottom_change_frequency: '',
    bottom_mc_set_temp: '',
    bottom_actual_temp: '',
    time: '',
    size1: '',
    size2: '',
    size3: '',
    size4: '',
    size5: '',
    size6: '',
    size7: '',
    mc_setting1: '',
    mc_setting2: '',
    mc_setting3: '',
    mc_setting4: '',
    mc_setting5: '',
    mc_setting6: '',
    mc_setting7: '',
    load_cell1: '',
    load_cell2: '',
    load_cell3: '',
    load_cell4: '',
    load_cell5: '',
    load_cell6: '',
    load_cell7: '',
    probe_location_image: '',
    probe_location_remark: '',
    ins_user: '',
    upd_user: '',
    imageList: []
  },
  {
    model_no: model_no.value,
    operation: operation.value,
    rtg_code: rtg_code.value,
    component_name: component_name.value,
    vendor: '',
    process_condition: '',
    press_type: 2,
    top_thickness: '',
    top_hardness: '',
    top_color: '',
    top_change_frequency: '',
    top_mc_set_temp: '',
    top_actual_temp: '',
    bottom_thickness: '',
    bottom_hardness: '',
    bottom_color: '',
    bottom_change_frequency: '',
    bottom_mc_set_temp: '',
    bottom_actual_temp: '',
    time: '',
    size1: '',
    size2: '',
    size3: '',
    size4: '',
    size5: '',
    size6: '',
    size7: '',
    mc_setting1: '',
    mc_setting2: '',
    mc_setting3: '',
    mc_setting4: '',
    mc_setting5: '',
    mc_setting6: '',
    mc_setting7: '',
    load_cell1: '',
    load_cell2: '',
    load_cell3: '',
    load_cell4: '',
    load_cell5: '',
    load_cell6: '',
    load_cell7: '',
    probe_location_image: '',
    probe_location_remark: '',
    ins_user: '',
    upd_user: '',
    imageList: []
  }
])

// 返回上一页
function back() {
  uni.navigateBack()
}

// 初始化热冷压规格列表
function initPressList() {
  pressList.value = [
    {
      model_no: model_no.value,
      operation: operation.value,
      rtg_code: rtg_code.value,
      component_name: component_name.value,
      vendor: '',
      process_condition: '',
      press_type: 1,
      top_thickness: '',
      top_hardness: '',
      top_color: '',
      top_change_frequency: '',
      top_mc_set_temp: '',
      top_actual_temp: '',
      bottom_thickness: '',
      bottom_hardness: '',
      bottom_color: '',
      bottom_change_frequency: '',
      bottom_mc_set_temp: '',
      bottom_actual_temp: '',
      time: '',
      size1: '',
      size2: '',
      size3: '',
      size4: '',
      size5: '',
      size6: '',
      size7: '',
      mc_setting1: '',
      mc_setting2: '',
      mc_setting3: '',
      mc_setting4: '',
      mc_setting5: '',
      mc_setting6: '',
      mc_setting7: '',
      load_cell1: '',
      load_cell2: '',
      load_cell3: '',
      load_cell4: '',
      load_cell5: '',
      load_cell6: '',
      load_cell7: '',
      probe_location_image: '',
      probe_location_remark: '',
      ins_user: '',
      upd_user: '',
      imageList: []
    },
    {
      model_no: model_no.value,
      operation: operation.value,
      rtg_code: rtg_code.value,
      component_name: component_name.value,
      vendor: '',
      process_condition: '',
      press_type: 2,
      top_thickness: '',
      top_hardness: '',
      top_color: '',
      top_change_frequency: '',
      top_mc_set_temp: '',
      top_actual_temp: '',
      bottom_thickness: '',
      bottom_hardness: '',
      bottom_color: '',
      bottom_change_frequency: '',
      bottom_mc_set_temp: '',
      bottom_actual_temp: '',
      time: '',
      size1: '',
      size2: '',
      size3: '',
      size4: '',
      size5: '',
      size6: '',
      size7: '',
      mc_setting1: '',
      mc_setting2: '',
      mc_setting3: '',
      mc_setting4: '',
      mc_setting5: '',
      mc_setting6: '',
      mc_setting7: '',
      load_cell1: '',
      load_cell2: '',
      load_cell3: '',
      load_cell4: '',
      load_cell5: '',
      load_cell6: '',
      load_cell7: '',
      probe_location_image: '',
      probe_location_remark: '',
      ins_user: '',
      upd_user: '',
      imageList: []
    }
  ]
}

// 获取已存在的部位名称
async function getExistingPartList() {
  await uni.request({
    url: urlPrefix + '/sop/getExistingPartList',
    method: 'POST',
    data: {
      model_no: model_no.value,
      operation: operation.value,
      rtg_code: rtg_code.value
    }
  }).then(res => {
    if (res.data.code) {
      let data = res.data.data ? res.data.data : []
      for (let item of data) {
        if (item) {
          componentList.value.push({
            value: item,
            text: item
          })
        }
      }
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 获取热冷压规格列表
async function getPressList() {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/getPressList',
    method: 'POST',
    data: {
      model_no: model_no.value,
      operation: operation.value,
      rtg_code: rtg_code.value,
      component_name: component_name.value
    }
  }).then(res => {
    if (res.data.code) {
      let data = res.data.data ? res.data.data : []
      for (let press of pressList.value) {
        for (let item of data) {
          if (press.press_type === item.press_type) {
            for (let key in item) {
              if (item[key]) {
                press[key] = item[key]
              }
            }
            break
          }
        }
      }
    }
    
    vendor.value = pressList.value[0].vendor
    process_condition.value = pressList.value[0].process_condition
    if (pressList.value[0].probe_location_image.length > 0) {
      pressList.value[0].imageList = pressList.value[0].probe_location_image.split(',')
    }
    if (pressList.value[1].probe_location_image.length > 0) {
      pressList.value[1].imageList = pressList.value[1].probe_location_image.split(',')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 保存热冷压规格数据
async function savePress() {
  if (component_name.value.length === 0) {
    tipPopup.value.showTipPopup('warn', '请选择部件！')
    return
  }
  
  uni.showLoading({
    title: '保存中...',
    mask: true
  })
  
  pressList.value[0] = {
    ...pressList.value[0],
    vendor: vendor.value,
    process_condition: process_condition.value,
    probe_location_image: pressList.value[0].imageList.join('|')
  }
  pressList.value[1] = {
    ...pressList.value[1],
    vendor: vendor.value,
    process_condition: process_condition.value,
    probe_location_image: pressList.value[1].imageList.join('|')
  }
  
  await uni.request({
    url: urlPrefix + '/sop/savePress',
    method: 'POST',
    data: pressList.value
  }).then(res => {
    if (res.data.code) {
      uni.hideLoading()
      tipPopup.value.showTipPopup('success', '保存成功！')
      getPressList()
    } else {
      uni.hideLoading()
      tipPopup.value.showTipPopup('error', '保存失败！')
    }
  }).catch(err => {
    uni.hideLoading()
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 选择图片
function selectImage(param) {
  uni.chooseImage({
    count: 9,
    sizeType: ['original', 'compressed'],
    extension: ['jpg', 'png'],
    sourceType: ['camera ', 'album'],
    success: async (res) => {
      for (let i = 0; i < res.tempFiles.length; i++) {
        // #ifdef WEB
        let file = res.tempFiles[i]
        let imageUrl = await webReadFileAsDataURL(file)
        // #endif
        
        // #ifdef APP
        let path = res.tempFiles[i].path
        let imageUrl = await appReadFileAsDataURL(path)
        // #endif
        
        param.imageList.push(imageUrl)
      }
    }
  })
}

// web 以 url 方式读取文件
function webReadFileAsDataURL(file) {
  return new Promise((resolve, reject) => {
    let reader = new FileReader()
    reader.onload = () => {
      resolve(reader.result)
    }
    reader.onerror = (err) => {
      reject(err)
    }
    reader.readAsDataURL(file)
  })
}

// app 以 url 方式读取文件
function appReadFileAsDataURL(path) {
  return new Promise((resolve, reject) => {
    plus.io.resolveLocalFileSystemURL(path, (entry) => {
      entry.file((file) => {
        let reader = new plus.io.FileReader()
        reader.onload = () => {
          resolve(reader.result)
        }
        reader.onerror = (err) => {
          reject(err)
        }
        reader.readAsDataURL(file)
      })
    })
  })
}

// 清除图片
function clearImage(param1, param2) {
  param1.imageList.splice(param2, 1)
}

watch(component_name, async () => {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  initPressList()
  await getPressList()
  
  uni.hideLoading()
})

onMounted(async () => {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await getExistingPartList()
  await getPressList()
  
  uni.hideLoading()
})
</script>

<template>
  <view class="sop-press-update">
    <view class="top-bar">
      <view @click="back()" class="back button">
        <uni-icons type="back" size="30" color="steelblue"></uni-icons>
      </view>
      
      <view class="title">
        Upper Heat/Cold Press Specification<br>鞋面热压/冷压规格
      </view>
      
      <view v-show="canChange" @click="savePress()" class="save button">
        <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
      </view>
    </view>
    
    <view v-show="canChange" class="info">
      <table>
        <thead>
          <tr>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 15%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 15%;"></td>
            <td style="width: 15%;"></td>
            <td style="width: 15%;"></td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th style="border-top-left-radius: 10px; border-bottom-left-radius: 10px;">STYLE NAME<br>型体名称</th>
            <td>{{ model_no }}</td>
            <th>COMPONENT NAME<br>部件名称</th>
            <td>
              <uni-data-select
                v-model="component_name"
                :localdata="componentList"
                :clear="false"
                placeholder=""
                emptyTips="暂无数据"
              ></uni-data-select>
            </td>
            <th>VENDOR<br>厂商</th>
            <td>
              <textarea v-model="vendor" auto-height></textarea>
            </td>
            <th>PROCESS CONDITION<br>流程条件</th>
            <td style="border-top-right-radius: 10px; border-bottom-right-radius: 10px">
              <textarea v-model="process_condition" auto-height></textarea>
            </td>
          </tr>
        </tbody>
      </table>
    </view>
    <view v-show="!canChange" class="info">
      <table>
        <thead>
          <tr>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 15%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 15%;"></td>
            <td style="width: 15%;"></td>
            <td style="width: 15%;"></td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th style="border-top-left-radius: 10px; border-bottom-left-radius: 10px;">STYLE NAME<br>型体名称</th>
            <td>{{ model_no }}</td>
            <th>COMPONENT NAME<br>部件名称</th>
            <td>
              <uni-data-select
                v-model="component_name"
                :localdata="componentList"
                :clear="false"
                placeholder=""
                emptyTips="暂无数据"
              ></uni-data-select>
            </td>
            <th>VENDOR<br>厂商</th>
            <td>{{ vendor }}</td>
            <th>PROCESS CONDITION<br>流程条件</th>
            <td style="border-top-right-radius: 10px; border-bottom-right-radius: 10px">{{ process_condition }}</td>
          </tr>
        </tbody>
      </table>
    </view>
    
    <view v-show="canChange && component_name.length > 0" class="detail">
      <table v-for="(item, index) in pressList" :key="index">
        <thead>
          <tr>
            <td style="width: 5%;"></td>
            <td style="width: 5%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th rowspan="2" style="border-top-left-radius: 10px;">NO</th>
            <th rowspan="2">PRESS TYPE<br>压机类型</th>
            <th rowspan="2" colspan="3">PAD SPECIFICATION<br>垫片规格说明</th>
            <th colspan="3">TEMP SPECIFICATION<br>温度设定</th>
            <th rowspan="2" colspan="3" style="border-top-right-radius: 10px;">PRESSURE SPECIFICATION(kg/cm2)<br>压力</th>
          </tr>
          <tr>
            <th>M/C or ACTUAL<br>机器或实际温度</th>
            <th>TEMP.<br>温度</th>
            <th>TIME<br>时间</th>
          </tr>
          <tr>
            <th rowspan="17" style="border-bottom-left-radius: 10px;">{{ item.press_type }}</th>
            <th v-show="item.press_type === 1" rowspan="17">HEAT PRESS<br>热压</th>
            <th v-show="item.press_type === 2" rowspan="17">COLD PRESS<br>冷压</th>
            <th rowspan="8">TOP PLATE<br>上垫片</th>
            <th rowspan="2">THICKNESS<br>厚度</th>
            <td rowspan="2">
              <!-- <textarea v-model="item.top_thickness" auto-height></textarea> -->
              <uni-combox v-model="item.top_thickness" :candidates="thicknessList"></uni-combox>
            </td>
            <th rowspan="4">M/C SET TEMP.<br>机器设定温度</th>
            <td rowspan="4">
              <!-- <textarea v-model="item.top_mc_set_temp" auto-height></textarea> -->
              <uni-combox v-model="item.top_mc_set_temp" :candidates="tempList"></uni-combox>
            </td>
            <td rowspan="17">
              <!-- <textarea v-model="item.time" auto-height></textarea> -->
              <uni-combox v-model="item.time" :candidates="timeList"></uni-combox>
            </td>
            <th rowspan="2">SIZE<br>大小</th>
            <th rowspan="2">M/C SETTING<br>机器设定</th>
            <th rowspan="2">LOAD CELL<br>测压元件</th>
          </tr>
          <tr></tr>
          <tr>
            <th rowspan="2">HARDNESS<br>硬度</th>
            <td rowspan="2">
              <!-- <textarea v-model="item.top_hardness" auto-height></textarea> -->
              <uni-combox v-model="item.top_hardness" :candidates="hardnessList"></uni-combox>
            </td>
            <td>
              <textarea v-model="item.size1" auto-height></textarea>
            </td>
            <td>
              <!-- <textarea v-model="item.mc_setting1" auto-height></textarea> -->
              <uni-combox v-model="item.mc_setting1" :candidates="pressureList"></uni-combox>
            </td>
            <td>
              <textarea v-model="item.load_cell1" auto-height></textarea>
            </td>
          </tr>
          <tr>
            <td>
              <textarea v-model="item.size2" auto-height></textarea>
            </td>
            <td>
              <!-- <textarea v-model="item.mc_setting2" auto-height></textarea> -->
              <uni-combox v-model="item.mc_setting2" :candidates="pressureList"></uni-combox>
            </td>
            <td>
              <textarea v-model="item.load_cell2" auto-height></textarea>
            </td>
          </tr>
          <tr>
            <th rowspan="2">COLOR<br>颜色</th>
            <td rowspan="2">
              <!-- <textarea v-model="item.top_color" auto-height></textarea> -->
              <uni-combox v-model="item.top_color" :candidates="colorList"></uni-combox>
            </td>
            <th rowspan="4">ACTUAL TEMP.<br>实际温度</th>
            <td rowspan="4">
              <!-- <textarea v-model="item.top_actual_temp" auto-height></textarea> -->
              <uni-combox v-model="item.top_actual_temp" :candidates="tempList"></uni-combox>
            </td>
            <td>
              <textarea v-model="item.size3" auto-height></textarea>
            </td>
            <td>
              <!-- <textarea v-model="item.mc_setting3" auto-height></textarea> -->
              <uni-combox v-model="item.mc_setting3" :candidates="pressureList"></uni-combox>
            </td>
            <td>
              <textarea v-model="item.load_cell3" auto-height></textarea>
            </td>
          </tr>
          <tr>
            <td>
              <textarea v-model="item.size4" auto-height></textarea>
            </td>
            <td>
              <!-- <textarea v-model="item.mc_setting4" auto-height></textarea> -->
              <uni-combox v-model="item.mc_setting4" :candidates="pressureList"></uni-combox>
            </td>
            <td>
              <textarea v-model="item.load_cell4" auto-height></textarea>
            </td>
          </tr>
          <tr>
            <th rowspan="2">CHANGING FREQUENCY<br>更换频率</th>
            <td rowspan="2">
              <!-- <textarea v-model="item.top_change_frequency" auto-height></textarea> -->
              <uni-combox v-model="item.top_change_frequency" :candidates="changeFrequencyList"></uni-combox>
            </td>
            <td>
              <textarea v-model="item.size5" auto-height></textarea>
            </td>
            <td>
              <!-- <textarea v-model="item.mc_setting5" auto-height></textarea> -->
              <uni-combox v-model="item.mc_setting5" :candidates="pressureList"></uni-combox>
            </td>
            <td>
              <textarea v-model="item.load_cell5" auto-height></textarea>
            </td>
          </tr>
          <tr>
            <td>
              <textarea v-model="item.size6" auto-height></textarea>
            </td>
            <td>
              <!-- <textarea v-model="item.mc_setting6" auto-height></textarea> -->
              <uni-combox v-model="item.mc_setting6" :candidates="pressureList"></uni-combox>
            </td>
            <td>
              <textarea v-model="item.load_cell6" auto-height></textarea>
            </td>
          </tr>
          <tr>
            <td colspan="5"></td>
            <td>
              <textarea v-model="item.size7" auto-height></textarea>
            </td>
            <td>
              <!-- <textarea v-model="item.mc_setting7" auto-height></textarea> -->
              <uni-combox v-model="item.mc_setting7" :candidates="pressureList"></uni-combox>
            </td>
            <td>
              <textarea v-model="item.load_cell7" auto-height></textarea>
            </td>
          </tr>
          <tr>
            <th rowspan="8">BOTTOM PLATE<br>下垫片</th>
            <th rowspan="2">THICKNESS<br>厚度</th>
            <td rowspan="2">
              <!-- <textarea v-model="item.bottom_thickness" auto-height></textarea> -->
              <uni-combox v-model="item.bottom_thickness" :candidates="thicknessList"></uni-combox>
            </td>
            <th rowspan="4">M/C SET TEMP.<br>机器设定温度</th>
            <td rowspan="4">
              <!-- <textarea v-model="item.bottom_mc_set_temp" auto-height></textarea> -->
              <uni-combox v-model="item.bottom_mc_set_temp" :candidates="tempList"></uni-combox>
            </td>
            <th rowspan="8">THERMOCOUPLE PROBE LOCATION<br>电热高温探针位置</th>
            <td rowspan="7" colspan="2">
              <view class="probe-location-image">
                <view
                  v-for="(item1, index1) in item.imageList"
                  :key="index1"
                  class="preview-image watermark"
                >
                  <img
                    @click="picturePopup.showPicturePopup(item1.startsWith('data:image') ? item1 : (urlPrefix + item1))"
                    :src="item1.startsWith('data:image') ? item1 : (urlPrefix + item1)"
                    alt=""
                    class="button"
                  />
                  
                  <view @click="clearImage(item, index1)" class="clear-image button">
                    <uni-icons type="closeempty" size="16" color="darkred"></uni-icons>
                  </view>
                </view>
                
                <view @click="selectImage(item)" class="add-image button">
                  <uni-icons type="plusempty" size="50" color="green"></uni-icons>
                </view>
              </view>
            </td>
          </tr>
          <tr></tr>
          <tr>
            <th rowspan="2">HARDNESS<br>硬度</th>
            <td rowspan="2">
              <!-- <textarea v-model="item.bottom_hardness" auto-height></textarea> -->
              <uni-combox v-model="item.bottom_hardness" :candidates="hardnessList"></uni-combox>
            </td>
          </tr>
          <tr></tr>
          <tr>
            <th rowspan="2">COLOR<br>颜色</th>
            <td rowspan="2">
              <!-- <textarea v-model="item.bottom_color" auto-height></textarea> -->
              <uni-combox v-model="item.bottom_color" :candidates="colorList"></uni-combox>
            </td>
            <th rowspan="4">ACTUAL TEMP.<br>实际温度</th>
            <td rowspan="4">
              <!-- <textarea v-model="item.bottom_actual_temp" auto-height></textarea> -->
              <uni-combox v-model="item.bottom_actual_temp" :candidates="tempList"></uni-combox>
            </td>
          </tr>
          <tr></tr>
          <tr>
            <th rowspan="2">CHANGING FREQUENCY<br>更换频率</th>
            <td rowspan="2">
              <!-- <textarea v-model="item.bottom_change_frequency" auto-height></textarea> -->
              <uni-combox v-model="item.bottom_change_frequency" :candidates="changeFrequencyList"></uni-combox>
            </td>
          </tr>
          <tr>
            <td colspan="2" style="border-bottom-right-radius: 10px;">
              <textarea v-model="item.probe_location_remark" auto-height></textarea>
            </td>
          </tr>
        </tbody>
      </table>
    </view>
    <view v-show="!canChange || component_name.length === 0" class="detail">
      <table v-for="(item, index) in pressList" :key="index">
        <thead>
          <tr>
            <td style="width: 5%;"></td>
            <td style="width: 5%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
            <td style="width: 10%;"></td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th rowspan="2" style="border-top-left-radius: 10px;">NO</th>
            <th rowspan="2">PRESS TYPE<br>压机类型</th>
            <th rowspan="2" colspan="3">PAD SPECIFICATION<br>垫片规格说明</th>
            <th colspan="3">TEMP SPECIFICATION<br>温度设定</th>
            <th rowspan="2" colspan="3" style="border-top-right-radius: 10px;">PRESSURE SPECIFICATION(kg/cm2)<br>压力</th>
          </tr>
          <tr>
            <th>M/C or ACTUAL<br>机器或实际温度</th>
            <th>TEMP.<br>温度</th>
            <th>TIME<br>时间</th>
          </tr>
          <tr>
            <th rowspan="17" style="border-bottom-left-radius: 10px;">{{ item.press_type }}</th>
            <th v-show="item.press_type === 1" rowspan="17">HEAT PRESS<br>热压</th>
            <th v-show="item.press_type === 2" rowspan="17">COLD PRESS<br>冷压</th>
            <th rowspan="8">TOP PLATE<br>上垫片</th>
            <th rowspan="2">THICKNESS<br>厚度</th>
            <td rowspan="2">{{ item.top_thickness }}</td>
            <th rowspan="4">M/C SET TEMP.<br>机器设定温度</th>
            <td rowspan="4">{{ item.top_mc_set_temp }}</td>
            <td rowspan="17">{{ item.time }}</td>
            <th rowspan="2">SIZE<br>大小</th>
            <th rowspan="2">M/C SETTING<br>机器设定</th>
            <th rowspan="2">LOAD CELL<br>测压元件</th>
          </tr>
          <tr></tr>
          <tr>
            <th rowspan="2">HARDNESS<br>硬度</th>
            <td rowspan="2">{{ item.top_hardness }}</td>
            <td>{{ item.size1 }}</td>
            <td>{{ item.mc_setting1 }}</td>
            <td>{{ item.load_cell1 }}</td>
          </tr>
          <tr>
            <td>{{ item.size2 }}</td>
            <td>{{ item.mc_setting2 }}</td>
            <td>{{ item.load_cell2 }}</td>
          </tr>
          <tr>
            <th rowspan="2">COLOR<br>颜色</th>
            <td rowspan="2">{{ item.top_color }}</td>
            <th rowspan="4">ACTUAL TEMP.<br>实际温度</th>
            <td rowspan="4">{{ item.top_actual_temp }}</td>
            <td>{{ item.size3 }}</td>
            <td>{{ item.mc_setting3 }}</td>
            <td>{{ item.load_cell3 }}</td>
          </tr>
          <tr>
            <td>{{ item.size4 }}</td>
            <td>{{ item.mc_setting4 }}</td>
            <td>{{ item.load_cell4 }}</td>
          </tr>
          <tr>
            <th rowspan="2">CHANGING FREQUENCY<br>更换频率</th>
            <td rowspan="2">{{ item.top_change_frequency }}</td>
            <td>{{ item.size5 }}</td>
            <td>{{ item.mc_setting5 }}</td>
            <td>{{ item.load_cell5 }}</td>
          </tr>
          <tr>
            <td>{{ item.size6 }}</td>
            <td>{{ item.mc_setting6 }}</td>
            <td>{{ item.load_cell6 }}</td>
          </tr>
          <tr>
            <td colspan="5"></td>
            <td>{{ item.size7 }}</td>
            <td>{{ item.mc_setting7 }}</td>
            <td>{{ item.load_cell7 }}</td>
          </tr>
          <tr>
            <th rowspan="8">BOTTOM PLATE<br>下垫片</th>
            <th rowspan="2">THICKNESS<br>厚度</th>
            <td rowspan="2">{{ item.bottom_thickness }}</td>
            <th rowspan="4">M/C SET TEMP.<br>机器设定温度</th>
            <td rowspan="4">{{ item.bottom_mc_set_temp }}</td>
            <th rowspan="8">THERMOCOUPLE PROBE LOCATION<br>电热高温探针位置</th>
            <td rowspan="7" colspan="2">
              <view class="probe-location-image">
                <view
                  v-for="(item1, index1) in item.imageList"
                  :key="index1"
                  class="preview-image watermark"
                >
                  <img
                    @click="picturePopup.showPicturePopup(item1.startsWith('data:image') ? item1 : (urlPrefix + item1))"
                    :src="item1.startsWith('data:image') ? item1 : (urlPrefix + item1)"
                    alt=""
                    class="button"
                  />
                </view>
              </view>
            </td>
          </tr>
          <tr></tr>
          <tr>
            <th rowspan="2">HARDNESS<br>硬度</th>
            <td rowspan="2">{{ item.bottom_hardness }}</td>
          </tr>
          <tr></tr>
          <tr>
            <th rowspan="2">COLOR<br>颜色</th>
            <td rowspan="2">{{ item.bottom_color }}</td>
            <th rowspan="4">ACTUAL TEMP.<br>实际温度</th>
            <td rowspan="4">{{ item.bottom_actual_temp }}</td>
          </tr>
          <tr></tr>
          <tr>
            <th rowspan="2">CHANGING FREQUENCY<br>更换频率</th>
            <td rowspan="2">{{ item.bottom_change_frequency }}</td>
          </tr>
          <tr>
            <td colspan="2" style="border-bottom-right-radius: 10px;">{{ item.probe_location_remark }}</td>
          </tr>
        </tbody>
      </table>
    </view>
  </view>
  
  <tip-popup ref="tipPopup" />
  <picture-popup ref="picturePopup" />
</template>

<style lang="scss">
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

page {
  width: 100%;
  height: 100%;
  padding: 2.5% 2% 1.5% 2%;
  background-color: #fdf6e3;
}

.sop-press-update {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-shadow: 0 0 1px 5px #ddd;
  font-size: 18px;
  overflow: auto;
  user-select: text;
  
  .top-bar {
    width: 100%;
    height: 80px;
    padding: 15px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: sticky;
    top: 0;
    left: 0;
    background-color: #fdf6e3;
    z-index: 10;
    
    .back, .save {
      width: 70px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bold;
    }
    
    .title {
      width: 50%;
      font-weight: bold;
      text-align: center;
      margin-right: calc(25% - 70px);
      margin-left: calc(25% - 70px);
    }
  }
  
  .info, .detail {
    width: 100%;
    background-color: #fdf6e3;
    z-index: 10;
    
    table {
      width: 100%;
      border-spacing: 0;
      border-radius: 10px;
      box-shadow: 0 0 5px gray;
      
      tbody {
        th, td {
          padding: 5px;
          height: 50px;
          font-size: 16px;
          text-align: center;
          border: 1px solid #ccc;
          word-break: break-all;
          
          uni-textarea {
            width: 100%;
            padding: 8px 6px 5px 6px;
            border-radius: 10px;
            box-shadow: 0 0 5px gray;
            
            &:deep(.uni-textarea-textarea) {
              font-size: 14px;
            }
          }
          
          .uni-combox {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 5px gray;
            
            .uni-input-input {
              font-size: 14px;
            }
          }
          
          &:deep(.uni-select) {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 5px gray;
            
            .uni-select__input-text {
              color: black;
              font-size: 14px;
            }
          }
        }
        
        td {
          .probe-location-image {
            width: 100%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-content: flex-start;
            
            .preview-image {
              height: 60px;
              margin: 10px;
              position: relative;
              cursor: pointer;
              box-sizing: content-box;
              
              img {
                height: 60px;
              }
              
              .clear-image {
                width: 20px;
                height: 20px;
                position: absolute;
                right: -10px;
                top: -10px;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #fdf6e3;
                border-radius: 50%;
                
                &:hover {
                  background-color: #ccc;
                }
              }
            }
            
            .add-image {
              width: 60px;
              height: 60px;
              margin: 10px;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
      }
    }
  }
  
  .info {
    padding: 0 15px;
    position: sticky;
    top: 80px;
    left: 0;
  }
  
  .detail {
    padding: 0 15px;
    
    table {
      margin: 15px 0;
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .watermark {
    position: relative;
    word-break: break-all;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    
    &:before {
      content: 'STELLA';
      position: absolute;
      top: 2px;
      left: 2px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      text-shadow: 0 0 2px black;
      pointer-events: none;
      z-index: 1;
    }
  }
}
</style>