<script setup>
import { ref, watch, onMounted } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap, proSeqMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 型体
const model = ref('')
// 型体输入框
const modelInput = ref('')
// 聚焦型体输入框
const focusModelInput = ref(false)
// 型体列表
const modelList = ref([])
// 可选型体列表
const modelOptionList = ref([])

// 制程
const operation = ref('')
// 聚焦制程选择器
const focusOperationSelector = ref(false)
// 制程列表
const operationList = ref([])

// 详细制程
const proSeq = ref('')
// 聚焦详细制程选择器
const focusProSeqSelector = ref(false)
// 详细制程列表
const proSeqList = ref([])

// 页码
const pageNo = ref(1)
// 页大小
const pageSize = ref(100)
// 是否已完成
const isCompleted = ref(true)
// 需要翻译
const needTranslation = ref(true)
// 加载状态
const loadType = ref('')
// 翻译列表
const translationList = ref([])

// 翻译弹框
const translationPopup = ref()
// 翻译数据
const translationData = ref()
// 翻译类型映射
const translationTypeMap = new Map([
  [0, '动作'],
  [1, '操作标准'],
  [2, '自检点']
])
// 翻译类型映射1
const translationTypeMap1 = new Map([
  [0, 'action'],
  [1, 'standard'],
  [2, 'check_point']
])
// 翻译语言映射
const translationLangMap = new Map([
  ['cn', '中文'],
  ['en', '英文'],
  ['vi', '越南文'],
  ['id', '印尼文'],
  ['bn', '孟加拉文']
])

// 返回
function back() {
  uni.navigateBack()
}

// 获取型体列表
async function getTranslationModelList() {
  await uni.request({
    url: urlPrefix + '/sop/getTranslationModelList',
    method: 'POST',
    data: {
      isCompleted: isCompleted.value
    }
  }).then(res => {
    if (res.data.code) {
      modelList.value = res.data.data ? res.data.data : []
      modelOptionList.value = modelList.value.slice(0, 50)
    } else {
      modelList.value = []
      modelOptionList.value = []
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 清空型体输入框
function clearModelInput() {
  modelInput.value = ''
  model.value = ''
}

// 选择型体
function selectModel(param) {
  modelInput.value = param
  model.value = param
}

// 获取制程列表
async function getTranslationOperationList() {
  await uni.request({
    url: urlPrefix + '/sop/getTranslationOperationList',
    method: 'POST',
    data: {
      isCompleted: isCompleted.value
    }
  }).then(res => {
    if (res.data.code) {
      operationList.value = res.data.data ? res.data.data : []
    } else {
      operationList.value = []
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 聚焦制程选择器
function focusOperation() {
  hideOption()
  focusOperationSelector.value = true
}

// 选择制程
function selectOperation(param) {
  if (param) {
    operation.value = param
  } else {
    operation.value = ''
  }
}

// 获取详细制程列表
async function getTranslationProSeqList() {
  await uni.request({
    url: urlPrefix + '/sop/getTranslationProSeqList',
    method: 'POST',
    data: {
      isCompleted: isCompleted.value
    }
  }).then(res => {
    if (res.data.code) {
      proSeqList.value = res.data.data ? res.data.data : []
    } else {
      proSeqList.value = []
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 聚焦详细制程选择器
function focusProSeq() {
  hideOption()
  focusProSeqSelector.value = true
}

// 选择详细制程
function selectProSeq(param) {
  if (param) {
    proSeq.value = param
  } else {
    proSeq.value = ''
  }
}

// 隐藏选项
function hideOption() {
  if (focusOperationSelector.value) {
    focusOperationSelector.value = false
  }
  if (focusProSeqSelector.value) {
    focusProSeqSelector.value = false
  }
}

// 切换是否只显示已完成列表
function changeIsCompleted() {
  isCompleted.value = !isCompleted.value
  uni.setStorageSync('sop-isCompleted', isCompleted.value)
}

// 切换是否只显示待翻译列表
function changeNeedTranslation() {
  needTranslation.value = !needTranslation.value
  uni.setStorageSync('sop-needTranslation', needTranslation.value)
}

// 获取翻译列表
async function getTranslationList(param) {
  if (!param) {
    uni.showLoading({
      title: '查询中...',
      mask: true
    })
  }
  
  await uni.request({
    url: urlPrefix + '/sop/getTranslationList',
    method: 'POST',
    data: {
      model: model.value,
      operation: operation.value,
      proSeq: proSeq.value,
      pageNo: param ? param : 1,
      pageSize: pageSize.value,
      isCompleted: isCompleted.value,
      needTranslation: needTranslation.value
    }
  }).then(res => {
    if (param) {
      translationList.value.push(...(res.data.data ? res.data.data : []))
    } else {
      translationList.value = res.data.data ? res.data.data : []
      pageNo.value = 1
    }
    
    if (!res.data.data || res.data.data.length < pageSize.value) {
      loadType.value = 'no-more'
    } else {
      loadType.value = 'more'
    }
    
    pageNo.value++
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  if (!param) {
    uni.hideLoading()
  }
}

// 滚动至底部
function scrollToLower() {
  if (loadType.value === 'loading' || loadType.value === 'no-more' || translationList.value.length < pageSize.value) {
    return
  }
  loadType.value = 'loading'
  getTranslationList(pageNo.value)
}

// 显示翻译弹框
function showTranslationPopup(param1, param2, param3) {
  translationData.value = {
    model: param1.model_no,
    operation: param1.operation,
    proSeq: param1.pro_seq,
    rtgCode: param1.rtg_code,
    seqNo: param1.seq_no,
    skey: param1.skey,
    type: param2,
    lang: param3,
    sourceTitle: translationTypeMap.get(param2) + '(' + translationLangMap.get('cn') + ')',
    sourceText: param1[translationTypeMap1.get(param2) + '_cn'],
    targetTitle: translationTypeMap.get(param2) + '(' + translationLangMap.get(param3) + ')',
    targetText: param1[translationTypeMap1.get(param2) + '_' + param3],
    focusTargetTextarea: false
  }
  
  translationPopup.value.open()
}

// 复制需要翻译的文本
function copySourceText(param) {
  uni.setClipboardData({
    data: param
  })
}

// 修改翻译
async function updateTranslation(param) {
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateTranslation',
    method: 'POST',
    data: {
      model_no: param.model,
      operation: param.operation,
      rtg_code: param.rtgCode,
      seq_no: param.seqNo,
      content: param.targetText,
      lang: param.lang,
      type: param.type
    }
  }).then(async (res) => {
    uni.hideLoading()
    if (res.data.code) {
      await getTranslationList()
      translationPopup.value.close()
      tipPopup.value.showTipPopup('success', '修改成功！')
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    uni.hideLoading()
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

let modelInputTimer = null
watch(modelInput, () => {
  if (modelInputTimer) {
    clearTimeout(modelInputTimer)
  }
  modelInputTimer = setTimeout(() => {
    modelOptionList.value = modelList.value.filter(item => item.toUpperCase().includes(modelInput.value.toUpperCase())).slice(0, 50)
  }, 300)
})

watch([model, operation, proSeq, needTranslation], () => {
  getTranslationList()
})

watch(isCompleted, async () => {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  await Promise.all([
    getTranslationModelList(),
    getTranslationOperationList(),
    getTranslationProSeqList()
  ]).finally(() => {
    uni.hideLoading()
  })
  
  if (model.value || operation.value || proSeq.value) {
    modelInput.value = ''
    model.value = ''
    operation.value = ''
    proSeq.value = ''
  } else {
    getTranslationList()
  }
  
  modelOptionList.value = modelList.value.filter(item => item.toUpperCase().includes(modelInput.value.toUpperCase())).slice(0, 50)
})

onMounted(async () => {
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  await Promise.all([
    getTranslationModelList(),
    getTranslationOperationList(),
    getTranslationProSeqList(),
    getTranslationList()
  ]).finally(() => {
    uni.hideLoading()
  })
})
</script>

<template>
  <view @click="hideOption()" class="sop-translation">
    <view class="top-bar">
      <view @click="back()" class="back button">
        <uni-icons type="back" size="30" color="steelblue"></uni-icons>
      </view>
      
      <view class="title">
        SOP 预估版翻译
      </view>
      
      <view
        @click="changeIsCompleted()"
        class="is-completed button"
        :style="{
          backgroundColor: isCompleted ? '#ccc' : 'transparent'
        }"
      >
        已完成
      </view>
      
      <view
        @click="changeNeedTranslation()"
        class="need-translation button"
        :style="{
          backgroundColor: needTranslation ? '#ccc' : 'transparent'
        }"
      >
        待翻译
      </view>
    </view>
    
    <view class="option">
      <view class="model">
        <text>型体：</text>
        
        <input
          v-model="modelInput"
          @focus="focusModelInput = true"
          @blur="focusModelInput = false"
          type="text"
          placeholder="请输入型体编号"
          class="model-input input"
          :style="{
            boxShadow: focusModelInput ? '0 0 5px blue' : '0 0 5px gray'
          }"
        />
        
        <view v-show="modelInput" @click="clearModelInput()" class="model-clear button">
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <!-- #ifdef APP -->
        <uni-transition
          :show="focusModelInput"
          mode-class="fade"
          class="model-option"
        >
        <!-- #endif -->
        <!-- #ifdef WEB -->
        <view
          class="model-option"
          style="transition: all 0.3s linear;"
          :style="{
            opacity: focusModelInput ? '1' : '0',
            visibility: focusModelInput ? 'visible' : 'hidden'
          }"
        >
        <!-- #endif -->
          <view class="model-option-list">
            <view
              v-for="item in modelOptionList"
              @click="selectModel(item)"
              class="model-option-item"
            >
              {{ item }}
            </view>
            
            <view v-show="modelOptionList.length === 0" class="model-option-empty">
              暂无可选型体
            </view>
          </view>
        <!-- #ifdef WEB -->
        </view>
        <!-- #endif -->
        <!-- #ifdef APP -->
        </uni-transition>
        <!-- #endif -->
      </view>
      
      <view class="operation">
        <text>制程：</text>
        
        <view
          @click.stop="focusOperation()"
          class="operation-selector"
          :style="{
            boxShadow: focusOperationSelector ? '0 0 5px blue' : '0 0 5px gray'
          }"
        >
          {{ operation ? operationMap.get(operation) : '全部' }}
        </view>
        
        <uni-transition
          :show="focusOperationSelector"
          mode-class="fade"
          class="operation-option"
        >
          <view class="operation-option-list">
            <view
              @click="selectOperation()"
              class="operation-option-item"
            >
              全部
            </view>
            
            <view
              v-for="(item, index) in operationList"
              :key="index"
              v-show="operationMap.get(item)"
              @click="selectOperation(item)"
              class="operation-option-item"
            >
              {{ operationMap.get(item) }}
            </view>
          </view>
        </uni-transition>
      </view>
      
      <view class="pro-seq">
        <text>详细制程：</text>
        
        <view
          @click.stop="focusProSeq()"
          class="pro-seq-selector"
          :style="{
            boxShadow: focusProSeqSelector ? '0 0 5px blue' : '0 0 5px gray'
          }"
        >
          {{ proSeq ? proSeqMap.get(proSeq) : '全部' }}
        </view>
        
        <uni-transition
          :show="focusProSeqSelector"
          mode-class="fade"
          class="pro-seq-option"
        >
          <view class="pro-seq-option-list">
            <view
              @click="selectProSeq()"
              class="pro-seq-option-item"
            >
              全部
            </view>
            
            <view
              v-for="(item, index) in proSeqList"
              :key="index"
              v-show="proSeqMap.get(item)"
              @click="selectProSeq(item)"
              class="pro-seq-option-item"
            >
              {{ proSeqMap.get(item) }}
            </view>
          </view>
        </uni-transition>
      </view>
    </view>
    
    <scroll-view
      :scroll-x="true"
      :scroll-y="true"
      @scrolltolower="scrollToLower()"
      class="translation"
    >
      <table>
        <thead>
          <tr>
            <th style="width: 180px;">型体编号</th>
            <th style="width: 100px;">制程</th>
            <th style="width: 100px;">详细制程</th>
            <th style="width: 100px;">主要代码</th>
            <th style="width: 100px;">排序号</th>
            <th style="width: 320px; left: -1px; z-index: 2;">动作(中文)</th>
            <th style="width: 320px;">动作(英文)</th>
            <th style="width: 320px;">动作(越南文)</th>
            <th style="width: 320px;">动作(印尼文)</th>
            <th style="width: 320px;">动作(孟加拉文)</th>
            <th style="width: 320px; left: -1px; z-index: 2;">操作标准(中文)</th>
            <th style="width: 320px;">操作标准(英文)</th>
            <th style="width: 320px;">操作标准(越南文)</th>
            <th style="width: 320px;">操作标准(印尼文)</th>
            <th style="width: 320px;">操作标准(孟加拉文)</th>
            <th style="width: 320px; left: -1px; z-index: 2;">自检点(中文)</th>
            <th style="width: 320px;">自检点(英文)</th>
            <th style="width: 320px;">自检点(越南文)</th>
            <th style="width: 320px;">自检点(印尼文)</th>
            <th style="width: 320px;">自检点(孟加拉文)</th>
          </tr>
        </thead>
        
        <tbody>
          <tr
            v-for="(item, index) in translationList"
            :key="index"
            :style="{
              backgroundColor: index % 2 === 0 ? '#edfbe4' : '#fdf6e3'
            }"
          >
            <td style="width: 180px;">
              <view class="model cell">
                {{ item.model_no }}
              </view>
            </td>
            <td style="width: 100px;">
              <view class="operation cell">
                {{ operationMap.get(item.operation) ? operationMap.get(item.operation) : '/' }}
              </view>
            </td>
            <td style="width: 100px;">
              <view class="pro-seq cell">
                {{ proSeqMap.get(item.pro_seq) ? proSeqMap.get(item.pro_seq) : '/' }}
              </view>
            </td>
            <td style="width: 100px;">
              <view class="rtg-code cell">
                {{ item.rtg_code }}
              </view>
            </td>
            <td style="width: 100px;">
              <view class="skey cell">
                {{ item.skey }}
              </view>
            </td>
            <td
              style="width: 320px; position: sticky; left: -1px; z-index: 1;"
              :style="{
                backgroundColor: index % 2 === 0 ? '#edfbe4' : '#fdf6e3'
              }"
            >
              <view class="action-cn cell">
                {{ item.action_cn }}
              </view>
            </td>
            <td style="width: 320px;">
              <view
                v-show="item.action_cn"
                @click="showTranslationPopup(item, 0, 'en')"
                class="action-en cell button"
              >
                {{ item.action_en ? item.action_en : '/' }}
              </view>
              <view v-show="!item.action_cn" class="action-en cell">
                /
              </view>
            </td>
            <td style="width: 320px;">
              <view
                v-show="item.action_cn"
                @click="showTranslationPopup(item, 0, 'vi')"
                class="action-vi cell button"
              >
                {{ item.action_vi ? item.action_vi : '/' }}
              </view>
              <view v-show="!item.action_cn" class="action-vi cell">
                /
              </view>
            </td>
            <td style="width: 320px;">
              <view
                v-show="item.action_cn"
                @click="showTranslationPopup(item, 0, 'id')"
                class="action-id cell button"
              >
                {{ item.action_id ? item.action_id : '/' }}
              </view>
              <view v-show="!item.action_cn" class="action-id cell">
                /
              </view>
            </td>
            <td style="width: 320px;">
              <view
                v-show="item.action_cn"
                @click="showTranslationPopup(item, 0, 'bn')"
                class="action-bn cell button"
              >
                {{ item.action_bn ? item.action_bn : '/' }}
              </view>
              <view v-show="!item.action_cn" class="action-bn cell">
                /
              </view>
            </td>
            <td
              style="width: 320px; position: sticky; left: -1px; z-index: 1;"
              :style="{
                backgroundColor: index % 2 === 0 ? '#edfbe4' : '#fdf6e3'
              }"
            >
              <view class="standard-cn cell">
                {{ item.standard_cn ? item.standard_cn : '/' }}
              </view>
            </td>
            <td style="width: 320px;">
              <view
                v-show="item.standard_cn"
                @click="showTranslationPopup(item, 1, 'en')"
                class="standard-en cell button"
              >
                {{ item.standard_en ? item.standard_en : '/' }}
              </view>
              <view v-show="!item.standard_cn" class="standard-en cell">
                /
              </view>
            </td>
            <td style="width: 320px;">
              <view
                v-show="item.standard_cn"
                @click="showTranslationPopup(item, 1, 'vi')"
                class="standard-vi cell button"
              >
                {{ item.standard_vi ? item.standard_vi : '/' }}
              </view>
              <view v-show="!item.standard_cn" class="standard-vi cell">
                /
              </view>
            </td>
            <td style="width: 320px;">
              <view
                v-show="item.standard_cn"
                @click="showTranslationPopup(item, 1, 'id')"
                class="standard-id cell button"
              >
                {{ item.standard_id ? item.standard_id : '/' }}
              </view>
              <view v-show="!item.standard_cn" class="standard-id cell">
                /
              </view>
            </td>
            <td style="width: 320px;">
              <view
                v-show="item.standard_cn"
                @click="showTranslationPopup(item, 1, 'bn')"
                class="standard-bn cell button"
              >
                {{ item.standard_bn ? item.standard_bn : '/' }}
              </view>
              <view v-show="!item.standard_cn" class="standard-bn cell">
                /
              </view>
            </td>
            <td
              style="width: 320px; position: sticky; left: -1px; z-index: 1;"
              :style="{
                backgroundColor: index % 2 === 0 ? '#edfbe4' : '#fdf6e3'
              }"
            >
              <view class="check-point-cn cell">
                {{ item.check_point_cn ? item.check_point_cn : '/' }}
              </view>
            </td>
            <td style="width: 320px;">
              <view
                v-show="item.check_point_cn"
                @click="showTranslationPopup(item, 2, 'en')"
                class="check-point-en cell button"
              >
                {{ item.check_point_en ? item.check_point_en : '/' }}
              </view>
              <view v-show="!item.check_point_cn" class="check-point-en cell">
                /
              </view>
            </td>
            <td style="width: 320px;">
              <view
                v-show="item.check_point_cn"
                @click="showTranslationPopup(item, 2, 'vi')"
                class="check-point-vi cell button"
              >
                {{ item.check_point_vi ? item.check_point_vi : '/' }}
              </view>
              <view v-show="!item.check_point_cn" class="check-point-vi cell">
                /
              </view>
            </td>
            <td style="width: 320px;">
              <view
                v-show="item.check_point_cn"
                @click="showTranslationPopup(item, 2, 'id')"
                class="check-point-id cell button"
              >
                {{ item.check_point_id ? item.check_point_id : '/' }}
              </view>
              <view v-show="!item.check_point_cn" class="check-point-id cell">
                /
              </view>
            </td>
            <td style="width: 320px;">
              <view
                v-show="item.check_point_cn"
                @click="showTranslationPopup(item, 2, 'bn')"
                class="check-point-bn cell button"
              >
                {{ item.check_point_bn ? item.check_point_bn : '/' }}
              </view>
              <view v-show="!item.check_point_cn" class="check-point-bn cell">
                /
              </view>
            </td>
          </tr>
        </tbody>
        
        <tbody v-show="translationList.length === 0">
          <tr>
            <td colspan="20">暂无数据</td>
          </tr>
        </tbody>
      </table>
      
      <view class="load-more">
        <uni-load-more
          v-show="translationList.length >= pageSize"
          :status="loadType"
          iconType="circle"
          color="black"
          :contentText="{
            contentdown: '上拉显示更多',
            contentrefresh: '正在加载...',
            contentnomore: '没有更多数据了'
          }"
        ></uni-load-more>
      </view>
    </scroll-view>
  </view>
  
  <uni-popup
    ref="translationPopup"
    type="center"
    :is-mask-click="false"
    class="translation-popup"
  >
    <view class="container">
      <view class="top-bar">
        <view
          @click="translationPopup.close()"
          class="cancel button"
        >
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <view class="description">
          {{ translationData.model + ' - ' + operationMap.get(translationData.operation) + ' - ' + (proSeqMap.get(translationData.proSeq) ? proSeqMap.get(translationData.proSeq) : '/') + ' - ' + translationData.rtgCode + ' - ' + translationData.skey }}
        </view>
        
        <view
          @click="updateTranslation(translationData)"
          class="submit button"
        >
          <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
        </view>
      </view>
      
      <view class="main">
        <view class="source">
          <view class="source-title">
            {{ translationData.sourceTitle }}
          </view>
          
          <view class="source-text">
            <textarea
              :value="translationData.sourceText"
              :disabled="true"
              class="textarea"
            ></textarea>
            
            <view
              v-show="translationData.sourceText"
              @click="copySourceText(translationData.sourceText)"
              class="copy-textarea button"
            >
              复制
            </view>
          </view>
        </view>
        
        <view class="target">
          <view class="target-title">
            {{ translationData.targetTitle }}
          </view>
          
          <view class="target-text">
            <textarea
              v-model="translationData.targetText"
              @focus="translationData.focusTargetTextarea = true"
              @blur="translationData.focusTargetTextarea = false"
              placeholder="请输入翻译"
              class="textarea"
              :style="{
                boxShadow: translationData.focusTargetTextarea ? '0 0 5px blue' : '0 0 5px gray'
              }"
            ></textarea>
            
            <view
              v-show="translationData.targetText"
              @click="translationData.targetText = ''"
              class="clear-textarea button"
            >
              <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss">
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

page {
  width: 100%;
  height: 100%;
  padding: 2.5% 2% 1.5% 2%;
  background-color: #fdf6e3;
}

.sop-translation {
  width: 100%;
  height: 100%;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 0 1px 5px #ddd;
  
  .top-bar {
    width: 100%;
    height: 50px;
    margin-bottom: 20px;
    display: flex;
    
    .back {
      width: 6%;
      height: 50px;
      margin-right: 24%;
    }
    
    .title {
      width: 40%;
      height: 50px;
      margin-right: 8%;
      font-size: 22px;
      font-weight: bold;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .is-completed, .need-translation {
      width: 10%;
      height: 50px;
    }
    
    .is-completed {
      margin-right: 2%;
    }
  }
  
  .option {
    width: 100%;
    height: 50px;
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    
    .model, .operation, .pro-seq {
      width: calc((100% - 40px) / 3);
      height: 50px;
      margin-right: 20px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      position: relative;
      
      text {
        width: 60px;
        font-weight: bold;
      }
      
      .model-input, .operation-selector, .pro-seq-selector {
        width: calc(100% - 60px);
        height: 50px;
        padding: 0 10px;
        font-weight: bold;
      }
      
      .operation-selector, .pro-seq-selector {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        border-radius: 10px;
        box-shadow: 0 0 5px gray;
        cursor: pointer;
        transition: all 0.3s linear;
      }
      
      .model-clear {
        width: 60px;
        height: 40px;
        margin-top: 5px;
        position: absolute;
        top: 0;
        right: 5px;
      }
      
      .model-option, .operation-option, .pro-seq-option {
        width: calc(100% - 60px);
        position: absolute;
        top: calc(100% + 15px);
        right: 0;
        border-radius: 10px;
        filter: drop-shadow(0 0 5px gray);
        background-color: #fdf6e3;
        z-index: 3;
        
        &::before {
          content: "";
          position: absolute;
          top: -9px;
          left: calc(50% - 10px);
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          border-bottom: 10px solid #fdf6e3;
          z-index: -1;
        }
        
        .model-option-list, .operation-option-list, .pro-seq-option-list {
          width: 100%;
          max-height: 250px;
          border-radius: 10px;
          overflow: auto;
          
          .model-option-item, .operation-option-item, .pro-seq-option-item {
            width: 100%;
            min-height: 50px;
            padding: 0 10px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            font-weight: bold;
            text-align: center;
            border-top: 1px solid #ccc;
            transition: all 0.1s linear;
            
            /* #ifdef APP */
            &:active {
              background-color: #ccc;
            }
            /* #endif */
            
            /* #ifdef WEB */
            cursor: pointer;
            &:hover {
              background-color: #ccc;
            }
            /* #endif */
          }
          
          .operation-option-item, .pro-seq-option-item {
            display: flex;
            justify-content: center;
            align-items: center;
          }
          
          .model-option-item:first-child,
          .operation-option-item:first-child,
          .pro-seq-option-item:first-child {
            border: none;
          }
          
          .model-option-empty {
            width: 100%;
            height: 50px;
            padding: 0 10px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            font-weight: bold;
            border: none;
          }
        }
      }
      
      .model-option {
        &::before {
          left: 20px;
        }
        
        .model-option-list {
          .model-option-item {
            text-align: left;
          }
        }
      }
    }
    
    .pro-seq {
      margin-right: 0;
      
      text {
        width: 100px;
      }
      
      .pro-seq-selector, .pro-seq-option {
        width: calc(100% - 100px);
      }
    }
  }
  
  .translation {
    width: 100%;
    height: calc(100% - 140px);
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    overflow: hidden;
    
    table {
      width: fit-content;
      min-width: 100%;
      max-height: 100%;
      border-spacing: 0;
      table-layout: fixed;
      
      tr {
        height: 60px;
      }
      
      th, td {
        padding: 10px;
        text-align: center;
        border: 1px solid #ccc;
        word-break: break-all;
      }
      
      th:first-child, td:first-child {
        border-left: none;
      }
      
      th:last-child, td:last-child {
        border-right: none;
      }
      
      th {
        position: sticky;
        top: 0;
        border-top: none;
        border-bottom: 2px solid #ccc;
        background-color: #fdf6e3;
        z-index: 1;
      }
      
      td .cell {
        width: 100%;
        min-height: 40px;
        padding: 5px;
        word-break: break-all;
        white-space: pre-wrap;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    
    .load-more {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      
      &:deep(.uni-load-more__text) {
        font-size: 18px;
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.3s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
    }
  }
}

.translation-popup {
  .container {
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .top-bar {
      width: 100%;
      height: 70px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .description {
        font-size: 22px;
        font-weight: bold;
      }
      
      .cancel, .submit {
        width: 75px;
        height: 50px;
      }
    }
    
    .main {
      width: 100%;
      padding: 5px;
      display: flex;
      
      .source, .target {
        width: 320px;
        
        .source-title, .target-title {
          width: 100%;
          height: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 20px;
          font-weight: bold;
        }
        
        .source-text, .target-text {
          padding: 5px;
          position: relative;
          word-break: break-all;
          white-space: pre-wrap;
          
          .textarea {
            width: 310px;
            height: 240px;
            padding: 5px;
          }
          
          .copy-textarea, .clear-textarea {
            width: 70px;
            height: 40px;
            position: absolute;
            right: 15px;
            bottom: 15px;
          }
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .textarea {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-textarea-placeholder), &:deep(.uni-textarea-textarea) {
      width: 100%;
      height: 100%;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>