<!-- 重置工序流程弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync('loUserNo')

// 提示弹框
const tipPopup = ref()

// 重置工序流程弹框
const resetFlowPopup = ref()

// 工序信息列表
const processInfoList = inject('processInfoList')
// 搜索型体
const searchModel = inject('searchModel')
// 是否只显示自己创建的工序列表
const isMine = inject('isMine')
// 楦头编号
const lastNos = inject('lastNos')
// Outsole
const osNo = inject('osNo')
// 获取工序信息列表
const getProcessInfoList = inject('getProcessInfoList')
// 是否选择所有工序信息
const isSelectedAllProcessInfo = inject('isSelectedAllProcessInfo')

// 型体编号
const model = ref('')
// 制程
const operation = ref('')
// 主要代码
const rtgCode = ref('')
// 详细制程
const proSeq = ref('')
// 客户
const brand = ref('')

// 显示重置工序流程弹框
function showResetFlowPopup(param1, param2, param3, param4, param5) {
  model.value = param1
  operation.value = param2
  rtgCode.value = param3
  proSeq.value = param4
  brand.value = param5
  resetFlowPopup.value.open()
}

// 重置工序流程
async function resetProcessFlow(param1, param2, param3, param4) {
  uni.showLoading({
    title: '重置中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/resetProcessFlow',
    method: 'POST',
    data: {
      model: param1,
      operation: param2,
      rtgCode: param3
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getProcessInfoList(param2, param4)
      tipPopup.value.showTipPopup('success', '重置成功！')
      resetFlowPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '重置失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  showResetFlowPopup
})
</script>

<template>
  <uni-popup
    ref="resetFlowPopup"
    type="center"
    :is-mask-click="false"
    class="reset-flow-popup"
  >
    <view class="container">
      <view class="title">
        提示
      </view>
      
      <view class="context">
        你确定要重置该选项的工序流程吗？
      </view>
      
      <view class="operate">
        <view @click="resetFlowPopup.close()" class="cancel button">
          取消
        </view>
        
        <view @click="resetProcessFlow(model, operation, rtgCode, brand)" class="confirm button">
          确定
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.reset-flow-popup {
  .container {
    padding: 10px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      padding: 5px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      color: red;
      font-size: 22px;
      font-weight: bold;
    }
    
    .context {
      padding: 30px 5px;
      color: black;
      font-size: 20px;
      font-weight: bold;
    }
    
    .operate {
      padding: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .cancel, .confirm {
        width: 145px;
        height: 45px;
        font-size: 20px;
      }
      
      .cancel {
        color: darkred;
      }
      
      .confirm {
        color: lightseagreen;
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
}
</style>