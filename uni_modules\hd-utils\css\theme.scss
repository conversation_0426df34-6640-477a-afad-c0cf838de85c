$color-white: #FFFFFF !default;
$color-black: #000000 !default;
$color-gray-1: #f7f8fa;
$color-gray-2: #f2f3f5;
$color-gray-3: #ebedf0;
$color-gray-4: #dcdee0;
$color-gray-5: #c8c9cc;
$color-gray-6: #969799;
$color-gray-7: #646566;
$color-gray-8: #323233;

$color-primary: #1C64FD; // 主色 
$color-primary-dark: mix($color-black, $color-primary, 10%) !default;
$color-primary-light: mix($color-white, $color-primary, 20%) !default;
$color-primary-lighter: mix($color-white, $color-primary, 50%) !default;
$color-primary-extra-lighter: mix($color-white, $color-primary, 90%) !default;

$color-success: #00925A !default; // 成功色
$color-success-dark: mix($color-black, $color-success, 10%) !default;
$color-success-light: mix($color-white, $color-success, 20%) !default;
$color-success-lighter: mix($color-white, $color-success, 50%) !default;
$color-success-extra-lighter: mix($color-white, $color-success, 90%) !default;

$color-warning: #FFA900 !default; // 警告色
$color-warning-dark: mix($color-black, $color-warning, 10%) !default;
$color-warning-light: mix($color-white, $color-warning, 20%) !default;
$color-warning-lighter: mix($color-white, $color-warning, 50%) !default;
$color-warning-extra-lighter: mix($color-white, $color-warning, 90%) !default;

$color-error: #FC2C4A !default; // 错误色
$color-error-dark: mix($color-black, $color-error, 10%) !default;
$color-error-light: mix($color-white, $color-error, 20%) !default;
$color-error-lighter: mix($color-white, $color-error, 50%) !default;
$color-error-extra-lighter: mix($color-white, $color-error, 90%) !default;


$color-info: #1C64FD !default; // 信息色
$color-info-dark: mix($color-black, $color-info, 10%) !default;
$color-info-light: mix($color-white, $color-info, 20%) !default;
$color-info-lighter: mix($color-white, $color-info, 50%) !default;
$color-info-extra-lighter: mix($color-white, $color-info, 90%) !default;

// 组件通用
$disabled-opacity: 0.5;

// 文字相关
$color-text-primary: #202124; // 一级信息，标题，主内容文字等
$color-text-secondary: #32353A; // 辅助文字，次要信息等
$color-text-thirdly: #65686F;
$color-text-fourth: #A1A1A1;

// Overlay
$overlay-background-color :rgba(0, 0, 0, 0.5);


// Padding
$padding-base: 8rpx;
$padding-xs: $padding-base * 2;
$padding-sm: $padding-base * 3;
$padding-md: $padding-base * 4;
$padding-lg: $padding-base * 6;
$padding-xl: $padding-base * 8;

// Collapse
$collapse-item-transition-duration: 0.3s;
$collapse-item-content-padding: 15px;
$collapse-item-content-font-size: 13px;
$collapse-item-content-line-height: 1.5;
$collapse-item-content-text-color: $color-gray-6;
$collapse-item-content-background-color: $color-white;
$collapse-item-title-disabled-color: $color-gray-5;


// Font
// 字体大小
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-md: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;
$font-size-xxl: 40rpx;
// 行高
$line-height-xs: 24rpx;
$line-height-sm: 30rpx;
$line-height-md: 34rpx;
$line-height-lg: 40rpx;
$line-height-xl: 44rpx;
$line-height-xxl: 48rpx;

$base-font-family: 'Helvetica Neue',
Helvetica,
Segoe UI,
Arial,
Roboto,
'PingFang SC',
'miui',
'Hiragino Sans GB',
'Microsoft Yahei',
sans-serif;
$price-integer-font-family: Avenir-Heavy,
PingFang SC,
Helvetica Neue,
Arial,
sans-serif;


// Loading
$loading-spinner-color: $color-text-fourth; // 动画颜色
$loading-spinner-size: 60rpx; // 动画大小
$loading-spinner-animation-duration: 0.8s; // 动画周期
$loading-text-color: $color-text-thirdly; // 文字颜色
$loading-text-font-size: $font-size-md; // 文字大小
$loading-text-line-height: $line-height-lg; // 文字行高

// Grid Item
$grid-item-content-padding: $padding-md $padding-xs;
$grid-item-content-background-color: $color-white;
$grid-item-content-active-color: $color-gray-2;
$grid-item-icon-size: 52rpx;
$grid-item-text-color: $color-gray-7;
$grid-item-text-font-size: $font-size-sm;


// Border
$border-color: $color-gray-3;
$border-width-base: 2rpx;
$border-radius-sm: 4rpx;
$border-radius-md: 8rpx;
$border-radius-lg: 16rpx;
$border-radius-max: 999rpx;

// Button
$button-mini-height: 56rpx;
$button-mini-min-width: 100rpx;
$button-mini-font-size: $font-size-sm;
$button-small-height: 64rpx;
$button-small-font-size: $font-size-md;
$button-small-min-width: 120rpx;
$button-normal-font-size: $font-size-lg;
$button-large-height: 100rpx;
$button-default-color: $color-gray-8;
$button-default-height: 76rpx;
$button-default-font-size: $font-size-lg;
$button-default-background-color: $color-white;
$button-default-border-color:$border-color;
$button-primary-color: $color-white;
$button-primary-background-color: $color-primary;
$button-primary-border-color: $color-primary;
$button-error-color: $color-white;
$button-error-background-color: $color-error;
$button-error-border-color: $color-error;
$button-warning-color: $color-white;
$button-warning-background-color: $color-warning;
$button-warning-border-color: $color-warning;
$button-success-color: $color-white;
$button-success-background-color: $color-success;
$button-success-border-color: $color-success;
$button-line-height: 20px;
$button-border-width: 2rpx;
$button-border-radius: $border-radius-sm;
$button-round-square-border-radius: $border-radius-lg;
$button-round-border-radius: $border-radius-max;
$button-plain-background-color: $color-white;
$button-disabled-opacity: $disabled-opacity;

// 颜色变量map 用于解决scss不支持变量名拼接
$var-map: (info: $color-info,
  primary: $color-primary,
  success: $color-success,
  warning: $color-warning,
  error: $color-error,
  primary-dark: $color-primary-dark,
  primary-light: $color-primary-light,
  primary-lighter: $color-primary-lighter,
  primary-extra-lighter: $color-primary-extra-lighter,
  success-dark: $color-success-dark,
  success-light: $color-success-light,
  success-lighter: $color-success-lighter,
  success-extra-lighter: $color-success-extra-lighter,
  warning-dark: $color-warning-dark,
  warning-light: $color-warning-light,
  warning-lighter: $color-warning-lighter,
  warning-extra-lighter: $color-warning-extra-lighter,
  error-dark: $color-error-dark,
  error-light: $color-error-light,
  error-lighter: $color-error-lighter,
  error-extra-lighter: $color-error-extra-lighter,
  info-dark: $color-info-dark,
  info-light: $color-info-light,
  info-lighter: $color-info-lighter,
  info-extra-lighter: $color-info-extra-lighter,
  text-primary: $color-text-primary,
  text-secondary: $color-text-secondary,
  text-thirdly: $color-text-thirdly,
  text-fourth: $color-text-fourth);


// 背景色
$color-module-bg:#F7F8FA; // 模块背景色
$color-global-bg:#F3F5F9; // 全局底部背景色
$color-icon-default:#CFD3DB; // 图标默认色