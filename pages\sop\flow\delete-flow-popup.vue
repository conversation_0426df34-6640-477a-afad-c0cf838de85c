<!-- 删除工序流程弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 传递消息
const emit = defineEmits(['change-flow-index'])

// 提示弹框
const tipPopup = ref()

// 删除工序流程弹框
const deleteFlowPopup = ref()

// 滚动 id
const scrollId = inject('scrollId')
// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getProcessFlowList = inject('getProcessFlowList')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')
// 流程下标
const flowIndex = inject('flowIndex')
// 是否自动切换流程下标
const isAutoChangeIndex = inject('isAutoChangeIndex')

// 型体编号
const model = ref('')
// 制程
const operation = ref('')
// 主要代码
const rtgCode = ref('')
// 选中的工序流程列表
const selectedProcessFlowList = ref([])

// 显示删除工序流程弹框
function showDeleteFlowPopup(param1, param2, param3, param4) {
  model.value = param1
  operation.value = param2
  rtgCode.value = param3
  selectedProcessFlowList.value = param4
  deleteFlowPopup.value.open()
}

// 删除工序流程
async function deleteProcessFlow(param1, param2, param3, param4) {
  uni.showLoading({
    title: '删除中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/deleteProcessFlow',
    method: 'POST',
    data: {
      processFlowList: param4
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getProcessFlowList(param1, param2, param3)
      if (flowIndex.value === processFlowList.value.length - 1) {
        scrollId.value = 'skey' + (processFlowList.value.length - 1)
      }
      emit('change-flow-index')
      tipPopup.value.showTipPopup('success', '删除成功！')
      deleteFlowPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '删除失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  showDeleteFlowPopup
})
</script>

<template>
  <uni-popup
    ref="deleteFlowPopup"
    type="center"
    :is-mask-click="false"
    class="delete-flow-popup"
  >
    <view class="container">
      <view class="title">
        提示
      </view>
      
      <view class="context">
        你确定要删除选中的工序流程吗？
      </view>
      
      <view class="operate">
        <view @click="deleteFlowPopup.close()" class="cancel button">
          取消
        </view>
        
        <view @click="deleteProcessFlow(model, operation, rtgCode, selectedProcessFlowList)" class="confirm button">
          确定
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.delete-flow-popup {
  .container {
    padding: 10px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      padding: 5px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      color: red;
      font-size: 22px;
      font-weight: bold;
    }
    
    .context {
      padding: 30px 15px;
      color: black;
      font-size: 20px;
      font-weight: bold;
    }
    
    .operate {
      padding: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .cancel, .confirm {
        width: 145px;
        height: 45px;
        font-size: 20px;
      }
      
      .cancel {
        color: darkred;
      }
      
      .confirm {
        color: lightseagreen;
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
}
</style>