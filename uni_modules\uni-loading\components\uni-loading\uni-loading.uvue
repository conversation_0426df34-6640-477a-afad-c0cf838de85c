<template>
	<!-- 如果没有插槽，则使用 load-inline 样式 -->
	<view class="uni-loading-main" :class="{'load-inline':$slots['default'] == null}">
		<template v-if="loading">
			<slot></slot>
			<template v-if="$slots['default'] == null">
				<LoadingCircle :speed="16" :size="loadWidth" :color="color"></LoadingCircle>
				<text v-if="text" class="inline-text" :style=" { color: color }">{{text}}</text>
			</template>
			<template v-else>
				<view class="uni-loading-mask" :style="{backgroundColor:background}">
					<LoadingCircle :speed="16" :size="loadWidth" :color="color"></LoadingCircle>
					<text v-if="text" class="block-text" :style=" { color: color }">{{text}}</text>
				</view>
			</template>
		</template>
		<template v-else>
			<slot></slot>
		</template>
	</view>
</template>
<script>
	import LoadingCircle from './loading-circle.uvue'
  // TODO 性能问题，其他类型暂时不对外开放
	// import Icon from './icon.uvue'
	// import UniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.uvue'

	// import { img_load_base } from './load-img.uts'

	/**
	 * Loading-x 加载动画
	 * @description 用于数据加载场景，使用loading等待数据返回
	 * @tutorial https://ext.dcloud.net.cn/plugin?name=uni-loading-x
	 * @property {Boolean} loading 是否显示加载动画，默认：true
	 * @property {String} type = [snow|circle|icon] 加载图标显示，默认：circle
	 * 	@value snow 显示雪花加载动画，性能问题暂时不支持
	 * 	@value circle 显示圆形加载动画
	 * 	@value icon 自定义图标 ,暂时不支持
	 * @property {String} background 加载遮罩颜色，支持 rgba 色值，默认：rgba(255,255,255,0.6)
	 * @property {String} color 加载图标以及加载文字颜色，默认：#333333
	 * @property {String} size 加载图标大小，默认：20
	 * @property {String} text 加载文本，默认：不显示
	 * @property {String} iconType 自定义图标类型，参考 uni-icons ，当前版本暂不支持
	 */

	export default {
		name: "uni-loading",
		components: { LoadingCircle },
		props: {
			loading: {
				type: Boolean,
				default: true,
			},
			type: {
				type: String,
				default: ''
			},
			iconType: {
				type: String,
				default: 'gear-filled'
			},
			size: {
				type: Number,
				default: 0
			},
			text: {
				type: String,
				default: ''
			},
			background: {
				type: String,
				default: 'rgba(255,255,255,0.6)'
			},
			color: {
				type: String,
				default: '#333'
			}
		},
		data() {
			return {};
		},
		computed: {
			loadWidth() : number {
				let width = this.size
				if (width == 0) {
					return 20
				}
				return width
			},
			styles() : string {
				return `width:${this.loadWidth}px;height:${this.loadWidth}px;`
			}
		},
		created() {},
		methods: {}
	}
</script>

<style scoped>
	.uni-loading-main {
		position: relative;
	}

	.uni-loading-main.load-inline {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.block-text {
		margin-top: 8px;
		font-size: 14px;
	}

	.inline-text {
		margin-left: 8px;
		font-size: 14px;
	}


	.uni-loading-mask {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		justify-content: center;
	}

	.uni-loading-mask {
		background-color: rgba(0, 0, 0, 0.3);
		z-index: 2;
	}

	.uni-load {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
	}

	.load-text {
		font-size: 14px;
		color: #fff;
		margin-top: 12px;
	}

	.uni-load .image,
	.load-image {
		width: 100%;
		height: 100%;
	}

	.load-ani {
		transition-property: transform;
		transition-duration: 0.1s;
		transition-timing-function: linear;
		transform: rotate(0deg);
	}

</style>
