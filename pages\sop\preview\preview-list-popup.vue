<!-- 预览列表弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import { operationMap, proSeqMap } from '@/pages/sop/common/commonMap.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 提示弹框
const tipPopup = ref()

// 预览列表弹框
const previewListPopup = ref()

// 工序信息
const processInfo = inject('processInfo')
// 预览下标
const previewIndex = inject('previewIndex')
// 预览列表
const previewList = inject('previewList')

// 滚动 id
const scrollId = ref('')

// 显示预览列表弹框
function showPreviewListPopup() {
  scrollId.value = ''
  setTimeout(() => {
    scrollId.value = 'index' + (previewIndex.value - 1)
  }, 1000)
  previewListPopup.value.open()
}

// 进入预览详情
function enterPreviewDetail(param) {
  previewIndex.value = param
  previewListPopup.value.close()
}

defineExpose({
  showPreviewListPopup
})
</script>

<template>
  <uni-popup
    ref="previewListPopup"
    type="center"
    class="preview-list-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        预览列表
      </view>
      
      <view class="description flex-row-center">
        {{ operationMap.get(processInfo.operation) }} - {{ proSeqMap.get(processInfo.proSeq) }} - {{ processInfo.brand }} - {{ processInfo.model }} - {{ processInfo.rtgCode }}{{ (processInfo.rtgType && processInfo.rtgType !== '/') ? (' - ' + processInfo.rtgType) : '' }}{{ (processInfo.material && processInfo.material !== '/') ? (' - ' + processInfo.material) : '' }}
      </view>
      
      <view class="main">
        <scroll-view
          :scroll-y="true"
          :scroll-into-view="scrollId"
          :scroll-with-animation="true"
          class="preview-list"
        >
          <table>
            <thead>
              <tr>
                <th style="width: 13%;">序号</th>
                <th style="width: 13%;">加工段</th>
                <th style="width: 32%;">工序名称</th>
                <th style="width: 32%;">动作</th>
                <th style="width: 10%;">详情</th>
              </tr>
            </thead>
            
            <tbody v-show="previewList.length > 0">
              <tr
                v-for="(item, index) in previewList"
                :key="index"
                :id="'index' + index"
                :style="{
                  backgroundColor: index % 2 === 0 ? '#edfbe4' : '#fdf6e3',
                  filter: scrollId === ('index' + (index - 1)) ? 'hue-rotate(90deg)' : 'none'
                }"
              >
                <td>{{ item.skey }}</td>
                <td>{{ item.wkGroup }}</td>
                <td>
                  <view class="name">
                    {{ item.seqName }}
                  </view>
                </td>
                <td>
                  <view class="action">
                    {{ item.actions }}
                  </view>
                </td>
                <td>
                  <view class="enter">
                    <uni-icons
                      @click="enterPreviewDetail(index)"
                      type="redo"
                      size="30"
                      color="darkviolet"
                      class="button"
                    ></uni-icons>
                  </view>
                </td>
              </tr>
            </tbody>
            
            <tbody v-show="previewList.length === 0">
              <tr>
                <td colspan="6">暂无数据</td>
              </tr>
            </tbody>
          </table>
        </scroll-view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  font-size: 18px;
  box-sizing: border-box;
}

.preview-list-popup {
  .container {
    width: 1000px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .description {
      width: 100%;
      height: 40px;
      font-size: 20px;
      font-weight: bold;
    }
    
    .main {
      width: 100%;
      padding: 10px;
      
      .preview-list {
        width: 100%;
        height: 500px;
        border-radius: 10px;
        box-shadow: 0 0 5px gray;
        overflow: hidden;
        
        &:deep(.uni-scroll-view) {
          // /* #ifdef WEB */
          // &::-webkit-scrollbar {
          //   display: none;
          // }
          // /* #endif */
        }
        
        table {
          width: 100%;
          max-height: 100%;
          border-radius: 10px;
          border-spacing: 0;
          
          tr {
            width: 100%;
            height: 60px;
          }
          
          th, td {
            text-align: center;
            border-right: 1px solid #ccc;
            border-bottom: 1px solid #ccc;
            
            &:last-child {
              border-right: none;
            }
          }
          
          th {
            position: sticky;
            top: 0;
            z-index: 1;
            background-color: #fdf6e3;
            /* #ifdef APP */
            border-top: 1px solid #ccc;
            /* #endif */
          }
          
          td {
            .name, .action {
              padding: 5px;
            }
            
            .enter {
              padding: 10px;
            }
            
            .button {
              height: 40px;
            }
          }
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .flex-row-start-center {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
</style>