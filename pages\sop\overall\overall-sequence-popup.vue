<!-- 序号弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 序号弹框
const overallSequencePopup = ref()

// 工序流程详情
const flowDetail = ref({})

// 工序信息
const processInfo = inject('processInfo')
// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getOverallFlow = inject('getOverallFlow')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')

// 滚动 id
const scrollId = ref('')

// 显示序号弹框
async function showOverallSequencePopup(param) {
  flowDetail.value = param
  scrollId.value = ''
  overallSequencePopup.value.open()
  setTimeout(() => {
    scrollId.value = 'item' + param.skey
  }, 1000)
}

// 选择序号
async function selectOverallSequence(param) {
  if (param === flowDetail.value.skey) {
    overallSequencePopup.value.close()
    return
  }
  
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateFlowSequence',
    method: 'POST',
    data: {
      model: flowDetail.value.model,
      operation: flowDetail.value.operation,
      rtgCode: flowDetail.value.rtgCode,
      skey: flowDetail.value.skey,
      wkGroup: flowDetail.value.wkGroup,
      targetSkey: param,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getOverallFlow()
      tipPopup.value.showTipPopup('success', '修改成功！')
      overallSequencePopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  showOverallSequencePopup
})
</script>

<template>
  <uni-popup
    ref="overallSequencePopup"
    type="center"
    class="overall-sequence-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择序号 - {{ flowDetail.skey }}
      </view>
      
      <view v-show="flowDetail.actions" class="flow-action flex-row-center">
        <view class="flow-action-description">
          {{ flowDetail.actions }}
        </view>
      </view>
      
      <scroll-view
        :scroll-y="true"
        :scroll-into-view="scrollId"
        :scroll-with-animation="true"
        class="flow-sequence-list"
      >
        <view
          v-for="(item, index) in processFlowList.length"
          :key="index"
          :id="'item' + item"
          class="flow-sequence flex-row-center"
        >
          <view
            @click="selectOverallSequence(item)"
            class="button"
            :style="{
              filter: item === flowDetail.skey ? 'hue-rotate(90deg)' : 'none',
              backdropFilter: item === flowDetail.skey ? 'hue-rotate(90deg)' : 'none'
            }"
          >
            {{ item }}
          </view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.overall-sequence-popup {
  .container {
    width: 430px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .flow-action {
      width: 100%;
      padding: 10px;
      
      .flow-action-description {
        width: 400px;
        font-size: 20px;
        font-weight: bold;
        text-align: center;
      }
    }
    
    .flow-sequence-list {
      min-height: 70px;
      max-height: 350px;
      // overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .flow-sequence {
        width: 100%;
        padding: 10px;
        
        .button {
          width: 270px;
          height: 50px;
          color: darkgreen;
          font-size: 20px;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>