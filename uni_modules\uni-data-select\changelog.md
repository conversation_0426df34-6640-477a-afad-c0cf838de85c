## 1.0.6（2023-04-12）
- 修复 微信小程序点击时会改变背景颜色的 bug
## 1.0.5（2023-02-03）
- 修复 禁用时会显示清空按钮
## 1.0.4（2023-02-02）
- 优化 查询条件短期内多次变更只查询最后一次变更后的结果
- 调整 内部缓存键名调整为 uni-data-select-lastSelectedValue
## 1.0.3（2023-01-16）
- 修复 不关联服务空间报错的问题
## 1.0.2（2023-01-14）
- 新增  属性 `format` 可用于格式化显示选项内容
## 1.0.1（2022-12-06）
- 修复  当where变化时，数据不会自动更新的问题
## 0.1.9（2022-09-05）
- 修复 微信小程序下拉框出现后选择会点击到蒙板后面的输入框
## 0.1.8（2022-08-29）
- 修复 点击的位置不准确
## 0.1.7（2022-08-12）
- 新增 支持 disabled 属性
## 0.1.6（2022-07-06）
- 修复 pc端宽度异常的bug
## 0.1.5
- 修复 pc端宽度异常的bug
## 0.1.4（2022-07-05）
- 优化 显示样式
## 0.1.3（2022-06-02）
- 修复 localdata 赋值不生效的 bug
- 新增 支持  uni.scss 修改颜色
- 新增 支持选项禁用（数据选项设置 disabled: true 即禁用）
## 0.1.2（2022-05-08）
- 修复 当 value 为 0 时选择不生效的 bug
## 0.1.1（2022-05-07）
- 新增 记住上次的选项（仅 collection 存在时有效）
## 0.1.0（2022-04-22）
- 初始化
