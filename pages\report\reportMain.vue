<template>
	<uv-loading-page bgColor="#2d3653" :loading="loading" loading-text="加载中..." font-size="24rpx"> </uv-loading-page>
	<view id="main" >
		<view id="frame2">
		    <view id="frameTool">
		        <uni-icons @click="back" type="left" size="32"
		                   style="margin-left:1vw;margin-top:10rpx;color:white"></uni-icons>
		        <!-- <uni-icons @click="reportChange" type="list" size="32"
		                   style="margin-left:1vw;margin-top:10rpx;color:white"></uni-icons> -->
				<uv-tabs :current="reportIndex" style="margin-left: 50px;" activeStyle="{ color: '#ffffff' }" lineColor="#ffffff" :list="reportList" @click="click"></uv-tabs>
		    </view>
		    <iframe marginwidth="0" marginheight="0" scrolling="no"
		            style="height:calc(100vh - var(--window-bottom) - var(--status-bar-height) - 4.5vw);width: 100%;margin:0;padding: 0;"
		            :src="reportUrl"></iframe>
		</view>
	</view>
</template>

<script>
import urlPrefix from '@/pages/common/urlPrefix.js'
	export default {
		data() {
			return {
				reportUrl: '',
				loading: true,
				userNo: uni.getStorageSync("loginUserName"),
				tabs:{
					'color':'white',
					'font-size':'30px'
				},
				reportList:[],
				reportIndex: 0
			}
		},
		onLoad() {
			setTimeout(() => {
				this.loading = false;
			}, 500);
			this.initMenu();
		},
		methods: {
			touchStart(event) {
				// 记录触摸起始点的横坐标
				this.startX = event.touches[0].clientX;
			},
			back(){
				uni.navigateBack({
					delta: 1,
					animationType: 'pop-out',
					animationDuration: 200
				})
			},
			initMenu(){
				uni.request({
				    url: urlPrefix + "/menu/findReportByUser?userNo=" + this.userNo + "",
				    method: "GET"
				}).then(res => {
				    // this.commonyUsedMenu = res.data.data;
				    // 创建一个新数组，将每个对象转换为value和text对象
					var i = 0;
				    res.data.data.map(item => {
						if(i == 0){
							this.reportUrl = item.menuUrl;
						}
				        this.reportList.push({name: item.menuDesc, url: item.menuUrl,index:i});
						i++;
				    });
					
					this.reportIndex = '0';
				}).catch(err => {
				    console.log(err)
				});
			},
			click(index,item){
				this.loading = true;
				this.reportUrl = index.url;
				setTimeout(() => {
					this.loading = false
					console.log(0)
				}, 500);
			},
			touchMove(event) {
				// 计算滑动距离
				const currentX = event.touches[0].clientX;
				this.deltaX = currentX - this.startX;
			},
			touchEnd() {
				// 判断滑动方向
				if (this.deltaX > 100) {
					// 向右滑动逻辑   这里只建议写一些性能消耗小的逻辑，比如：this.status = !this.status 因为当他的横坐标大于或小于50时，每隔一个数字都会执行一次，所以...非常消化性能
					uni.navigateTo({
						url: '/pages/menu/menu',
						"animationType": "slide-in-left",
						"animationDuration": 500
					});
				} else if (this.deltaX < -100) {
					// 向左滑动逻辑   这里只建议写一些性能消耗小的逻辑，比如：this.status = !this.status 因为当他的横坐标大于或小于50时，每隔一个数字都会执行一次，所以...非常消化性能
					
				}
				this.deltaX = 0;
				// 清除触摸起始点记录，这里也可以写一些比较复杂的逻辑，每滑动一次松后执行。
			}
		}
	}
</script>

<style>
#main{
	background-color: #2d3653;
}
#frameTool {
    padding-left: 50px;
	padding-top: 10px;
    height: 4.5vw;
    background-color: #2d3653;
    color: white;
    display: flex;
    align-items: center;
}

::v-deep .uv-tabs__wrapper__nav__item__text{
  color: #fff !important;
  font-size: 20px !important;
}
</style>
