<!-- 部位弹框 -->
<script setup>
import { ref, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 部位弹框
const overallPartPopup = ref()

// 工序流程详情
const flowDetail = ref({})
// 部位列表
const partList = ref([])
// 部位输入框
const partInput = ref('')
// 是否聚焦部位输入框
const focusPartInput = ref(false)

// 工序信息
const processInfo = inject('processInfo')
// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getOverallFlow = inject('getOverallFlow')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')

// 获取部位列表
async function getPartList() {
  await uni.request({
    url: urlPrefix + '/sop/getPartList',
    method: 'GET'
  }).then(res => {
    if (res.data.code) {
      partList.value = res.data.data ? res.data.data : []
    } else {
      partList.value = []
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示部位弹框
async function showOverallPartPopup(param) {
  flowDetail.value = {
    ...param,
    imgList: []
  }
  partInput.value = ''
  
  if (partList.value.length === 0) {
    uni.showLoading({
      title: '加载中...',
      mask: true
    })
    
    await getPartList()
    
    uni.hideLoading()
  }
  
  overallPartPopup.value.open()
}

// 修改部位
async function updatePart(param) {
  if (flowDetail.value.partName && param === flowDetail.value.partName) {
    overallPartPopup.value.close()
    return
  }
  
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateOverallFlow',
    method: 'POST',
    data: {
      ...flowDetail.value,
      partName: param,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getOverallFlow()
      tipPopup.value.showTipPopup('success', '修改成功！')
      overallPartPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

defineExpose({
  getPartList,
  showOverallPartPopup
})
</script>

<template>
  <uni-popup
    ref="overallPartPopup"
    type="center"
    class="overall-part-popup"
  >
    <view class="container">
      <view class="title flex-row-center">
        请选择部位 - {{ flowDetail.skey }}
      </view>
      
      <view class="overall-part-list">
        <view class="overall-part flex-row-center">
          <input
            v-model="partInput"
            @focus="focusPartInput = true"
            @blur="focusPartInput = false"
            type="text"
            placeholder="请输入部位"
            class="input"
            :style="{
              boxShadow: focusPartInput ? '0 0 5px blue' : '0 0 5px gray'
            }"
          />
          
          <view v-show="partInput" @click="partInput = ''" class="clear button">
            <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
          </view>
          
          <view
            @click="updatePart(partInput)"
            class="save button"
          >
            <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
          </view>
        </view>
        
        <view
          v-for="(item, index) in partList.filter(i => i.includes(partInput.toUpperCase())).splice(0, 50)"
          :key="index"
          class="overall-part flex-row-center"
        >
          <view
            @click="updatePart(item)"
            class="button"
            :style="{
              color: item === flowDetail.partName ? 'darkorchid' : 'darkmagenta'
            }"
          >
            {{ item }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.overall-part-popup {
  .container {
    width: 430px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      font-size: 22px;
      font-weight: bold;
    }
    
    .overall-part-list {
      height: 350px;
      overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .overall-part {
        width: 100%;
        padding: 10px;
        position: relative;
        
        .button {
          width: 270px;
          min-height: 50px;
          color: darkmagenta;
          font-size: 20px;
          padding: 5px;
        }
        
        .input {
          width: 270px;
          height: 50px;
          padding: 0 10px;
        }
        
        .clear {
          width: 60px;
          min-height: 40px;
          position: absolute;
          top: 15px;
          right: 85px;
        }
        
        .save {
          width: 50px;
          height: 50px;
          position: absolute;
          top: 10px;
          right: 15px;
        }
        
        &:first-child {
          position: sticky;
          top: 0;
          background-color: #fdf6e3;
          z-index: 1;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .input {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-input-placeholder), &:deep(.uni-input-input) {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>