<!-- 编辑弹框 -->
<script setup>
import { ref, inject } from 'vue'
import { operationMap } from '@/pages/sop/common/commonMap.js'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync('loUserNo')

// 提示弹框
const tipPopup = ref()

// 编辑弹框
const editPopup = ref()

// 工序信息
const processInfo = ref()

// 热冷压规格制程列表
const pressOperationList = ref(['1D', '1E'])

// 工序信息列表
const processInfoList = inject('processInfoList')
// 搜索型体
const searchModel = inject('searchModel')
// 是否只显示自己创建的工序列表
const isMine = inject('isMine')
// 楦头编号
const lastNos = inject('lastNos')
// Outsole
const osNo = inject('osNo')
// 获取工序列表
const getProcessInfoList = inject('getProcessInfoList')
// 是否为审核人
const isAuditer = inject('isAuditer')

// 显示编辑弹框
function showEditPopup(param) {
  processInfo.value = param
  editPopup.value.open()
}

// 修改完成状态
async function changeState() {
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateCompleteState',
    method: 'POST',
    data: {
      model: processInfo.value.model,
      operation: processInfo.value.operation,
      rtgCode: processInfo.value.rtgCode,
      cplFlag: processInfo.value.cplFlag === 'Y' ? 'N' : 'Y'
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getProcessInfoList(processInfo.value.operation, processInfo.value.brand)
      if (processInfo.value.cplFlag === 'Y') {
        processInfo.value.cplFlag = 'N'
      } else {
        processInfo.value.cplFlag = 'Y'
      }
      editPopup.value.close()
      tipPopup.value.showTipPopup('success', '修改成功！')
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 单项编辑
function edit() {
  if (processInfo.value.cplFlag === 'Y') {
    tipPopup.value.showTipPopup('warn', '已完成的工序无法进行编辑！')
    return
  }
  editPopup.value.close()
  uni.navigateTo({
    url: `/pages/sop/sop-flow?info=${JSON.stringify({...processInfo.value, modelDesc: ''})}`,
    animationType: 'pop-in',
    animationDuration: 300
  })
}

// 批量编辑
function batchEdit() {
  if (processInfo.value.cplFlag === 'Y') {
    tipPopup.value.showTipPopup('warn', '已完成的工序无法进行编辑！')
    return
  }
  editPopup.value.close()
  uni.navigateTo({
    url: `/pages/sop/sop-overall?info=${JSON.stringify({...processInfo.value, modelDesc: ''})}`,
    animationType: 'pop-in',
    animationDuration: 300
  })
}

// 热冷压规格
function pressEdit() {
  if (processInfo.value.cplFlag === 'Y') {
    tipPopup.value.showTipPopup('warn', '已完成的工序无法进行编辑！')
    return
  }
  editPopup.value.close()
  if (user === processInfo.value.insUser) {
    uni.navigateTo({
      url: `/pages/sop/sop-press-update?info=${JSON.stringify({...processInfo.value, modelDesc: ''})}`,
      animationType: 'pop-in',
      animationDuration: 300
    })
  } else {
    uni.navigateTo({
      url: `/pages/sop/sop-press?info=${JSON.stringify({...processInfo.value, modelDesc: ''})}`,
      animationType: 'pop-in',
      animationDuration: 300
    })
  }
}

defineExpose({
  showEditPopup
})
</script>

<template>
  <uni-popup
    ref="editPopup"
    type="center"
    class="edit-popup"
  >
    <view class="container">
      <view class="title">
        请选择编辑方式
      </view>
      
      <view class="description">
        {{ operationMap.get(processInfo.operation) }} - {{ processInfo.model }} - {{ processInfo.rtgCode }}
      </view>
      
      <view
        v-show="(
          (user === processInfo.insUser && processInfo.cplFlag === 'N') ||
          (isAuditer && processInfo.cplFlag === 'Y')
        ) && processInfo.flowNumber > 0"
        class="state"
      >
        <text>未完成</text>
        
        <view @click="changeState()" class="switch">
          <view class="inner" :style="{ transform: processInfo.cplFlag === 'Y' ? 'translateX(0px)' : 'translateX(-40px)' }">
            <view class="ball"></view>
          </view>
        </view>
        
        <text>已完成</text>
      </view>
      
      <view class="type">
        <view class="edit">
          <view @click="edit()" class="button">
            单项编辑
          </view>
          
          <view v-show="processInfo.cplFlag === 'Y'" class="lock">
            <uni-icons type="locked-filled" size="20" color="#c21e56"></uni-icons>
          </view>
        </view>
        
        <view class="batch-edit">
          <view @click="batchEdit()" class="button">
            批量编辑
          </view>
          
          <view v-show="processInfo.cplFlag === 'Y'" class="lock">
            <uni-icons type="locked-filled" size="20" color="#c21e56"></uni-icons>
          </view>
        </view>
      </view>
      
      <view v-show="pressOperationList.includes(processInfo.proSeq)" class="type">
        <view class="press-edit">
          <view @click="pressEdit()" class="button">
            热冷压规格
          </view>
          
          <view v-show="processInfo.cplFlag === 'Y'" class="lock">
            <uni-icons type="locked-filled" size="20" color="#c21e56"></uni-icons>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.edit-popup {
  .container {
    min-width: 300px;
    padding: 0 10px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .title {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 22px;
      font-weight: bold;
    }
    
    .description {
      width: 100%;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
    }
    
    .state {
      width: 100%;
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bold;
    }
    
    .type {
      display: flex;
      justify-content: center;
      align-items: center;
      
      .edit, .batch-edit, .press-edit {
        margin: 10px 15px 15px 15px;
        position: relative;
        
        .button {
          padding: 10px 30px;
          font-size: 20px;
        }
        
        .lock {
          position: absolute;
          right: -10px;
          top: -10px;
        }
      }
      
      .edit {
        color: darkred;
      }
      
      .batch-edit {
        color: lightseagreen;
      }
      
      .press-edit {
        margin: 0 0 15px 0;
        color: purple;
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .switch {
    width: 80px;
    height: 40px;
    margin: 10px;
    background-color: #f6f6f6;
    border-radius: 20px;
    box-shadow: 0 0 5px gray;
    overflow: hidden;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    .inner {
      width: 80px;
      height: 40px;
      background-color: skyblue;
      border-radius: 20px;
      transition: all 0.25s ease;
      
      .ball {
        width: 40px;
        height: 40px;
        margin-left: 40px;
        background-color: white;
        border-radius: 20px;
        box-shadow: 0 0 5px gray;
      }
    }
  }
}
</style>