<template>
	<view class="container">
	 <view class="right-top-top">
		<uni-icons class="back" @click="back" type="back" size="36" style="margin-left:1vw;margin-top:10rpx;"></uni-icons>
		<view class="uni-padding-wrap" style="margin-top:1.22vw;margin-left: 5vw;">
			<view>
				<radio-group>	
				<label class="radio"><radio @click="radioChangeOn(0)" value="0" :checked="cxStatus === 0"/>{{ t('全部') }}</label>
				<label class="radio"><radio @click="radioChangeOn(1)" value="1" :checked="cxStatus === 1"/>{{ t('A线') }}</label>
				<label class="radio"><radio @click="radioChangeOn(2)" value="2" :checked="cxStatus === 2"/>{{ t('B线') }}</label>
				</radio-group>
			</view>
		</view>
		<uni-title
		  type="h2"
		  :title="t('已配套楦头确认')"
		  align="left"
		  style="margin-top:-0.7vw;margin-left: 15vw;"
		></uni-title>
	</view>


	<view class="cTr" style="height: 86%;margin-top:20rpx;">
		<zb-table
			:show-header="true"
			:columns="column"
			:stripe="true"
			:isShowLoadMore="true"
			ref="zbTable"
			:highlight="true"
			show-summary
			:currentChange="currentChange"
			@edit="queRenCk"
			:pullUpLoading="pullUpLoadingAction"
			:summary-method="getSummaries"
			:border="true"
			:cell-style="cellStyle"
			:data="dataList"></zb-table>
	</view>
	
	<view class="uwrap" style="margin-top: 1vw;">
		<text style="margin-left: 1vw;color: lightcoral;font-weight: 600;">{{ t('总笔数：') }} {{countSum}}</text>
	</view>
	
	<!--单击单行弹出详情-->
	<view class="modelPopup">
	  <uni-popup
	    ref="modelPopup"
	    type="center"
		:mask-click="false"
	  >
	  <view class="modelBox" align="center">
	    <uni-title
	      :title="t('确认楦头(可编辑楦头数量)')"
	      type="h2"
	      align="center"
	    ></uni-title>
		<view align="center">{{ t('楦头编号') }}:{{xtbh}}</view>
		<uni-table border  :emptyText="t('暂无更多数据')" style="height: 68%;overflow: auto;">
			
			<!-- 表头行 -->
			<uni-tr>
				<uni-th align="center" style="width:18vh">{{ t('类型') }}(US)</uni-th>
				<uni-th align="center" style="width:12vh">L B R</uni-th>
				<uni-th align="center" style="width:12vh">{{ t('肥度') }}</uni-th>
				<uni-th align="center" v-for="vv in sizeArr">{{vv}}</uni-th>
			</uni-tr>
			<!-- 表格数据行 -->
			<uni-tr v-for="v in dataArr" >
				<uni-td align="center" style="width:18vh">{{v.typesOf}}</uni-td>
				<uni-td align="center" style="width:12vh">{{v.lr_mark}}</uni-td>
				<uni-td align="center" style="width:12vh">{{v.width}}</uni-td>
				<uni-td align="center" v-for="v2 in v.yp" >{{v2}}</uni-td>
			</uni-tr>
			
			<uni-tr v-for="(v2,inn) in dataSizeArr">
				<uni-td align="center" style="width:18vh" >{{ t('楦头数量') }}</uni-td>
				<uni-td align="center" style="width:12vh" >{{v2.lr_mark}}</uni-td>
				<uni-td align="center" style="width:12vh" >{{v2.width}}</uni-td>
				<uni-td align="center" v-for="(v3,i) in v2.yp" style="z_index:99;"> 
					<input type="number" :id="inn+','+i"  @input="handleInputChange" :value="v3"/>
				</uni-td> 
			</uni-tr>
		</uni-table>
		<view style="position: absolute;bottom: 1vh;width: 100%;pointer-events: auto;height: 10%;">
			<view style="display: flex; justify-content: space-evenly;">
				<button @click="onClean()" type="default" plain="true" style="">{{ t('取消') }}</button>
				<button @click="onQuer()" type="primary" plain="true" >{{ t('确认') }}</button>
			</view>
	   </view>
		</view>
	  </uni-popup>
	</view>
	
	</view>
</template>

<script setup>
  import { onMounted,ref, reactive, getCurrentInstance } from 'vue'
  import { onPullDownRefresh } from '@dcloudio/uni-app'
  import { useI18n } from 'vue-i18n'
  import urlPrefix from '@/pages/common/urlPrefix.js'
  
  // 国际化
  const { t } = useI18n()
   
	// const urlPrefix = "http://***********:8200";
	//const urlPrefix = "http://**********:8080";  ***********:8200
	
	//信息数组
	const dataArr=ref([])
	const dataSizeArr=ref([])
	
	const sizeArr=ref([])
	const xtbh=ref()
	
	const selectWt=ref()
	const wtsm1=ref([])
	const switchDay=ref(false)
	//第几页
	const firstPageNo=ref(1)
	const firstPageSize=ref(12)
	const pageCount=ref(0)
	
	//表单数据
	const dataList=ref([])
	const dataList2=ref([])
	
	//扫描状态
	const brandCode=ref(0)
	
	//切换状态
	const cxStatus = ref(0)
	
	//详情弹窗
	const modelPopup = ref()
	
	//状态
	const Qstatus = ref(0)
	
	//总笔数
	const countSum = ref(1)
	
	const insUs=uni.getStorageSync("loUserNo")
	const insName=uni.getStorageSync("loginUserName")
	const iuser=ref("")
	
	//修改弹窗
	const updateSpecificationPopup = ref()
	const updateSpecificationDetail = reactive({
	  pat_flag: "",
	  model_no: "",
	  shp_date:"",
	  ord_no: "",
	  dev_type: "",
	  tot_qty: "",
	  ushelf_no: "",
	  bshelf_no6: "",
	  bshelf_no2: "",
	  bshelf_no1: "",
	  bshelf_no3: ""
	})
	
	//删除参数
	const deleSpecificationDetail = reactive({
	  ord_no: "",
	  pb_dept: "",
	  pb_addr: "",
	  upd_user: insUs,
	  ins_date: ""
	})
	
	//保存参数
	const saveInfos = reactive({
	  sizeList: [],
	  sizeListCot : [],
	  ord_no : "",
	  last_no : "",
	  width : [],
	  lr_mark : [],
	})
	
	//下拉刷新
	 onPullDownRefresh (()=>{	//通过 onPullDownRefresh  可以监听到下拉刷新的动作
			uni.startPullDownRefresh({
				success(){
					dataList.value=[]
					getData();
					getData2()
					uni.stopPullDownRefresh()	//停止当前页面下拉刷新。
				},fail(){}
			})
	    }) 
    const stus=ref(0)

	const column=ref([
		  { name: '', type:'operation',align: "center",label: t('确认'),renders:[
			 {
			   name:'确认',
			   type:'primary',
			   func:'edit'
			 }
		  ]},
		  // { name: 'shp_date', label: dataList.model_no,width:'125'},
		  { name: 'dev_type', label: t('样品类型'),emptyString:'/',width:'100'},
		  { name: 'model_pic', label: t('鞋图'),type:"img",align: "center"},
		  { name: 'model_no', label: t('型体编号'),emptyString:' ',width:'120'},
		  { name: 'last_no', label: t('楦头编号'),emptyString:'/',width:'160'},
		  // { name: 'ord_no', label: '样品单号',width:'185'},
	   //    { name: 'dev_type', label: '样品类型',width:'152',emptyString:'/'},
		  { name: 'tot_qty', label: t('开单数'),sorter:true,emptyString:' ',width:'80'},
		  { name: 'pat_qty', label: t('待配送'),sorter:true,emptyString:' ',width:'80'},
		  { name: 'ypat_qty', label: t('已配送'),sorter:true,emptyString:' ',width:'80'},
		  { name: 'las_qty', label: t('楦头'),sorter:true,emptyString:' ',width:'150'},
		  { name: 'sz_data', label: t('开单号码及数量'),emptyString:' ',width:'170'}
	      
        ]);	

	const deleCsInfos = reactive({
	  mating_no: ""
	})
	
	//判断颜色
	function cellStyle({row, column, rowIndex, columnIndex}){
    }
	

	//点击线别
	async function radioChangeOn(v){
		cxStatus.value=v
		dataList.value=[]
		await getData()
		await getData2()
	}
	
	//点击确认
	async function queRenCk(e){
		saveInfos.sizeListCot=[]
		saveInfos.width=[]
		saveInfos.lr_mark=[]
		dataSizeArr.value=[]
		saveInfos.ord_no = e.ord_no
		saveInfos.last_no = e.last_no		
		xtbh.value=e.last_no
		await uni.request({
					   url: urlPrefix + "/match/getMatchConfirmDetail",
					   data:{
							"ord_No":e.ord_no,
							"last_no":e.last_no
						},
					   method: "POST"
					 }).then(res => {
						const dataCon=res.data.data
						dataArr.value=dataCon
						//赋值
						for (let i=0;i<dataArr.value.length;i++) {
							if(dataArr.value[i].typesOf==="样品数量"){
								saveInfos.sizeListCot.push(dataArr.value[i].yp)
								saveInfos.width.push(dataArr.value[i].width) 
								saveInfos.lr_mark.push(dataArr.value[i].lr_mark) 
								dataSizeArr.value.push(dataArr.value[i])
								dataArr.value[i]= JSON.parse(JSON.stringify(dataArr.value[i]));
								
							}
						}
						sizeArr.value=dataCon[0].cm
						saveInfos.sizeList=dataCon[0].t_name
						//console.log(JSON.stringify(dataArr.value))
						//console.log(JSON.stringify(dataSizeArr.value))
						//saveInfos.sizeListCot=dataCon[0].yp
					 }).catch(err => {
					   console.log(err)
					   uni.showToast({
						title: t('获取数据失败！'),
						icon:"error"
					   });
					 })
					 
		

		modelPopup.value.open()
	}
	//点击取消
	function onClean(){
		modelPopup.value.close();
	}
	
	function  handleInputChange(e) {
      // 执行需要在文本框值改变时执行的方法
      //console.log('文本框的值发生改变'+JSON.stringify(e.currentTarget.id))
	  //console.log(JSON.stringify(e.currentTarget.id)+","+JSON.stringify(e.detail.value))
	  let hs=e.currentTarget.id
	  let va=e.detail.value
	  let gg=hs.split(",")
		//saveInfos.sizeListCot
		//console.log(saveInfos.sizeListCot)
	  saveInfos.sizeListCot[gg[0]][gg[1]]=va
	  
 
      
    }
	
	//点击确认配送
	function onQuer(){
		
		//console.log(JSON.stringify(saveInfos))
		
		uni.request({
			  url: urlPrefix + "/match/insertConfirmDetail",
			  method: "POST",
			  data: saveInfos
			}).then(res => {
			  //console.log(res.data)
			  if (res.data.code) {
			    uni.showToast({
			    	title: t('保存成功！'),
					duration: 2000
			    });
					  getPageCount();
					  getData();
			  } else {
			    uni.showToast({
			    	title: t('保存失败！'),
			    	icon:"error",
					duration: 2000
			    });
			  }
			}).catch(err => {
			  console.log(err)
			})
		
		
		modelPopup.value.close();
	}
	
	//确认状态
	async function queRenCo(e){
		deleCsInfos.mating_no = e.mating_no
		var flagg=e.las_c_flag
		if(flagg=="OK"){
			uni.showToast({
				title: t('单号已OK！'),
				icon:"error",
				duration: 2000
			});
			return
		}
		uni.showModal({
			title:t('提示'),
			content:t('确定楦头状态为OK吗？'),
			success: function (res) {
					if (res.confirm) {
					uni.request({
						  url: urlPrefix + "/match/updateXuanTouState",
						  method: "POST",
						  data: deleCsInfos
						}).then(res => {
						  //console.log(res.data)
						  if (res.data.code) {
						    uni.showToast({
						    	title: t('确认成功！'),
								duration: 2000
						    });
							e.las_c_flag="OK";
						  } else {
						    uni.showToast({
						    	title: t('确认失败！'),
						    	icon:"error",
								duration: 2000
						    });
						  }
						}).catch(err => {
						  console.log(err)
						})
					}else if (res.cancel) {
						return
					}
			}
		});
	
	}
	
	//返回首页
	function back() {
	  uni.navigateBack({
	  	delta: 1,
	  	animationType: 'pop-out',
	  	animationDuration: 200
	  })
	}	
	
	//关闭弹窗
	function backDrom() {
	  dataList2.value=[];
	  updateSpecificationPopup.value.close()
	}	
	
	//选中当前行
	function currentChange(row,index){
	  
	}
	
	//下拉刷新
	const flagLoad=ref(true)
	function pullUpLoadingAction(done){
	  if(!flagLoad.value){
		done('ok')
	    return
	  }
	  setTimeout(()=>{
	    firstPageNo.value++
		done()
	    getData(firstPageNo.value,firstPageSize.value)
	  },300)
	}
	
	//表尾合计
	function getSummaries(param){
	  const { columns, data } = param;
	  const sums = [];
	  columns.forEach((column, index) => {
	    if (index === 0) {
	      sums[index] = t('合计');
	      return;
	    }		
	    if(column.name==='tot_qty'||column.name==='pat_qty'||column.name==='ypat_qty'||column.name==='las_qty'){
	      const values = data.map(item => Number(item[column.name]));
	      if (!values.every(value => isNaN(value))) {
	        sums[index] = values.reduce((prev, curr) => {
	          const value = Number(curr);
	          if (!isNaN(value)) {
	            return prev + curr;
	          } else {
	            return prev;
	          }
	        }, 0);
	        sums[index] += '  ';
	      }
	    }else{
	      sums[index] = ' ';
	    }
	  }); 
	  return sums;
	}
	
	
	//点击详情
	function rowClick(e){
		updateSpecificationDetail.ord_no = e.ord_no
		uni.request({
			   url: urlPrefix + "/match/getNoMatchDetail",
			   data:{
					"brand_no": e.brand_no,
					"status": Qstatus.value
				},
			   method: "POST"
			 }).then(res => {
				 
				dataList2.value = res.data.data
				for(let i=0;i<dataList2.value.length;i++){
					dataList2.value[i].model_pic='data:image/png;base64,'+dataList2.value[i].model_pic
				}
				
			 }).catch(err => {
			   console.log(err)
			   uni.showToast({
			   	title: t('获取数据失败！'),
				icon:"error"
			   });
			 })
		updateSpecificationPopup.value.open()
	}	

	//获取数据
	async function getData(pageNo,pageSize){
		//console.log(pageNo+"，"+pageSize+"，"+cxStatus.value);
		await uni.request({
			   url: urlPrefix + "/match/getMatchConfirm",
			   data:{
					"page_no": pageNo,
					"page_size": pageSize,
					"status": cxStatus.value
			   	},
			   method: "POST"
			 }).then(res => {
				const dataCon=res.data.data
				for(let i=0;i<dataCon.length;i++){
					dataCon[i].model_pic='data:image/png;base64,'+dataCon[i].model_pic
					dataList.value.push(dataCon[i]);
				}
				flagLoad.value=dataCon.length<firstPageSize.value?false:true;
				//console.log(flagLoad.value+"**"+dataCon.length+"**"+firstPageSize.value);
			 }).catch(err => {
			   console.log(err)
			   uni.showToast({
			   	title: t('获取数据失败！'),
				icon:"error"
			   });
			 })
	}
	
	//获取数据2
	async function getData2(){
		await uni.request({
			   url: urlPrefix + "/match/getformingCount",
			   data:{
			   		"status": 1,
					"status3": cxStatus.value
			   	},
			   method: "POST"
			 }).then(res => {
				countSum.value=res.data.data[0]
			 }).catch(err => {
			 })
	}

	//预加载
	onMounted(async () => {
	  await getData()
	  await getData2()
	  //await search()
	})
	
</script>

<style lang="scss">
	.radio{
		margin-left: 1.5vw;
		font-size: 15px;
		font-weight:480;
	}
	page {
	  width: 100%;
	  height: 100%;
	  padding: 2.5% 2% 1.5% 2%;
	  box-sizing: border-box;
	  background-color: white;
	}
	
	.item-tr{
	  height: 40px;
	  font-size: 17px;
	  font-weight:480;
	}
	
	// .cTr .item-tr{
	// 	background-color: seagreen;
	// }
	
	.container {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		border-radius: 10px;
		box-shadow: 0 0 1px 5px #dddddd;
		
	}
	
	.modelPopup {
	  .modelBox {
	    width: 80vw;
	    height: 66vh;
	    border-radius: 1vw;
	    background-color: white;
	  }
	}
	
	::v-deep{
	  .uni-card{
	    margin: 8px!important;
	    padding: 0!important;
	    .uni-card__content{
	      padding: 0!important;
	    }
	  }
	}
	.right-top-top{
		display: flex;
	}
.propertyBox{
	margin-top: 2rpx;
	flex-flow: row;
	justify-content: flex-start;
	display: flex;
	position: -webkit-sticky;
	top: var(--window-top);
}
.inpBr{
	width: 15%;
	margin-left:10rpx;
}
	.left-bottom {
	width: 100%;
	height: 10%;
	display: flex;
	justify-content: center;
	align-items: center;

	.uni-pagination__total {
	  margin-right: 0.4375rem !important;
	}

	.uni-pagination__num-tag {
	  min-width: 1.875rem !important;
	  background-color: #F0F0F0 !important;
	}

	.page--active {
	  color: white !important;
	  background-color: deeppink !important;
	}
	}
	
	.updateSpecificationPopup {
	  .updateSpecificationBox {
	    width: 96vw;
	    height: 90vh;
		
	    border-radius: 1vw;
	    background-color: white;
	    
	    .updateSpecificationData {
	      width: 100%;
	      position: relative;
	      display: flex;
	      justify-content: center;
	      align-items: flex-start;
	      flex-wrap: wrap;
		  height: 89%;
	      
	      .updateSpecificationAttribute {
	        width: 35%;
	        margin-left: 15%;
	        
	        .uni-easyinput {
	          width: 70%;
	          margin-left: 1rem;
	        }
	        
	        .uni-stat__select {
	          width: 70%;
	          margin-left: 1rem;
	        }
	        
	        .uni-numbox {
	          margin-left: 1rem;
	        }
	        
	        .uni-file-picker {
	          margin-left: 1rem;
	        }
	      }
	    }
	  }
	}
	
	.submit {
	  display: flex;
	  justify-content: center;
	  align-items: center;
	}
	.back {
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		position: absolute;
		left: 1.5%;
		width: 50px;
		height: 50px;
		top: 6%;
		border-radius: 50%;
		box-shadow: 0 0 5px gray;
		cursor: pointer;
		z-index: 1;
	}
</style>


