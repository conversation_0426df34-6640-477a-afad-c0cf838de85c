<!-- 动作弹框 -->
<script setup>
import { ref, watch, inject } from 'vue'
import urlPrefix from '@/pages/common/urlPrefix.js'
import TipPopup from '@/pages/sop/common/tip-popup.vue'

// 用户
const user = uni.getStorageSync("loUserNo")

// 提示弹框
const tipPopup = ref()

// 动作弹框
const overallActionPopup = ref()

// 工序流程详情
const flowDetail = ref({})

// 标签
const tag = ref('全部')
// 标签列表
const tagList = ref(['全部'])
// 动作列表
const overallActionList = ref([])
// 显示动作列表
const showOverallActionList = ref([])
// 动作输入框
const overallActionInput = ref('')
// 是否聚焦动作输入框
const focusOverallActionInput = ref(false)

// 工序信息
const processInfo = inject('processInfo')
// 工序流程列表
const processFlowList = inject('processFlowList')
// 获取工序流程列表
const getOverallFlow = inject('getOverallFlow')
// 是否选择所有工序流程
const isSelectedAllProcessFlow = inject('isSelectedAllProcessFlow')

// 获取动作列表
async function getOverallActionList(param) {
  await uni.request({
    url: urlPrefix + '/sop/getFlowActionList',
    method: 'POST',
    data: {
      proSeq: param
    }
  }).then(res => {
    if (res.data.code) {
      overallActionList.value = res.data.data ? res.data.data : []
      showOverallActionList.value = res.data.data ? res.data.data : []
      for (let item of overallActionList.value) {
        if (!tagList.value.includes(item.tag)) {
          tagList.value.push(item.tag)
        }
      }
    } else {
      overallActionList.value = []
      showOverallActionList.value = []
      tipPopup.value.showTipPopup('warn', '暂无动作列表数据！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
}

// 显示动作弹框
async function showOverallActionPopup(param1, param2) {
  tag.value = '全部'
  tagList.value = ['全部']
  overallActionInput.value = param1.actions
  flowDetail.value = {
    ...param1,
    imgList: []
  }
  
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  
  await getOverallActionList(param2)
  
  uni.hideLoading()
  
  // if (overallActionList.value.length === 0) {
  //   return
  // }
  
  overallActionPopup.value.open()
}

// 选择动作
async function selectOverallAction(param) {
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/selectOverallAction',
    method: 'POST',
    data: {
      id: param.id,
      sopFlowDetail: JSON.stringify({
        ...flowDetail.value,
        actions: param.actionCn,
        updUser: user
      })
    }
    // url: urlPrefix + '/sop/updateOverallFlow',
    // method: 'POST',
    // data: {
    //   ...flowDetail.value,
    //   actions: param.actionCn,
    //   updUser: user
    // }
  }).then(async (res) => {
    if (res.data.code) {
      await getOverallFlow()
      tipPopup.value.showTipPopup('success', '修改成功！')
      overallActionPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('error', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

// 修改整体流程
async function updateOverallFlow() {
  if (!overallActionInput.value) {
    tipPopup.value.showTipPopup('warn', '请输入动作！')
    return
  }
  
  uni.showLoading({
    title: '修改中...',
    mask: true
  })
  
  await uni.request({
    url: urlPrefix + '/sop/updateOverallFlow',
    method: 'POST',
    data: {
      ...flowDetail.value,
      actions: overallActionInput.value,
      updUser: user
    }
  }).then(async (res) => {
    if (res.data.code) {
      await getOverallFlow()
      tipPopup.value.showTipPopup('success', '修改成功！')
      overallActionPopup.value.close()
    } else {
      tipPopup.value.showTipPopup('success', '修改失败！')
    }
  }).catch(err => {
    tipPopup.value.showTipPopup('error', '请检查网络！')
  })
  
  uni.hideLoading()
}

watch(tag, () => {
  if (tag.value === '全部') {
    showOverallActionList.value = overallActionList.value
  } else {
    showOverallActionList.value = overallActionList.value.filter(item => item.tag === tag.value)
  }
})

defineExpose({
  showOverallActionPopup
})
</script>

<template>
  <uni-popup
    ref="overallActionPopup"
    type="center"
    :is-mask-click="false"
    class="overall-action-popup"
  >
    <view class="container">
      <view class="top-bar">
        <view
          @click="overallActionPopup.close()"
          class="cancel button"
        >
          <uni-icons type="closeempty" size="30" color="darkred"></uni-icons>
        </view>
        
        <view class="title">
          请选择动作 - {{ flowDetail.skey }}
        </view>
        
        <view
          @click="updateOverallFlow()"
          class="submit button"
        >
          <uni-icons type="checkmarkempty" size="30" color="lightseagreen"></uni-icons>
        </view>
      </view>
      
      <view class="flow-action-input flex-row-center">
        <textarea
          v-model="overallActionInput"
          @focus="focusOverallActionInput = true"
          @blur="focusOverallActionInput = false"
          placeholder="请输入动作"
          class="textarea"
          :style="{
            boxShadow: focusOverallActionInput ? '0 0 5px blue' : '0 0 5px gray'
          }"
        ></textarea>
        
        <view
          v-show="overallActionInput"
          @click="overallActionInput = ''"
          class="clear-textarea button"
        >
          <uni-icons type="closeempty" size="30" color="brown"></uni-icons>
        </view>
      </view>
      
      <view class="flow-action-tag-list">
        <view
          v-for="item in tagList"
          @click="tag = item"
          class="flow-action-tag button"
          :style="{
            backgroundColor: tag === item ? '#ccc' : 'transparent'
          }"
        >
          {{ item ? item : '其它' }}
        </view>
      </view>
      
      <view class="flow-action-list">
        <view
          v-for="(item, index) in showOverallActionList"
          :key="index"
          v-show="item.actionCn && item.actionCn.includes(overallActionInput.toUpperCase())"
          class="flow-action flex-row-center"
        >
          <view
            @click="selectOverallAction(item)"
            class="button"
            :style="{
              color: item.actionCn === flowDetail.actions ? 'darkorchid' : 'darkmagenta'
            }"
          >
            {{ item.tag ? (item.tag + ' - ' + item.actionCn) : item.actionCn }}
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  
  <tip-popup ref="tipPopup" />
</template>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-size: 18px;
}

.overall-action-popup {
  .container {
    width: 500px;
    background-color: #fdf6e3;
    border-radius: 10px;
    box-shadow: 0 0 5px white;
    overflow: hidden;
    
    .top-bar {
      width: 100%;
      height: 70px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title {
        font-size: 22px;
        font-weight: bold;
      }
      
      .cancel, .submit {
        width: 75px;
        height: 50px;
      }
    }
    
    .flow-action-input {
      width: 100%;
      height: 160px;
      position: relative;
      
      .textarea {
        width: 350px;
        height: 140px;
        padding: 5px;
      }
      
      .clear-textarea {
        width: 70px;
        height: 40px;
        position: absolute;
        right: 80px;
        bottom: 15px;
      }
    }
    
    .flow-action-tag-list {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      
      .flow-action-tag {
        width: calc((100% - 80px) / 4);
        height: 40px;
        margin: 10px;
      }
    }
    
    .flow-action-list {
      height: 280px;
      overflow: auto;
      
      // /* #ifdef WEB */
      // &::-webkit-scrollbar {
      //   display: none;
      // }
      // /* #endif */
      
      .flow-action {
        width: 100%;
        padding: 10px;
        position: relative;
        
        .button {
          width: 350px;
          min-height: 50px;
          color: darkmagenta;
          font-size: 20px;
          padding: 5px;
        }
      }
    }
  }
  
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    /* #ifdef WEB */
    cursor: pointer;
    /* #endif */
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 0 1px gray;
      /* #ifdef APP */
      background-color: #ccc;
      /* #endif */
    }
    
    /* #ifdef WEB */
    &:hover {
      background-color: #ccc;
    }
    /* #endif */
  }
  
  .textarea {
    border-radius: 10px;
    box-shadow: 0 0 5px gray;
    transition: all 0.1s linear;
    
    &:deep(.uni-textarea-placeholder), &:deep(.uni-textarea-textarea) {
      width: 100%;
      height: 100%;
      font-size: 20px;
      font-weight: bold;
    }
  }
  
  .flex-row-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>