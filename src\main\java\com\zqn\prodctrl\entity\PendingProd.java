package com.zqn.prodctrl.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/9/10 11:29
 */
@Data
public class PendingProd {

    //項次
    private String item_no;

    //上綫日期
    @JsonFormat(pattern = "YY/MM/dd")
    private Date online_date;

    //樣品單號
    private String ord_no;

    //樣品類型
    private String dev_type;

    //鞋圖
    private byte[] model_pic;

    //面師/底師
    private String upper_ser;
    private String sole_ser;

    //業務
    private String dutyer;

    //型體
    private String model_no;

    //sku
    private String sku_no;

    //楦頭編號
    private String last_no;

    //楦頭數量
    private String app_qty;

    //派工日
    @JsonFormat(pattern = "YY/MM/dd")
    private Date wo_date;

    //出貨日
    @JsonFormat(pattern = "YY/MM/dd")
    private Date shp_date;

    //樣品雙數
    private String tot_qty;

    //型體確認
    private String model_cfm_flag;

    //形體確認註記
    private String dev_types;

    //模具新舊
    private String mod_old;

    //庫存標記
    private String kc_flag;

    //面料交期
    private String pur_u_flag;

    //底料交期
    private String pur_s_flag;

    //品牌
    private String brand_no;

    //品牌描述
    private String brand_desc;

    private Integer Tot_qty_sum;

    private String phase;

    private String pd_line;

}
